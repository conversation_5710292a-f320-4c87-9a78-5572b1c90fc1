/*---------------------------------------
  CUSTOM PROPERTIES ( VARIABLES )             
-----------------------------------------*/
:root {
  --white-color:                  #ffffff;
  --primary-color:                #535da1;
  --secondary-color:              #44A2C6;
  --section-bg-color:             #f9f9f9;
  --dark-color:                   #1e1e1e;
  --p-color:                      #1e1e1e;
  --border-color:                 #e9eaeb;
  --featured-border-color:        #727aab;

  --body-font-family:             'Montserrat', sans-serif;

  --h1-font-size:                 3rem;
  --h2-font-size:                 2.5rem;
  --h3-font-size:                 2rem;
  --h4-font-size:                 1.75rem;
  --h5-font-size:                 1.5rem;
  --h6-font-size:                 1.25rem;
  --p-font-size:                  14px;
  --menu-font-size:               0,8125rem;
  --copyright-font-size:          14px;

  --border-radius-large:          100px;
  --border-radius-medium:         20px;
  --border-radius-small:          10px;

  --font-weight-normal:           400;
  --font-weight-medium:           500;
  --font-weight-bold:             700;
}

body {
    background: #FEFEFE;
    font-family: var(--body-font-family); 
}


/*---------------------------------------
  TYPOGRAPHY               
-----------------------------------------*/

h2,
h3,
h4,
h5,
h6 {
  color: var(--dark-color);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: var(--font-weight-bold);
}

h1 {
  font-size: var(--h1-font-size);
}

h2 {
  font-size: var(--h2-font-size);

}

h3 {
  font-size: var(--h3-font-size);
}

h4 {
  font-size: var(--h4-font-size);
}

h5 {
  font-size: var(--h5-font-size);
  line-height: normal;
}

h6 {
  font-size: var(--h6-font-size);
}

p {
  color: var(--p-color);
  font-size: var(--p-font-size);
  font-weight: var(--font-weight-normal);
}

ul li {
  color: var(--p-color);
  font-size: var(--p-font-size);
  font-weight: var(--font-weight-normal);
}

a, 
button {
  touch-action: manipulation;
  transition: all 0.3s;
}
button:active{
  color: var(--secondary-color);
}
a:active{
  color: var(--secondary-color);
}
a {
  color: var(--p-color);
  text-decoration: none;
}

a:hover {
  color: var(--secondary-color);
}

::selection {
  background: var(--secondary-color);
  color: var(--white-color);
}

::-moz-selection {
  background: var(--secondary-color);
  color: var(--white-color);
}

.section-padding {
  padding-top: 120px;
  padding-bottom: 120px;
}

b,
strong {
  font-weight: var(--font-weight-bold);
}

.section-title-wrap {
  background: var(--secondary-color);
  border-radius: var(--border-radius-small);
  padding: 10px 30px;
}
.form-select:focus{
  border-color: rgba(68, 162, 198, 0.8);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset, 0 0 8px rgba(68, 162, 198, 0.6);
  outline: 0 none;
}
textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus,

.uneditable-input:focus {   
  border-color: rgba(68, 162, 198, 0.8);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset, 0 0 8px rgba(68, 162, 198, 0.6);
  outline: 0 none;
}
input[type="radio"]:checked,
input[type="checkbox"]:checked{
  background-color: #44A2C6;
  border-color: #44A2C6;

}
input[type="radio"]{
  border-color: #44A2C6;
}

.btn-primary{
  background-color: #44A2C6;
  border-color: #44A2C6;
}
.btn-primary:active{
  background-color: #44A2C6!important;
  border-color: #44A2C6!important;;
}
.btn:active{
  background-color: #44A2C6!important;
  border-color: #44A2C6!important;;
}
.btn:focus{
  background-color: #44A2C6!important;
  border-color: #44A2C6!important;;
}
.btn-primary:hover{
  background-color: rgba(68, 162, 198, 0.8);
  border-color:   rgba(68, 162, 198, 0.8);
}
.btn-primary:focus{
  background-color: rgba(68, 162, 198, 0.8)!important;
  border-color:   rgba(68, 162, 198, 0.8)!important;;
}
.form-select.decorated option:checked  {
  background-color: #44A2C6;
}
.form-check-input{
border:2px solid #44A2C6;
}
.blue{
  color: #44A2C6;
}
.second-color{
  color: #F6CA03;
}
.black{
  color: #1e1e1e;
}
section{
  margin-bottom: 3rem;
}
section h6{
  margin-bottom:2rem;
}
.h-flex{
display: flex;
flex-direction: row;
flex-wrap: wrap;
}
.h-flex-between{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  }
.h-flex-responsive{
  display: flex;
  flex-direction: row;
  }
.v-flex{
  display:flex ;
  flex-direction:column ;
}
.h-flex-responsive-nav{
  display: flex;
  flex-direction: row;
  }
  .conseils-card{
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
     border-radius: 15px;
     padding: 25px 30px;
     display: flex;
     flex-direction: row;
     align-items: center;
     justify-content: space-between;
     height:117px;
    }
    .conseils-card p {
      font-size: 18px;
      font-weight: 600;
    }
    .conseils-card a {
      color: #44a2c6;
    }

.vertical-center{
  display: flex;
  flex-direction: column;
justify-content: center;
align-items: center;
height:100%;
text-align:left;
}
.filter-row p{
color: #D2D1D1;
}
.filter-row p:hover{
  color: #44A2C6;
  }
  .filter-row span{
    margin-right: 1rem;
    margin-left: 0.2rem;
  }
  .filter-row a{
    margin-right: 0.8rem;
  }
  .filter-row i{
    margin-right: 0.2rem;
  }
  .filter-row .active p{
    color: #44A2C6;
  }
/*---------------------------------------
  TOOLTIP
---------------------------------------*/
/* Tooltip container */
.tooltip {
  position: relative;
  display: inline-block;
  border-bottom: 1px dotted black; /* If you want dots under the hoverable text */
}

/* Tooltip text */
.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background-color: black;
  color: #fff;
  text-align: center;
  padding: 5px 0;
  border-radius: 6px;
 
  /* Position the tooltip text - see examples below! */
  position: absolute;
  z-index: 1;
}

/* Show the tooltip text when you mouse over the tooltip container */
.tooltip:hover .tooltiptext {
  visibility: visible;
}

/*---------------------------------------
  AVATAR IMAGE               
-----------------------------------------*/
.avatar-image {
  border-radius: var(--border-radius-large);
  width: 160px;
  height: 160px;
  object-fit: cover;
}

.avatar-image-large {
  width: 90.4px;
  height: 90.4px;
}


/*---------------------------------------
  PRE LOADER               
-----------------------------------------*/
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
  background: none repeat scroll 0 0 var(--white-color);
}

.spinner {
  border: 1px solid transparent;
  border-radius: var(--border-radius-small);
  position: relative;
}

.spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 45px;
  height: 45px;
  margin-top: -10px;
  margin-left: -10px;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  border-top-color: var(--white-color);
  animation: spinner .9s linear infinite;
}

@-webkit-@keyframes spinner {
  to {transform: rotate(360deg);
  }
}

@keyframes spinner {
  to {transform: rotate(360deg);}
}


/*---------------------------------------
  CUSTOM ICON               
-----------------------------------------*/
.navbar-icon {
  background: var(--white-color);
  border-radius: var(--border-radius-large);
  color: var(--dark-color);
  width: 47px;
  height: 47px;
  line-height: 47px;
  text-align: center;
}

.is-sticky .navbar-icon {
  background: var(--secondary-color);
  color: var(--white-color);
}

.form-check-icon {
  color: var(--secondary-color);
}


/*---------------------------------------
  CUSTOM BUTTON               
-----------------------------------------*/
.btn{
  font-size: 13px;

}
.button-black{
  background-color:#424143;
   color:#fff;

   border-radius: 10px;
   padding:0.75rem 3rem;
}
.button-black:hover{
  background-color:rgba(26,25,26,.6);
  color: #fff;
}
.button-black:active{
  background-color:rgba(26,25,26,.8)!important;
  border:rgba(26,25,26,.8)!important;

  color: #fff!important;
}
.button-black:focus{
  background-color:rgba(26,25,26,.8)!important;
  border:rgba(26,25,26,.8)!important;
  color: #fff!important;
}
.button-white{
  background-color:rgba(255,255,255,.8);;
   color:#1e1e1e;
   margin-left: auto;
   margin-right: auto;
   border-radius: 10px;
   padding:0.75rem 3rem;
}
.button-white:hover{
  background-color:rgba(255,255,255,1);
  color: #1e1e1e;
}
.button-white:focus{
  background-color:rgba(255,255,255,1)!important;
  border:rgba(255,255,255,1)!important;

  color: #1e1e1e!important;
}
.button-outline-grey{
   margin-left: auto;
   margin-right: auto;
   border-radius: 10px;
   padding:0.75rem 3rem;
}
.custom-btn,
.navbar .custom-btn {
  font-size: var(--p-font-size);
  font-weight: var(--font-weight-bold);
}

.navbar .custom-btn {
  background: transparent;
  border-width: 2px;
  border-style: solid;
  border-color: var(--white-color);
  color: var(--white-color);
  padding: 8px 22px;
}

.navbar .custom-btn:hover {
  background: var(--white-color);
  border-color: transparent;
  color: var(--secondary-color);
}

.custom-btn {
  background: var(--secondary-color);
  border-radius: var(--border-radius-large);
  color: var(--white-color);
  font-weight: var(--font-weight-bold);
  padding: 12px 24px;
}

.custom-btn:hover {
  background: var(--primary-color);
  box-shadow: 0 1rem 3rem rgba(0,0,0,.175);
  color: var(--white-color);
}

.custom-border-btn {
  background: transparent;
  border: 2px solid var(--border-color);
  color: var(--p-color);
}

.custom-border-btn:hover {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--white-color);
}

.custom-link {
	background-color: var(--primary-color);
}

.custom-link:hover {
	background-color: var(--secondary-color);
}

.box{
  width: 145px;
  height: 100%;
}
.brand-carousel {
  background: #fff;
  margin: 0;
}
.owl-dots {
  text-align: center;
  margin-top: 40px;
}

.owl-carousel .owl-stage {
  display: flex;
  align-items: center;
}
.owl-dot {
    display: inline-block;
    height: 6px !important;
    width: 18px !important;
    background-color: #D2D1D1 !important;
    opacity: 1;
    border-radius: 10px;
    margin: 0 5px;
}

.owl-dot.active {
  background-color: #F6CA03 !important;
  width: 25px!important;
  border-radius: 10px;

}

/*---------------------------------------
  NAVIGATION              
-----------------------------------------*/
.sticky-wrapper {
  position: relative;
  z-index: 222;
  height: auto !important;
}

.is-sticky,
.is-sticky .navbar .container {
  background: var(--white-color);
  box-shadow: 0 0.5rem 0.75rem rgb(0 0 0 / 10%);
}
.is-sticky .submenu .container {
  background: #424143;
  box-shadow: 0 0.5rem 0.75rem rgb(0 0 0 / 10%);
}
.is-sticky,
.is-sticky .navbar-profil .container {
  box-shadow: none
}
.is-sticky .navbar .container {
border:none;
}
.is-sticky .navbar-brand,
.is-sticky .navbar-brand:hover {
  color: var(--dark-color);
}

.is-sticky .navbar-nav .nav-link {
  color: var(--p-color);
  font-weight:600;
}
.is-sticky .submenu .navbar-nav .nav-link {
  color: #fff;
  font-weight:300;
}
.is-sticky .navbar .custom-btn {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
}

.is-sticky .navbar .custom-btn:hover {
  background: var(--secondary-color);
  color: var(--white-color);
}

.navbar {
  background: transparent;
  position: absolute;
  z-index: 9;
  right: 0;
  left: 0;
  transition: all 0.3s;
  /*padding-top: 15px;*/
  padding:0;
  padding-bottom: 0;
}
.navbar .navbar-expand-lg{
  padding-top:0;
}
.navbar-nav{
  margin-left: auto;
}
.navbar-submenu{
  display: none;
  background-color: #424143;
}
.submenu .navbar-nav{
  margin-right: auto;
  margin-left: 0;
}
.separator:after{
  color:#bdbdbd;
  font-size: small;
  content: "|";

  padding: 0 0px;
}
.navbar .container {
/*  border-radius: var(--border-radius-small);*/
max-width: 100%;
  padding: 0px 120px;
  margin-left: 0;
  margin-right: 0;
  border-bottom: 0.2px solid rgba(255,255,255,0.25);
}
.logo {
  /*  border-radius: var(--border-radius-small);*/
  content: url("../images/logo-white.png");
  max-height: 55px;
  margin-top: 0.1rem;
  margin-bottom: 0.1rem;
  }
  .is-sticky .logo {
    /*  border-radius: var(--border-radius-small);*/
    content: url("../images/logo-black.png");
    max-height: 55px;
    margin-top: 0.1rem;
    margin-bottom: 0.1rem;
    }
.navbar-brand {
  font-size: var(--h4-font-size);
  font-weight: var(--font-weight-bold);
  padding:0;
}

.navbar-brand,
.navbar-brand:hover {
  color: var(--white-color);
}

.navbar-expand-lg .navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
  margin-right: 15px;
  margin-left: 15px;
}
.nav-icon{
  width:16px;
  margin-right:5px;
}
/*
.navbar-expand-lg .navbar-nav .nav-link-separator-right{
  padding-right: 0;
  padding-left: 0;
  margin-right: 7.5px;
  margin-left: 15px;
}
.navbar-expand-lg .navbar-nav .nav-link-separator-left{
  padding-right: 0;
  padding-left: 0;
  margin-right: 15px;
  margin-left: 7.5px;
}*/
.navbar-nav .nav-link {
  display: inline-block;
  color: var(--section-bg-color);
  font-size: var(--menu-font-size);
  font-weight: var(--font-weight-medium);
/*  text-transform: uppercase;*/
  position: relative;
  padding-top: 25px;
  padding-bottom: 25px;
}
.submenu .nav-link{
  padding-top: 15px;
  padding-bottom: 15px;
}
.navbar-nav .nav-link::after {
  content: "";
  background: transparent;
  position: absolute;
  bottom: 0px;
  right: 0;
  left: 0;
  width: 100%;
  height: 2px;
}

.navbar-nav .nav-link.active::after, 
.navbar-nav .nav-link:hover::after {
  background: var(--secondary-color);
}

.navbar-nav .nav-link.active, 
.navbar-nav .nav-link:hover {
  color: var(--secondary-color);
}
.submenu .navbar-nav .nav-link.active::after, 
.submenu .navbar-nav .nav-link:hover::after {
  background:none;
}

.submenu .navbar-nav .nav-link.active, 
.submenu .navbar-nav .nav-link:hover {
  color: var(--secondary-color);
}
.navbar-toggler {
  border: 0;
  padding: 0;
  cursor: pointer;
  margin: 0;
  width: 30px;
  height: 35px;
  outline: none;
}

.navbar-toggler:focus {
  outline: none;
  box-shadow: none;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon {
  background: transparent;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:before,
.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:after {
  transition: top 300ms 50ms ease, -webkit-transform 300ms 350ms ease;
  transition: top 300ms 50ms ease, transform 300ms 350ms ease;
  transition: top 300ms 50ms ease, transform 300ms 350ms ease, -webkit-transform 300ms 350ms ease;
  top: 0;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:before {
  transform: rotate(45deg);
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:after {
  transform: rotate(-45deg);
}

.navbar-toggler .navbar-toggler-icon {
  background: var(--white-color);
  transition: background 10ms 300ms ease;
  display: block;
  width: 30px;
  height: 2px;
  position: relative;
}

.navbar-toggler .navbar-toggler-icon:before,
.navbar-toggler .navbar-toggler-icon:after {
  transition: top 300ms 350ms ease, -webkit-transform 300ms 50ms ease;
  transition: top 300ms 350ms ease, transform 300ms 50ms ease;
  transition: top 300ms 350ms ease, transform 300ms 50ms ease, -webkit-transform 300ms 50ms ease;
  position: absolute;
  right: 0;
  left: 0;
  background: var(--white-color);
  width: 30px;
  height: 2px;
  content: '';
}

.navbar-toggler .navbar-toggler-icon::before {
  top: -8px;
}

.navbar-toggler .navbar-toggler-icon::after {
  top: 8px;
}


/* The container <div> - needed to position the dropdown content */
.dropdown {
  position: relative;
  display: inline-block;
}

/* Dropdown Content (Hidden by Default) */
.dropdown-content {
  display: none;
  position: absolute;
  right:0;
  background-color: #f1f1f1;
  min-width: 300px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 1;
}

/* Links inside the dropdown */
.dropdown-content a {
  color: #1e1e1e;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}
.dropdown-conseil:hover .content-conseil {
  display: block;
}
.dropdown-recruteur:hover .content-recruteur {
  display: block;
}
.dropdown-candidat:hover .content-candidat {
  display: block;
}
.dropdown-content a:hover {background-color: #ddd;}
.dropdown:hover .dropbtn {background-color: #3e8e41;}

/*---------------------------------------
  HERO              
-----------------------------------------*/
.hero {
  background-image: url("../images/banner_signup.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 341px;
  padding-bottom: 271px;
}
.container-form{
  padding-left:2rem ;
  padding-right:2rem ;
}
.hero .container{
  max-width:1221px;
}
.hero .card{
  border: none;
}
.hero .card-header{
  background-color: rgb(66,65,67,0.9);
  padding-top: 1rem;
}
.hero .card-body{
  background-color: rgb(249,249,249,0.9);
}

.form-group{
  margin-bottom: 1.25rem;
}
.form-group label{
  margin-bottom: 0.625rem;
  font-size: 14px;
  font-weight: 700;
}
.hero .form-group input{
  font-size: var(--p-font-size);

}
.title-col{
  display: flex;
  justify-content: center;
  align-items: center;
}
.hero .card-header h6{
font-size: 20px;
color: #fff;
}
.hr-signup{
  max-width:45%;
  height: 1px;
  background-color: #44A2C6;
  border-radius: 10px;
}

@media  screen and (min-width: 991px) {
  .hero {
    height: 60vh;
  }
}


.hero-title {
	font-size: 44px;
}

.hero h2 {
	font-size: 38px;
  letter-spacing: normal;
  font-weight: 700;
  padding: 2rem 0;
  color:#ffffff;

}
.hero .small-text{
font-size:var(--menu-font-size);
color: #969696;
margin-bottom: 0;
}

.hero-text {
  position: relative;
  z-index: 22;
  top: 70px;
}

.hero-image-wrap {
  background: var(--white-color);
  border-radius: 100%;
  width: 350px;
  height: 350px;
  position: absolute;
  z-index: 22;
  top: -50px;
  right: 0;
  left: 0;
  margin: auto;
  pointer-events: none;
}

.hero-image {
  position: absolute;
  z-index: 22;
  top: 0;
  width: 100%;
  min-width: 550px;
}

.hero svg {
  position: absolute;
  z-index: 2;
  bottom: 0;
  right: 0;
  left: 0;
  overflow: hidden;
  height: 100%;
  pointer-events: none;
}
/*---------------------------------------
  LOGIN
----------------------------------------*/
/* Progress Bar */





#heading {
  text-transform: uppercase;
  color: #44A2C6;
  font-weight: normal
}

#msform {
  text-align: center;
  position: relative;
  margin-top: 20px
}

#msform fieldset {
  background: white;
  border: 0 none;
  border-radius: 0.5rem;
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  padding-bottom: 20px;
  position: relative
}

.form-card {
  text-align: left
}

#msform fieldset:not(:first-of-type) {
  display: none
}



.card {
  z-index: 0;
  border: none;
  position: relative
}

.fs-title {
  font-size: 25px;
  color: #44A2C6;
  margin-bottom: 15px;
  font-weight: normal;
  text-align: left
}

.purple-text {
  color: #44A2C6;
  font-weight: normal
}

.steps {
  font-size: 25px;
  color: gray;
  margin-bottom: 10px;
  font-weight: normal;
  text-align: right
}

.fieldlabels {
  color: gray;
  text-align: left
}

#progressbar {
  margin-bottom: 30px;
  overflow: hidden;
  color: lightgrey
}

#progressbar .active {
  color: #44A2C6
}
#progressbar .was-active {
  color: #424143
}
#progressbar li {
  list-style-type: none;
  font-size: 15px;
  width: 25%;
  float: left;
  position: relative;
  font-weight: 400
}

#progressbar #account:before {
  font-family: FontAwesome;
  content: "\f13e"
}

#progressbar #personal:before {
  font-family: FontAwesome;
  content: "\f007"
}

#progressbar #payment:before {
  font-family: FontAwesome;
  content: "\e4e8"
}

#progressbar #confirm:before {
  font-family: FontAwesome;
  content: "\f00c"
}

#progressbar li:before {
  width: 50px;
  height: 50px;
  line-height: 45px;
  display: block;
  font-size: 20px;
  color: #ffffff;
  background: lightgray;
  border-radius: 50%;
  margin: 0 auto 10px auto;
  padding: 2px
}

#progressbar li:after {
  content: '';
  width: 100%;
  height: 2px;
  background: lightgray;
  position: absolute;
  left: 0;
  top: 25px;
  z-index: -1
}

#progressbar li.active:before,
#progressbar li.active:after {
  background: #44A2C6
}

#progressbar li.was-active:before,
#progressbar li.was-active:after {
  background: #424143
}
.progress {
  height: 20px
}

.progress-bar {
  background-color: #44A2C6
}

.fit-image {
  width: 100%;
  object-fit: cover
}
/* login form*/
.login-header{
  background-image: url("../images/banner_login.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 242.75px;
  padding-bottom: 142.75px;
}
.login-form{
  padding:0px 0; 
}
.info-section{
  margin-bottom: 3rem;
}
.bottom-login{
  display: flex;
  flex-direction: row;
}
.bottom-login i {
  margin-right: 1rem;
}

/*---------------------------------------
  ACCUEIL              
-----------------------------------------*/
.index-header{
  background-image: url("../images/banner_home.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 150px;
  padding-bottom: 70px;
  padding-left: 6rem;
  padding-right: 6rem;
}
.rubrique-card h6{
  color:white;
}
.rubrique-card a{
  color: #F6CA03;
  margin-bottom: 0.5rem;
  font-weight: 600;
}
.rubrique-card.card1{
  background-image: url("../images/Group 265.png");

}
.rubrique-card.card2{
  background-image: url("../images/Group 264.png");

}
.rubrique-card.card3{
  background-image: url("../images/Group 266.png");

}
.rubrique-card.card4{
  background-image: url("../images/Group 263.png");

}
.rubrique-card.card4 p{
  font-size: 16px;
  margin-bottom: 0.5rem;
  font-weight: 600;


}
.rubrique-card{
  
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  border-radius: 15px;
}
.text-color{
  color:rgba(0, 0, 0, 0.7);
  font-size:13px;
}
.jobs-index span{
  font-size: 15px;
}
.jobs-index .bg-light{
  border-radius: 8px;
  margin:0 10px;
  margin-bottom:1rem;

}
.job-search-card{
  border-radius: 1rem;
  display: flex;
  flex-direction: row;
  background-color: #F9F9F9;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);;
}
.job-icon{
  padding: 2rem;
  background-color: #44A2C6;
  border-top-left-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.job-form{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 2rem;
}
.middle-form{
  margin-left: 1rem;
  margin-right: 1rem;
}

.job-button{
padding-left:1rem;
}
.job-button button{
  width:130px;

}
.small-logo{
  width:46px;
  height:100%;
  margin-right: 0.5rem;
}
/* job card form */
.form-item {
  border-bottom: 1px solid #AFAFAF;
  overflow: hidden;
}

.job-form label {
  float: left;
  width: 20px;
  margin-right: 10px;
  text-align: center;
}

.job-form input,.job-form select {
  float: left;
  width: 100%;
  margin-top: 1rem;
  padding: 0 0 6px 0;
  background-color: transparent;
  color: #1e1e1e;
  font: var(--p-font-size);
  font-weight: 200;
  outline: none;
  border: none;
  box-shadow: none;

}

.job-form input::-webkit-input-placeholder {
  padding: 0;
  color: #999999;
  font-weight: 200;
}
.job-form input:-moz-placeholder {
  color: #cdcdcd;
  font-weight: 200;
}
.job-form select::-webkit-input-placeholder {
  padding: 0;
  color: #999999;
  font-weight: 200;
}
.job-form select:-moz-placeholder {
  color: #cdcdcd;
  font-weight: 200;
}
.job-form input:-ms-input-placeholder {
  color: #cdcdcd;
  font-weight: 200;
}
.job-form ,select:-ms-input-placeholder{
  color: #cdcdcd;
  font-weight: 200;
}
.job-form input:focus{
  box-shadow: none;
  border:none;
  outline:none;
}
.job-form select:focus{
  box-shadow: none;
  border:none;
  outline:none;
}
.form-padding{
  padding:0 7rem;
}
/* Cards section */
.carousel-section .carousel-col{
 padding: 2rem; 
}
.centered-text-card {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.centered-text-card a{
  color: #fff;
  font-weight: 600;
  text-align: center;
}
.centered-text-card a:hover{
  color: #44A2C6;
  font-weight: 600;
}
.carousel-section .carousel-col img{
  border-radius: 2rem;
}

.carousel-section .carousel button{
  position: static;
  max-height: 10%;
  width: 10%;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 0.25rem;
}
.carousel-section .carousel-button-group{
position: absolute;
width: 100%;
bottom: 1.5rem;
}
.carousel-section .carousel button span{
  width:1.5rem;
  height:1.5rem;
  margin:  0.5rem;
  color: #1e1e1e;
}
.carousel-section .carousel-control-next{
  margin-left: 1rem;
}
.carousel-section .carousel-col .container i{
  color:#fff;
}
.carousel-section .carousel-col .container i:hover{
  color:#44A2C6;
}
/* LOGO CAROUSEL */

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-180px * 7));
  }
}
.slider {
/*   background: white;
  box-shadow: 0 10px 20px -5px rgba(0, 0, 0, .125); */
  height: 100px;
  margin: auto;
  overflow: hidden;
  position: relative;
  width: 1120px;
}
.slider::before, .slider::after {
/*   background: linear-gradient(to right, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%); */
  content: "";
  height: 100px;
  position: absolute;
  width: 200px;
  z-index: 2;
}
.slider::after {
  right: 0;
  top: 0;
  transform: rotateZ(180deg);
}
.slider::before {
  left: 0;
  top: 0;
}
.slider .slide-track {
  animation: scroll 20s linear infinite;
  display: flex;
  width: calc(180px * 14);
}
.slider .slide {
  height: 100px;
  width: 180px;
  padding-left: 60px;
  display: flex;
  align-items: center;
  text-align: center;
}
.offres-section img{
  width: 154px;
}
.logo-card{
  display: flex;
  align-self: center;
  margin: 0 1rem;

}
.logo-carde{
  display: flex;
  align-self: center;
  margin: 0 1rem;

}
.offres-section .card-offre{
  align-items: center;
}
.card-offre{
  background-color: #fff;
  border-radius: 1.5rem;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  padding-top:2rem;
  padding-bottom:2rem;

}
.card-offre ul{
  list-style: none;
  padding: 0;
  margin-left: 1rem;
  margin-bottom: 0;
}
.middle-card{
  margin: 2rem 0;
}
.middle-li{
  margin:0.5rem 0;
}
.card-offre ul li span{
  font-size: var(--copyright-font-size) ;
}
.menu-offres .nav-link{
 color: var(--dark-color);
 font-size: var(--menu-font-size);
 padding:1.25rem; 
}
.menu-offres .navbar-nav {
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  border-bottom: 1px solid #bdbdbd;
}
.offres-list li{
margin-bottom:1rem
}
.article-section .card{
  padding: 1rem;
  border-radius: 1.25rem;
  box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.15);
  height: 100%;
}
.article-section h6 {
  margin-bottom: 2rem;
}
.article-section p{
  margin-bottom:10px;

}
.article-section .title{
  font-size: 18px;
  font-weight: 600;
  
}
/*---------------------------------------
  ABOUT              
-----------------------------------------*/
.profile-thumb {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-medium);
  position: relative;
  overflow: hidden;
}

.profile-title {
  border-bottom: 1px solid var(--border-color);
  padding: 15px 30px;
}

.profile-small-title {
  border-right: 1px solid var(--border-color);
  color: var(--secondary-color);
  font-weight: var(--font-weight-bold);
  min-width: 140px;
  margin-right: 10px;
  padding: 13px 30px;
  display: inline-block;
}

.profile-body p {
  margin-bottom: 0;
}

.profile-body p:nth-of-type(even) {
  background: var(--white-color);
}

.about-image {
  border-radius: var(--border-radius-medium);
}

.about-thumb {
  padding-right: 20px;
  padding-left: 20px;
}


/*---------------------------------------
  FEATURED              
-----------------------------------------*/
.featured-numbers {
  font-size: var(--h1-font-size);
  line-height: normal;
  display: block;
}

.featured-text {
  color: var(--secondary-color);
}

.featured-border-bottom {
  border-bottom: 1px solid var(--border-color);
}

.featured-border-start {
  border-left: 1px solid var(--border-color);
}


/*---------------------------------------
  CLIENTS              
-----------------------------------------*/

.clients-item-height {
	height: 120px;
}

.clients-image {
  display: block;
  max-width: 100px;
  margin: auto;
  transition: all ease 0.2s;
}

.clients-image:hover {
  transform: scale(1.3);
}


/*---------------------------------------
  SERVICES              
-----------------------------------------*/
.services,
.featured {
  background: var(--section-bg-color);
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.services-thumb {
  background: var(--white-color);
  border: 2px solid transparent;
  border-radius: var(--border-radius-medium);
  position: relative;
  overflow: hidden;
  margin-bottom: 24px;
  padding: 40px 40px 240px 40px;
  transition: all 0.5s;
}

.services-thumb-up {
  position: relative;
  bottom: 50px;
  margin-bottom: -50px;
}

.services-thumb:hover {
  border: 2px solid var(--secondary-color);
  box-shadow: 0 1rem 3rem rgba(0,0,0,.175);
}

.services-thumb:hover .services-icon-wrap {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--white-color);
}

.services-icon-wrap {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  position: absolute;
  bottom: 0;
  right: 0;
  width: 50%;
  height: 55%;
  transform: rotate(-35deg) translateY(55px);
  transition: all ease 0.5s;
}

.services-icon {
  font-size: 90px;
  position: relative;
  bottom: 15px;
}

.services-thumb:hover .services-price-wrap {
  background: var(--secondary-color);
}

.services-thumb:hover .services-price-overlay {
  background: var(--primary-color);
}

.services-price-wrap {
  background: var(--primary-color);
  border-radius: var(--border-radius-medium);
  position: relative;
  overflow: hidden;
  padding: 6px 20px 6px 15px;
  transition: all ease 0.5s;
}

.services-price-text {
  color: var(--white-color);
  font-size: var(--copyright-font-size);
  font-weight: var(--font-weight-bold);
}

.services-price-overlay {
  background: var(--secondary-color);
  border-bottom-left-radius: 100%;
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  pointer-events: none;
}


/*---------------------------------------
  PROJECTS              
-----------------------------------------*/
.projects-thumb {
  background: var(--section-bg-color);
  border: 2px solid var(--white-color);
  border-radius: var(--border-radius-medium);
  position: relative;
  overflow: hidden;
  margin-top: 24px;
  margin-bottom: 24px;
  padding: 40px;
  transition: all ease 0.5s;
}

.projects-thumb:hover {
  border-color: var(--secondary-color);
}

.projects-thumb:hover .projects-image,
.projects-thumb:focus .projects-image {
  transform: rotate(0) translateY(0);
}

.projects-thumb .popup-image {
  display: block;
  width: 100%;
  height: 100%;
}

.projects-image {
  border-radius: var(--border-radius-medium);
  display: block;
  width: 100%;
  transform: rotate(10deg) translateY(80px);
  transition: all ease 0.5s;
}

.projects-title {
  margin-bottom: 20px;
}

.projects-tag {
  font-size: var(--menu-font-size);
  font-weight: var(--font-weight-medium);
  color: var(--secondary-color);
  text-transform: uppercase;
  margin-bottom: 5px;
}


/*---------------------------------------
  CONTACT              
-----------------------------------------*/
.contact {
  background: var(--section-bg-color);
}

.contact-info {
  background: var(--white-color);
  border-top-right-radius: var(--border-radius-small);
  border-bottom-right-radius: var(--border-radius-small);
  padding: 60px 30px 30px 30px;
  height: 100%;
}

.contact-info-border-start {
  border-right: 1px solid var(--border-color);
  border-radius: var(--border-radius-small) 0 0 var(--border-radius-small);
}

.contact-form {
  margin-left: 10px;
}


/*---------------------------------------
  CUSTOM FORM               
-----------------------------------------*/
.custom-form .form-control {
  background: var(--white-color);
  box-shadow: none;
  border: 2px solid var(--border-color);
  color: var(--p-color);
  margin-bottom: 24px;
  padding-top: 13px;
  padding-bottom: 13px;
  outline: none;
}

.form-floating>label {
  color: var(--p-color);
}

.form-check-inline {
  vertical-align: middle;
  width: 100%;
  position: relative;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 24px;
  padding: 0;
}

.custom-form .form-check-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.form-check-label-text {
  color: var(--p-color);
  display: block;
  font-size: copyright-font-size;
  margin-top: 5px;
}
/*
.form-check-input[type=checkbox] {
  background: var(--white-color);
  border: 2px solid var(--border-color);
  box-shadow: none;
  outline: none;
  width: 100%;
  margin-top: 0;
  margin-left: 0;
  padding: 40px 50px;
}

.form-check-input:checked[type=checkbox] {
  background-image: none;
}

.form-check-input:hover,
.form-check-input:checked {
  background-color: transparent;
  border-color: var(--secondary-color);
}

.custom-form .form-control:hover,
.custom-form .form-control:focus {
  background: transparent;
  border-color: var(--secondary-color);
}
*/
.custom-form .form-floating textarea {
  height: 150px;
}

.custom-form button[type="submit"] {
  background: var(--secondary-color);
  border: none;
  border-radius: var(--border-radius-large);
  color: var(--white-color);
  font-weight: var(--font-weight-bold);
  transition: all 0.3s;
  margin-bottom: 0;
}

.custom-form button[type="submit"]:hover,
.custom-form button[type="submit"]:focus {
  background: var(--secondary-color);
  border-color: transparent;
}

/*--------------------------------------- 
CANDIDAT SECTION
---------------------------------------*/ 
.candidat-section{
  padding-top:10rem;
}

.profil-card{
  margin-top: 2rem;
  padding: 2rem 2rem;
  background-color: #F9F9F9;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  border-radius: 20px;
}
.body-card{
  display: flex;
  flex-direction: row;
  justify-content: space-between;

}
.profil-card ul {
  list-style: none;
  padding: 0;
}
.profil-card span{
  color: #1e1e1e;
}
.profil-card .card-divider{
  width: 1px;
  background-color: #bdbdbd;
  height: 100%;
}
.li-title{
  font-size: 16px;
  font-weight: 700;
}
.first-card-section{
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.card-section{
  display: flex;
  flex-direction: row;
}
.section-list{
  padding-left: 40px;
}
.profil-card li{
  margin-bottom: 3px;
  
}
.footer-profil-card{
  padding: 1rem;
  padding-bottom: 0;
}
.user-title .text-muted{
  font-size:0.8125em;
}

.info-button{
  background-color: #F9F9F9;
  padding: 2rem 1rem;
  border-radius:20px;
  height: 100%;
  box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.2);
  transition: 0.8s;
}
.button-title{
  font-size: 18px;
  font-weight: 700;
}
.section-embauche .col-lg-4{
  margin-bottom: 1rem;
}
.info-button:hover{
margin-top:-10px;
background-color: #44A2C6;
}
.info-button:hover p {
color: #fff;
  }
/*---------------------------------------
  PROFIL CV
---------------------------------------*/
.profil-cv h6{
  margin-bottom: 0;
}
.profil-cv .sub-title{
  margin-top: 1rem;
}
.profil-cv .section-list{
  padding-left: 20px;
}
/*---------------------------------------
  OFFRES              
-----------------------------------------*/
.offres-header{
  background-image: url("../images/offre-section.jpg") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 242.75px;
  padding-bottom: 142.75px;
  margin-bottom: 0;
}
.recherche-offre{
  background-color: #F9F9F9;
  margin-bottom: 0px;

}
.recherche-offre button{
  margin-top: 1rem;
}
.recherche-offre .container{
padding:3rem ;
}


.offre-card{
  margin-bottom: 2rem;
  padding: 2rem 2rem;
  background-color: #F9F9F9;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  border-radius: 20px;
  color: #1e1e1e;
}
.offre-card p {
  text-align: justify;
  text-justify: inter-word;
}
.offre-card span{
    margin-right: 0.5rem;
    font-size: 14px;
}
.desc-card{
  padding: 2rem 2rem;
  height:100vh;
  background-color: #F9F9F9;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  border-radius: 20px;
  transition: all 0.2s;

}

.desc-card .v-flex > * {
  margin-bottom:1rem;
}
.desc-card i {
  color: #1e1e1e;
}
.desc-card a:hover i{
  color: #44A2C6;
  transition: all 0.3s;
}
.desc-card span{
  font-size: 14px;
}
.desc-card .social-media-links i{
  margin: 0 0.1rem;
  color: #767676;
}
.desc-card .social-media-links{
  margin: 0 0.3rem;
  color: #767676;
}
.desc-card .social-media-links a{
margin:0;
}
.job-description{
  overflow-y: auto; /* Enable vertical scrolling if content exceeds the height */
}
.job-description > * {
  margin-bottom:1rem;

}
/* custom scrollbar */
::-webkit-scrollbar {
  width: 20px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #d6dee1;
  border-radius: 20px;
  border: 6px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a8bbbf;
}
  .justified{
    text-align: justify;
    text-justify: inter-word;
  }
.job-active{
  border: 1px solid #44A2C6;
}
.contrat-travail{
  padding:3rem 0 0 0;
  margin-bottom: 0;
}
.contrat-travail span{
  margin-right: 1rem;
  font-size: 14px;
}
.contrat-travail a{
  text-decoration: none;
}
.contrat-travail > *{
  margin-bottom:2rem;
}
.contrat-travail h6{
  font-size: 16px;
}
.pagination {
  display: inline-block;
}

.pagination a {
  color: black;
  float: left;
  padding: 8px 16px;
  text-decoration: none;
}

.pagination a.active {
  background-color: #44A2C6;
  color: white;
  border-radius: 5px;
}

.pagination a:hover:not(.active) {
  background-color: #ddd;
  border-radius: 5px;
}
.pagination-center{
padding: 2rem 0 0 0;
}
.offre-card img {
  max-height: 180px;
}
/*---------------------------------------
  OFFRE FORM
---------------------------------------*/
.offre-form-header{
  padding-top: 150.75px;
  padding-bottom: 80.75px;
  color: #fff;
  background-color: #44A2C6;
}
.job-info span:nth-child(-n + 2) {
  margin-right:0.5rem;
}
.job-info span{
  font-size: 14px;

}
.job-title{
  margin: 0.5rem 0;
}

.job-title h4{
  color: white;
  line-height: 150%;
}
.button-offre{
  background-color: #36819E;
  color: white;
  padding: 0.5rem 1.5rem;
  margin-top:2rem;
}
.button-offre:hover{
  color: white;

  background-color: #36819EAA;
}
.button-inscription{
  background-color: #44A2C6;
  color: white;
  border-radius:10px;
  padding: 0.5rem 1.5rem;
}
.button-inscription:hover{
  color: white;

  background-color: #44A2C6AA;
}
.job-title:last-child{
  margin:0;
}
.decouvrir-col{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.decouvrir{

  width: 100%;
}
.decouvrir{
}
.decouvrir a{
  float: right;
}
/*---------------------------------------
  ESPACE CANDIDAT
---------------------------------------*/
.espace-candidat-header{
  background-image: url("../images/espace-candidat.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 150.75px;
  padding-bottom: 50.75px;
}
.info-button i {
  margin-bottom: 2rem;
  font-size: 3rem;
  }
.info-button:hover i {
color: white;
}
.login-candidat-section{
  background-image: url("../images/login-espace-candidat.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 50.75px;
  padding-bottom: 50.75px;
}
.login-candidat-section .form-group{
max-width: 400px;
margin-left: auto;
margin-right: auto;
}
.reseau-section img{
  object-fit: cover;
  width: 100%;

}
/*---------------------------------------
  UPDATE PROFILE
---------------------------------------*/
.update-profil-header{
  padding-top: 150.75px;
  padding-bottom: 80.75px;
  background-color: #FCFCFC;
  color: #1e1e1e;
}
/*---------------------------------------
  AJOUTER CV
---------------------------------------*/
.ajouter-cv-header{
  padding-top: 150.75px;
  padding-bottom: 50.75px;
  background-color: #FCFCFC;
  color: #1e1e1e;
}
/*---------------------------------------
  FORGET PASSWORD
---------------------------------------*/
.password-header{
  padding-top: 150.75px;
  padding-bottom: 80.75px;
  background-color: #FCFCFC;

}
/*---------------------------------------
  ENVIRONNEMENT
---------------------------------------*/
.env-header{
  background-image: url("../images/env-header.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 171.75px;
  padding-bottom: 100.75px;

}
.env h6{
  color: #44A2C6;
}
.env .info-button:hover h6{
  color: #fff;
}
.env p{
  font-size: 14px;
}
.carousel-fiche-metier img{
  max-height: 400px;
  object-fit: cover;
}
.carousel-fiche-metier button{
background-color: rgba(42, 42, 42, 0.4);
}
.carousel-overlay{
  position: absolute;
  right: 15%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);  
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  color: #fff;
  text-align: center;
}
/*---------------------------------------
  URBANISM
---------------------------------------*/
.urbanism-header{
  background-image: url("../images/urbanisme-header.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 171.75px;
  padding-bottom: 100.75px;
}
/*---------------------------------------
  URBANISM
---------------------------------------*/
.btp-header{
  background-image: url("../images/btp-header.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 171.75px;
  padding-bottom: 100.75px;
}
/*---------------------------------------
  FORGET PASSWORD
---------------------------------------*/
.emploi-header{
  background-image: url("../images/emploi-header.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 242.75px;
  padding-bottom: 142.75px;
  margin-bottom: 0;

}
.failpayment-header{
  background-image: url("../images/sad.jpg") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 242.75px;
  padding-bottom: 142.75px;
  margin-bottom: 0;

}
.donepayment-header{
  background-image: url("../images/happy.jpg") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 242.75px;
  padding-bottom: 142.75px;
  margin-bottom: 0;

}
.recherche-offre span{
 margin-right: 10px;
}
.recherche-offre .form-check{
  margin-right: 10px;
 }
/*---------------------------------------
  ESPACE INGENIEUR
---------------------------------------*/
.espace-ingenieur-header{
  background-image: url("../images/espace-ingenieur.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: repeat;
  background-color: #1e1e1e;
  position: relative;
  overflow: hidden;
}
.espace-ingenieur-header h5 {
  margin-top:190.75px ;
  margin-bottom:119.75px ;
}
.espace-ingenieur-header .container{
  padding-top: 40px;
  padding-bottom: 40px;
}
.header-text{
  background-color: rgba(26,25,26,.2);
  backdrop-filter: blur(2px);
}
.zoom-metier img{
  width: 100%;
}
.zoom-metier .container{
  padding-right: 0;
  padding-left: 0;
  margin-right: 0;
  margin-left: 0;
  max-width:100vw;
  overflow-x: hidden;
}
.zoom-metier .img-container{
  padding: 0;
}
.zoom-metier .txt-container{
  background-color: #F9F9F9;
  padding: 2rem;
}
.la-une .card-offre{
  padding-right:2rem;
}
.la-une .middle-li{
  margin:1rem 0;
}
/*---------------------------------------
  CONSEIL CV              
---------------------------------------*/
.conseils-cards .card img{
  border-radius: 1.25rem; 
  height: 200px;
  object-fit: fill;
  
}
.conseils-cards .card{
  padding: 1.5rem;
  background-color: #fff;
  box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.15);
  border-radius: 1.25rem; 
  margin-bottom: 2.5rem;

}
/*---------------------------------------
  ARTICLE 
---------------------------------------*/
.content img{
  border-radius: 25px;
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.content .img-div {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  margin-bottom: 2rem;
}
.header-and-text{
  margin-bottom: 2rem;

}
/*---------------------------------------
  ACTUALITES
---------------------------------------*/

.actualites-header{
  background-image: url("../images/actualites-header.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: repeat;
  background-color: #1e1e1e;
  position: relative;
  overflow: hidden;
  margin-bottom:0;
}
.actualites-header h5 {
  margin-top:190.75px ;
  margin-bottom:119.75px ;
}
.actualites-header .container{
  padding-top: 40px;
  padding-bottom: 40px;
}
/*---------------------------------------
  ENTREPRISES
---------------------------------------*/
.entreprises-header{
  background-image: url("../images/entreprises-header.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: repeat;
  background-color: #1e1e1e;
  position: relative;
  overflow: hidden;
}
.entreprises-header h5 {
  margin-top:190.75px ;
  margin-bottom:119.75px ;
}
.entreprises-header .container{
  padding-top: 40px;
  padding-bottom: 40px;
}
.entreprises-carousel-section .card{
border: 1px solid #bdbdbd;
border-radius: 1.5rem;
}
.entreprises-carousel-section .card img{
  border-radius: 1.5rem 1.5rem 0 0 ;
  height: 392px;
  }
  .card-entreprise{
    background-color: #fff;
    border-radius: 1.5rem;
    box-shadow:rgba(0, 0, 0, 0.16) 0px 1px 4px;
    padding:2rem;

  }
/*---------------------------------------
  ENTREPRISES PAGE
---------------------------------------*/
.entreprise-profile img{
  width:200px;
}
.entreprise-profile h6 {
  margin-bottom: 1rem;
}
.entreprise-emploi p {
  font-size: 12px;
}
/*---------------------------------------
  METIER
---------------------------------------*/
.voir-aussi a{
  margin-right: 1rem;
}
/*---------------------------------------
  404
---------------------------------------*/
.not-found{
  padding: 8rem 0 3rem 0;
}
.not-found h1{
  font-size: 4rem;
}
/*---------------------------------------
  QUI SOMMES NOUS
---------------------------------------*/
.qui-sommes-nous-header{
  background-image: url("../../../DirectEtudiant/Front/images/qui-sommes-nous.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 242.75px;
  padding-bottom: 142.75px;
}
/*--------------------------------------
  MENTION LEGALES
--------------------------------------*/
.mentions-legales-header{
  background-image: url("../images/mention-legal.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 242.75px;
  padding-bottom: 142.75px;
}
/*--------------------------------------
  CONFIDENTIAL
--------------------------------------*/

.confidential-header{
  background-image: url("../images/confidential.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 242.75px;
  padding-bottom: 142.75px;
}
/*---------------------------------------
  CONTACT
---------------------------------------*/
.contact-header{
  background-image: url("../images/contact.png") ;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding-top: 242.75px;
  padding-bottom: 142.75px;
}
.plan-site .col-lg-4{
  margin-bottom: 2rem
}
/*---------------------------------------
  SITE FOOTER              
---------------------------------------*/

.site-footer {
  border-top: 1px solid var(--border-color);
  padding-top: 30px;
  text-align: center;
  margin-bottom:0;
}
.site-footer img{
  max-height: 110px;
}

.image-footer{
margin-bottom: 30px;
}
.footer-menu-title{
font-size: var(--p-font-size);
font-weight: 700;
text-align:left;
margin-bottom: 0.5rem;
}
.footer-menu-text{
  font-size: var(--copyright-font-size);
  color:#1e1e1e;
  text-align:left;
  margin-bottom: 0.5rem;
  }
.site-footer-title {
  font-size: var(--menu-font-size);
  color: var(--dark-color);
  text-transform: uppercase;
}
.footer-all-menus{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  }
.site-footer .fa-twitter{
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
i{
  color:#44A2C6;
}
i:hover{
  color:rgba(68, 162, 198, 0.8);
}
.social-media-links{
  margin-bottom: 2rem;
  margin-top: 1.5rem;
}
.copyright-section{
  background-color:#44A2C6;
  padding:25px 0;
}

.copyright-text {
  font-size: var(--copyright-font-size);
  color: #fff;
  margin-bottom: 0;
}

.copyright-text-wrap a {
  font-weight: var(--font-weight-bold);
}

.footer-menu {
  margin: 0;
  padding: 0;
}

.footer-menu-item {
  list-style: none;
  display: inline-block;
  vertical-align: top;
}

.footer-menu-link {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-medium);
  font-size: var(--copyright-font-size);
  font-weight: var(--font-weight-medium);
  display: inline-block;
  vertical-align: top;
  text-align: center;
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 6px 14px;
  min-width: 70px;
}

.footer-menu-link:hover {
  background: var(--secondary-color);
  border-color: transparent;
  color: var(--white-color);
}


/*---------------------------------------
  SOCIAL ICON               
-----------------------------------------*/
.social-icon {
  margin: 0;
  padding: 0;
}

.social-icon-item {
  list-style: none;
  display: inline-block;
  vertical-align: top;
}

.social-icon-link {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-large);
  font-size: var(--copyright-font-size);
  color: var(--dark-color);
  display: inline-block;
  vertical-align: top;
  margin: 2px 2px 5px 2px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
}

.social-icon-link:hover {
  background: var(--secondary-color);
  border-color: transparent;
  color: var(--white-color);
}

.show {
  display: block;
}


.job-description{
  overflow-y: scroll;
                      }
.job-description > * {
  margin-bottom:1rem;

}
@media screen and (min-width: 2501px) and (min-height: 1081px) and (max-height: 1440px) {
    .job-description {
        height: 870px;
    }
}

@media screen and (min-width: 2501px) and (max-height: 1080px) {
    .job-description {
        height: 730px;
    }
}

@media screen and (min-width: 2501px) and (min-height: 1441px) {
    .job-description {
        height: 1170px;
    }
}
@media screen and (min-width: 1900px) and (max-width: 2499px) {
.job-description{
    height:730px;
                                              }

}
@media screen and (min-width: 1440px) and (max-width: 1899px) {
.job-description{
    height:480px;
                                              }

}
@media only screen and (max-width: 1439px) and (min-width: 1300px){
.job-description{
    height:450px;
                                              }
                                              }

@media only screen and (max-width: 1299px) and (min-width: 1200px){
.job-description{
    height:450px;
                                              }
                                              }
@media only screen and (max-width: 1199px) and (min-width: 992px){
.job-description{
    height:270px;
                                              }
}

/*---------------------------------------
  RESPONSIVE STYLES               
-----------------------------------------*/
@media screen and (min-width: 1600px) {
  .hero {
    padding-top: 380px;
    padding-bottom: 380px;
  }

  .hero-image-wrap {
    top: -50px;
    width: 400px;
    height: 400px;
  }

  .hero-image {
    min-width: 650px;
  }
  
  .hero-title,
	.hero h2 {
	  font-size: var(--h2-font-size);
	}
  .zoom-metier .container{
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    max-width: 1320px;

  }
}

@media screen and (max-width: 991px) {
  
  .h-flex-responsive-nav{
    flex-direction: column;
    align-items: start;
  }
  .h-flex-responsive-nav li{
    width:100%;
  }
  .h-flex-responsive-nav a {
    width:100%;
  }
  .job-button {
    margin-top: 2rem;
    padding-left: 0rem;
}

  .decouvrir a{
    float:none;
  }
  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(calc(-180px * 7));
    }
  }
  .slider {
  /*   background: white;
    box-shadow: 0 10px 20px -5px rgba(0, 0, 0, .125); */
    height: 100px;
    margin: auto;
    overflow: hidden;
    position: relative;
    width: 520px;
  }
  .slider::before, .slider::after {
  /*   background: linear-gradient(to right, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%); */
    content: "";
    height: 100px;
    position: absolute;
    width: 200px;
    z-index: 2;
  }
  .slider::after {
    right: 0;
    top: 0;
    transform: rotateZ(180deg);
  }
  .slider::before {
    left: 0;
    top: 0;
  }
  .slider .slide-track {
    animation: scroll 20s linear infinite;
    display: flex;
    width: calc(180px * 14);
  }
  .slider .slide {
    height: 100px;
    width: 180px;
    padding-left: 60px;
    display: flex;
    align-items: center;
    text-align: center;
  }





  .plan-site .col-lg-4 a{
   text-align: center;
  }
  .plan-site .col-lg-4 p{
    text-align: center;
   }
  .popup {
    padding: 5rem 3rem;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}
.display-none{
  display:none;

}

.desc-card{
  padding: 2rem 2rem;
  background-color: #F9F9F9;
  height: 100%;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  border-radius: 20px;
  transition: all 0.2s;

}
.job-description{
  height:100%;
}

  /* custom scrollbar */
::-webkit-scrollbar {
  width: 0;
}
.entreprises-carousel-section .carousel-item .card button{
  width: 100%;
  background-color:#424143!important;
  color:#fff;
  border-radius: 10px;
  padding:0.75rem 3rem;
}
.logo-entreprise{
  margin-top: 2rem;
  }
  .card-offre .logo-carde{
    margin-bottom:2rem;
  }
  .entreprise-profile{
    text-align: center;
  }
::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #d6dee1;
  border-radius: 20px;
  border: 6px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a8bbbf;
}
  h1 {
    font-size: 48px;
  }

  h2 {
    font-size: 36px;
  }

  h3 {
    font-size: 32px;
  }

  h4 {
    font-size: 28px;
  }

  h5 {
    font-size: 20px;
  }

  h6 {
    font-size: 18px;
  }
  .navbar{
    overflow-y:scroll;
    max-height: 400px;
  }
  .nav-link
  .dropdown {
    position: relative;
    display: inline-block;
  }
  .navbar .navbar-brand img{
    content: url("../images/logo-black.png");
  }
  /* Dropdown Content (Hidden by Default) */
  .dropdown-content {
    display: block;
    position: inherit;
    right:0;
    background-color: #fff;
    box-shadow: none;
    z-index: 1;
  }
  /* Links inside the dropdown */
.dropdown-content a {
  color: #767676;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}/* Links inside the dropdown */
.dropdown-content a:hover {
  color: #44A2C6;
  background:none;
}

  .section-padding {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .custom-btn,
  .navbar .custom-btn {
    font-size: var(--copyright-text-font-size);
    padding: 8px 16px;
  }

  .navbar .container {
    background: var(--white-color);
  }

  .navbar-brand,
  .navbar-brand:hover {
    color: var(--dark-color);
  }

  .navbar-icon {
    background: var(--secondary-color);
    color: var(--white-color);
    width: 44px;
    height: 44px;
    line-height: 44px;
  }

  .navbar .custom-btn {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
  }

  .navbar-toggler .navbar-toggler-icon,
  .navbar-toggler .navbar-toggler-icon:before,
  .navbar-toggler .navbar-toggler-icon:after {
    background: var(--dark-color);
  }

  .submenu .navbar-toggler .navbar-toggler-icon,
  .submenu .navbar-toggler .navbar-toggler-icon:before,
  .submenu .navbar-toggler .navbar-toggler-icon:after {
    background: var(--white-color);
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    margin-left: 0;
  }

  .navbar-submenu{
    display: block;
    background-color: #fff;
  }
  .container-submenu{
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);

  }
  .navbar-nav .nav-link {
    color: var(--p-color);
    padding-top: 10px;
    padding-bottom: 10px;
    display: block;
    text-align: left;
    margin-bottom: 0.5rem;
  }
  .menu-offres .nav-link{
    margin-bottom: 0;
  }
  .hero {
    padding-top: 200px;
    padding-bottom: 100px;
  }
  
  .hero .card{
    margin-top:5rem;
  }
  .hero-text {
    top: 0;
    margin-bottom: 120px;
  }

  .about-thumb {
    padding-right: 0;
    padding-left: 0;
  }

  .about-numbers {
    font-size: 42px;
  }

  .services-thumb-up {
    bottom: 0;
    margin-bottom: 32px;
  }

  .services-thumb {
    margin-bottom: 32px;
    padding-bottom: 270px;
  }

  .services-icon-wrap {
    width: 45%;
    height: 60%;
  }

  .services .col-lg-10 .row .col-lg-6:last-child,
  .projects .col-lg-4:last-child {
    margin-bottom: 0;
  }

  .projects-thumb {
    margin-top: 0;
    margin-bottom: 32px;
  }

  .contact-info {
    border-radius: 0 0 var(--border-radius-small) var(--border-radius-small);
    padding: 40px 30px;
  }

  .contact-info-border-start {
    border-right: 0;
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius-small) var(--border-radius-small) 0 0;
  }
  .footer-all-menus{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    }
    .footer-all-menus a {
      text-align: center;
    }
    .footer-menu{
      margin-top: 2rem;
    }
    /*-------------------
    Accueil
    -------------------*/
    .index-header{
      padding-left: 5rem;
      padding-right: 5rem;
    }
    .index-header h6{
      text-align: center;
    }
    .job-search-card{
      border-radius: 1rem;
      display: flex;
      flex-direction: column;
      background-color: #F9F9F9;
      box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
      position: static;
      margin:2rem 0;
    }
    .job-icon{
      border-top-right-radius: 1rem;
      border-bottom-left-radius:0 ;
    }
    .job-form{
      flex-direction: column;
    }
    .middle-form{
      margin:1rem 0;
    }
    .carousel-col{
      padding: 1rem; 
     }
     .carousel button{
      position: static;
      max-height: 10%;
      width: 20%;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 0.25rem;
    }
    .form-padding{
      padding:0 0;
    }
    /*----------------------------
    Candidat Section
    ----------------------------*/
    .candidat-section{
      padding-top:6rem;
    }

    .body-card{
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    
    }
    .profil-card .card-divider{
      height: 1px;
      background-color: #bdbdbd;
      width: 100%;
    }
    .first-card-section{
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
    .first-card-section{
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-bottom: 0.5rem;
    }
    
    .user-title{
      margin-bottom: 1rem;
    }
    .card-section{
      display: flex;
      flex-direction: column;
    }
    .section-list{
      padding-left: 0;
      padding-top: 20px;
    }
    .profil-cv .section-list{
      padding-left: 0px;
    }
    .profil-cv h6{
      margin-bottom: 1rem;
    }
    .conseils-cards .card img{
      border-radius: 1.25rem; 
      width: 100%;
      object-fit: fill;
      margin-bottom: 1rem;
    }
    .h-flex-responsive{
      display: flex;
      flex-direction: column;
      align-items: center;
    }
}

@media screen and (max-width: 575px) {
  .navbar{
    box-shadow: 0 0.5rem 0.75rem rgb(0 0 0 / 10%);

  }
  .navbar .container {
    /*  border-radius: var(--border-radius-small);*/
    max-width: 100%;
      padding: 0px 30px;
      box-shadow: 0 0.5rem 0.75rem rgb(0 0 0 / 10%);
      
  }
  .slider {
    /*   background: white;
      box-shadow: 0 10px 20px -5px rgba(0, 0, 0, .125); */
      height: 100px;
      margin: auto;
      overflow: hidden;
      position: relative;
      width: 400px;
    }
    /*

  .navbar .container {
    margin-right: 12px;
    margin-left: 12px;
  }*/
  
  .navbar-nav .separator:after{
    color:#bdbdbd;
    font-size: small;
    content: "";
  
    padding: 0 0px;
  }
  .h-flex-responsive{
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .button-black{
  background-color:#424143;
   color:#fff;
   border-radius: 10px;
   padding:0.75rem 3rem;
}
.button-black:hover{
  background-color:rgba(26,25,26,.6);
  color: #fff;
}
.logo-entreprise{
margin-top: 2rem;
}
.entreprise-profile{
  text-align: center;
}
}

@media screen and (max-width: 480px) {
  h1 {
    font-size: 40px;
  }

  h2 {
    font-size: 28px;
  }

  h3 {
    font-size: 26px;
  }

  h4 {
    font-size: 22px;
  }

  h5 {
    font-size: 20px;
  }

  .custom-btn,
  .navbar .custom-btn {
    font-size: 13px;
    padding: 6px 12px;
  }

  .navbar-icon {
    font-size: var(--copyright-font-size);
    width: 35.5px;
    height: 35.5px;
    line-height: 35.5px;
  }

  .hero-image-wrap {
    width: 300px;
    height: 300px;
  }

  .hero-image {
    min-width: inherit;
  }
  .button-black{
    background-color:#424143!important;
     color:#fff;
     border-radius: 10px;
     padding:0.75rem 3rem;
  }
  .button-black:hover{
    background-color:rgba(26,25,26,.6);
    color: #fff;
  }
}

