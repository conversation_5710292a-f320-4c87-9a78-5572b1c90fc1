
(function ($) {

    "use strict";

    // PRE LOADER
    $(window).load(function(){
        $('.preloader').fadeOut(1000); // set duration in brackets
    });

    // CUSTOM LINK
    $('.custom-link').click(function(){
        var el = $(this).attr('href');
        var elWrapped = $(el);
        var header_height = $('.navbar').height() + 10;

        scrollToDiv(elWrapped,header_height);
        return false;

        function scrollToDiv(element,navheight){
            var offset = element.offset();
            var offsetTop = offset.top;
            var totalScroll = offsetTop-navheight;

            $('body,html').animate({
                scrollTop: totalScroll
            }, 300);
        }
    });

})(window.jQuery);


// ------------step-wizard-------------
$(document).ready(function () {

    $('.nav-tabs > li a[title]').tooltip();

    //Wizard
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {

        var target = $(e.target);

        if (target.parent().hasClass('disabled')) {
            return false;
        }
    });

    $(".next-step").click(function (e) {

        var active = $('.wizard .nav-tabs li.active');
        active.next().removeClass('disabled');
        nextTab(active);

    });
    $(".prev-step").click(function (e) {

        var active = $('.wizard .nav-tabs li.active');
        prevTab(active);

    });
});

function nextTab(elem) {
    $(elem).next().find('a[data-toggle="tab"]').click();
}
function prevTab(elem) {
    $(elem).prev().find('a[data-toggle="tab"]').click();
}


$('.nav-tabs').on('click', 'li', function() {
    $('.nav-tabs li.active').removeClass('active');
    $(this).addClass('active');
});

/* // get the element of navigation offset Top and left (Right div)
var elementPosition = $('#navigation').offset();
// get the element of parent (right col-lg-6)
var divParent = document.getElementById("navigation-parent")
//get the width of right col-lg-6
var widthParent = divParent.offsetWidth
var widthParentString = widthParent.toString();
console.log(widthParentString)
//
var scrollBottom = $(window).scrollTop() + $(window).height();

var stopBottom =  $(window).height();

console.log("element Position",elementPosition.top)
console.log("stopBottom",stopBottom)

var navigationHeight = stopBottom - 120.5

windowOffset=$(window).offset()

$('#navigation').css("height",navigationHeight.toString()+"px")

var offreDivHeight = $('#offreDiv').height();
var offreDivOffset = $('#offreDiv').offset();


$(window).scroll(function(){
  console.log($(window).scrollTop())

  console.log(offreDivOffset.top)

  console.log(offreDivHeight)
    console.log(offreDivOffset.top + offreDivHeight)
    console.log($(window).scrollTop() + 95.5 + offreDivHeight)

    if(($(window).scrollTop() + navigationHeight + 120.5  ) > offreDivOffset.top + offreDivHeight){
      $('#navigation').css('position','static').css("margin-top",(offreDivHeight - elementPosition.top) + 100.5).css("margin-bottom","0");
    }
    else if(($(window).scrollTop() + 95.5) > elementPosition.top){

        $('#navigation').css('position','fixed').css('top','0').css('width',widthParentString+"px").css('margin-top',"95.5px").css("margin-bottom","20px");

        }  else if (($(window).scrollTop() + 95.5) < elementPosition.top){
        $('#navigation').css('position','static').css("margin-top","0px");
      }
        */

/* var screenWidth = $(window).width();

if (screenWidth > 991){
  var elementPosition = $('#navigation').offset();
  var divParent = document.getElementById("navigation-parent");
  var widthParent = divParent.offsetWidth;
  var widthParentString = widthParent.toString();

  var stopBottom = $(window).height();
  var navigationHeight = stopBottom - 120.5;

  var offreDivOffset = $('#offreDiv').offset();
  var offreDivOffHeight = $('#offreDiv').height();

  if (navigationHeight > 600){
    navigationHeight = 600
  }

  var navigationTopOffset =  offreDivOffset.top + (offreDivOffHeight - navigationHeight )

  var contentHeight = navigationHeight-64

  $("#content").css("height",contentHeight+"px")
  $('#navigation').css("height", navigationHeight.toString() + "px");

  $(window).scroll(function() {
      var navigationBottom = $(window).scrollTop() + navigationHeight;
      var offreDivHeight = $('#offreDiv').height();
      var offreDivOffset = $('#offreDiv').offset().top;

      if (navigationBottom +50 > offreDivOffset + offreDivHeight) {
          $('#navigation').css('position', 'static').css('margin-top', '0px');
          $('#navigation').offset({top:navigationTopOffset});

      } else if ($(window).scrollTop() > elementPosition.top) {
          $('#navigation').css('position', 'fixed').css('top', '0').css('width', widthParentString + "px").css('margin-top', "95.5px").css("bottom", "auto");
      } else {
          $('#navigation').css('position', 'static').css("margin-top", "0px").css("bottom", "auto");
      }
});
} else {

  $(".offre-card").click(function() {

    $("#mypopup").removeClass("display-none")

    $("#mypopup").addClass("show");

    let jobDescHeight = $("#content").height() + 25 ;

    $('#navigation').css("height", jobDescHeight.toString() + "px");
});

$(document).click(function(event) {
    if ($(event.target).is("#mypopup")) {
        $("#mypopup").removeClass("show");
        $("#mypopup").addClass("display-none");

    }
});
} */

$('.land').mouseenter(function () {
    var title = $(this).attr('title');
    $('#region-info').text(title);
}).mouseleave(function(){
    $('#region-info').text("Trouvez un emploi dans votre région");

});

function adjustNavigation() {
    var screenWidth = $(window).width();

    if (screenWidth > 991) {
        var elementPosition = $('#navigation').offset();
        var divParent = document.getElementById("navigation-parent");
        var widthParent = divParent.offsetWidth;
        var widthParentString = widthParent.toString();

        var stopBottom = $(window).height();
        var navigationHeight = stopBottom - 120.5;

        var offreDivOffset = $('#offreDiv').offset();
        var offreDivOffHeight = $('#offreDiv').height();


        if (screenWidth >2500){
            if ($(window).height() >1080 && $(window).height() <= 1440  ){
                navigationHeight = 1100;

            }
            if ($(window).height() <= 1080  ){
                navigationHeight = 960;

            }
            if ($(window).height() > 1440  ){
                navigationHeight = 1300;

            }
        }
    else if (screenWidth >1900 && screenWidth <2500){
            navigationHeight = 960;

        }
        else if (screenWidth >1439 && screenWidth <1899){
            navigationHeight = 700;

        }
        else if (screenWidth >1300 && screenWidth <1439){
            navigationHeight = 680;

        }else if (screenWidth >1200 && screenWidth <1299){
            navigationHeight = 680;

        }
        else if (screenWidth >992 && screenWidth <1199){
            navigationHeight = 480;

        }

        var navigationTopOffset = offreDivOffset.top + (offreDivOffHeight - navigationHeight);

        var contentHeight = navigationHeight - 64;

        $("#content").css("height", contentHeight + "px");
        $('#navigation').css("height", navigationHeight.toString() + "px");

        $(window).on('scroll', function () {
            var navigationBottom = $(window).scrollTop() + navigationHeight;
            var offreDivHeight = $('#offreDiv').height();
            var offreDivOffset = $('#offreDiv').offset().top;

            if (navigationBottom + 50 > offreDivOffset + offreDivHeight) {
                $('#navigation').css('position', 'static').css('margin-top', '0px');
                $('#navigation').offset({ top: navigationTopOffset });

            } else if ($(window).scrollTop() > elementPosition.top) {
                $('#navigation').css('position', 'fixed').css('top', '0').css('width', widthParentString + "px").css('margin-top', "95.5px").css("bottom", "auto");
            } else {
                // Reset styles when the condition is not met
                $('#navigation').css('position', 'static').css("margin-top", "0px").css("bottom", "auto").css("height", "auto");
            }
        });
    } else {
        // Reset styles when screen size is below 991
        let jobDescHeight = $("#content").height() + 25;

        $('#navigation').css('position', 'static').css("margin-top", "0px").css("bottom", "auto").css("height", jobDescHeight.toString() + "px").css("width","auto");

        // Click event for the button
        $(".offre-card").click(function () {
            $("#mypopup").removeClass("display-none")
            $("#mypopup").addClass("show");

            let jobDescHeight = $("#content").height() + 25;
            console.log(jobDescHeight)
            $('#navigation').css('position', 'static').css("margin-top", "0px").css("bottom", "auto").css("height", jobDescHeight.toString() + "px").css("width","auto");
        });
        $(window).on('scroll', function () {
            let jobDescHeight = $("#content").height() + 25;

            $('#navigation').css('position', 'static').css("margin-top", "0px").css("bottom", "auto").css("height", jobDescHeight.toString() + "px").css("width","auto");

        });
        // Click event for the window
        $(document).click(function (event) {
            if ($(event.target).is("#mypopup")) {
                $("#mypopup").removeClass("show");
                $("#mypopup").addClass("display-none");
            }
        });
    }
}

// Initial adjustment
adjustNavigation();

$('#offrelink').on( "click", function() {
    console.log("TEST");
    adjustNavigation();

});
if ( $('dynamic-content').children().length > 0 ) {
    console.log("TEST");
    adjustNavigation();
}
// Adjust on resize
$(window).on('resize', function () {
    adjustNavigation();
});

