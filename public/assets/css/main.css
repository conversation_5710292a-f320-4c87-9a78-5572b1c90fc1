@charset "utf-8";

/* CSS Document */

#sfWebDebugBar .menu,
#sfWebDebugBar .menu a {
    background: #333;
    color: #FFF;
}

.smart,
.tab,
.menu4,
.home_dossier_smart,
#menu_cand2 {
    display: none;
}

header,
nav,
article,
aside,
footer {
    display: block;
}

html {
    height: 100%;
}

body {
    /* font-family: "Open Sans", Arial, Helvetica, sans-serif; */
    font-family: Arial, Helvetica, sans-serif;
    color: #333333;
    padding: 0px;
    margin: 0px;
}

body,
td,
th,
input,
select,
textarea {
    font-size: 15px;
}

input,
select,
textarea {
    font-family: Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: #333;
}

option {
    font-weight: normal;
}

#id_region.checkbox_select,
.checkbox_select,
.select_cadre {
    width: 410px;
    min-width: 180px;
    max-height: 460px;
    overflow: auto;
    border: 1px solid #ccc;
    padding: 5px;
}

#id_region.checkbox_select {
    min-height: 230px;
}

.checkbox_select {
    background-color: #FFF;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1_title {
    color: #252424;
    padding: 0px;
    font-weight: bold;
}

h1,
.h1_title,
h2.type_h1 {
    font-size: 1.8em;
    margin: 5px 0px 10px 0px;
    padding: 0px 5px 10px 5px;
    border-bottom: 1px solid #c2c2c2;
}

h2.type_h1,
.box_rech h1,
.box_rech h2.h3_title {
    border: 0px;
    color: #FFF;
}

.box_rech h1,
.box_rech h2.h3_title {
    padding: 0px;
    margin: 2px;
}

h2,
h1.h2_title,
h3.h2_title {
    font-size: 1.4em;
    margin: 30px 0 20px 0;
    padding: 0px 13px 10px 13px;
    border-bottom: 1px solid #c2c2c2;
}

.h2_title {
    text-transform: none;
}

h3 {
    font-size: 1.3em;
    margin: 25px 0px 16px 0px;
    padding: 0px 13px 10px 13px;
    border-bottom: 1px solid #c2c2c2;
}

h4 {
    font-size: 1.2em;
    margin: 18px 0 13px 0;
}

h5 {
    font-size: 1.1em;
    margin: 13px 0px 10px 0px;
}

#mc_metier p {
    padding-bottom: 5px;
}

.titre_bleu {
    font-size: 1.6em;
    color: #4B84AD;
    font-weight: bold;
}

.img img {
    border: 1px solid #000;
}
/*
.site {
    margin: auto;
    width: 1011px;
}*/

a img {
    border: 0px;
}

a {
    text-decoration: none;
    color: #4B84AD;
}

a:hover {
    color: #333;
}

.bandeau {
    padding: 50px 0px 8px 0px;
}

.logo,
.pub_haut {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    padding-left: 15px;
}

.slogan,
h1.slogan {
    float: left;
    padding: 2px 5px;
    margin-top: 11px;
    font-size: 1em;
    border: 0px;
    font-weight: bold;
}

.colG {
    float: left;
}

.colD {
    float: right;
}

.affich_nombre {
    font-size: 0.9em;
}

input,
select {
    font-weight: bold;
    margin: 2px 0px 2px 0px;
    color: #333333;
    padding: 5px 10px;
    font-family: Arial, Helvetica, sans-serif;
    -ms-box-sizing: content-box;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    border: 1px solid #666;
}

select {
    padding: 0px 0px 0px 3px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    min-width: 65px;
}

select#civilite {
    min-width: 150px;
}

select:not(.all) {
    background: #FFF url('../images/select.png') no-repeat right center;
}

.small {
    font-size: 0.9em;
}

.smaller {
    font-size: 0.8em;
}

.identification {
    height: 191px;
    width: 160px;
}

.identifie {
    width: 160px;
    margin-bottom: 15px;
}

.identification .int {
    padding: 0px 20px;
}

.reseaux {
    float: right;
    padding: 5px 0px 8px 0px;
}

.reseaux span {
    display: block;
    float: left;
    font-weight: bold;
    padding: 6px 5px 0px 0px;
    color: #666;
}

.reseaux a {
    display: block;
    float: left;
    padding: 2px;
    margin: 1px;
}

.reseaux a img {
    width: 24px;
    height: 24px;
}

.reseaux a:hover {
    background-color: #333;
}

.id_text {
    font-weight: bold;
}

.pager {
    float: right;
    margin: -5px 10px 0px 0px;
}

.pager a {
    display: inline-block;
    background: #24567B;
    padding: 2px 13px;
    color: #FFF;
    font-weight: bold;
}

.pager a:first-child {
    border-radius: 6px 0px 0px 6px;
}

.pager a:last-child {
    border-radius: 0px 6px 6px 0px;
}

.onglets:after {
    display: block;
    content: "";
    visibility: visible;
    background-color: #407AA2;
    background: linear-gradient(to top, #407AA2, #538DB6);
    background: -webkit-gradient(to top, #407AA2, #538DB6);
    height: 1px;
    width: auto;
}

div.field2 {
    border: 1px solid #437CA4;
    padding: 20px;
}

div.field2 .item {
    padding: 2px 0px;
}

.field .onglets {
    margin: 0px -12px;
}

.onglets .item {
    display: inline-block;
}

.onglets a {
    display: inline-block;
    background: #24567B;
    padding: 6px 5px;
    width: 134px;
    text-align: center;
    color: #FFF;
    font-weight: bold;
    border-radius: 6px 6px 0px 0px;
    margin-right: -2px;
}

.onglets .active a,
.onglets a:hover,
.onglets a.active,
.pager a:hover,
.pager a.active {
    background: #538DB6;
    transition: 0.1s;
}

.onglets a {
    padding: 6px 15px;
    width: auto;
}

.contener {
    padding: 0px 15px;
}

.lien_s {
    display: block;
    text-decoration: underline;
    float: right;
    padding-top: 13px;
    font-size: 1.1em;
}

.lien_s2 {
    display: block;
    text-decoration: underline;
    float: right;
    padding: 5px;
    font-size: 0.8em;
    font-weight: 300;
}

.field .onglets a:first-child {
    /* margin-left:12px; */
}

.footer {
    background-color: #4ba8c5;
    background: linear-gradient(to top, #4ba8c5, #4ba8c5);
    margin-top: 35px;
    color: #FFF;
    padding: 20px 0px;
}

.footer .copy {
    padding: 5px 0px;
}

.footer a,
.footer .liens a {
    color: #FFF;
    font-weight: normal;
}

.footer .liens {
    color: #FFF;
    margin-bottom: 5px;
}

.table {
    display: table;
    width: 100%;
    border-collapse: collapse;
}

.row {
    display: table-row;
}

.td {
    display: table-cell;
}

.list .td {
    border-bottom: 1px solid #C9C9C9;
    vertical-align: top;
}

.td_img {
    padding: 13px 17px;
    width: 222px;
}

.list .texte {
    padding: 13px 13px;
}

fieldset {
    border: 2px solid #4B93C5;
    margin: 15px 5px;
    padding: 12px;
}

fieldset ul {
    padding: 0px;
    margin: 0px;
}

fieldset ul li {
    list-style: none;
    background: url('/../images/directemploi/puce.png') no-repeat left 7px;
    padding: 0px 0px 0px 14px;
    margin: 7px 0px;
}

fieldset ul li a {
    color: #333;
}

fieldset ul li a span {
    color: #4079A1;
}

legend {
    font-weight: bold;
    padding: 0 10px;
    font-size: 1.3em;
}

#fil_ariane {
    padding: 0px 0px 3px 15px;
}

.espaceRecruteur #fil_ariane {
    padding: 0px 0px 10px 15px;
}

.documents .lien {
    padding-left: 15px;
}

.documents .lien a {
    color: #383E49;
    font-weight: bold;
}

.documents .lien a:hover {
    color: #F4970B;
}


/****************************************************************/


/****************************************************************/


/*#loadingListingMembres /*Ecran de chargement pour le component "listing" du module "user" (charif)*/


/*{
	  display:none;
	  background:white;
	  border:1px solid #999;
	  margin:0 auto;
	  width:350px;
	  padding:20px;
	  text-align:center;
  }*/


/** ####################################################### */


/** #----------------------BOUTONS------------------------# */


/** ####################################################### */

.submit input,
.submit a,
a.submit,
input[type=submit] {
    border: 1px solid #333;
    margin: 0px;
    font-size: 1.2em;
    padding: 6px 35px;
    display: inline-block;
    cursor: pointer;
    font-weight: bold;
    transition: 0.2s;
    background-color: #4BA8C5 ;
    color: #FFF;
    border-radius: 8px;
}

.submit input:hover,
.submit a:hover,
a.submit:hover,
input[type=submit]:hover {
    background-color: #4ba8c5;
    background: linear-gradient(to top, #4ba8c5, #59AEEC);
}

.submit.pt input,
.submit.pt a,
a.submit.pt,
input[type=submit].pt {
    padding: 4px 15px;
    font-size: 1em;
}

.postuler .submit,
.sub_jaune a,
input[type=submit].sub_jaune,
a.sub_jaune {
    border-color: #666;
    background: linear-gradient(to top, #FFA21E, #FFBF3B);
    padding: 4px 35px;
    border-radius: 3px;
}

.postuler .submit:hover,
.sub_jaune a:hover,
input[type=submit].sub_jaune:hover,
a.sub_jaune:hover {
    background: linear-gradient(to top, #4ba8c5, #59AEEC);
}

.sub_bleu a,
a.sub_bleu,
input[type=submit].sub_bleu,
input.sub_bleu {
    background: linear-gradient(to top, #4ba8c5, #59AEEC);
    color: #FFF;
}

.sub_bleu a:hover,
a.sub_bleu:hover,
input[type=submit].sub_bleu:hover,
input.sub_bleu:hover {
    background-color: #4179A1;
    background: linear-gradient(to top, #4ba8c5, #59AEEC);
}

.sub_grey a,
a.sub_grey,
input.sub_grey {
    border-color: #666 !important;
    background: #E4E4E4 !important;
    border-radius: 4px;
    color: #333;
}

.sub_grey a:hover,
a.sub_grey:hover,
input.sub_grey:hover {
    background-color: #4ba8c5;
    background: linear-gradient(to top, #4ba8c5, #59AEEC);
}

.submit.sub_small input,
.submit.sub_small a,
a.submit.sub_small,
input[type=submit].sub_small {
    padding: 3px 25px;
    border-radius: 4px;
}

.annuler {
    background-color: #A63C4E;
    background: linear-gradient(to top, #A63C4E, #BF5264);
}

.supprim {
    background-color: #000;
    background: linear-gradient(to top, #000, #454545);
}

.retour {
    background-color: #D1D0D1;
    background: linear-gradient(to top, #D1D0D1, #F1F0F1);
}

.supprimer {
    margin: 15px 10px 0px 0px;
    padding: 7px 13px 7px 9px;
    float: left;
    font-weight: bold;
    cursor: pointer;
}


/* style de tableaux pour listings */

.listing {
    border-collapse: collapse;
    empty-cells: show;
    margin: 20px auto;
    width: 100%;
    color: black;
    background: white;
}

.listing thead {
    border-bottom: 2px solid #bbb;
}

.listing tfoot {
    border-top: 2px solid #bbb;
}

.listing thead th {
    color: #0785BF;
}

.listing tr:hover,
.tr_1:hover,
.tr_2:hover {
    background: #fff0dc
}

.listing td {
    padding: 5px;
    border: 1px solid #bbb;
}

.listing thead th,
.listing tfoot td {
    background: #eee;
    text-align: left;
    padding: 5px;
}

.listing .pagination {
    float: right;
    padding-top: 3px
}

td.listing_offre {
    padding: 7px 10px;
    font-weight: bold;
}

div.listing_offre {
    margin: 20px 0px;
    border: 1px solid #ccc;
    padding: 10px 20px;
    /* height:135px; */
}

div.listing_offre_vignette {
    margin: 20px 30px;
    border: 2px solid #ccc;
    height: 310px;
    width: 43%;
    border-radius: 6% 6%;
}

.title_vignette {
    height: 45px;
}

#offres_vignette {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin: 20px 0px 0px 10px;
}

#desc_court {
    display: none;
}

.checkboxVignette {
    text-align: center;
    margin-top: 35px;
    margin-bottom: 35px;
}

.listing_offre.even {
    border-color: #ACACAC;
}

.listing_offre.odd {
    border-color: #D6D6D6
}

.even {
    background-color: #EAEAEA;
}

.odd {
    background-color: #FFFF;
}

.onglets .odd {
    background-color: #FFF;
}

td.texte {
    padding: 0px 10px;
}

.box_gris {
    margin: 20px 0px;
    background: #EAEAEA;
    padding: 10px 18px;
    color: #333;
    margin-bottom: 2ch;
}

.listing_offre h3 {
    border: 0px;
    margin: 0px;
    padding: 0px;
}

.listing_logo {
    width: 118px;
    background: #FFF;
    border: 1px solid #333;
    text-align: center;
    padding: 0px;
}

.listing_stats {
    text-align: center;
    margin-left: 0em;
}

#tableListingUsersSC1 {
    margin-left: -21em;
}

#tableListingUsersSC2 {
    margin-left: -21em;
}

#tableListingUsersSC3 {
    margin-left: -21em;
}

#tableListingUsersSC4 {
    margin-left: -21em;
}

#tableListingUsersSC {
    margin-left: -21em;
}

.listing_stats th {
    background: dimgrey;
    color: #FFFFFF;
    padding: 5px 9px;
}

.listing_stats .titre_listing {
    background: #A4A2A3;
    color: #FFFFFF;
    font-weight: bold;
}

.listing_stats td {
    padding: 5px 9px;
}

.intitule {
    font-weight: bold;
}

.intitule_vignette {
    font-weight: bold;
    text-align: center;
}

#intitule_nom_cv {
    font-weight: bold;
    color: #006599;
}

.onglets .intitule {
    color: #222222;
    font-weight: bold;
    font-size: 12px;
}

.list_date,
.ref,
.localisation,
.type {
    color: #444444;
    font-weight: bold;
    font-size: 11px;
}

.listing_offre .text {
    color: #444444;
    font-weight: normal;
    margin-top: 5px
}

.listing_offre .tag {
    color: #444444;
    font-weight: normal;
    margin: 5px 0px;
}

.listing_offre_vignette .tag {
    color: #444444;
    font-weight: normal;
    margin: 0px 5px;
}

.listing_offre .tag a {
    text-decoration: underline;
    font-weight: normal;
}

.img_logo img {
    max-width: 120px;
    max-height: 120px;
    height: auto;
    width: auto;
}


/* style de tableaux pour les enquetes */

.enquetes_tableau_croise {
    border-collapse: collapse;
    empty-cells: show;
    margin: 20px auto;
    width: 100%;
    color: black;
    background: white;
    text-align: center;
}

.enquetes_tableau_croise thead {
    border-bottom: 2px solid #bbb;
}

.enquetes_tableau_croise tfoot {
    border-top: 2px solid #bbb;
}

.enquetes_tableau_croise thead th {
    color: #0785BF;
}

.enquetes_tableau_croise {
    border: 2px solid #bbb;
}

.enquetes_tableau_croise tr:hover,
.tr_1:hover,
.tr_2:hover {
    background: #fff0dc
}

.enquetes_tableau_croise td {
    padding: 5px;
    border: 1px solid #bbb;
}

.enquetes_tableau_croise thead th,
.listing tfoot td {
    background: #eee;
    padding: 5px;
}

.enquetes_tableau_croise .pagination {
    float: right;
    padding-top: 3px
}


/* style de tableaux pour les statistiques Offres */

.stats {
    border-collapse: collapse;
    empty-cells: show;
    margin: 20px auto;
    width: 100%;
    color: black;
    background: white;
}

.stats thead {}

.stats tfoot {
    border-top: 2px solid #bbb;
}

.stats thead th {
    border: 2px solid #bbb;
    color: #0785BF;
    border-left: 2px solid #bbb;
}

.stats thead td {
    background: #eee;
    border-left: 2px solid #bbb;
    border-bottom: 0px;
}

.statscustom {
    background-color: white;
    border-bottom: 2px solid #bbb;
}

.stats {
    border: 2px solid #bbb;
}

.stats tbody tr:hover,
.tr_1:hover,
.tr_2:hover {
    background: #fff0dc
}

.stats tbody td {
    padding: 5px;
    border: 1px solid #bbb;
}

.stats thead th,
.listing tfoot td {
    background: #eee;
    text-align: left;
    padding: 5px;
}

.stats .pagination {
    float: right;
    padding-top: 3px
}


/** style pour les messages d'erreur / d'alerte **/

.error_msg h1 {
    color: #f33;
}

.error_msg h2 {
    font-size: 14px;
    color: #f33;
    border: 0px;
}

.error_msg ul {
    margin-left: 20px;
}

.error_msg li {
    font-style: italic;
    font-size: 11px;
}

.error_msg {
    background: url(../images/direct-emploi/alerte.gif) no-repeat top left;
    padding: 5px 10px 7px 10px;
    margin-top: 10px;
    font-weight: bold;
    color: #FF3333;
    font-size: 14px;
}

.double {
    background: url(../images/direct-emploi/alerte2.gif) no-repeat top left;
}

.error_msg_body {
    font-size: small;
}

.success_msg {
    background: url(../images/direct-emploi/alerte.gif) no-repeat top left;
    padding: 5px 10px 7px 10px;
    margin-top: 10px;
    font-weight: bold;
    color: #0aa20e;
    font-size: 14px;
    width: 788.56px;
}

.voir_plus {
    border: solid green 2px;
    border-radius: 0.5em;
    height: 1.5em;
    background: limegreen;
    padding: 6px 10px 5px 10px;
    width: max-content;
    margin: 1em 21em 0 auto;
    font-weight: bold;
    color: white;
}

.notice_msg_body {
    font-size: small;
}

.notice_msg {
    background: url(../images/direct-emploi/alerte.gif) no-repeat top left;
    padding: 5px 10px 7px 10px;
    margin-top: 10px;
    font-weight: bold;
    color: #000;
}

.notice_msg_body {
    font-size: small;
}


/** style pour les messages d'erreur / d'alerte blanc**/

.error_msg_white h1 {
    color: #f33;
}

.error_msg_white h2 {
    font-size: 14px;
    color: #f33;
    border: 0px;
}

.error_msg_white ul {
    margin-left: 20px;
}

.error_msg_white li {
    font-style: italic;
    font-size: 11px;
}

.error_msg_white {
    background: url(../images/direct-emploi/alerte_blanc.gif) no-repeat top left;
    padding: 5px 10px 7px 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    font-weight: bold;
    color: #FF3333;
    font-size: 14px;
}

.error_msg_white_body {
    font-size: small;
}

.success_msg_white {
    background: url(../images/direct-emploi/alerte_blanc.gif) no-repeat top left;
    padding: 5px 10px 7px 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    font-weight: bold;
    color: #0aa20e;
    font-size: 14px;
}

.notice_msg_white_body {
    font-size: small;
}

.notice_msg_white {
    background: url(../images/direct-emploi/alerte_blanc.gif) no-repeat top left;
    padding: 5px 10px 7px 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    font-weight: bold;
    color: #000;
}

.notice_msg_white_body {
    font-size: small;
}

.align_center {
    text-align: center;
    valign: middle;
}

.display_none {
    display: none;
}


/* style affichage onglets administration */

.adminchamps {
    padding: 8px;
    background-color: #f9f9f9;
    color: black;
    border: 1px solid #bbb;
}

.adminchamps h1 {
    font-size: 18px;
    font-weight: bold;
    color: #000;
    margin: 5px 0px;
    padding: 0px;
}

.adminchamps h2 {
    font-size: 15px;
    font-weight: bold;
    padding: 0px;
    border-bottom: 1px solid #7F8591;
    background: none;
}

.adminchamp {
    float: left;
    padding: 0px 0px 0px 0px;
    background-color: #EBEDF2;
}

.adminchamps .box {
    border: 1px solid #999;
    background: #fff;
    padding: 5px;
    margin: 20px;
    color: #000;
}

.adminchamps .box .head_box .titre {
    font-weight: bold;
    font-size: 16px;
}

.adminchamps .box .head_box {
    margin: 0;
}

.adminchamps .box .head_box .liens {
    float: right;
}

.fieldset_creation_entreprise {
    border: 2px solid #ff6501;
    background-color: #ededed;
    padding-top: -20px;
    padding: 12px;
    margin: 20px;
}

.fieldset_creation_entreprise_legend {
    color: #ff6501;
    background-color: #ededed;
    font-size: 14px;
    font-weight: bold;
}


/* Dédoublonnage des entreprises */

.fiche_entr {
    padding: 0px 4px 4px 4px;
    margin-right: 4px;
    background-color: #edf2f6;
}

.listLiens ul,
#listFichiers {
    padding: 0;
    list-style: none;
}

.listLiens li,
#listFichiers li {
    margin: 1px;
    background: #F9F9F9;
    border: 1px solid #000;
    padding: 5px;
    cursor: move;
}

.listFichiers li {
    cursor: default;
}

.organiser li {
    background: #fff0dc;
    border: 1px solid #000;
    padding: 5px;
    list-style-type: none;
    background-image: url(../images/direct-emploi/puce.gif);
    background-position: 4px;
    background-repeat: no-repeat;
    padding-left: 18px;
    margin-bottom: 8px;
    cursor: move;
}

#listing_puces li {
    list-style-type: none;
    background-image: url(../images/direct-emploi/puce.gif);
    background-repeat: no-repeat;
    padding-left: 12px;
}

#listing_puces ul {
    margin-top: 12px;
}

.listing_trie {
    font-size: 11px;
    border-collapse: collapse;
    width: 100%;
}

.listing_offres {
    border-collapse: collapse;
    width: 100%;
}

.listing_offres .act {
    text-align: right;
    white-space: nowrap;
    font-size: 0.8em;
}

.listing_offres .act a {}

.listing_offres h3 {
    margin: 10px 0px 0px 0px;
}

.fond_legende {
    margin-top: 12px;
    margin-bottom: 12px;
    background: #F9F9F9;
    border: 1px solid #000;
    padding: 5px;
}

.clear {
    clear: both;
    height: 0px;
}

.clearL {
    clear: left;
    height: 0px;
}

.clearR {
    clear: right;
    height: 0px;
}


/* Style pour la partie admin des newsletter*/

.news_retrait1 {
    padding-left: 20px;
}

.news_retrait2 {
    padding-left: 40px;
    margin-top: 5px;
}

.news_retrait3 {
    padding-left: 60px;
    margin-top: 5px;
}

fieldset.info_contact {
    background: rgb(94.9%, 80%, 70.6%);
    border: 1px solid rgb(91%, 36.1%, 12.9%);
}

#tagsphere li {
    color: #F89B17;
}

span.accessKey {
    text-decoration: underline;
}

div.mois_grand {
    padding: 0px 0px 4px 0px;
    font-size: 12px;
    color: black;
    text-transform: uppercase;
    font-weight: bold;
}

table.avec_cadre th {
    border: 1px solid black;
    padding: 6px;
}

table.formulaire {
    width: 100%;
    background-color: #ebebeb;
    margin: 10px 0px;
}

table.formulaireCv {
    width: 100%;
    background-color: rgb(240, 240, 240);
    margin: 0px 0px;
}

table.formulaire th {
    text-align: left;
}

table.formulaire td,
table.formulaire th {
    padding: 6px 10px;
}

table.formulaire span {
    padding-top: 2px;
}

table.formulaire input[type=submit] {
    margin: 5px 0px;
    font-size: 1em;
    padding: 3px 15px;
    border-radius: 4px;
}

table.crea {
    width: 100%;
    background-color: #ebebeb;
    margin-top: 10px 0px;
}

table.creaCv {
    width: 100%;
    background-color: rgb(240, 240, 240);
    margin: 0px 0px;
}

table.crea th {
    text-align: left;
}

table.crea td,
table.crea th {
    padding: 0px 10px;
}

table.crea span {
    padding-top: 2px;
}

table.crea input[type=submit] {
    margin: 5px 0px;
    font-size: 1em;
    padding: 3px 15px;
    border-radius: 4px;
}

.padbis {
    padding-left: 10px;
    padding-right: 15px;
    padding-bottom: 15px;
}

.padbismail {
    padding-left: 10px;
    padding-right: 15px;
    padding-bottom: 14px;
}

.padbismaillib {
    padding-left: 10px;
    padding-right: 15px;
    padding-bottom: 11px;
}

.colgri {
    background-color: #ebebeb;
}

.pad {
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 15px;
}

.aide_bt {
    display: inline-block;
    margin: 5px 10px;
    width: 18px;
    height: 18px;
    background: radial-gradient(ellipse at center top, #55A4CA, #1983B7);
    border-radius: 35px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.aide_bt a {
    display: block;
    background: url('../images/aide.png') no-repeat center;
    width: 18px;
    height: 18px;
}

form {
    margin: 0px;
    padding: 0px;
}

.label,
.formulaire th {
    width: 192px;
    font-weight: bold;
    text-align: left;
    vertical-align: top;
    padding-top: 4px;
}

label {
    font-weight: bold;
}

.ob {
    color: #FF3333;
    font-size: 18px;
    line-height: 15px;
    font-weight: bold;
    width: 10px;
}

.ol {
    color: #000000;
    font-size: 18px;
    line-height: 15px;
    font-weight: bold;
    width: 10px;
}

.bt_diffuser a {
    background: url(../images/direct-emploi/bt_diffuser.gif) no-repeat top right;
    color: #FFFFFF;
    font-size: 13px;
    text-align: center;
    padding: 7px 10px 7px 10px;
    display: block;
    width: 158px;
}

.statut_on {
    background: url(../../images/enligne.gif) no-repeat top left;
    width: 15px;
    height: 15px;
}

.statut_off {
    background: url(../../images/horsligne.gif) no-repeat top left;
    width: 15px;
    height: 15px;
}

.trie_enligne {
    background: url(../../images/enligne.gif) no-repeat top left;
    padding: 0px 0px 3px 17px;
}

.trie_horsligne {
    background: url(../../images/horsligne.gif) no-repeat top left;
    padding: 0px 0px 3px 17px;
}

.act {
    font-weight: bold;
    text-align: center
}

.contenu .rouge {
    color: #FF3435;
}

.contenu .rouge a {
    color: #FF3435;
}


/** #-----------------------PAGER-------------------------# */

.pagination {
    color: #FFF;
    font-weight: bold;
    margin: 0px auto;
    padding: 10px 0px;
    text-align: center;
}

.pagination table {
    margin: auto;
}

.pagination .items {
    display: inline-block;
}

.pagination div {
    background: #3AB1E7;
    border-radius: 4px;
    margin-right: 10px;
}

.pager_prec,
.pager_suiv {
    width: 85px;
    text-align: center;
    float: left;
    line-height: 22px;
}

.pager_prec a,
.pager_suiv a {
    font-weight: normal;
    padding: 5px 5px;
}

.pager_suiv {
    margin-right: 0px;
}

.pagination a {
    background: #0785BF;
    border-radius: 4px;
    display: table;
    font-weight: bold;
    color: #FFF;
    padding: 0px;
}

.pagination .bt_pager_chiffre {
    height: 32px;
    text-align: center;
    float: left;
    line-height: 32px;
}

.bt_pager_chiffre {
    width: 30px;
}

.bt_pager_chiffre a {
    width: 30px;
}

.listing_text {
    float: left;
    width: 590px;
}

.listing_text h4 {
    margin: 7px 0px 0px;
    font-size: 1em;
}

.infos_vignette {
    display: block;
}

.retour {
    margin-top: 10px;
}

.nothing {
    margin: 20px 0px;
    text-align: center;
    font-style: italic;
    color: #666;
    font-size: 1.1em;
}

.voir_statistiques {
    background: url(../images/direct-emploi/bt_voir-statistiques.gif) no-repeat top left;
    width: 120px;
    height: 48px;
    margin-top: 10px;
    text-align: center;
}

.voir_statistiques a {
    display: block;
    font-weight: bold;
    padding: 8px 6px 0px 6px;
    color: #FFF;
}

.submit_text input {
    border: 0px;
    background: url(../images/direct-emploi/spacer.gif) repeat top left;
}

.fiche_user {
    border: 1px solid #333;
    padding: 10px;
    margin-bottom: 10px;
}

.fiche_user h2 {
    background: url(../images/direct-emploi/spacer.gif) repeat top left;
}

.fiche_user .item {
    margin: 0px;
    border-top: 1px solid #e5e5e5;
    padding: 3px 15px;
}

.contenu .fiche_user .item a {
    font-weight: normal;
}

hr {
    height: 1px;
    border-bottom: 1px solid #979797;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    padding: 0px;
    margin: 30px 0px;
}

.lien_ajout_cv {
    float: right;
    font-weight: bold;
    color: #0785BF;
    text-decoration: underline;
    padding-bottom: 10px;
}

.contenu_article .dablink img {
    border: 1px solid #333;
    margin: 0px 0px 15px 15px;
}

.menu_superadmin {
    width: 100%;
    color: #333;
}

.menu_superadmin a {
    font-weight: bold;
}

.menu_superadmin .label {
    width: auto;
    color: #000;
    padding: 15px 0px 4px 0px;
}

.convertir input {
    background: none;
    font-weight: bold;
    font-size: 12px;
    padding: 0px;
    border: 0px;
    font-size: 11px;
    color: white
}

.non_valide {
    color: #FF3333;
    font-weight: bold;
    display: block;
}

.center {
    text-align: center;
}

.convert {
    padding: 10px 0px;
}

.convert a {
    color: #FFFFFF;
}

.afirm {
    float: left;
    font-weight: bold;
}

.note_cv {
    margin-top: 8px;
    width: 17px;
    height: 22px;
    color: #FFFFFF;
    font-size: 14px;
    font-weight: bold;
    background: url(../images/direct-emploi/note_cv.png) no-repeat top left;
    padding: 5px 0px 0px 9px;
}

.resultats {
    padding-top: 5px;
}

.lien_modif {
    font-weight: bold;
    padding-right: 15px;
}

.lien_info {
    padding-right: 15px;
}

.page_them h1.h3_title {
    margin-top: 10px;
    font-size: 1.4em;
}

.page_them .chapo {
    font-weight: bold;
    color: #252424;
    font-size: 1.2em;
    padding: 0px 2px 15px 2px;
}

.img_them {
    border: 1px solid #333333;
    margin-left: 3px;
}

.page_them .text {
    padding-left: 25px;
    font-size: 0.9em;
}

.page_home h2 {
    margin-top: 25px;
}

.vert {
    color: green;
}

.vert a {
    color: green;
    font-weight: bold;
}

.rouge {
    color: red;
}

.rouge a {
    color: red;
    font-weight: bold;
}

.notitle h2,
.notitle h3 {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12px;
    color: #333333;
    padding: 0px;
    margin: 0px;
    background: none;
    display: inline;
    height: auto;
    font-weight: normal;
}

.specialCarriereInformatique a img {
    width: 100px;
    height: 65px;
}

#control_overlay {
    background-color: #000;
}

.modal {
    background-color: #fff;
    padding: 10px;
    border: 1px solid #333;
}

.popupAlertMail {
    width: 520px;
    padding: 15px;
    border-radius: 5px;
}

.popupAlertMail p {
    width: 345px;
}

.popupAlertMail input[type=text] {
    float: left;
    height: 28px;
    margin: 0 5px 5px 0;
    padding: 0;
    width: 400px;
    font-size: 14px;
    font-weight: normal;
}

.popupAlertMail input[type=text].step2 {
    width: 200px;
}

.popupAlertMail .submit {
    margin-right: 0;
}

.popupAlertMail .title {
    background: url("/images/directemploi_mini.jpg") no-repeat scroll left top transparent;
    color: #1983B7;
    font-size: 14px;
    font-weight: bold;
    height: 40px;
    padding-left: 100px;
    padding-top: 14px;
    text-transform: uppercase;
}

.popupAlertMail hr {
    border-style: none none dotted;
    border-width: 0 0 1px;
    height: 1px;
    margin: 0;
    padding: 0;
}

.popupAlertMail #window_close {
    background: none repeat scroll 0 0 #CCCCCC;
    border: 1px solid #AAAAAA;
    border-radius: 5px 5px 5px 5px;
    clear: both;
    color: #FFFFFF;
    font-family: Georgia, 'Times New Roman', serif;
    font-size: 4px;
    height: 10px;
    line-height: 7px;
    padding: 2px 3px;
    position: absolute;
    right: 8px;
    text-shadow: 1px 1px 0 #999999;
    top: 8px;
    vertical-align: middle;
    cursor: pointer;
}

.popupAlertMail #window_close em {
    font-size: 12px;
    font-style: normal;
}

.clearfix1 {
    margin-top: 32px;
}

.clearfix2 {
    padding-bottom: 16px;
    border-bottom: 1px solid #c2c2c2;
}

.clearfix3 {
    margin-top: 20px;
}

.clearfix4 {
    margin-top: 10px;
}

.clearfix:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0;
}

#caja_logos ul li {
    background: none;
    float: left;
    margin-left: 10px;
    list-style-type: none;
}

#caja_logos img {
    width: 80px;
    height: 43px;
}

#caja_radioterminos input {
    width: 20px;
}

#cdcr_sporsorslistcheck input {
    margin-bottom: 2px;
}

.bt_sauv {
    font-weight: bold;
}

.menu td {
    padding: 0px;
    position: relative;
}

.smenu {
    position: absolute;
    display: none;
    z-index: 600;
    padding-top: 15px;
    padding-bottom: 10px;
}

.smenu .int {
    background: #FFF;
    width: 265px;
    border-radius: 0px 0px 6px 6px;
    padding-bottom: 15px;
    box-shadow: 4px 3px 4px rgba(0, 0, 0, 0.2);
}

.smenu a {
    padding: 5px 15px;
    font-weight: bold;
    display: block;
    font-size: 1.1em;
}

.smenu a:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.envoi_mail hr {
    margin-top: 0px;
}

.envoi_mail h5 {
    font-size: 12px;
    padding: 8px 0px;
}

.envoi_mail hr {
    border-color: #666;
}

.message_text_envoi {
    text-align: center;
}

.message_valid_envoi a {
    text-decoration: underline;
}

.listing_offres a.sub_jaune {
    margin-top: 9px;
}

.listing_offresCv {
    border: 1px solid grey;
}

formucv {
    border: 2px solid grey;
}

.columns {
    -webkit-columns: 4;
    -moz-columns: 4;
    columns: 4;
}

.columns p {
    color: #333;
    padding: 3px;
    margin: 0;
    background-color: beige;
}

.edit_mc {
    /*width: 250px;*/
    min-height: 150px;
    height: auto;
    padding-top: 5px;
}

.edit_mc_localisation {
    background-color: #fff;
    padding: 15px;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
    min-height: 225px;
    height: auto;
    position: absolute;
    margin-left: 0px;
    margin-top: 5px;
}

#liste_SeoMotCles .columns {
    margin-bottom: 10px;
}

.depot_express {
    height: 95px;
    width: 160px;
    margin-bottom: 15px;
}

.depot_express h3 {
    background: none;
}

.overlay_trans {
    position: fixed;
    top: 0px;
    left: 0px;
    height: 100%;
    width: 100%;
    overflow: hidden;
    opacity: 0.85;
    z-index: 200;
    cursor: pointer;
    display: none;
    background: none;
}

ul.filArianneInscription h2{
    float: left;
    width: 150px;
    height: 30px;
    /* padding-top: 65px; */
    text-transform: uppercase;
    font-size: 14px;
    margin: 0px 10px;
    text-align: center;
    color: lightgrey;
    border: none;
}

ul.filArianneInscription .active h2{
    color: #006599;
}

.obli {
    padding: 0px 13px;
}

ul.filArianneInscription {
    list-style: none;
}

ul.filArianneInscription li {
    float: left;
    width: 150px;
    height: 30px;
    padding-top: 47px;
    text-transform: uppercase;
    font-size: 12px;
    margin: 5px 10px;
    text-align: center;
    color: #888;
}

ul.filArianneInscription li.arrow {
    width: 30px;
    height: 95px;
    padding: 0;
    background: url("/images/images/direct-emploi/page-inscription-candidat/arrow.png") no-repeat scroll left 34px transparent;
}

ul.filArianneInscription li.active {
    color: #006599;
}

ul.filArianneInscription li.step_account {
    background: url("/images/images/direct-emploi/page-inscription-candidat/serrure-off.png") no-repeat scroll 50px 5px transparent;
}

ul.filArianneInscription li.step_account.active {
    background: url("/images/images/direct-emploi/page-inscription-candidat/serrure-on.png") no-repeat scroll 50px 5px transparent;
}

ul.filArianneInscription li.step_profil {
    background: url("/images/images/direct-emploi/page-inscription-candidat/profil-off.png") no-repeat scroll 50px 20px transparent;
}

ul.filArianneInscription li.step_profil.active {
    background: url("/images/images/direct-emploi/page-inscription-candidat/profil-on.png") no-repeat scroll 50px 20px transparent;
}

ul.filArianneInscription li.step_alerte {
    background: url("/images/images/direct-emploi/page-inscription-candidat/alter-mail-off.png") no-repeat scroll 50px 10px transparent;
}

ul.filArianneInscription li.step_alerte.active {
    background: url("/images/images/direct-emploi/page-inscription-candidat/alter-mail-on.png") no-repeat scroll 50px 10px transparent;
}

ul.inscriptionPlus {
    list-style: none;
}

ul.inscriptionPlus .coche {
    margin: 34px 60px 20px 0px;
}

ul.inscriptionPlus div.icon {
    float: left;
    width: 80px;
    height: 40px;
    margin: 20px 0px;
}

ul.inscriptionPlus .text {
    width: 720px;
    float: left;
    margin: 20px 0px;
}

ul.inscriptionPlus div.icon.add-cv {
    padding-top: 20px;
    background: url("/images/images/direct-emploi/page-inscription-candidat/add-cv.png") no-repeat scroll left center transparent;
}

ul.inscriptionPlus div.icon.alter-mail-petit {
    background: url("/images/images/direct-emploi/page-inscription-candidat/alter-mail-petit.png") no-repeat scroll left center transparent;
}

ul.inscriptionPlus div.icon.newsletter {
    background: url("/images/images/direct-emploi/page-inscription-candidat/newsletter.png") no-repeat scroll left center transparent;
}

ul.inscriptionPlus div.icon.legal-notice {
    background: url("/images/images/direct-emploi/page-inscription-candidat/legal-notice.png") no-repeat scroll left center transparent;
}

.chapo_compte .text {
    font-weight: bold;
    width: 600px;
    float: left;
}

.floatR {
    float: right;
}

.box_user .smenu a.logout {
    background: url("/images/logout.png") no-repeat scroll 6px 8px;
    padding-left: 30px;
}

.bordure {
    border: 3px solid #8cc0db;
    padding: 0px 8px 5px 8px;
    margin: 0px -8px -5px -8px;
}

@media (max-width: 640px) {
    .container_items {
        margin-left: 50px;
    }
}

.box_society {
    width: 96%;
    margin: auto;
}

.box_society .item {
    float: left;
    width: 25%;
    height: 82px;
    overflow: hidden;
    margin: 0px 0px 20px 0px;
}

.box_society .img_logo {
    display: table-cell;
    vertical-align: middle;
    height: 75px;
    width: 90px;
    text-align: center;
    border: 1px solid #333;
}

.box_society .img_logo img {
    max-height: 73px;
    max-width: 88px;
    width: auto;
    height: auto;
    border: 0px;
}

.box_society .img_logo .text_nom {
    font-weight: bold;
    font-size: 0.7em;
    padding: 0px 3px;
    word-break: break-all;
    border: 0px;
}

.box_society .text {
    display: table-cell;
    vertical-align: top;
    padding: 0px 15px 0px 6px;
}

.box_society .text .int {
    height: 77px;
    overflow: hidden;
    font-size: 0.8em;
}

.box_society .text h4 {
    margin: 0px;
    padding: 0px;
    font-size: 1.1em;
}

.droits_auteur {
    position: absolute;
    top: 470px;
    right: 10px;
    font-size: 0.7em;
    color: rgba(0, 0, 0, 0.4);
}

.img_admin {
    width: 490px;
}

.img_admin .img {
    float: left;
    margin: 5px;
}

.img_admin .img img {
    height: 70px;
    width: auto;
}

.admin_candidature {
    margin: 0px 0px 0px 5px;
    font-size: 1em;
}

.tour_logo {
    display: inline-block;
    background: #FFF;
    border-radius: 6px;
    padding: 4px;
}

.reprise_offres img:not(.auto) {
    height: 50px;
    width: auto;
}

.reprise_offres .tour_DE img {
    height: 60px;
}

.form_box .reprise_offres h3 {
    font-size: 1.1em;
    background: none;
    padding: 20px 0px 10px 25px;
}

.referencement .ref_h5 {
    border-bottom: 1px solid #9a9a9a;
    padding: 0 10px 10px 0;
    margin-top: 17px;
    font-size: 1.1em;
    margin: 13px 0 10px 0;
    color: #252424;
    padding: 0;
    font-weight: 700;
    line-height: 1.2;
}

.referencement li {
    list-style-type: none;
}

.referencement li a {
    text-decoration: underline;
    display: block;
    padding: 4px 0px;
    font-size: 0.85em;
}

.creer_compte .cc_h5 {
    background: #3c8dce;
    color: #fff;
    padding: 23px 4%;
    margin: 0;
    font-size: 1.1em;
    font-weight: 700;
    line-height: 1.2;
    text-align: center;
}

.box_newsletter {
    background-color: #487EA4;
    background: linear-gradient(to top, #487EA4, #5E95BD);
    border-radius: 2%;
    color: #FFF;
    padding: 20px;
}

.box_newsletter h3 {
    color: #FFF;
    padding: 0px;
    margin: 0px;
    border: 0px;
}

.box_newsletter h3 span {
    color: #E8B63E;
}

.box_newsletter div {
    margin: 6px 0px;
}

.form_newsletter input[type="text"] {
    width: 320px;
}

.box_newsletter .form_newsletter input[type="submit"] {
    width: auto;
    color: #666;
    font-size: 1em;
}

.espaceAdmin h2 {
    margin-bottom: 10px;
}

.retour_liste {
    background: url('../../images/directemploi/retour.png') no-repeat left center;
    padding: 4px 0px 4px 20px;
}

.share_offre {
    float: right;
}

.boxG {
    float: left;
}

.boxD {
    float: right;
}

.act_recrut .submit {
    display: inline-block;
    margin: 15px 0px 15px 0px;
}

.act_recrut .submit a {
    font-size: 1em;
    padding: 5px 15px;
}


/* DEBUT FCK EDITOR ARTICLE */

.img_liste_logo {
    float: right;
    margin: 22px 0px;
}

.img_liste_logo_vignette {
    margin: 17px 155px;
    width: 30%;
}

.img_logo .int,
.img_logo .text_nom,
.img_liste_logo .int,
.img_liste_logo .text_nom {
    display: table-cell;
    width: 120px;
    height: 80px;
    border: 1px solid #333;
    background: #FFF;
    text-align: center;
    vertical-align: middle;
    font-size: 0.9em;
    background: #FFF;
}

.img_logo .int {
    height: 120px;
}

.img_logo .int img,
.img_liste_logo .int img {
    width: auto;
    height: auto;
    max-height: 80px;
    max-width: 120px;
    border: 0px;
}

.img_logo .int img {
    max-height: 120px;
}

.couleur_site {
    color: #333333;
}

.couleur_bleu {
    color: #006599;
}

.couleur_orange {
    color: #FB4F00;
}

.couleur_rouge {
    color: #FF3300;
}

.couleur_verte {
    color: #588101;
}

.phrase1 {
    color: #006599;
    font-size: 14px;
    font-style: italic;
    margin: 10px 0px;
}

.phrase2 {
    color: #333333;
    font-size: 13px;
    font-style: italic;
    margin: 10px 0px;
}

.bloc_attention {
    color: #FF3300;
    border: 1px solid #FF3300;
    margin: 10px 0px;
    padding: 6px;
}

.img_article {
    text-align: center;
}

.article_title_h1 {
    font-size: 1.8em;
    margin: 5px 0px 10px 0px;
    padding: 0px 5px 10px 5px;
    border-bottom: 1px solid #c2c2c2;
    color: #252424;
    font-weight: bold;
    font-family: Arial, Helvetica, sans-serif;
}

.contenu_article h2,
.contenu_article h3.h2 {
    clear: both;
    color: #2D6C9F;
}

.contenu_article p {
    padding: 0px 13px;
}

.mag_liste .even {
    background: none;
}

.list .intitule {
    font-size: 1.2em;
    padding-bottom: 4px;
}

.img img,
.td_img img {
    border: 1px solid #333;
    max-width: 100%;
    height: auto;
}

.td_img img {
    width: 222px;
    height: 91px;
}

.liste_actus .td_img {
    padding: 25px 17px;
}

.liste_actus .list .texte {
    padding: 20px 13px;
}

.list .texte h4 {
    margin: 0px;
}


/* FIN FCK EDITOR ARTICLE */

.version-light {
    margin: 20px 0;
}

.version-light .red {
    color: rgb(234, 234, 234);
    font-weight: bold;
    display: table-cell;
    vertical-align: top;
    padding-left: 15px;
}

.version-light ul {
    display: table-cell;
    vertical-align: top;
}

.version-light a {
    text-decoration: underline;
}

.box_insc_info {
    border: 2px solid #4b84ad;
    padding: 15px 35px;
    color: #4b84ad;
    font-weight: 600;
    margin: 15px 0px 25px 0px;
    position: relative;
}

.lienInfo {
    position: absolute;
    bottom: 5px;
    right: 5px;
    text-decoration: underline;
}

#moteurcv {
    border: 2px solid grey;
    border-radius: 10px;
}

#h2cvmaintxt {
    color: white;
    margin: 0px;
    padding: 12px;
}

.h2cvmain {
    background-color: #4e86af;
    border-radius: 8px 8px 0px 0px;
}

#h2cvsous {
    width: 42%;
    border: 0px;
    background-color: rgb(240, 240, 240);
    margin-left: 8%;
}

#h2cvsousfirst {
    width: 42%;
    border: 0px;
    background-color: rgb(240, 240, 240);
    margin-left: 4%;
}

.trformCV {
    border: 1px solid #c8c8c8;
    background-color: white;
    width: 100%;
}

.convertcv {
    border: 0px;
    color: #4e86af;
    text-align: right
}

.submit_rechercheCV {
    text-align: center;
    margin-left: 3.5%;
    /*margin-top: 36px;
	/*margin-left: 36px;
	margin-left: 41px;
    margin-right: 36px;*/
}

#submit_manu {
    background: #9ea2a3;
}

#lign {
    /*border-bottom: 1px solid grey;*/
}

#poste_cv {
    width: 353%;
    margin-left: 44%;
}

#competence_cv {
    width: 353%;
    margin-left: 49%;
}

.select_form_cv {
    margin-top: 10%;
    text-align: center;
}

.left_select_cv {
    width: 40%;
    margin-left: 10%;
}

.right_select_cv {
    width: 40%;
    margin-right: 10%;
}

.form_intitul_cv {
    padding-top: 1%;
    padding-left: 7%;
    width: 50%;
    text-align: inherit;
}

.checkbox_select_cv {
    margin-left: 12%;
    width: 80%;
}

.more_cv {
    background: #FFF url(../images/select.png) no-repeat right center;
    background-image: url(../images/select.png);
    background-position-x: right;
    background-position-y: center;
    background-size: initial;
    background-repeat-x: no-repeat;
    background-repeat-y: no-repeat;
    background-attachment: initial;
    background-origin: initial;
    background-clip: initial;
    background-color: rgb(255, 255, 255);
}

.libel_form_cv {
    margin-left: 13%;
    float: left;
    border: 0px;
}

#input_field_cv {
    float: left;
    margin-left: -8%;
    width: 48%;
}

#trcv20 {
    width: 20%;
}

#trcv60 {
    width: 60%;
}

.border_list_cv {
    border: 1px solid grey;
    margin: 1%;
    padding-bottom: 1%;
    padding-left: 1%;
}

.spancv {
    margin-left: 3%;
}

.spantitle {
    margin-left: 6%;
}

#resultatscv {
    border-bottom: 1px solid grey;
}

.inputcv {
    background: #4B84AD;
    border-radius: 4px;
    color: white;
    padding: 3px;
}

#blank {
    margin-top: 3%;
}

#blank2 {
    margin-top: 4%;
}

#blank3 {
    margin-top: 7%;
}

.picturecv {
    display: inline-block;
    margin-left: 34%;
    margin-top: 3%;
    position: absolute;
    text-align: center;
}

#first_block_cv {
    padding: 1%;
    border: 0px;
    background-color: rgb(220, 220, 220);
    padding-bottom: 2%;
}

#other_block_cv {
    line-height: 151%;
}

.picturecv {
    display: inline-block;
    margin-left: 34%;
    margin-top: 0%;
    position: absolute;
    text-align: center;
}

#h2cv {
    border: 0px;
    font-size: 1.8em;
    margin: 5px 0px 10px 0px;
    padding: 0px 5px 10px 5px;
}

#block_txt_cv {
    padding-left: 4%;
    margin-bottom: -1%;
    line-height: 151%;
}

#show_cv_content {
    margin-top: 5%;
    margin-bottom: 5%;
}

#txt_welcome_cv_no_com {
    text-align: justify;
    padding: 1%;
    color: black;
    background-color: rgb(234, 234, 234);
    line-height: 68%;
}

#txt_cv_no_com {
    text-align: center;
    color: grey;
}

.bt_diffuser_cv a {
    background: url(../images/direct-emploi/bt_diffuser.gif) no-repeat top right;
    color: #FFFFFF;
    font-size: 15px;
    text-align: center;
    padding: 7px 10px 7px 10px;
    display: inline-block;
    width: 158px;
    margin-top: 26px;
    margin-left: 38%;
    position: absolute;
}

#txt_welcome_cv {
    text-align: justify;
    padding: 1%;
    color: black;
    background-color: rgb(234, 234, 234);
    line-height: 68%;
}


/* #save_search{
	border: 1px solid #333;
    margin: 0px;
    font-size: 1.2em;
    padding: 6px 35px;
    display: inline-block;
    cursor: pointer;
    font-weight: bold;
    transition: 0.2s;
    background-color: #447CA4;
    background: linear-gradient(to top,#4179A1,#447CA4,#5790BA);
    color: #FFF;
    border-radius: 8px;
} */

#note_cv {
    margin-left: 5%;
}

#note_cv_up {
    text-align: right;
    padding-right: 1%;
}

#blankmc {
    margin-top: 10%;
    margin-left: 20%;
}

#alerteeditcv {
    width: 77%;
    margin-left: 12%;
}

#input_save_search {
    float: left;
    margin-left: -8%;
    margin-bottom: 30px;
    width: 48%;
}

#champ_obli {
    margin-left: 10%;
}

#voir_no_cv {
    text-align: center;
    margin-top: 2%;
}

#voir_cv {
    /*margin top: 70%;*/
    /* vertical-align: middle; */
    margin-left: 30%;
    position: fixed;
    /* margin-bottom: -24%; */
    margin-top: -8%;
}

#cvshow {
    margin-left: 13%;
}

#h1descv {
    text-align: center;
    color: green;
    border: 0;
}

#successcv {
    color: white;
    background-color: darkgreen;
    padding: 1%;
    line-height: 2;
    font-size: 93%;
}

#validcv {
    color: white;
    background-color: darkred;
    padding: 1%;
    font-size: 93%;
}

.cv_no_found {
    margin-left: 9%;
}

#voicvlink {
    width: 19%;
    text-align: center;
    vertical-align: middle;
}

#script_column {
    color: white;
}

.script_button {
    border-radius: 10px;
    display: block;
    margin: auto;
    background-color: #2BD834;
    background: #2BD834;
    color: #FFF;
    padding: 0.5% 0.5%;
    outline: none;
}

.script_box {
    overflow: auto;
    height: 800px;
}

#script_actif {
    outline: none;
    border-radius: 10px;
    width: 100%;
    display: block;
    margin: auto;
    background-color: #2BD834;
    color: white;
}

#script_non_actif {
    border-radius: 10px;
    width: 100%;
    display: block;
    margin: auto;
    color: #FFF;
    background-color: #0b7012;
    outline: none;
    text-decoration: none;
}

#script_en_cours {
    border-radius: 10px;
    width: 100%;
    display: block;
    margin: auto;
    background-color: #8a00cf;
    color: white;
}

#script_en_attente {
    border-radius: 10px;
    width: 100%;
    display: block;
    margin: auto;
    background-color: #60cf60;
    color: white;
}

#script_fini {
    border-radius: 10px;
    width: 100%;
    display: block;
    margin: auto;
    background-color: #64c2cf;
    color: white;
}

#script_bientot_sans_souscription {
    outline: none;
    border-radius: 10px;
    width: 100%;
    display: block;
    margin: auto;
    background-color: #79A246;
    color: white;
}

#script_sans_souscription {
    outline: none;
    border-radius: 10px;
    width: 100%;
    display: block;
    margin: auto;
    background-color: #063d0a;
    color: white;
}

.listing_script {
    font-weight: bold;
    padding: 5px 9px;
    color: #FFFFFF;
    background-color: white;
}

.listing_script th {
    background: grey;
    color: #FFFFFF;
    padding: 5px 9px;
}

.listing_script td {
    padding: 5px 9px;
}


/*
.sect_block_valid_left{
	background-color: #4CAF50; /* Green
	border: none;
	color: white;
	padding: 15px 20px 15px 20px;
	text-align: center;
	text-decoration: none;
	display: inline-block;
	font-size: 20px;
	border-radius: 8px;
}*/

.sect_block_valid_right,
.sect_block_valid_left,
.sect_block_invalid_right,
.sect_block_invalid_left {
    border: none;
    color: white;
    padding: 15px 20px 15px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 20px;
    border-radius: 8px;
}

.sect_block_valid_right {
    background-color: #4CAF50;
    /* Green */
    float: right;
    margin: 0 180px 15px 0;
}

.sect_block_valid_left {
    background-color: #4CAF50;
    /* Green */
    float: left;
    margin: 0 0 15px 180px;
}

.sect_block_invalid_right {
    background-color: #e50000;
    /* Green */
    float: right;
    margin: 0 180px 15px 0;
}

.sect_block_invalid_left {
    background-color: #e50000;
    /* Green */
    float: left;
    margin: 0 0 15px 180px;
}


/*SI FIREFOX*/

@-moz-document url-prefix() {
    input::-webkit-calendar-picker-indicator {
        display: none;
    }

    .myarrow:after {
        content: url(https://i.stack.imgur.com/i9WFO.png);
        margin-left: -20px;
        padding: .1em;
        pointer-events: none;
    }
}

.case_sect {
    width: 30px;
    font-size: 12px;
}

.case_sous_sect {
    width: 35px;
    font-size: 12px;
}

#sect_button_validate {
    position: sticky;
    float: right;
    margin: 10px 41px 0 0;
    bottom: 0;
}

.sect_button_activate,
.sect_button_desactivate {
    border-radius: 10px;
    width: 100%;
    display: block;
    margin: auto;
    color: #000000;
    background-color: #81a9e8;
    outline: none;
    text-decoration: none;
}

.sect_button_activate {
    background-color: #2c821b;
}

.sect_button_desactivate {
    background-color: #4c514b;
}

.sect_button_text {
    color: #ffffff;
}

.sect_button_text:hover {
    color: #bcbcbc;
}

.sect_button_afficher_all {
    background-color: white;
    color: black;
    border: 2px solid #008CBA;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 20px;
    font-weight: bold;
    margin: 6px 30% 10px 25%;
    cursor: pointer;
    width: 40%;
    transition-duration: 0.1s;
}

.sect_button_afficher_all:hover {
    background-color: #008CBA;
    /* Green */
    color: white;
}

.table_voir_cv {
    width: 40%;
    font-size: larger;
}

.table_voir_cv_right {
    width: 40%;
    font-size: larger;
    text-align: center;
}

#partie_deux_show_cv {
    border: 1px solid #ccc;
    width: 98%;
    margin-top: 1%;
    padding-left: 2%;
    padding-bottom: 1%;
}

#h2_partie_deux_show_cv {
    border: 0px;
    margin: 0px;
    margin-bottom: 4%;
    padding: 0px;
}

#table_partie_deux_show_cv {
    width: 105%;
}

#id_cvshow {
    margin-top: 2%;
    width: 100%;
    height: 100%;
    text-align: center;
}

.select_border_bottom {
    border-bottom: 1px solid grey;
    padding-bottom: 2%;
}

.select_border_up {
    padding-top: 2%;
}

.select_form_cv_metier {
    border: solid 1px grey;
    padding: 1%;
    margin-top: 3%;
    margin-left: 3%;
    margin-bottom: 3%;
    margin-right: 3%;
}

.error_mssg_form {
    text-align: center;
    color: red;
    font-size: 1.3em;
}

.text_area_conseil {
    border: solid 1px #4E86AF;
    font-weight: normal;
    width: 78%;
    padding: 2%;
    color: #4E86AF;
}

.td_conseil {
    text-align: center;
    vertical-align: middle;
}

.liste_offres_moment {
    background-color: #3D759D;
    clear: both;
}

.titre_offre_moment {
    color: #ffffff;
    text-align: center;
    padding-top: 11px;
    height: 10px;
    font-size: large;
}

.titre_bas_offre_moment {
    color: #ffffff;
    text-align: center;
    text-decoration: underline;
    height: 1px;
}

.flex_container {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    /*justify-content: center;*/
    justify-content: center;
    align-items: center;
    align-content: space-around;
    background-color: #3D759D;
}

.flex_container>.flex_case {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    /*justify-content: center;*/
    justify-content: center;
    flex-grow: 0;
    flex-shrink: 0;
    align-items: stretch;
    align-content: stretch;
    background-color: #f1f1f1;
    width: 23%;
    margin: 7px 5px 7px;
    /*margin: auto;*/
    text-align: center;
    height: 80px;
    font-size: 0.84em;
}

.flex_container>.flex_case>.offre_entreprise {
    margin: auto;
    background-color: #0e90d2;
    /*background-color: #6692C2;*/
    color: #ffffff;
    font-weight: bold;
    width: 100%;
    height: 30%;
    padding-top: 3%;
    padding-bottom: 2%;
    font-size: 1.07em;
}

.flex_container>.flex_case>.offre_intitule {
    margin: auto;
    font-weight: bold;
    width: 100%;
    height: 35%;
    padding-top: 3%;
    padding-bottom: 1%;
}

.flex_container>.flex_case>.offre_info {
    margin: auto;
    width: 100%;
    height: 35%;
    padding-bottom: 2%;
}

.box_cvtheque {
    float: right;
    position: relative;
    /*width:auto;*/
    /*height:250px;*/
    background-color: #3D759D;
    border: 1px solid black;
    margin: 20px 0px 32px 0px;
}

.box_cvtheque_theme {
    position: relative;
    float: right;
    /*width: 100%;
	height:100%;*/
    background-color: #3D759D;
    border: 1px solid black;
    margin: 20px 0px 32px 0px;
}

.box_cvtheque_theme>.text_container {
    position: absolute;
    width: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.4);
    color: #ffffff;
}

.box_cvtheque_theme .titre_cvtheque {
    color: #ffffff;
    font-weight: bold;
    font-size: 1.3em;
    text-align: center;
}

.box_cvtheque_theme hr {
    margin: 1% 10% 1% 10%;
    width: 80%;
    height: 1px;
    background-color: #ffffff;
    border: none;
    /*on supprime le style par défaut*/
}

.box_cvtheque_theme .text_cvtheque {
    color: #ffffff;
    /*font-weight: bold;*/
    font-size: 1em;
    text-align: center;
    padding: 0 15% 0 15%;
}

.box_cvtheque_theme .buttons {
    margin: 0 auto;
    width: 550px;
}

.box_cvtheque_theme .buttons img {
    vertical-align: -36%;
}

.box_cvtheque_theme .button_cvtheque {
    background-color: #0e90d2;
    font-weight: bold;
    border-radius: 6px;
    color: white;
    border: 0.8px solid black;
    padding: 7px 30px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 10px 4px;
    -webkit-transition-duration: 0.2s;
    /* Safari */
    transition-duration: 0.2s;
    cursor: pointer;
}

.box_cvtheque_theme .button_cvtheque:hover {
    background-color: #66acd0;
    font-weight: bold;
    color: white;
}

.box_cvtheque>.text_container {
    position: absolute;
    width: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.4);
    color: #ffffff;
}

.box_cvtheque .titre_cvtheque {
    color: #ffffff;
    font-weight: bold;
    font-size: 1.3em;
    text-align: center;
}

.box_cvtheque hr {
    margin: 1% 10% 1% 10%;
    width: 80%;
    height: 1px;
    background-color: #ffffff;
    border: none;
    /*on supprime le style par défaut*/
}

.box_cvtheque .text_cvtheque {
    color: #ffffff;
    /*font-weight: bold;*/
    font-size: 1em;
    text-align: center;
    padding: 0 15% 0 15%;
}

.box_cvtheque .buttons {
    margin: 0 auto;
    width: 550px;
}

.box_cvtheque .buttons img {
    vertical-align: -36%;
}

.box_cvtheque .button_cvtheque {
    background-color: #0e90d2;
    font-weight: bold;
    border-radius: 6px;
    color: white;
    border: 0.8px solid black;
    padding: 7px 30px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 10px 4px;
    -webkit-transition-duration: 0.2s;
    /* Safari */
    transition-duration: 0.2s;
    cursor: pointer;
}

.box_cvtheque .button_cvtheque:hover {
    background-color: #66acd0;
    font-weight: bold;
    color: white;
}

#ul1_ville {
    width: 18%;
    margin-right: 1%;
    margin-left: 1%;
}

#formationOffre {
    -webkit-border-radius: 13px 13px 13px 13px;
    -moz-border-radius: 13px 13px 13px 13px;
    border-radius: 13px 13px 13px 13px;
    background: #5F9EA0;
    padding: 2%;
    margin-top: 3%;
    text-align: center;
    color: white;
    font-size: 20px;
}

#arrow-down {
    width: 0;
    height: 5px;
    border-right: 16px solid transparent;
    border-left: 16px solid transparent;
    border-top: 20px solid #5F9EA0;
    margin-left: auto;
    margin-right: auto;
    margin-top: 2%;
    margin-bottom: -40px;
}

#box_continue {
    margin-top: 160px;
}

#formationOrga {
    background: #4B84AD;
    width: 12%;
    color: white;
    display: inline-block;
    padding: 6px 5px;
    width: 134px;
    text-align: center;
    color: #FFF;
    font-weight: bold;
    border-radius: 6px;
    margin-right: -2px;
    font-size: 1.4em;
    margin-bottom: 2%;
}

#ul1_departement {
    width: 24%;
    float: left;
    margin-left: 1%;
    margin-right: 0%;
}

.img-validate_cv {
    width: 2.5em;
    height: 2.5em;
}

#td_cv_result {
    vertical-align: middle;
    text-align: center;
}

#orange_button {
    background: linear-gradient(to top, #e65c00, #ff6600);
}

#alert_case {
    border: 1px solid #4ba8c5;
    margin-top: 2em;
    background-color: #4ba8c5;
    color: #F1F0F1;
    padding: 0.3em;
}

#fiche_postuler {
    margin-top: 1em;
}

#separateur_fiche {
    height: 2px;
    border-bottom: 1px solid #979797;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    padding: 0px;
    margin: 0px;
    /*width: 40%;
	margin-left: 38%;*/
    margin-bottom: 0.8em;
}

#form_simplified_cgu {
    border-bottom: 0px;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    padding: 0px;
    margin: 0px;
    /*width: 40%;
	margin-left: 38%;*/
    margin-bottom: 0.8em;
}

#a_form {
    color: white;
}

#error_date_cv_download {
    color: red;
    text-align: end;
    vertical-align: middle;
}

#b2_stat {
    background-color: orange;
    margin: 1px;
    border: solid 1px black;
    border-radius: 2px;
    padding: 1px;
}

#b1_stat {
    background-color: blue;
    color: white;
    border: solid 1px black;
    border-radius: 2px;
    margin: 1px;
    padding: 1px;
}

#b3_stat {
    background-color: yellow;
    margin-top: 3px;
    border: solid 1px black;
    border-radius: 2px;
    margin: 1px;
    padding: 1px;
}

#legende_stats {
    color: black;
    font-style: italic;
    font-weight: 0;
}

.img_explain {
    width: 100%;
    height: 100%;
}

.img_explain_metier {
    width: 100%;
}

.input_select_cv {
    width: 86%;
}

.label_input {
    margin-bottom: -0.25em;
    margin-top: 0.5em;
    border: 2px solid rgb(63, 114, 159);
    border-bottom: 0;
    border-radius: 5px;
    border-bottom-left-radius: 0%;
    border-bottom-right-radius: 0%;
    width: 74%;
    text-align: center;
    padding: 3px;
}

.label_input_cvtheque {
    border: 2px solid rgb(63, 114, 159);
    border-bottom: 0;
    border-radius: 5px;
    border-bottom-left-radius: 0%;
    border-bottom-right-radius: 0%;
    width: 63%;
    text-align: center;
    padding: 5px;
    color: rgb(90 150 189);
}

.invisible-scrollbar::-webkit-scrollbar {
    display: none;
}

.visible-scrollbar,
.invisible-scrollbar,
.mostly-customized-scrollbar {
    display: block;
    width: 10em;
    overflow: auto;
    height: 2em;
}

.label_input_text_mc {
    margin-bottom: -0.25em;
    margin-top: 0.5em;
    border: 2px solid rgb(63, 114, 159);
    border-bottom: 0;
    border-radius: 5px;
    border-bottom-left-radius: 0%;
    border-bottom-right-radius: 0%;
    width: 87.2%;
    text-align: center;
    padding: 3px;
}

.tr_label_input {
    text-align: -webkit-center;
    text-align: -moz-center;
}

.paraHcss {
    background-color: rgb(240, 240, 240);
    margin-top: -0.2%;
}

.input_border_cv {
    border-color: rgb(63, 114, 159);
}

.explain {
    background-color: rgb(240, 240, 240);
    border: solid 1px rgb(240, 240, 240);
    float: right;
    font-size: 78%;
    padding: 0.3em;
    text-align: right;
    border-radius: 1em;
    margin-right: 10.9%;
    margin-top: -0.25em;
}

.legend_image_cvtheque {
    border: 2px solid rgb(63, 114, 159);
    padding: 5%;
    margin: 5%;
}

.href_image_cvtheque {
    text-align: center;
    margin-top: 4%;
}

.newmess {
    background-color: #ebebeb;
    position: absolute;
    left: 0;
    right: 0;
    margin: 0px;
    width: 100%;
    background-size: cover;
    background-position: top;
    background-repeat: no-repeat;
    vertical-align: bottom;
    text-align: center;
    padding-top: 20px;
    padding-bottom: 20px;
    margin-top: 20px;
}

.href_refresh {
    color: #4B84AD;
    font-size: 16px;
    float: left;
    padding: 1em;
    font-weight: bold;
}

.title_h3_listing {
    color: rgb(63, 114, 159);
    font-size: 1.4em;
}

#title_h2_listing {
    font-size: 1.4em;
    color: rgb(63, 114, 159);
    margin: 30px 0 20px 0;
    padding: 0px 13px 10px 13px;
    border-bottom: 1px solid #c2c2c2;
}


/* #button_support_cv{
	border: 1px solid #333;
    margin: 0px;
    font-size: 1.2em;
    padding: 6px 35px;
    display: inline-block;
    cursor: pointer;
    font-weight: bold;
    transition: 0.2s;
    background-color: #447CA4;
    background: linear-gradient(to top,#4179A1,#447CA4,#5790BA);
    color: #FFF;
    border-radius: 8px;
} */

.href_cvtheque {
    color: #FFF;
}


/* css pour la page entreprise annuaire  */

#ul_entreprise_annuaire {
    width: 8%;
    float: left;
}

#ul_entreprise_annuaire1 {
    padding-inline-start: 18px;
    padding-inline-end: 18px;
    width: 0.001%;
    float: left;
}

.box_society .itembis {
    /*background-image:url(../images/imagesfond.jpg);*/
    float: left;
    width: 22%;
    height: 230px;
    overflow: hidden;
    margin: 0px 25px 20px 0px;
}

.box_society .fondbuilding .img_logobis {
    display: table-cell;
    vertical-align: middle;
    height: 100px;
    width: 110px;
    text-align: center;
    /*border: 1px solid #333;*/
    margin-bottom: 50px;
}

.box_society .intbis {
    height: 77px;
    padding-top: 3px;
    text-align: center;
    opacity: 0.8;
    background-color: black;
    font-size: 0.8em;
    color: white;
}

.box_society .intbis h4 {
    color: white;
    margin: 0px;
    padding: 0px;
    font-size: 1.1em;
    padding-right: 0.5em;
    padding-left: 0.5em;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}

.box_society .intbis h4:hover {
    color: white;
}

.box_society .itembis .fondbuilding {
    height: 150px;
    padding-top: 35px;
    padding-left: 48px;
    background-size: 100%;
}


/* Fin css page entreprise annuaire*/

.error_msg_off {
    /* background:url(../images/direct-emploi/alerte.gif) no-repeat top left; */
    padding: 5px 10px 7px 10px;
    margin-top: 10px;
    font-weight: bold;
    color: #FF3333;
    font-size: 14px;
}

#hr_custom {
    margin: 0px;
    padding: 0;
    font-family: arial;
    font-weight: unset;
    text-align: center;
    line-height: 1;
}

#td_off_custom {
    padding: 8px;
    width: 11%;
    text-align: center;
}

#mssg_vert_offre {
    text-align: center;
    color: #0aa20e;
    margin-top: 20px;
    font-weight: bold;
}

.liste_pub_offre_entreprise {
    margin-top: 15px;
    margin-bottom: 10px;
    /*On centre les element contenu aumilieu de la box, car il font toute la largeur de la fenetre*/
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.liste_pub_offre_entreprise>h3 {
    width: 100%;
    box-sizing: border-box;
    border-bottom: none;
    margin-bottom: 5px;
    margin-top: 0px;
}

.bande_couleur_entreprise {
    width: 100vw;
    display: flex;
    justify-content: center;
}

.bande_entreprise {
    width: 1005px;
    padding: 15px 0px 15px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}

.bande_entreprise>.espace_case {
    height: 100%;
    width: 2%;
}

.case_entreprise {
    height: 110px;
    width: 49%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}

.case_entreprise>img {
    height: 100%;
    width: 30%;
    border: 1px solid #000000;
    box-sizing: border-box;
}

.case_entreprise>.logo_image {
    height: 100%;
    width: 30%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}

.case_entreprise>.logo_image>img {
    height: 80%;
    width: 80%;
    border: 1px solid #000000;
    box-sizing: border-box;
}

.case_entreprise>.contenu {
    width: 70%;
    height: 100%;
    padding-left: 2%;
    box-sizing: border-box;
}

.case_entreprise>.contenu>h4 {
    display: block;
    text-align: center;
    border-bottom: 1px solid;
    margin-bottom: 0px;
    margin-top: 0px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.case_entreprise>.contenu>.offre {
    padding-top: 10px;
    padding-bottom: 5px;
}

.case_entreprise>.contenu>.offre>li {
    width: 100%;
    padding-left: 3%;
    box-sizing: border-box;
    list-style-type: none;
}

.case_entreprise>.contenu>.offre>li::before {
    content: "•";
}

.case_entreprise>.contenu>.offre>.voir_toutes_offres {
    margin-top: 5px;
    text-align: center;
}

.case_entreprise>.contenu>.offre>.voir_toutes_offres>a {
    display: inline-block;
    /*utiliser pour annuler la le display:block de la balise generale*/
}

#fieldset_entreprises {
    width: auto;
    height: auto;
}


/* Page Cadre*/

.bande_couleur_entreprise_cadre {
    width: 500px;
    display: flex;
    justify-content: right;
}

.bande_entreprise_cadre {
    width: 1005px;
    padding: 15px 0px 15px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}

.bande_entreprise_cadre>.espace_case {
    height: 100%;
    width: 2%;
}

.case_entreprise_cadre {
    height: 122px;
    width: 92%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}

.case_entreprise_cadre>img {
    height: 100%;
    width: 30%;
    border: 1px solid #000000;
    box-sizing: border-box;
}

.case_entreprise_cadre>.logo_image {
    height: 100%;
    width: 35%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}

.case_entreprise_cadre>.logo_image>img {
    height: 80%;
    width: 80%;
    border: 1px solid #000000;
    box-sizing: border-box;
}

.case_entreprise_cadre>.contenu {
    width: 65%;
    height: 100%;
    padding-left: 2%;
    box-sizing: border-box;
}

.case_entreprise_cadre>.contenu>h4 {
    display: block;
    text-align: center;
    border-bottom: 1px solid;
    margin-bottom: 0px;
    margin-top: 0px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.case_entreprise_cadre>.contenu>.offre {
    padding-top: 10px;
    padding-bottom: 5px;
}

.case_entreprise_cadre>.contenu>.offre>li {
    width: 100%;
    padding-left: 3%;
    box-sizing: border-box;
    list-style-type: none;
}

.case_entreprise_cadre>.contenu>.offre>li::before {
    content: "•";
}

.case_entreprise_cadre>.contenu>.offre>.voir_toutes_offres {
    margin-top: 5px;
    text-align: center;
}

.case_entreprise_cadre>.contenu>.offre>.voir_toutes_offres>a {
    display: inline-block;
    /*utiliser pour annuler la le display:block de la balise generale*/
}

#liste_pub_offre_entreprise_page_cadre {
    margin-bottom: 0px;
}

#table_cadre fieldset {
    margin-bottom: 0px;
    height: 938px;
    /*On fixe la hauteur pour corresondre à celle de la partie de droite(attention margin-top: 15px; a prendre en compte). Les 2 colonnes doivent etre aligner horizontalement*/
    box-sizing: border-box;
    width: 90%;
}

#table_rb fieldset {
    margin-bottom: 0px;
    height: 100%;
    /*On fixe la hauteur pour corresondre à celle de la partie de droite(attention margin-top: 15px; a prendre en compte). Les 2 colonnes doivent etre aligner horizontalement*/
    box-sizing: border-box;
    width: 90%;
}

.metier_cadre {
    height: 36px;
}

#li_cadre {
    list-style: inside;
    background: none;
    padding: 0px;
    margin: 0px;
}

#DE_container_rech_cadre {
    margin: 0px;
    width: 100%;
    height: 240px;
    background-size: cover;
    background-position: top;
    background-repeat: no-repeat;
    vertical-align: bottom;
}

#DE_container_rech_cadre_boite {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: top;
    background-repeat: no-repeat;
    vertical-align: bottom;
    border: 1px solid #333;
}

#DE_container_rech_cadre_boite .inside {
    width: 100%;
}

.Carriere_container_metier,
.Carriere_container_metier_de {
    height: 240px;
    width: 100%;
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}

.Carriere_container_metier .lien_cadre,
.Carriere_container_metier .pave_fiche_metier,
.Carriere_container_metier_de .lien_cadre,
.Carriere_container_metier_de .pave_fiche_metier,
.pave_fiche_metier {
    width: 32%;
    height: 100%;
}

#DE_container_rech_cadre_boite {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: top;
    background-repeat: no-repeat;
    vertical-align: bottom;
}

.espace_Carriere_container_metier {
    width: 2%;
}

#DE_box_rech_cadre {
    background: rgba(0, 0, 0, 0.5);
    padding: 13px 0px;
    margin-bottom: 15px;
    position: relative;
    width: 100%;
    left: 0px;
    height: 57px;
    padding: 13px 0px;
    z-index: 200;
    bottom: -70%;
    margin-bottom: 10px;
}

#DE_container_rech_cadre_boite .inside {
    width: 100%;
}

.slideshow,
.slideshowInterim,
.slideshowEntreprise {
    width: 370px;
    height: 520px;
    overflow: hidden;
    border: 3px solid #F2F2F2;
}

.slideshow ul,
.slideshowInterim ul,
.slideshowEntreprise ul {
    /* 4 images donc 4 x 100% */
    width: 1700%;
    height: 520px;
    padding: 0;
    margin: 0;
    list-style: none;
}

.slideshow li,
.slideshowInterim li,
.slideshowEntreprise li {
    float: left;
}

.Carriere_container_metier,
.Carriere_container_metier_de {
    height: 240px;
    width: 100%;
    margin-top: 10px;
}

.lien_cadre:hover #DE_box_rech_cadre {
    background: rgba(0, 0, 0, 0.8);
}

#Carriere_container_fiche_metier {
    background-image: url("/images/carriere_btp/image_page_cadre.png");
    position: absolute;
    left: 0;
    right: 0;
    margin: 0px;
    width: 100%;
    height: 240px;
    background-size: cover;
    background-position: top;
    background-repeat: no-repeat;
    vertical-align: bottom;
}

#Carriere_container_fiche_metier_after {
    display: block;
    content: "";
    position: relative;
    height: 250px;
    margin-bottom: 20px;
    background-size: cover;
    background-position: top;
    background-repeat: no-repeat;
    background-color: #f9ae81;
    /* couleur par default si l'image ne s'affihche pas*/
}

fieldset ul #li_cadre a:hover {
    color: #F75A09;
}

#DE_container_cadre_boite {
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    background-position: top;
    background-repeat: no-repeat;
    vertical-align: bottom;
    border: 1px solid #333;
}

#liste_metiers_cadre {
    font-family: "Open sans";
}

.image_dossier1_cadre img {
    height: 203px;
}

.image_dossier1_cadre_interim img {
    height: 83px;
}


/* FIN Page Cadre*/


/* Page Fiche Metier*/

.zone_pave_fiche_metier {
    height: 240px;
    width: 100%;
}

#lien_liste_metier:hover #DE_box_rech_cadre,
.fiche_metier_lien_autre_fiche_metier:hover #DE_box_rech_cadre {
    background: rgba(0, 0, 0, 0.6);
}

#DE_h3_title_pave_page_cadre {
    padding: 0px 0px 5px 0px;
    font-size: 1.5em;
    font-weight: normal;
    text-align: center;
    color: white;
}

#accueil_DE_h3_title_liste_metier {
    font-size: 2em;
    font-weight: normal;
    text-align: center;
    color: white;
    width: 70%;
}

.texte_fiche_metier h3 {
    font-size: 16px;
}

.td-250-px {
    width: 250px;
    height: 100px;
}

#DE_h2_title_fiche_metier {
    padding: 0px 15px 5px 0px;
    font-size: 2em;
    font-weight: normal;
    text-align: center;
}

.fiche_metier_lien_autre_fiche_metier {
    width: 32%;
    height: 240px;
}

.zone_lien_fiche_metier {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
}

.espace_fiche_metier_lien_autre_fiche_metier {
    width: 1%;
}

.box_fiche_metier {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.box_titre_fiche_metier {
    font-size: 20px;
}

.accueil_teinte_noir_fiche_metier {
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.2);
}


/* FIN Page Fiche Metier*/


/*CSS cartes interactives*/

.pave_carte_interractive {
    width: 32%;
    height: 100%;
    background-image: url('../images/all/img_fond_carte_france.jpg');
    background-size: cover;
    background-position: center;
    border: 1px solid #333;
}

.pave_carte_interractive_de {
    width: 34.35em;
    /* width: 50%;
    height: 50%; */
    background-image: url('../images/all/image_fond_carte_france.jpg');
    background-size: cover;
    background-position: center;
    border: 1px solid #333;
    margin-top: 3.5%;
}

.pave_carte_region {
    width: 33.4em;
    /* width: 50%;
    height: 50%; */
    background-size: cover;
    background-position: center;
    border: 1px solid #333;
    margin-top: 3.5%;
}

#carte_interractive_France {
    width: 100%;
    height: 100%;
    /* display:flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;  */
}

#carte_interractive_France_de {
    width: 100%;
    height: 457px;
    display: flex;
    flex-direction: column;
}

#carte_interractive_France_de svg {
    height: 130%;
    width: 83.7%;
    left: 7.5%;
    position: relative;
    margin-top: 8%;
    margin-bottom: -1%;
}

#carte_interractive_France .titre_carte_droite,
#carte_interractive_France .titre_carte_gauche {
    font-size: 15px;
    font-weight: bold;
    color: white;
    position: relative;
    z-index: 100;
}

#carte_interractive_France .titre_carte_droite {
    top: -11.5%;
    left: 15%;
}

#carte_interractive_France .titre_carte_gauche {
    top: -96.5%;
    left: 1%;
}

#carte_interractive_France_de .titre_carte_droite,
#carte_interractive_France_de .titre_carte_gauche {
    font-size: 20px;
    font-weight: bold;
    color: white;
    position: relative;
    z-index: 100;
}

#carte_interractive_France_de .titre_carte_droite {
    margin-bottom: 25px;
    margin-left: 315px;
}

#carte_interractive_France_de .titre_carte_gauche {
    margin-top: 25px;
    margin-left: 30px;
    position: absolute;
}

#titre_bandeau_haut,
#titre_bandeau_bas {
    width: 100%;
    color: white;
    text-align: center;
    vertical-align: middle;
    font-weight: bold;
    font-size: 17px;
}

#carte_interractive_France svg {
    height: 240px;
    width: 285px;
    left: 3%;
    position: relative;
}

#carte_interractive_France svg g {
    height: 280px;
}

#carte_interractive_departement {
    margin-top: 20px;
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    text-align: center;
}

#carte_interractive_departement svg {
    height: 100%;
    max-width: 1005px;
}

#carte_interractive_France path,
#carte_interractive_France_de path,
#carte_interractive_departement path {
    fill: #f66409;
    /*valeur par default*/
    fill-opacity: 1;
    stroke: white;
    stroke-opacity: 1;
    stroke-width: 1;
}

#carte_interractive_France a:hover path,
#carte_interractive_departement a:hover path {
    fill: #b74803;
    fill-opacity: 1;
}

#carte_interractive_France text,
#carte_interractive_departement text {
    fill: white;
    font-size: 10px;
}

.balise_centrage_bande_carte_dep {
    margin-top: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

#carte_interractive_France_departement_bordure {
    width: 1011px;
    height: 500px;
    border-top: 5px solid #f66309;
    border-bottom: 5px solid #f66309;
    position: absolute;
    z-index: -1;
}

#carte_interractive_France_departement_image {
    width: 1011px;
    height: 500px;
    background-color: white;
    background-size: cover;
    background-position: center;
    filter: blur(2px);
    position: absolute;
    z-index: -1;
}

.balise_centrage_image_carte_interractive {
    /* div utiliser pour recentrer l'image (position absolue), necessaire sur certains navigateur : internet explorer*/
    width: 1011px;
}

#balise_reajustement_width_normaliser_carte_dep text {
    fill: white;
    font-size: 7px;
}

.ligne_enreprise_carte_dep {
    height: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}

.logo_carte_dep {
    height: 100%;
    width: 50%;
    padding: 5px;
    box-sizing: border-box;
}

.logo_carte_dep img {
    height: inherit;
    width: 100%;
    border: 1px solid black;
}

.logo_carte_dep a {
    height: inherit;
    width: 100%;
}

.entreprises_carte_departement {
    width: 50%;
    height: 275px;
    float: right;
    padding-left: 3%;
    box-sizing: border-box;
}

.zone_actu_entreprise_carte_dep {
    width: 100%;
    margin-top: 40px;
    height: 275px;
}

.balise_reajustement_width_normaliser_carte_dep {
    width: 1005px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 5px;
    margin-bottom: 5px;
}

#balise_reajustement_width_normaliser_carte_dep .zone_lien_toute_offre_carte_interractive {
    padding-bottom: 10px;
    padding-top: 20px;
    text-align: center;
}

.zone_lien_toute_offre_carte_interractive a {
    background-color: #0e90d2;
    font-weight: bold;
    border-radius: 6px;
    color: white;
    border: 0.8px solid black;
    padding: 10px 40px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    -webkit-transition-duration: 0.2s;
    /* Safari */
    transition-duration: 0.2s;
    cursor: pointer;
}

.zone_lien_toute_offre_carte_interractive a:hover {
    background-color: #66acd0;
    font-weight: bold;
    color: white;
}


/*FIN CSS cartes interactives*/


/*CSS onglet conseils et espace candidat */

/* .conseils_espace h2 {
    font-size: 25px;
    color: #292929;
    margin-top: 50px;
    color: #292929;
    text-align: center;
    font-weight: normal;
} */

.conseils_contener {
    height: 289px;
    margin-left: auto;
    margin-right: auto;
    border: 0px solid black;
    border-radius: 11px;
}

.conseils_contener table {
    border-spacing: 9px 0px;
}

#ul_alert_mail {
    width: 28%;
    display: inline-block;
    text-align: left;
    vertical-align: top;
    list-style: none;
}


/*fin css conseils*/


/*css module espace Recruteur pour gestion des candidatures*/

.recruteur_candidatures .tab_recruteur_candidature .recrutG {
    width: 17%;
    padding-right: 20px;
    vertical-align: top;
    padding-top: 75px;
    display: inline-block;
    zoom: 1;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutG .item {
    border: 1px solid #000;
    border-radius: 10px;
    margin: 10px 0px;
    padding: 0px;
    background: #3F77A1;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutG .item.active {
    background: #5C98BE;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutG .item h2 {
    border: none;
    margin: 0px;
    padding: 0px;
    text-align: center;
    padding: 5px 0px;
    font-size: 1.2em;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutG .item a {
    color: white;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutD {
    width: 80%;
    padding: 0px;
    display: inline-block;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutD table {
    width: 100%;
    border-collapse: collapse;
    border: thin solid #000;
    text-align: center;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutD table thead tr {
    height: 40px;
    border: thin solid #000;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutD table thead tr td {
    font-weight: bold;
    font-size: 17px;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutD table tbody tr {
    height: 40px;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutD table .couleur1 {
    background: #EAEAEA;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutD table .couleur2 {
    background: #FFFFFF;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutD table td form {
    display: inline-block;
}

.recruteur_candidatures .tab_recruteur_candidature .recrutD table td form input {
    width: 30px;
    height: 30px;
    padding: 0px;
    border: none;
    cursor: pointer;
}

.recruteur_candidatures .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
}

.recruteur_candidatures .modal .modal_wrapper {
    position: absolute;
    top: 5%;
    left: 25%;
    background: #FFF;
    width: 50%;
    height: 90%;
    margin: auto;
    overflow: auto;
    display: flex;
    flex-direction: column;
}

.recruteur_candidatures .modal .modal_wrapper .bouton_fermer_modal {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    padding: 2%;
}

.recruteur_candidatures .modal .modal_wrapper h1 {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
}

.recruteur_candidatures .modal .modal_wrapper iframe {
    width: 100%;
    height: 100%;
    border-width: 0;
}


/*fin css espace Recruteur gestion des candidatures*/


/* NOUVELLE BANNIERE CANDIDAT */

div.box_rectangle {
    float: right;
    width: 680px;
    height: auto;
    margin-top: 20px;
}

div.rectangle_bas {
    width: auto;
    height: 200px;
}

div.rectangle_bas_forma {
    width: auto;
    height: 211px;
    padding-top: 12px;
}

div.rectangle_haut_forma {
    width: auto;
    display: flex;
    flex-direction: row;
    text-align: center;
    height: 52px;
    border-bottom: none;
}

div.rectangle_haut {
    width: auto;
    display: flex;
    flex-direction: row;
    text-align: center;
    height: 52px;
    border-bottom: solid white;
}

p.texte_candidat {
    color: white;
    text-align: center;
    font-size: 17px;
    padding-top: 0px;
    padding-top: 10px;
    padding-left: 30px;
    padding-right: 30px;
}

div.separation {
    margin-left: auto;
    margin-right: auto;
    border-top: 3px solid white;
    width: 600px;
    height: 1px;
}


/*fin*/

.td_table_admin_logo {
    text-align: center;
    border: 2px dashed White;
}

.img_logo_url {
    margin-top: 12px;
    width: 150px;
    max-width: 150px;
    height: 100px;
    border-style: solid;
    border-width: 1px;
    border-color: black;
}


/* css page "recherche avancée" */

.domaines_recherche_avancee {
    width: 41%;
    display: inline-block;
    margin-left: 6%;
}

.domaines_recherche_avancee_carriere {
    width: 76%;
    display: inline-block;
    margin-left: 12%;
}


/*fin*/

@media (min-width: 640px) {
    div.encadrement_ordi {
        display: flex;
        flex-direction: row;
        height: 90px;
    }

    div.encadrement_ordi .encadre_boutons_conseils {
        width: 100%;
        padding: 0px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        height: 90px;
        margin-top: 0.5%;
        margin-left: 1%;
    }

    div.encadrement_ordi .encadre_boutons_conseils a {
        width: 31.5%;
        height: 42.5%;
        padding: 0px;
        margin-left: 1%;
        display: grid;
        align-items: center;
    }

    div.encadrement_mobile {
        display: none;
    }

    .texte_et_bouton_inscription {
        display: flex;
        flex-direction: row;
        padding-top: 1%;
    }

    .texte_et_bouton_inscription .texte_candidat_gauche {
        width: 30%;
    }

    .texte_et_bouton_inscription .texte_candidat_gauche .texte_candidat {
        margin: 0px;
        margin-left: 15%;
    }

    .texte_et_bouton_inscription .texte_candidat_droite {
        width: 30%;
    }

    .texte_et_bouton_inscription .texte_candidat_droite .texte_candidat {
        margin: 0px;
        margin-right: 15%;
    }

    .texte_et_bouton_inscription .encadre_inscription {
        width: 40%;
        align-self: center;
    }

    .texte_et_bouton_inscription div .texte_candidat {
        padding: 0px;
        margin-bottom: 0px;
    }

    p.title_candidat {
        width: 100%;
        color: white;
        text-align: center;
        vertical-align: middle;
        font-weight: bold;
        font-size: 17px;
    }

    .title_formation {
        width: 100%;
        color: white;
        text-align: center;
        vertical-align: middle;
        font-weight: bold;
        font-size: 17px;
        padding-top: 7px;
    }

    .bouton_forma {
        background-color: orange;
        border-radius: 7px;
        padding-top: 12px;
        padding-bottom: 12px;
        width: 83%;
        text-align: center;
        font-size: 15px;
        font-weight: bold;
        color: white;
        margin: 0 auto 9px auto
    }

    .bouton_forma>a {
        color: white;
    }

    p.texte_conseils {
        text-align: center;
        padding: 0px;
        margin: 0px;
        font-size: 17px;
        color: white;
        font-weight: bold;
        margin-bottom: 5px;
    }

    a.bouton_inscription {
        background-color: orange;
        border-radius: 7px;
        color: white;
        padding-top: 15px;
        padding-bottom: 15px;
        padding-left: 50px;
        padding-right: 60px;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    a.bouton_inscription:hover {
        background-color: #ff8800;
    }

    div.encadre_inscription {
        text-align: center;
        margin: 0px;
        padding: 0px;
    }

    .symbole_inscription {
        margin-top: 10px;
        margin-bottom: -6px;
        margin-right: 10px;
    }

    a.bouton_entretien,
    a.bouton_lm,
    a.bouton_cv,
    a.bouton_recherche,
    a.bouton_metiers,
    a.bouton_domaines {
        background-color: orange;
        text-align: center;
        vertical-align: center;
        border-radius: 7px;
        color: white;
        padding-top: 8px;
        padding-bottom: 8px;
        padding-left: 20px;
        padding-right: 20px;
        font-size: 16px;
        font-weight: bold;
    }

    a.bouton_entretien:hover,
    a.bouton_lm:hover,
    a.bouton_cv:hover,
    a.bouton_recherche:hover,
    a.bouton_metiers:hover,
    a.bouton_domaines:hover {
        background-color: #ff8800;
    }

    div .carte_container {
        display: flex;
    }
}

@media (max-width: 640px) {
    div.encadrement_ordi {
        display: none;
    }

    div.encadrement_mobile {
        display: flex;
        flex-direction: column;
    }

    p.title_candidat {
        color: white;
        text-align: center;
        padding-top: 19px;
        padding-bottom: 15px;
        font-weight: bold;
        font-size: 17px;
    }

    div.espace_ordi {
        display: none;
    }

    div.rectangle_bas {
        margin-top: -17px;
        width: 100%;
        height: auto;
        max-width: 639px;
    }

    div.rectangle_haut {
        width: 100%;
        height: auto;
        max-width: 639px;
    }

    div.box_rectangle {
        width: 100%;
        max-width: 639px;
    }

    div.separation {
        margin-left: auto;
        margin-right: auto;
        border-top: 2px solid white;
        width: 100%;
        height: 1px;
    }

    p.texte_conseils {
        font-size: 17px;
        color: white;
        float: initial;
        padding-left: 21px;
        padding-top: 5px;
        font-weight: bold;
        text-align: center;
    }

    div.encadre_boutons_conseils {
        text-align: center;
        display: flex;
        flex-direction: column;
    }

    div.encadre_inscription {
        text-align: center;
        margin-left: 30px;
        margin-right: 30px;
        padding-bottom: 10px;
        margin-bottom: 25px;
    }

    a.bouton_inscription {
        background-color: orange;
        border-radius: 7px;
        color: white;
        padding-top: 15px;
        padding-bottom: 15px;
        padding-left: 50px;
        padding-right: 60px;
        font-size: 16px;
        font-weight: bold;
    }

    .symbole_inscription {
        margin-top: 10px;
        margin-bottom: -6px;
        margin-right: 10px;
    }

    a.bouton_entretien,
    a.bouton_lm,
    a.bouton_cv,
    a.bouton_recherche,
    a.bouton_metiers,
    a.bouton_domaines {
        margin-bottom: 5px;
        margin-top: 5px;
        background-color: orange;
        text-align: center;
        vertical-align: center;
        border-radius: 7px;
        color: white;
        padding-top: 8px;
        padding-bottom: 8px;
        padding-left: 20px;
        padding-right: 20px;
        font-size: 16px;
        font-weight: bold;
    }

    div.encadre_boutons_conseils {
        text-align: center;
        padding-top: 5px;
        padding-bottom: 5px;
    }

    div .carte_container {
        display: inline-block;
        margin-top: 3.5%;
    }

    div .pave_carte_interractive_de {
        /* width: 100%; */
        display: flex;
    }

    div #carte_interractive_France_de .titre_carte_gauche {
        margin-top: 25px;
        margin-left: 25px;
        position: absolute;
    }

    div #carte_interractive_France_de .titre_carte_droite {
        margin-top: auto;
        margin-left: auto;
        margin-right: 25px;
    }
}


/* cacher 'plan du site' sur mobiles */

@media (max-width: 640px) {
    div.plan_footer {
        display: none;
    }
}

.annuinterimtrup {
    border-top: solid 1px grey;
    border-left: solid 1px grey;
    border-right: solid 1px grey;
    margin-top: 2px;
}

.annuinterimtrdown {
    border-left: solid 1px grey;
    border-right: solid 1px grey;
    border-bottom: solid 1px grey;
}

.annuinterimtrmiddle {
    border: solid 1px grey;
    border-left: solid 1px grey;
    border-right: solid 1px grey;
}

#annu_interim_mss_success {
    color: green;
}

#abon_table {
    margin-top: 10px;
    text-align: center;
    border-collapse: collapse;
    width: 100%;
}

#abon_table tr:nth-child(even) {
    background-color: #f2f2f2;
}

#abon_table tr:hover {
    background-color: #ddd;
}

#abon_table th {
    padding-top: 12px;
    padding-bottom: 12px;
    background-color: #0e90d2;
    color: white;
}

#search_cp_table,
{
    margin-top: 10px;
    text-align: center;
    border-collapse: collapse;
    width: 100%;
}

#search_cp_table td,
#search_cp_tr th {
    border: solid 1px #0e90d2;
}

#search_cp_table tr:nth-child(even) {
    background-color: #f2f2f2;
}

#search_cp_table tr:hover {
    background-color: #ddd;
}

#search_cp_table th {
    padding-top: 12px;
    padding-bottom: 12px;
    background-color: #0e90d2;
    color: white;
}

#search_cp_mssg_red {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: center;
    color: red;
}


/*css module conseils*/

.title {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 28px;
    padding-left: 425px;
    padding-top: 25px;
    padding-right: 25px;
    color: #252525;
    font-weight: bold;
}

.desc {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 18px;
    color: #575757;
    font-weight: normal;
    padding-top: 20px;
}

.border_table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 25px;
}

.border_th {
    border: 1px rgb(177, 177, 177) solid;
    text-align: left;
    background: #ffffff;
}

.border_th img {
    width: 389px;
    height: 259px;
    float: left;
    margin-top: -1px;
    margin-bottom: -1px;
    margin-left: -1px;
}

.contener_encadre_boutons_conseils {
    background-color: #098fd5;
    width: 700px;
    height: 60px;
    margin-left: auto;
    margin-right: auto;
    border: 0px solid black;
    border-radius: 12px;
}


/*fin responsive module conseils*/

.main_title {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 20px;
    padding-left: 125px;
    padding-top: 25px;
    padding-right: 25px;
    color: #252525;
    font-weight: bold;
}

.border_table_list {
    width: 100%;
    border-collapse: collapse;
    font-family: Arial, Helvetica, sans-serif;
}

.nb_list {
    width: 20px;
    height: 30px;
    border: 1px rgb(182, 182, 182) solid;
    text-align: center;
    background: #ffffff;
    font-size: 18px;
    font-weight: lighter;
}

.nom_de_enreprise {
    width: 300px;
    height: 30px;
    border: 1px rgb(182, 182, 182) solid;
    text-align: center;
    background: #ffffff;
    font-size: 18px;
    font-weight: lighter;
}

.offre_list_crawl {
    width: 150px;
    height: 30px;
    border: 1px rgb(182, 182, 182) solid;
    text-align: center;
    background: #ffffff;
    font-size: 18px;
    font-weight: lighter;
}

.id_user {
    width: 200px;
    height: 30px;
    border: 1px rgb(182, 182, 182) solid;
    text-align: center;
    background: #ffffff;
    font-size: 18px;
    font-weight: 500;
}

.title_conseil {
    color: white;
    font-size: 19px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 12px;
}



.texte_conseil {
    font-size: 17px;
    font-weight: normal;
    color: #eeeeee;
    padding-bottom: 10px;
}

.depot_cv {
    background: #ffffff;
}

.checkbox_size {
    width: 18px;
    height: 18px;
}

.multiselect {
    width: 397px;
}

.multiselect_mail {}

.selectBox {
    position: relative;
}

.selectBox select {
    width: 66%;
}

.overSelect {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}

#checkboxes {
    display: none;
    border: 1px #333333 solid;
    background-color: #FFFFFF;
    width: 397px;
    padding-left: 3px;
    padding-top: 2px;
    padding-bottom: 2px;
}

#checkboxes_mail {
    display: none;
    border: 1px #333333 solid;
    background-color: #FFFFFF;
    width: 66%;
    text-align: left;
    padding-left: 3px;
    padding-top: 2px;
    padding-bottom: 2px;
    margin-left: auto;
    margin-right: auto;
}

#checkboxes_mail2 {
    display: none;
    border: 1px #333333 solid;
    background-color: #FFFFFF;
    width: 66%;
    text-align: left;
    padding-left: 3px;
    padding-top: 2px;
    padding-bottom: 2px;
    margin-left: auto;
    margin-right: auto;
}

#checkboxes label {
    display: block;
    padding-left: 3px;
    font-weight: normal;
}

#checkboxes_mail label {
    display: block;
    padding-left: 3px;
    font-weight: normal;
}

#checkboxes_mail2 label {
    display: block;
    padding-left: 3px;
    font-weight: normal;
}

#checkboxes label:hover {
    background-color: #1e90ff;
}

#entreprises {
    width: 1030px;
}

#annuaireInc {
    width: 1100px;
}

.margin_zoom_sectoriel {
    margin-top: -38px;
}

.cv_portable {
    display: none;
}

.cv_ordinateur {
    display: block;
}

.listazh1 {
    color: #4BA8C5;
    font-size: 3.1em;
    border-bottom: 0;
    font-family: unset;
    text-align: center;
    margin-top: 10px;
}

.listazh2 {
    color: #0b0b0b;
    font-size: 1.6em;
    border-bottom: 0;
    text-align: center;
    font-weight: lighter;
    margin: unset;
}

.listazalpha {
    border-top: 1px solid #c2c2c2;
    margin-top: 20px;
}

.listazth {
    border-right: 3px solid #fcb842;
}

.listaztr {
    height: 25px;
}

.listaztrtop {
    height: 15px;
}

.listazbottom {
    border-top: 1px solid #c2c2c2;
    margin-top: 20px;
}

.listazbottomth {
    border-right: 3px solid #fcb842;
}

.listazbottomtr {
    height: 5px;
}

.listazbottomtrtop {
    height: 5px;
}

.conseils_contener_bottom {
    margin-top: 35px;
    height: 130px;
    margin-left: auto;
    margin-right: auto;
    border: 0px solid black;
    border-radius: 11px;
    text-align: -webkit-center;
    text-align: -moz-center;
}

.conseils_contener_bottom table {
    border-spacing: 9px 0px;
}

.title_conseil_bottom {
    color: white;
    font-size: 15px;
    font-weight: bold;
    text-align: center;
}

.conseils_contener {
    display: block;
}

.conseils_contener_responsive {
    display: none;
}

.conseils_contener_bottom {
    display: block;
}

.conseils_contener_bottom_responsive {
    display: none;
}


/*Slider Caroussel */

.hidden {
    display: none;
}

.slider-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.slider-container>div {
    display: inline-block;
}

#slideshow,
#slideshowInterim,
#slideshowEntreprise {
    height: 520px;
}

.arrowL {
    cursor: pointer;
    color: black;
    font-size: 2em;
}

.arrowR {
    cursor: pointer;
    color: black;
    font-size: 2em;
}

#slideshows {
    margin: 0 auto;
    position: relative;
    width: 100%;
    padding: 1% 1% 56.25% 1%;
    border-radius: 20px;
    box-sizing: border-box;
}

#slideshows>div {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
}

#slideshows:hover a,
#slideshows:hover .slider-dots {
    opacity: 1;
}

.slideitem {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    width: 100%;
}

.slideitem div {
    background-color: #fff;
    box-shadow: 0 15px 35px #3232321a, 0 5px 15px #0000001a;
    transition: transform 0.3s;
    transform: translate3d(0, -1px, 0);
}

.slideitem div:hover {
    transform: translate3d(0, -10px, 0);
}

.slidebtn {
    z-index: 99;
    background: transparent;
    outline: none;
    border: none;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
    padding: 0 10px 0 10px;
}

.slidebtn:active,
.slidedtn:focus {
    outline: none;
}

.slidebtn a {
    color: #000;
    font-size: 72px;
    opacity: 0.2;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.prev .prevInterim .prevEntreprise {
    position: absolute;
    top: 10px;
    left: 10px;
    bottom: 10px;
}

.next .nextInterim .nextEntreprise {
    position: absolute;
    top: 10px;
    right: 10px;
    bottom: 10px;
}

.slider-dots {
    opacity: 0.2;
    list-style: none;
    display: inline-block;
    padding-left: 0;
    margin-bottom: 0;
    position: absolute;
    left: 50%;
    bottom: 3%;
    transform: translate(-50%, 0);
    z-index: 99;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}

.slider-dots li {
    color: #000;
    display: inline;
    font-size: 48px;
    margin-right: 5px;
    cursor: pointer;
}

.slider-dots li.active-dot {
    color: #fff;
}

/* .alert {
    padding: 20px;
    background-color: #f44336;
    /* Red */
/* color: white; */
/* margin-bottom: 15px; */
/* }  */

.alert.green {
    background-color: green;
}

#btn_submit {
    border: 1px solid #333;
    margin: 0px;
    font-size: 1.2em;
    padding: 6px 35px;
    display: inline-block;
    cursor: pointer;
    font-weight: bold;
    transition: 0.2s;
    background-color: #447CA4;
    background: linear-gradient(to top, #4179A1, #447CA4, #5790BA);
    color: #FFF;
    border-radius: 8px;
}

.button_insc_cand {
    border: 1px solid #333;
    margin: 0px;
    font-size: 1.2em;
    padding: 6px 35px;
    display: inline-block;
    cursor: pointer;
    font-weight: bold;
    transition: 0.2s;
    background-color: #447CA4;
    background: linear-gradient(to top, #4179A1, #447CA4, #5790BA);
    color: #FFF;
    border-radius: 8px;
}