@charset "utf-8";

/* CSS Document */

a,
.affich_nombre {
    color: #4BA8C5;
}

h4.titreentr a {
    color: white;
}


/* OFFRES PROPOSEES */

.page_offres_propose {
    font-family: Arial, Helvetica, sans-serif;
}

.present .text {
    margin-bottom: 7px;
}

.present h3 {
    background: url("../../images/direct-emploi/page-offre-propo/puce-bleu.gif") no-repeat scroll left center transparent;
    list-style-type: none;
    padding: 0px 0px 0px 15px;
    margin: 6px 0px 3px 0px;
    border: 0px;
    font-size: 1em;
}

.divers .img {
    float: right;
    text-align: center;
}

.divers .img img {
    border: 1px solid #333;
    margin-left: 8px;
}

.box_packs_emploi .img img,
.box_emploi_spe .img img {
    border: 0px;
}

.box_packs_emploi .img img {
    border: 0px;
    padding-top: 23px;
}

.ulphrase {
    text-align: center;
    maring: 0px;
    padding: 0px 15px;
}

.ulphrase li {
    font-weight: bold;
    font-size: 1.2em;
}

.divers .textes {
    float: left;
    width: 45%;
}

.divers .box_formats_com .textes {
    width: 35%;
    margin-left: 35px;
}

ul.liste_vignettes {
    list-style: none;
    padding: 0px;
}

ul.liste_vignettes li {
    padding-bottom: 10px;
    font-weight: bold;
}

ul.liste_vignettes li span {
    background-color: #56db52;
    padding: 1px 6px;
    color: #FFF;
    font-size: 17px;
    margin-right: 5px;
}

.titre_couplage {
    background-color: #EEE;
    padding: 7px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
}

.info_mail {
    font-size: 1.1em;
    font-weight: bold;
    text-align: center;
    padding: 10px 15px;
    margin-top: 25px;
}

.page_offres_propose {}

.page_offres_propose .info_mail {
    font-size: 1.1em;
    font-weight: bold;
    text-align: center;
    padding: 13px 15px;
    margin-top: 35px;
}

.info_mail a {
    text-decoration: underline;
}

.box_reseau table {
    width: auto;
}

.box_reseau td {
    padding: 8px;
}

.box_reseau td.autres_sites {
    width: 370px;
}

.box_reseau td.autres_sites a {
    display: inline-block;
    padding: 2px 8px;
}

.box_reseau td a:hover img {
    display: block;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.3);
    transition: 0.2s;
}

.box_reseau td.sites_partenaire {
    width: 200px;
}

.box_reseau td.sites_partenaire img {
    width: 140px;
}

.box_reseau td.sites_partenaire a {
    display: inline-block;
    padding: 2px 1px;
}

.page_offres_propose h2 {
    color: rgb(77, 137, 177);
}

.boxOffre h2 {
    margin-top: 10px;
}

.boxOffre {
    width: 46%;
}

.box_reseau .autres_sites a {
    float: left;
}

.boxOffre.boxG .img:after {
    display: block;
    content: "";
    visibility: visible;
    position: absolute;
    height: 275px;
    width: 1px;
    background: #ccc;
    left: 50%;
    margin-top: -246px;
}

.boxOffre.boxG .img {
    text-align: center;
}

.boxOffre.boxD .img {
    height: 203px;
    padding-top: 20px;
}

.boxOffre.boxG .img img {
    height: 218px;
    width: auto;
}

.boxOffre .img img:first-child {
    margin-right: 10px;
}

.boxOffre .text {
    margin: 10px 20px 25px 20px;
    font-weight: bold;
    text-align: center;
}

.ligne_contact {
    text-align: center;
    margin-top: 25px;
}

.page_offres_propose {
    position: relative;
}

.ligne_contact:before {
    display: block;
    content: "";
    visibility: visible;
    position: absolute;
    height: 1px;
    width: 100%;
    background: #ccc;
    left: 0px;
    margin-top: 25px;
    z-index: 2;
}

.ligne_contact span {
    display: inline-block;
    margin: auto;
    background: #FFF;
    padding: 10px 20px;
    position: relative;
    z-index: 20;
}

a.offre_info {
    position: relative;
    text-decoration: none;
}

a.offre_info,
.pack h3 a.offre_info span {
    font-size: 14px;
}

a.offre_info span {
    display: none;
    /* On masque l'infobulle. */
}

a.offre_info:hover {
    background: none;
    /* Correction d'un bug d'Internet Explorer. */
    z-index: 500;
    /* On définit une valeur pour l'ordre d'affichage. */
    cursor: help;
    /* On change le curseur par défaut par un curseur d'aide. */
}

a.offre_info:hover span {
    display: inline;
    /* On affiche l'infobulle. */
    position: absolute;
    white-space: nowrap;
    /* On change la valeur de la propriété white-space pour qu'il n'y ait pas de retour à la ligne non désiré. */
    top: 30px;
    /* On positionne notre infobulle. */
    left: 20px;
    background: white;
    color: #4CA3E1;
    padding: 3px;
    border: 1px solid #4b84ad;
    border-left: 5px solid #4CA3E1;
}

#cb_bloc_icon {
    /* RESEAU */
    display: flex;
    flex-direction: row;
    border: none;
    background: rgb(197, 188, 177);
    width: 75%;
    margin-left: auto;
    margin-right: auto;
    padding: 0px;
    margin-top: 3%;
    margin-bottom: 3%;
    position: relative;
    overflow: hidden;
}

#cb_bloc_icon .clearfix {
    width: 100%;
    display: flex;
}

#cb_bloc_icon #icon_CB_left {
    width: 18%;
    align-self: center;
    text-align: center;
}

#cb_bloc_icon #icon_CB_left img {
    width: 50%;
}

#cb_bloc_icon #texte_bloc_CB {
    width: 82%;
    color: #F9F9F9;
    display: flex;
    flex-direction: column;
}

#cb_bloc_icon #texte_bloc_CB h4 {
    color: #F9F9F9;
    margin: 0px;
    margin-top: 1%;
    margin-bottom: 1%;
    padding: 0px;
}

#cb_bloc_icon #texte_bloc_CB #offre_CB_bloc_CB {
    display: flex;
    height: fit-content;
}

#cb_bloc_icon #texte_bloc_CB #offre_CB_bloc_CB .check {
    width: 2%;
    display: flex;
    align-items: center;
}

#cb_bloc_icon #texte_bloc_CB #offre_CB_bloc_CB div {
    text-align: center;
    position: relative;
    width: 32.66%;
    font-size: 17px;
    font-weight: lighter;
}

#cb_bloc_icon #texte_bloc_CB #offre_CB_bloc_CB div #ligne_milieu_cb {
    width: 100%;
    height: 100%;
    position: absolute;
    border-top: solid;
    border-width: 2px;
    left: -1%;
    top: 50%;
}

#cb_bloc_icon .border_left_pack_cb {
    position: absolute;
    width: 100%;
    height: 100%;
    border-left: solid;
    border-color: white;
    left: 1%;
}

#cb_bloc_icon .border_corner_pack_cb {
    position: absolute;
    width: 100%;
    height: 100%;
    border-top: solid;
    border-color: white;
    transform: rotate(45deg);
    left: 45%;
}

#contener_pack_proposes {
    display: flex;
    margin-top: 3%;
    margin-bottom: 3%;
}

#contener_pack_proposes #contener_pack_emploi {}

#contener_pack_proposes #contener_pack_alternance {}

#contener_pack_proposes #contener_pack_stage {}

#contener_pack_proposes img {
    height: 130px;
}

#contener_pack_proposes h3 {
    font-size: 2em;
    text-align: center;
    border: none;
    color: #5B97BE;
    margin: 0px;
    padding: 0px;
    margin-bottom: 4%;
}

#contener_pack_proposes .clearfix {
    width: 75%;
    margin: 0px;
    padding: 0px;
    display: flex;
    flex-direction: column;
    margin-left: auto;
    margin-right: auto;
}

#contener_pack_proposes .clearfix .pack {
    align-items: center;
    width: 100%;
    margin: 0px;
    padding: 0px;
    float: none;
}

#contener_pack_proposes .clearfix .pack .pack_listing {
    color: black;
    font-weight: normal;
    background: white;
    display: flex;
    flex-direction: column;
}

#contener_pack_proposes .clearfix .pack .pack_listing a {
    text-decoration: none;
    color: black;
}

#contener_pack_proposes .clearfix .pack .pack_listing .texte_pack {
    text-align: center;
    padding-left: 5%;
    padding-right: 5%;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo {
    display: flex;
    flex-direction: column;
    border: 1px solid #5BB3E0;
    border-bottom: none;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo .pack_listing_item {
    display: flex;
    flex-direction: row;
    height: 75px;
    align-items: center;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo .pack_listing_item {
    font-size: 14px;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo .pack_listing_item .pack_listing_offre {
    width: 55%;
    padding-left: 3%;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo .pack_listing_item .pack_listing_trait {
    width: 17%;
    height: 100%;
    position: relative;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo .pack_listing_item .pack_listing_trait div {
    height: 100%;
    width: 100%;
    border-top: 1px solid black;
    position: absolute;
    top: 50%;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo .pack_listing_item .new_packprix {
    width: 30%;
    text-align: right;
    padding: 0px;
    padding-right: 3%;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo .pack_listing_item .pack_listing_offre_illimitee {
    width: 40%;
    padding-left: 3%;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo .pack_listing_item .pack_listing_trait_illimitee {
    width: 15%;
    position: relative;
    height: 100%;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo .pack_listing_item .pack_listing_trait_illimitee div {
    height: 100%;
    width: 190%;
    border-top: 1px solid black;
    position: absolute;
    top: 50%;
    left: -100%;
}

#contener_pack_proposes .clearfix .pack .pack_listing .pack_listing_no_photo .pack_listing_item .contact a {
    width: 50%;
    color: #5B97BE;
}

#contener_pack_proposes .clearfix .pack table {
    box-sizing: content-box;
}

#contener_pack_proposes .clearfix .pack table tbody {
    box-sizing: content-box;
}

#contener_pack_proposes .clearfix .pack table tbody tr {
    box-sizing: content-box;
}

#contener_pack_proposes .clearfix .pack table tbody td {
    padding: 0px;
    margin: 0px;
}

#contener_nos_services {
    margin-top: 2%;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    display: flex;
}

#contener_nos_services h3 {
    font-size: 2em;
    text-align: center;
    border: none;
    color: #5B97BE;
    margin: 0px;
    padding: 0px;
    margin-bottom: 4%;
}

#contener_nos_services .clearfix {
    width: 50%;
    display: flex;
    flex-direction: column;
    color: white;
}

#contener_nos_services .clearfix .pack {
    width: 80%;
    align-self: center;
    margin: 0px;
    padding: 0px;
}

#contener_nos_services .clearfix .pack img {
    height: 275px;
    object-fit: cover;
}

#contener_nos_services .clearfix .pack {
    width: 80%;
    align-self: center;
    margin: 0px;
    padding: 0px;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_service {
    display: flex;
    flex-direction: column;
    background: #C5BCB1;
    position: relative;
    overflow: hidden;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_service .trait_gauche_service {
    width: 100%;
    height: 100%;
    position: absolute;
    border-left: 3px solid white;
    left: 9px;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_service .trait_haut_service {
    width: 100%;
    height: 100%;
    position: absolute;
    border-top: 3px solid white;
    top: 9px;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_service .trait_corner_service {
    width: 100%;
    height: 100%;
    position: absolute;
    border-bottom: 3px solid white;
    transform: rotate(-45deg);
    left: 30%;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_service .pack_listing_item_service {
    height: 65px;
    display: flex;
    flex-direction: row;
    align-items: center;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_cvtheque {
    display: flex;
    flex-direction: column;
    background: #C5BCB1;
    position: relative;
    overflow: hidden;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_cvtheque .trait_gauche_cvtheque {
    width: 100%;
    height: 100%;
    position: absolute;
    border-left: 3px solid white;
    left: 10px;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_cvtheque .trait_haut_cvtheque {
    width: 100%;
    height: 100%;
    position: absolute;
    border-top: 3px solid white;
    top: 10px;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_cvtheque .trait_corner_cvtheque {
    width: 100%;
    height: 100%;
    position: absolute;
    border-bottom: 3px solid white;
    transform: rotate(-45deg);
    left: 30%;
}

#contener_nos_services .clearfix .pack .pack_listing_item_cvtheque {
    height: 65px;
    display: flex;
    flex-direction: row;
    align-items: center;
}

#contener_nos_services .clearfix .pack .pack_listing_item_cvtheque div {
    width: 25%;
    text-align: center;
}

#contener_nos_services .clearfix .pack .pack_listing_item_cvtheque .trait_cvtheque {
    width: 50%;
    position: relative;
}

#contener_nos_services .clearfix .pack .pack_listing_item_cvtheque .trait_cvtheque div {
    width: 100%;
    height: 100%;
    position: absolute;
    border-top: 1px solid white;
}

#contener_nos_services .clearfix .pack .texte_pack {
    height: 110px!important;
}

#contener_nos_services .clearfix .pack .texte_pack p {
    height: 110px!important;
}

#contener_nos_services .clearfix .pack .texte_pack div {
    width: 100%!important;
    text-align: left!important;
}

#contener_nos_services .clearfix .pack .pack_listing_cvtheque .texte_pack {
    text-align: center;
    margin-left: 15%;
    margin-right: 15%;
    font-size: 20px;
}

#contener_nos_services .clearfix .pack .pack_listing_service .texte_pack {
    text-align: center;
    margin-left: 12%;
    margin-right: 12%;
    font-size: 20px;
}

#contener_nos_services .clearfix .pack .pack_listing_item_service div {
    width: 25%;
    text-align: center;
}

#contener_nos_services .clearfix .pack .pack_listing_item_service .trait_service {
    width: 50%;
    position: relative;
}

#contener_nos_services .clearfix .pack .pack_listing_item_service .trait_service div {
    width: 100%;
    height: 100%;
    position: absolute;
    border-top: 1px solid white;
}

.pack_listing_no_photo_visibilite {
    height: 175px;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_visibilite .trait_gauche_visibilite {
    width: 100%;
    height: 100%;
    position: absolute;
    border-left: 3px solid white;
    left: 10px;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_visibilite .trait_haut_visibilite {
    width: 100%;
    height: 100%;
    position: absolute;
    border-top: 3px solid white;
    top: 10px;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_visibilite .trait_corner_visibilite {
    width: 100%;
    height: 100%;
    position: absolute;
    border-bottom: 3px solid white;
    transform: rotate(-45deg);
    left: 30%;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_visibilite {
    display: flex;
    flex-direction: column;
    background: #C5BCB1;
    position: relative;
    overflow: hidden;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_visibilite .pack_listing_item_visibilite {
    font-size: 20px;
    margin-top: auto;
    margin-bottom: auto;
    height: auto !important;
}

#contener_nos_services .clearfix .pack .pack_listing_no_photo_visibilite .pack_listing_item_visibilite li {
    padding: 2px;
}

#contener_notre_reseau {
    background: #C5BCB1;
    display: flex;
    width: 75%;
    margin: auto;
    border: 1px solid #c2c2c2;
    margin-bottom: 5%;
    margin-top: 2%;
}

#contener_notre_reseau .contener_logo_reseau {
    background: #FFF;
    display: flex;
    text-align: center;
    align-items: center;
}

#contener_notre_reseau .clearfix {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    overflow: hidden;
}

#contener_notre_reseau .clearfix .trait_corner_gauche {
    position: absolute;
    width: 100%;
    height: 100%;
    border-top: 5px solid white;
    top: -98%;
    left: -5%;
    transform: rotate(-45deg);
}

#contener_notre_reseau .clearfix .trait_corner_droit {
    position: absolute;
    width: 100%;
    height: 100%;
    border-top: 5px solid white;
    top: 98%;
    left: 50%;
    transform: rotate(-45deg);
}

#contener_notre_reseau .clearfix #logos_carriere {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding: 10px 18px;
}

#contener_notre_reseau .clearfix #logos_carriere .rang1 {
    display: flex;
    width: 100%;
}

#contener_notre_reseau .clearfix #logos_carriere .rang2 {
    display: flex;
    width: 100%;
}

#contener_notre_reseau .clearfix #logos_carriere .contener_logo_reseau {
    display: flex;
    width: 100%;
    width: 140px;
    height: 84px;
    margin: auto;
    margin-top: 2%;
    margin-bottom: 2%;
}

#contener_notre_reseau .clearfix #logos_carriere .contener_logo_reseau img {
    width: 75%;
}

#contener_pack_proposes_formateur {
    margin-bottom: 5%;
}

#contener_notre_reseau_formateur {
    background: #C5BCB1;
    display: flex;
    width: 100%;
    margin: auto;
    border: 1px solid #c2c2c2;
    margin-bottom: 5%;
    margin-top: 2%;
}

#contener_notre_reseau_formateur .contener_logo_reseau {
    background: #FFF;
    display: flex;
    text-align: center;
    align-items: center;
}

#contener_notre_reseau_formateur .clearfix {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    position: relative;
    overflow: hidden;
}

#contener_notre_reseau_formateur .clearfix .trait_corner_gauche {
    position: absolute;
    width: 100%;
    height: 100%;
    left: -40%;
    border-top: 5px solid white;
    transform: rotate(-45deg);
}

#contener_notre_reseau_formateur .clearfix .trait_corner_droit {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 40%;
    border-bottom: 5px solid white;
    transform: rotate(-45deg);
}

#contener_notre_reseau_formateur .clearfix #logos_carriere {
    display: flex;
    flex-wrap: wrap;
    width: 75%;
    padding: 15px 0px;
}

#contener_notre_reseau_formateur .clearfix #logo_formation {
    display: flex;
    flex-wrap: wrap;
    width: 25%;
    height: 100%;
}

#contener_notre_reseau_formateur .clearfix #logo_formation .contener_logo_formation {
    background: #FFF;
    display: flex;
    flex-wrap: wrap;
    width: 80%;
    height: 50%;
    margin: auto;
}

#contener_notre_reseau_formateur .clearfix #logo_formation .contener_logo_formation img {
    width: 100%;
    padding: 0px;
    margin: auto;
}

#contener_notre_reseau_formateur .clearfix #logos_carriere .rang1 {
    display: flex;
    width: 100%;
}

#contener_notre_reseau_formateur .clearfix #logos_carriere .rang2 {
    display: flex;
    width: 100%;
}

#contener_notre_reseau_formateur .clearfix #logos_carriere .contener_logo_reseau {
    display: flex;
    width: 100%;
    width: 140px;
    height: 84px;
    margin: auto;
    margin-top: 2%;
    margin-bottom: 2%;
}

#contener_notre_reseau_formateur .clearfix #logos_carriere .contener_logo_reseau img {
    width: 80%;
}

#contener_nos_partenaires .box_gris {
    background: #C5BCB1;
}

#contener_nos_partenaires {
    position: relative;
    overflow: hidden;
}

#contener_nos_partenaires .nos_partenaire_corner1 {
    position: absolute;
    width: 100%;
    height: 100%;
    border-top: 5px solid white;
    top: -98%;
    left: -12%;
    transform: rotate(-45deg);
}

#contener_nos_partenaires .nos_partenaire_corner2 {
    position: absolute;
    width: 100%;
    height: 100%;
    border-bottom: 5px solid white;
    transform: rotate(-45deg);
    top: 98%;
    left: 12%;
}

#button_inscription_formateur {
    text-align: center;
    margin: auto;
    background: #538DB6;
    width: 40%;
    padding: 10px 10px;
    border: solid 1px black;
}

#button_inscription_formateur:hover {
    background: #3C97D7;
}

#button_inscription_formateur a {
    text-align: center;
    color: white;
}

#contener_item_formateur {
    display: flex;
    flex-direction: row;
}

.item_pack_formateur {
    width: 22%;
    margin-left: 1.5%;
    margin-right: 1.5%;
    display: flex;
    flex-direction: column;
    margin-bottom: 3%;
    margin-top: 3%;
}

.photo_pack_formateur {
    display: flex;
    width: 100%;
    height: 175px;
    border: solid 1px #5BB3E0;
}

.photo_pack_formateur img {
    width: 90%;
    max-height: 100%;
    margin: auto;
}

.texte_pack_formateur {
    width: 100%;
    height: 175px;
    background: #C5BCB1;
    border: solid 1px #5BB3E0;
    display: flex;
}

.texte_pack_formateur p {
    padding: 3px;
    margin: auto;
    font-weight: bold;
    font-size: 22px;
    text-align: center;
}


/* // // // // FIN OFFRES PROPOSEES // // // //  */


/* // // // // ESSAI DEPOT GRATUIT  // // // // */

#contener_pourquoi_DE {
    width: 85%;
    margin: auto;
}

#contener_pourquoi_DE .contener_liste_pourquoi_DE {
    background: #C5BCB1;
    color: white;
    font-weight: bold;
    font-size: 19px;
    position: relative;
    overflow: hidden;
}

#contener_pourquoi_DE .contener_liste_pourquoi_DE .trait_gauche {
    position: absolute;
    width: 100%;
    height: 100%;
    border-left: 4px solid white;
    left: 0.7%;
}

#contener_pourquoi_DE .contener_liste_pourquoi_DE .trait_corner {
    position: absolute;
    width: 100%;
    height: 100%;
    border-bottom: 4px solid white;
    transform: rotate(-45deg);
    left: 40%;
}

#contener_pourquoi_DE .contener_liste_pourquoi_DE .liste_pourquoi_DE {
    padding-top: 2%;
    padding-bottom: 2%;
    padding-left: 7%;
    padding-right: 7%;
}

#contener_pourquoi_DE .contener_liste_pourquoi_DE .liste_pourquoi_DE div {
    padding-bottom: 0.5%;
    padding-top: 0.5%;
}

#contener_pourquoi_DE #logos {
    width: 100%;
    border: 1px solid #5BB3E0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding: 2%;
}

#contener_pourquoi_DE #logos a {
    width: 11.5%;
    margin-right: 0.5%;
    margin-left: 0.5%;
}

#contener_pourquoi_DE #logos a img {
    max-width: 100%;
}

.contener_depot_gratuit {
    width: 80%;
    margin: auto;
    display: flex;
    flex-direction: row;
}

.contener_depot_gratuit .contener_icon_texte {
    border: 1px solid #5BB3E0;
    width: 22%;
}

.contener_depot_gratuit .contener_icon_texte .contener_icon {
    width: 100%;
    height: 100%;
    text-align: center;
    height: 150px;
}

.contener_depot_gratuit .contener_icon_texte img {
    margin: auto;
    padding-top: 25px;
    height: 100px;
}

.contener_depot_gratuit .contener_icon_texte .contener_texte {
    background: #C5BCB1;
}

.contener_depot_gratuit .contener_icon_texte .contener_texte {
    background: #C5BCB1;
    height: 150px;
    text-align: center;
    font-size: 24px;
    color: white;
    padding: 5% 5%;
    display: flex;
    align-items: center;
}

.contener_depot_gratuit .contener_icon_texte .contener_texte p {
    margin: 0px;
}

#texte_fin {
    width: 85%;
    margin: auto;
    font-size: 15px;
    text-align: justify;
}

.contener_paragraphe {
    margin-top: 4%;
    text-align: center;
    margin-bottom: 4%;
}

.contener_paragraphe p {
    padding: 0px;
    margin: 0px;
}

.contener_bouton_profitez {
    width: 45%;
    margin: auto;
    border: 1px solid #5BB3E0;
}

.contener_bouton_profitez .bouton_profitez {
    margin-top: 2%;
    margin-bottom: 2%;
    text-align: center;
}

.contener_bouton_profitez p {
    margin: 0px;
    margin-bottom: 2%;
    margin-top: 2%;
    font-size: 13px;
    text-align: center;
}


/* FIN OFFRES PROPOSEES */


/* RESEAU */

.logoD {
    float: left;
}

.logoDE img {
    width: 185px;
    height: auto;
}

.logo_le_reseau img {
    width: 100%;
    height: auto;
}

.divers .box_site .textes {
    /* width:54%; */
    width: 30%;
}

.divers .box_site .boxL .box_gris {
    /* width:300px; */
    width: 100%;
    box-sizing: border-box;
}

.textes .box_gris {
    margin: 0px;
}

.boxL .box_gris {
    float: right;
    text-align: justify;
}

.box_cadre {
    border: 1px solid #eaeaea;
    text-align: center;
    margin-top: 10px;
}

.box_cadre p {
    margin: 7px 15px;
}

.box_stage_alter .boxL .box_gris {
    width: 68%;
    margin: 3px 0px 5px 0px;
}

.box_stage_alter .boxL {
    width: 100%;
}

.box_emploi_spe .boxL .box_gris {
    width: 280px;
    margin: 0px 0px 14px 0px;
    padding: 5px 10px;
}

.box_emploi_spe .boxL {
    width: 100%;
}

.box_stage_alter .boxL:first-child .box_gris {
    margin: 3px 0px 18px 0px;
}


/* .divers .box_emploi_spe .img {
    width:543px;
} */

.green_price {
    color: green;
}

.pack_black {
    color: black;
}


/* page_public */

.pagePacks h3 {
    border: 0px;
    margin: 6px 0px 3px 0px;
    font-size: 1.2em;
    float: left;
    width: 46%;
}

.pagePacks h3 span {
    font-size: 0.8em;
}

.pack {
    width: 50%;
    margin-right: 0px;
    float: left;
}

#pack_new {
    width: 100%;
    margin-right: 0px;
    float: unset;
}

.pack_cb {
    background: #EEEEEE;
    background-size: 40px auto;
    border: 3px solid #EAA622;
    padding: 12px 20px 8px 13px;
    margin: -5px 0px 20px 0px;
}

.packCadre {
    background: #EEEEEE;
    border: 1px solid #BABABA;
    padding: 10px 20px;
}

.page_offres_propose .pack_listing {
    background: #EEEEEE;
}

.page_offres_propose .pack {
    width: 48%;
}

.page_offres_propose .pack:last-child {
    float: right;
}

.page_offres_propose .pack_cb {
    margin-top: 30px;
}

h2.noir {
    color: #000;
}

.pack_cb .text .rouge {
    display: block;
    padding-top: 4px;
    float: left;
    font-weight: 600;
    font-size: 1.1em;
}

.pack_cb .text .rouge:first-child {
    background: url('../images/logo_cb.png') no-repeat 0px 0px;
    background-size: 40px auto;
    padding: 4px 10px 4px 60px;
    margin-left: 20px;
}

.pack_cb input {
    float: left;
    margin: 8px 15px 0px 0px;
}

.pack_cb .text .rouge:last-child {
    float: right;
    padding-right: 40px;
}

.cb_connect {
    padding-top: 5px;
    text-align: right;
}

.cb_connect a {
    color: #000;
    font-weight: 600;
    text-decoration: underline;
}

.cb_connect a:hover {
    color: #ff3435;
}

.pack h3 {
    font-size: 1.2em;
    margin: 0px;
}

.pack h3 span {
    font-size: 0.7em;
}

.pack_listing {
    color: #4b84ad;
    font-size: 0.9em;
    font-weight: bold;
    width: 100%;
}

.pack_listing_2019 {
    font-size: 0.9em;
    font-weight: bold;
    width: 100%;
}

.pack_listing td {
    padding: 4px 15px;
}

.pack_listing td.check {
    padding: 4px 0px;
    width: 25px;
}

.packR .pack_listing td.check {
    padding-left: 35px;
}

.packL .pack_listing td:last-child {
    padding-right: 35px;
}

.pack_listing .packprix {
    color: #000;
    width: 120px;
}


/* .pack_listing .new_packprix {
	color:#4B84AD;
} */

.page_public .logoD img {
    display: block;
    margin: 8px 0px;
}

.packdiv .logoD,
.packdiv .textes {
    width: 100%;
    display: table-cell;
    vertical-align: middle;
    float: none;
}

.packEmploi .packdiv img {
    width: 200px;
    height: auto;
    margin: 0px 0px 0px 15px;
}

.packStage .packdiv img {
    width: 130px;
    height: auto;
    margin: 0px 0px 0px 15px;
}

.packCadre hr {
    width: 70%;
    margin: 17px auto;
    border-color: #D6D6D6;
}

.packStage .packdiv img {
    display: inline-block;
}

.divers {
    background-color: #FFF;
}

ul.liste_fleche {
    margin: 0px 0px 0px 20px;
    padding: 0px;
}

ul.liste_fleche li {
    background: url("../../images/direct-emploi/pucefleche.png") no-repeat scroll left 4px transparent;
    font-size: 1em;
    list-style-type: none;
    padding: 3px 0 0 23px;
    font-weight: bold;
    margin: 10px 0px;
}

.boxformD {
    width: 215px;
}

.box_infoformateur {
    margin-top: -70px;
}

.box_gris2 {
    background: #E2DFDF;
    padding: 6px 15px 0px 15px;
    border-radius: 4px;
    margin: 25px 0px;
}

.box_gris2 p {
    padding: 15px 0px;
    margin: 0px;
    font-weight: bold;
    font-size: 0.8em;
    color: #000;
}

.box_gris2 hr {
    background: #FFF;
    border: 0px;
    padding: 0px;
    margin: 0px;
    height: 1px;
    display: block;
}

.boxformD .submit {
    text-align: center;
}

h5.or {
    font-size: 1em;
    color: #FB4F00;
    margin: 17px 0px 4px 0px;
}

.bRoll {
    cursor: pointer;
    position: relative;
}

.zRoll {
    display: none;
}


/* @media all and (min-width:990px) {
.bRoll:hover .zRoll{
	display:block;
}
} */

.tab_off_com {
    width: 60%;
    display: block;
    background: #EEEEEE;
    border: 1px solid #BABABA;
    padding: 10px 20px;
    float: left;
}

.off_com {
    background-color: lightgrey;
    width: 30%;
    display: block;
    float: right;
}

.back_tab_off_com {
    width: 100%;
    display: block;
}

#pack_newL {
    width: 57%;
    float: left;
    margin: 12px;
}

#pack_newR {
    width: 37%;
    float: right;
    margin: 12px;
}

#cb_new_off {
    background-image:url(../../images/logo_cb.png);
    color: black;
}

#cb_new_off_green {
    color: green;
    float: right;
    margin-left: 1%;
}

#check_cb_new_off {
    margin: 0%;
    margin-left: 2px;
}

#pack_listing_black {
    color: black;
    padding: 0%;
    border: 1px solid #979797;
}

#hr_offre {
    border: 1px solid #979797;
    height: 0;
    border-bottom: none;
    padding: 0px 10px;
}

.img_off_logo {
    vertical-align: middle;
}

#td_off_prop_right {
    width: unset;
    float: right;
}

#td_off_prop_left {
    width: unset;
    float: left;
}

.img_off_prop {
    width: 100%;
    height: 200px;
}

#td_off_prop {
    padding: 0px 10px;
}

.logo_off_prop {
    width: 7%;
    padding: 2px;
    margin: 4px;
    vertical-align: middle;
}

#img_off_prop_R {
    padding: 0%;
    text-align: center;
    background-color: white;
}

#cb_yellow_bloc {
    margin: 12px;
}

#h3_off_prop {
    font-size: 1.5em;
    margin: 10px 0px 16px 0px;
    padding: 0px 13px 10px 13px;
    border-bottom: 1px solid #c2c2c2;
}

.cb_connect_new {
    margin: 25px;
    text-align: center;
}

.cb_connect_new a {
    color: #000;
    font-weight: 600;
    text-decoration: underline;
}

.cb_connect_new a:hover {
    color: #ff3435;
}

#fieldset_entreprises {
    background-color: #046693;
    margin-top: 28px;
}

.fond_rechercher_avancee {
    background-color: #4BA8C5 ;
    height: 44px;
    width: 253px;
    border: 2px solid #FFF;
}

.text_domaine_recherche_avancee {
    font-size: 1.3em;
    padding-bottom: 5px;
    padding-top: 5px;
    margin-top: 20px;
    font-weight: bold;
    margin-bottom: 30px;
    background-color: #4BA8C5 ;
    text-align: center;
    width: 69%;
    margin-left: auto;
    margin-right: auto;
    border-radius: 6px 6px 6px 6px;
    color: #E8E9E8;
}

.cadre_domaine_recherche_avancee {
    border: 7px solid #3E759D;
}

.page_reseau .box_gris {
    background: #eaeaea;
    padding: 0px;
    position: relative;
    overflow: hidden;
}

.page_reseau .box_emploi_spe .boxL .box_gris {
    padding: 0px;
}

.page_reseau .box_emploi_spe .boxL .box_gris p {
    padding: 5px 10px;
}

.page_reseau .box_gris .border_left {
    position: absolute;
    border-left: solid;
    border-width: 5px;
    border-color: white;
    width: 100%;
    height: 100%;
    padding: 0px;
    margin-left: 0.75%;
    display: flex;
}

.page_reseau .box_gris .border_corner_bottom_right {
    position: absolute;
    border-left: solid;
    border-width: 5px;
    border-color: white;
    width: 100%;
    height: 100%;
    left: 95%;
    top: 95%;
    display: flex;
    transform: rotate(45deg);
}

.page_reseau .box_stage_alter .box_gris .border_corner_bottom_right {
    position: absolute;
    border-left: solid;
    border-width: 5px;
    border-color: white;
    width: 100%;
    height: 100%;
    left: 85%;
    top: 95%;
    display: flex;
    transform: rotate(45deg);
}

.page_reseau .box_stage_alter .box_gris #border_corner_bottom_right_DAlt {
    left: 86.5%;
}

.page_reseau .box_emploi_spe .box_gris .border_left {
    margin-left: 1%;
}

.page_reseau .box_emploi_spe .box_gris .border_corner_bottom_right {
    position: absolute;
    border-left: solid;
    border-width: 5px;
    border-color: white;
    width: 100%;
    height: 100%;
    left: 82.5%;
    top: 165%;
    display: flex;
    transform: rotate(45deg);
}

.page_reseau .box_emploi_spe .box_gris #border_corner_bottom_right_special {
    top: 190%;
}

/* .page_reseau .box_gris p {
    color: #F9F9F9;
} */

.page_reseau .box_site {
    margin-bottom: 7%;
}

.page_reseau .box_stage_alter {
    margin-bottom: 7%;
}

.page_reseau .box_emploi_spe {
    margin-bottom: 7%;
}

#titre_DE {
    margin-bottom: 0%;
}

#box_contener_stage_alter {
    width: 115%;
    margin-left: -7.5%;
    display: flex;
    flex-wrap: wrap;
}

.page_reseau .textesDE .boxL .box_gris p {
    margin-right: 6.5%;
    margin-left: 4%;
    font-size: 16px;
}

#textes_Stage_Alter {
    width: 50%;
    display: flex;
}

#textes_Stage_Alter .box_gris {
    margin: 0px;
    padding: 0px;
}

#textes_Stage_Alter #boxL_DEtudiant {
    margin-bottom: 3%;
}

#images_Stage_Alter {
    width: 50%;
    display: flex;
}

#images_Stage_Alter img {
    max-width: 50%;
}

.page_reseau #box_contener_stage_alter .boxL {
    display: flex;
}

.page_reseau #box_contener_stage_alter .logoD {
    float: none;
    display: flex;
    width: 32%;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.page_reseau #box_contener_stage_alter .logoD a {
    height: min-content;
    width: min-content;
    vertical-align: middle;
}

.page_reseau #box_contener_stage_alter .logoD img {
    vertical-align: middle;
}

.page_reseau #box_contener_stage_alter .box_gris {
    float: none;
    display: flex;
    flex-direction: row;
}

#contener_emploi_spe {
    display: flex;
    width: 107.5%;
}

#contener_emploi_spe .textesCarriere {
    width: 55%;
}

#contener_emploi_spe .textesCarriere p {
    padding-right: 20%;
}

#contener_emploi_spe .textesCarriere .boxL {
    display: flex;
}

#contener_emploi_spe .textesCarriere .boxL .logoD {
    margin-right: 5%;
    align-items: center;
}

#contener_emploi_spe .textesCarriere .boxL .box_gris {
    width: auto;
}

#contener_emploi_spe .img {
    width: 55%;
}

#contener_emploi_spe .img img {
    height: 100%;
}

.page_reseau .textesCarriere .box_gris p {
    padding: 0px;
    margin: 0px;
    text-align: left;
    margin-left: 3%;
}

.page_reseau #box_contener_formation {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.page_reseau #box_contener_formation #textes_Formation {
    display: flex;
    flex-direction: row;
    width: 60%;
}

.page_reseau #box_contener_formation #textes_Formation .logoD {
    width: 33%;
}

.page_reseau #box_contener_formation #textes_Formation .box_gris {
    width: 62%;
    margin-right: 5%;
}

.page_reseau #box_contener_formation #textes_Formation .box_gris .border_left {
    margin-left: 2.5%;
}

.page_reseau #box_contener_formation #textes_Formation .box_gris .border_corner_bottom_right {
    left: 60%;
}

.page_reseau #box_contener_formation #textes_Formation .box_gris p {
    margin-right: 7%;
    margin-left: 7%;
    text-align: justify;
}

.page_reseau #box_contener_formation #image_Formation {
    width: 40%;
    display: flex;
    height: auto;
    max-height: fit-content;
    position: relative;
}

.page_reseau #box_contener_formation #image_Formation img {
    position: absolute;
    width: 70%;
    left: 15%;
    margin: auto;
    height: 100%;
}

@media all and (min-width:639px) {
    .page_reseau .box_site .img {
        float: right;
        text-align: center;
        width: fit-content;
    }
    .page_reseau .box_site .img img {
        margin-left: 0px;
        height: 97%;
        width: 96%;
        margin-left: 4%;
        margin-top: 5%;
    }
    #box_DE_texte_logo_image {
        display: flex;
        flex-basis: fill;
        flex-wrap: wrap;
    }
    .page_reseau #texte_DE {
        height: fit-content;
        width: 70%;
        display: flex;
    }
    #contener_texte_image_DE {
        display: flex;
    }
    #boxR_DE {
        width: 30%;
        display: flex;
        height: auto;
        max-height: fit-content;
    }
    .page_reseau .textesSA {
        height: auto;
    }
    .page_reseau .textesSA p {
        margin-left: 5%;
        margin-right: 5%;
        font-weight: lighter;*
    font-size: 14px;
    }
    /* .page_reseau .textesCarriere {
        height: 674px;
    } */
    .page_reseau .divers .box_site .boxL {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }
    .page_reseau .logo_le_reseau {
        width: 35%;
        height: auto;
        padding-bottom: 20px;
    }
    .page_reseau .textes {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }
    .page_reseau .textesDE .boxL {
        width: 96%;
        margin-left: 3%;
        margin-right: 1%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }
    @media all and (min-width:990px) {
        .bRoll:hover .zRoll {
            display: block;
        }
    }
}


/* Pages Conseil*/

.cadre_conseil {
    width: 400px;
    height: 65px;
    background-color: #4BA8C5;
    border: 1px solid black;
    border-radius: 14px;
    box-shadow: 2px 5px 5px grey;
}

.conseils_h1 {
    margin-left: auto;
    margin-right: auto;
    margin-top: 30px;
    text-decoration: none;
    font-family: Arial, Helvetica, sans-serif;
    color: #4BA8C5;
    text-align: center;
    font-weight: bold;
    background: transparent;
    font-size: 43px;
}

.conseils_espace h1 {
    border-bottom: none;
    margin-left: auto;
    margin-right: auto;
    margin-top: 30px;
    text-decoration: none;
    font-family: Arial, Helvetica, sans-serif;
    color: #4BA8C5;
    text-align: center;
    font-weight: bold;
    background: transparent;
    font-size: 55px;
}

.cadre_conseil_bottom {
    width: 243px;
    height: 29px;
    background-color: #4BA8C5;
    border: 1px solid #4BA8C5;
    border-radius: 14px;
    box-shadow: 2px 5px 5px grey;
    margin-bottom: 12px;
}

.cta_read_more {
    font-size: 18px;
    float: right;
    text-decoration: underline;
    padding:5px;
}
.cta_read_more:hover {
    color: #4ba8c5;
}

/* Page Admin Formation CLick */

#tableClicks {
    font-family: Arial, Helvetica, sans-serif;
    border-collapse: collapse;
    width: 100%;
}

#tableClicks td,
#tableClicks th {
    border: 1px solid #ddd;
    padding: 8px;
}

#tableClicks tr:nth-child(even) {
    background-color: #f2f2f2;
}

#tableClicks tr:hover {
    background-color: #ddd;
}

#tableClicks th {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: left;
    background: linear-gradient(to top, #4ba8c5, #558FBA);
    color: white;
}

#search {
    padding: 5px;
    border: 2px solid #111d5e;
    font-size: 16px;
    color: black;
}

.label_click {
    font-size: 1rem;
    padding-right: 10px;
}

.select_click {
    font-size: .9rem;
    padding: 2px 5px;
}

.table.sticky {
    /*width:100%; */
    display: block;
    empty-cells: show;
    /* Decoration */
    border-spacing: 0;
    border: 1px solid;
}

.table.sticky thead {
    background-color: #f1f1f1;
    position: relative;
    display: block;
    width: 100%;
}

.table.sticky tbody {
    /* Position */
    display: block;
    position: relative;
    width: 101.5%;
    overflow-y: scroll;
    /* Decoration */
    max-height: 300px;
    border-top: 1px solid rgba(0, 0, 0, 0.2);
}

.table.sticky tr {
    width: 100%;
    display: flex;
}

.table.sticky td,
.table.sticky th {
    flex-basis: 100%;
    flex-grow: 2;
    display: block;
    padding: 1rem;
    text-align: left;
}

.page_reseau h3{
    font-size: 1.1em;
    padding: 10px 11px 2px 22px;
    color: #4b84ad;
    margin: 0px;
    border-bottom: none;
}

/*----------------------- Espace Recruteur -------------------------*/
.blue{
    color: #44A2C6;
}
.green{
    color: #4EC644;
}
.green-background{
    background-color: #4EC644;
}
.yellow-background{
    background-color: #F6CA03;
}
.red-background{
    background-color: #F33232;
}
.red{
    color: #F33232;
}
.second-color{
    color: #F6CA03;
}
.black{
    color: #1e1e1e;
}
.dot {
    height: 10px;
    width: 10px;
    border-radius: 50%;
    display: inline-block;
}
.button-offre{
    background-color: #36819E!important;
    color: white!important;
    padding: 0.5rem 1.5rem!important;
    margin-top:2rem!important;
}
.button-offre:hover{
    color: white!important;

    background-color: #36819EAA!important;
}
.button-inscription{
    background-color: #44A2C6!important;
    color: white!important;
    border-radius:10px!important;
    padding: 0.5rem 1.5rem!important;
}
.button-inscription:hover{
    color: white!important;

    background-color: #44A2C6AA!important;
}
.button-outlined-blue{
    background-color: transparent!important;
    color: #44A2C6!important;
    border: 1px solid #44A2C6!important;
    border-radius:10px!important;
    padding: 0.5rem 1.5rem!important;
}
.button-outlined-blue:hover{
    background-color: #44A2C6!important;
    color: white!important;
    border: 1px solid #44A2C6!important;
    border-radius:10px!important;
    padding: 0.5rem 1.5rem!important;
}
.button-outlined-blue:focus{
    background-color: #44A2C6!important;
    color: white!important;
    border: 1px solid #44A2C6!important;
    border-radius:10px!important;
    padding: 0.5rem 1.5rem!important;
}
.settings-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 15px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
}
.settings-card > * {
    margin-bottom: 15px;
}
h6 {
    font-size: 18px;
    margin-bottom: 15px;
}
.settings-card a {
    font-weight:400;
}
.settings-card .menu-link {
    font-size: 15px;
}
.settings-card .active {
    color:#44A2C6;
    font-weight:500;
}
.de-div{
    background-color: #fff;
    border-radius: 10px;
    padding: 15px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}
.call-to-action p{
    font-weight: 500;
    font-size: 15px;
}
.de-div.product span{
    font-weight: 500;
}

.de-div.product span:first-child{
    margin-bottom: 10px;
}
.de-div.product .situation{
    font-size: 14px;
    font-weight: 400;
}
.de-div.product {
    margin-bottom: 15px;
}

hr{
    margin-top:0;
}
.medium{
    font-weight: 500;
}
.bold{
    font-weight: 600;
}
.call-to-action .job-form{
    padding: 0;
    margin-bottom: 15px;
}
.call-to-action .job-form select{
    margin: 0;
    font-weight: 400;
}

.filter-div{
    padding: 25px;
    background-color: rgba(0, 0, 0, 0.03);
    margin-bottom: 1rem;
}
.header-line{
    height: 5px;
    width:50px;
    background-color: #44A2C6;
    border-radius: 5px;
}

.blue-line{
    height: 5px;
    width:20%;
    background-color: #8EC7DC;
    border-radius: 5px;
}
.offres-list-recruteur span {
    font-size: 15px;
}
.offres-list-recruteur .v-flex > span {
    margin-bottom: 5px;
}
.gray{
    color: rgba(0, 0, 0, 0.6);
}
.yellow{
    color: #F6CB0A;
}

.offres-list-recruteur a{
    font-size: 14px;
}
.de-div.offres-list-recruteur{
    margin-bottom: 15px;
}
.cvtheque-banner{
    padding:80px 0;
    background-image: linear-gradient(rgba(0,0,0,.4),rgb(0,0,0,.4)),url(/images/direct-emploi/cvt/Frame_59.png);
    background-position: center;
    border-radius: 20px ;
}
.cvtheque-card-row img{
    border-radius: 15px;
    height:300px

}
.cvtheque-header{
    background-image:url(/images/direct-emploi/cvt/cvtheque-header.png) ;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    overflow: hidden;
    padding-top: 142.75px;
    padding-bottom: 142.75px;
    margin-bottom: 0;
}

.h-flex-steps{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}
.resultat-header{
    background-image: url(/images/direct-emploi/cvt/cvtheque-header.png) ;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    overflow: hidden;
    padding-top: 80px;
}

.single-step{
    align-self: center;
}
.circle {
    background: #44A2C6;
    border-radius: 50%;
    color: black;
    display: inline-block;
    height: 80px;
    font-weight: bold;
    font-size: 1.2em;
    width: 80px;
    margin: 0 auto;
}

.circle span {
    display: table-cell;
    vertical-align: middle;
    height: 80px;
    width: 80px;
    text-align: center;
    padding: 0 15px;
}

.single-step i{

    color:white;
}
.single-step i:hover{

    color:white;
}

.outlined-div label{
    position: absolute;
    top: -0.7rem;
    left: 2rem;
    font-size: 15px;
    font-weight: 500;
    background-color: #fff;
    padding: 0 5px;
    color: #1e1e1e;
}
.outlined-div{
    position: relative;
    padding: 1rem;
    border: 0.5px solid #BDBDBD;
    border-radius: 20px;
    margin-bottom: 2rem;
}
.header-text{
    padding: 50px 0;
    background-color: rgba(26,25,26,.2);
    backdrop-filter: blur(2px);
}
.btn.btn-outline-light:focus{
    background-color: white!important;
}
.btn.btn-outline-secondary:focus{
    background-color: #6c757d!important;
}
.cv-result .tag{
    margin-right: 1rem;
    margin-bottom: 0.5rem;
}


.table-striped td {
    font-size: 14px;
    vertical-align: middle;

}
.table-striped td:nth-child(1) {
    min-width:75px ;

}
.table-striped td:nth-child(3) {
    min-width:50px ;

}


/* CVTheque custom select */
/*
.custom-select-single {
  position: relative;
  display: inline-block;
}

.selected-option-single {
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 5px;
  cursor: pointer;
}
*/

.select-box-single {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    border: 1px solid #ccc;
    border-radius: 5px;
    width: 200px;
    background-color: #fff;
}

.option-single {
    cursor: pointer;
    padding: 3px;
}

.option-single:hover {
    background-color: #f0f0f0;
}

.form-blue{
    background: red;
}
/*

.custom-select {
  position: relative;
  display: inline-block;
}

.select-box {
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 5px;
  width: 200px;
  max-height: 100%;
  overflow-y: auto;
}
*/

.option {
    cursor: pointer;
    padding: 8px;
    margin: 10px 0;
    background-color: #EDEDED;
    border-radius: 5px;

}

.option:hover {
    background-color: #f0f0f0;
}

.option.selected {
    background-color: #44A2C6;
    color: #fff;
}



/* gestion candidature table */
.container-table100 {
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.wrap-table100 {
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    width: 100%;
    border-radius: 10px;
    overflow-x: scroll;
}

.table {
    width: 100%;
    display: table;
    margin: 0;

}

@media screen and (max-width: 768px) {
    .table {
        display:block;
    }
    .h-flex-steps{
        display: flex;
        flex-direction: column;

        justify-content: center;
        align-items: center;
    }



}



.row-table {
    display: table-row;
    background: #fff;
}

.row-table.header {
    color: rgba(0, 0, 0, 0.6);
    background: transparent;
}

@media screen and (max-width: 768px) {
    .blue-line{
        width:5px!important;
        background-color:#8EC7DC;
        height: 100px!important;
        margin-bottom: 15px;
    }
    .row-table {
        display:block
    }

    .row-table.header {
        padding: 0;
        height: 0
    }

    .row-table.header .cell {
        display: none
    }

    .row-table .cell:before {
        font-size: 12px;
        color: gray;
        line-height: 1.2;
        text-transform: uppercase;
        font-weight: unset!important;
        margin-bottom: 13px;
        content: attr(data-title);
        min-width: 98px;
        display: block
    }
}

.cell {
    display: table-cell;
    vertical-align: middle; /* Align vertically center */

}

@media screen and (max-width: 768px) {
    .cell {
        display:block
    }
}

.row-table .cell {
    font-size: 15px;
    color: #666;
    line-height: 1.2;
    font-weight: unset!important;
    padding-top: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f2f2f2
}

.row-table.header .cell {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.6);
    line-height: 1.2;
    font-weight: unset!important;
    padding-top: 19px;
    padding-bottom: 19px;
}

.row-table .cell:nth-child(1) {
    min-width: 150px;
    padding-left: 10px;
}

.row-table .cell:nth-child(2) {
    width: 12%;
    padding-left: 5px;

}

.row-table .cell:nth-child(3) {
    width: 12%;
    padding-left: 5px;

}

.row-table .cell:nth-child(4) {
    word-break: break-all;

    min-width: 100px;
    padding-left: 5px;

}
.row-table .cell:nth-child(5) {
    width: 20%;
    padding-left: 5px;

}
.row-table .cell:nth-child(6) {
    width: 12%;
    padding-left: 5px;

}
.row-table .cell:nth-child(7) {
    width: 12%;
    padding-left: 5px;

    padding-right: 0;

}

.table,.row-table {
    width: 100%!important
}

.row-table:hover {
    background-color: rgba(68,162,198,0.1);
    cursor: pointer
}

@media(max-width: 768px) {
    .row-table {
        border-bottom:1px solid #f2f2f2;
        padding-bottom: 18px;
        padding-top: 30px;
        padding-right: 15px;
        margin: 0
    }

    .row-table .cell {
        border: none;
        padding-left: 30px;
        padding-top: 16px;
        padding-bottom: 16px
    }

    .row-table .cell:nth-child(1) {
        padding-left: 5px;
    }

    .row-table .cell {
        font-size: 18px;
        color: #555;
        line-height: 1.2;
        font-weight: unset!important
    }

    .table,.row-table,.cell {
        width: 100%!important
    }
}
.table a{
    padding: 0 0 0 5px!important ;
}
.interessant{
    padding: 5px;
    color: white;
    border-radius: 10px;
    font-size: 12px;
    background-color: #4EC644!important;
}
.non-interessant{
    padding: 5px;
    color: white;
    font-size: 12px;
    border-radius: 10px;
    background-color: #F33232!important;
}
.en-attente{
    padding: 5px;
    color: white;
    font-size: 12px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.4)!important;
}
.blue-line{
    height:5px;
    background-color:#8EC7DC;
    width: 15%;
}