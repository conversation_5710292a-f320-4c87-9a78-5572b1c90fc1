.logo, .pub_haut {
    padding-left: 45px;
}
.slogan, h1.slogan {
    color: #666;
}
.accueil .info_tp {
	display:none;
}
.pub_haut {
    padding-left: 45px;
}
.accueil .contenu_gen {
	background:url('../images/carriere_btp/bandeau.jpg') no-repeat center top;
	background-size:auto 412px;
	padding-top:259px;
	/* padding-top:0px; */
}
.page_home .box_rech {
	background: rgb(181 181 181 / 75%);
	height:155px;
}
/* couleurs */
.form_box h2, .form_box h3 {
	background:#515151;
	background:linear-gradient(to top, #515151, #666);
}
.postuler .submit:hover,
.sub_jaune a:hover,
input[type=submit].sub_jaune:hover,
a.sub_jaune:hover,
.submit input:hover, 
.submit a:hover, 
a.submit:hover, 
input[type="submit"]:hover {
	background-color:#515151;
	background:linear-gradient(to top, #515151, #666);
    border-color:#515151;
}
/* .slogan, h1.slogan {
    color:#FFF;
} */
.box_alaune,
.bordure {
	border-color:#BFBFBF; 
}
/* fin couleurs */
.navi .item {
	flex-basis: 14.25%;
}
.box_user .menu_recrut .niv1 {
    border-right: 0 none;
}
.box_user {
    width: 230px;
}
.navi a.niv1 {
    padding: 10px 25px;
}
.box_metiers  {
	margin-top:35px;
}
.box_metiers .int {
	max-height:none;
	height:auto;
	padding:5px 15px;
}
.box_metiers .item {
    height:135px;
	padding:15px 0px;
	margin:0px;
	width:49%;
}
.box_metiers h4 {
	margin:0px 0px 3px 0px;
}
.box_metiers .item:nth-child(2),
.box_metiers .item:nth-child(4) {
    border-right:0px;
	width:50%;
}
.box_metiers .item:nth-child(3),
.box_metiers .item:nth-child(4) {
    border-bottom:0px;
}
.box_metiers .img,
.box_metiers .text {
   display:table-cell;
   vertical-align:top;
}
.box_metiers .text {
   padding-left:15px;
   width:65%;
}
.box_metiers .item:nth-child(2) .img,
.box_metiers .item:nth-child(4) .img {
   padding-left:15px;
}
.box_metiers .text span {
  display:block;
}
.box_metiers .text span a {
 color:#000;
 font-size:0.9em;
}
.box_metiers .int_text {
	height:114px;
	overflow:hidden;
}
.box_metiers .lien_s {
    display: block;
    float: right;
    font-size: 1em;
    padding-top:2px;
}
.creer_alerte,
.creer_compte h5 {
    background-color: #666;
}
.creer_compte {
	border-color:#666;
}
body .sub_grey a,
body a.sub_grey,
body input.sub_grey {
	border-color:#666;
	background:#E4E4E4;
	border-radius:4px;
	color:#333;
}
body .sub_grey a:hover,
body a.sub_grey:hover,
body input.sub_grey:hover {
	border-color:#ccc;
	background:#FFF;
	color:#000;
}
.retour_liste {
    background-image: url('../images/retour_carrieres.png');
}

.box_metiers1 .item {
	border-right:0px solid #C2C2C2;
	border-bottom:0px solid #C2C2C2;
	display:table;
	width:100%
}

#menu_all .item{
	width: 14%;
}

#phrase_emploi_pc {
	background: #FFF;
}



#phrase_emploi_pc h1 {
	margin: 0% 0% 0% 4%;
	display: inline-block;
	font-size: 1.4em !important;

}

#phrase_emploi_pc h1 td {
	font-size: 0.7em !important;

}


#phrase_emploi_pc h1 td .countOffre {
	font-size: 1.45em !important;

}



#phrase_emploi_telephone {
	display: none !important;
}

.countOffre {
	
	margin-right: 10px;
}

/* CSS pavé candidat pour les carrieres */

.encadre_inscription .bouton_inscription
{
	border: solid 2px white;
}

.texte_et_bouton_inscription{
	padding-top:15px;
	padding-bottom: 15px;
}

div.encadrement_ordi .encadre_boutons_conseils {
	padding-top: 5px;
}

/* fin pavé candidat */

/* responsive menu carriere */
@media all and (max-width:639px) {	

	div.encadrement_ordi{
		display: none;
	}

	p.texte_conseils{
		float: initial;
		padding: 0px;
	}

	.page_home {
    	background-color: #FFF !important;
	}

	.navi {
		background: rgba(0,0,0,0.9) !important;
	}

	.navi .site {
    	background: none !important;
	}

	.navi a.niv1 {
		background : none !important;
		text-align: left !important;
		font-size: 1.1em !important;
		padding: 20px 0px 4px 0px !important;
		border-right: none !important;
	}

	.d_car .navi .smenu {
    	background: none !important;
	}


	.creer_compte {
   	 	background-color: #666 !important;
	}

	#menu_all .item {
		width : auto !important;
	}

	#phrase_emploi_pc {
		display:none !important;
	}

	#phrase_emploi_telephone {
		display: block !important;
		font-size: 0.85em;
	}
	#phrase_emploi_telephone h1 {
		color: #FFF;
	}

	.logo, .pub_haut {
    padding-left: 0px;
	}

	h3{
		background : none !important;
	}



}

