<?php

namespace App\Command;

use App\Script\Seris;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'Seris',description: 'start Seris script')]
class SerisCommand extends Command
{

    private $Seris;

    public function __construct(Seris $Seris)
    {
        $this->Seris = $Seris;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $this->Seris->SerisScript();

        return Command::SUCCESS;
    }
}
