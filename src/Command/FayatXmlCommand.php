<?php
namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Script\FayatXml;

#[AsCommand(
    name: 'FayatXml',
    description: 'FayatXml Script',
    hidden: false,
)]
class FayatXmlCommand extends Command
{
    private $FayatXml;

    public function __construct(FayatXml $FayatXml)
    {
        $this->FayatXml = $FayatXml;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
    
        $this->FayatXml->FayatXmlScript();

        return Command::SUCCESS;
    }
}