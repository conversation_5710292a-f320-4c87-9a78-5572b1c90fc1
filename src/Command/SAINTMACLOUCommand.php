<?php
namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Script\SAINTMACLOU;

#[AsCommand(
    name: 'SAINTMACLOU',
    description: 'SAINTMACLOU Script',
    hidden: false,
)]
class SAINTMACLOUCommand extends Command
{
    private $SAINTMACLOU;

    public function __construct(SAINTMACLOU $SAINTMACLOU)
    {
        $this->SAINTMACLOU = $SAINTMACLOU;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
    
        $this->SAINTMACLOU->SAINTMACLOUScript();

        return Command::SUCCESS;
    }
}

?>