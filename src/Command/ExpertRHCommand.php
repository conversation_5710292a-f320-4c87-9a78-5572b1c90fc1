<?php

namespace App\Command;

use App\Script\ExpertRH;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'ExpertRH',description: 'start 2A ExpertRH script')]
class ExpertRHCommand extends Command
{

    private $ExpertRH;

    public function __construct(ExpertRH $ExpertRH)
    {
        $this->ExpertRH = $ExpertRH;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $this->ExpertRH->ExpertRHScript();

        return Command::SUCCESS;
    }
}
