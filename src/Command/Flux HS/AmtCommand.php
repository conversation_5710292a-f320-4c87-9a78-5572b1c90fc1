<?php
namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Script\Amt;

#[AsCommand(
    name: 'Amt',
    description: 'Amt Script',
    hidden: false,
)]
class AmtCommand extends Command
{
    private $Amt;

    public function __construct(Amt $Amt)
    {
        $this->Amt = $Amt;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
    
        $this->Amt->AmtScript();

        return Command::SUCCESS;
    }
}