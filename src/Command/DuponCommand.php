<?php

namespace App\Command;

use App\Script\Dupon;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'Dupon',description: 'start Dupon script')]
class DuponCommand extends Command
{

    private $Dupon;

    public function __construct(Dupon $Dupon)
    {
        $this->Dupon = $Dupon;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $this->Dupon->DupontScript();

        return Command::SUCCESS;
    }
}
