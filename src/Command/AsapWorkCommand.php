<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Script\AsapWork;

#[AsCommand(
    name: 'AsapWork',
    description: 'AsapWork Script',
    hidden: false,
)]
class AsapWorkCommand extends Command
{
    private $AsapWork;

    public function __construct(AsapWork $AsapWork)
    {
        $this->AsapWork = $AsapWork;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
    
        $this->AsapWork->AsapWorkScript();

        return Command::SUCCESS;
    }
}
?>