<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Script\Decathlon;

#[AsCommand(
    name: 'Decathlon',
    description: 'Decathlon Script',
    hidden: false,
)]
class DecathlonCommand extends Command
{
    private $decathlon;

    public function __construct(Decathlon $decathlon)
    {
        $this->decathlon = $decathlon;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
    
        $this->decathlon->DecathlonScript();

        return Command::SUCCESS;
    }
}
?>