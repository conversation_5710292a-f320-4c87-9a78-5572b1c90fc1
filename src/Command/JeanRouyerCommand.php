<?php

namespace App\Command;

use App\Script\JeanRouyer;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: '<PERSON><PERSON><PERSON><PERSON>',description: 'start <PERSON><PERSON><PERSON><PERSON> script')]
class JeanRouyerCommand extends Command
{

    private $JeanRouyer;

    public function __construct(<PERSON><PERSON><PERSON><PERSON> $JeanRouyer)
    {
        $this-><PERSON><PERSON><PERSON><PERSON> = $JeanRouyer;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $this-><PERSON><PERSON><PERSON><PERSON>-><PERSON><PERSON><PERSON><PERSON>Script();

        return Command::SUCCESS;
    }
}
