<?php

namespace App\Command;

use App\Script\Galileo;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: '<PERSON>',description: 'start Galileo script')]
class GalileoCommand extends Command
{

    private $Galileo;

    public function __construct(Galileo $Galileo)
    {
        $this->Galileo = $Galileo;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $this->Galileo->GalileoScript();

        return Command::SUCCESS;
    }
}
