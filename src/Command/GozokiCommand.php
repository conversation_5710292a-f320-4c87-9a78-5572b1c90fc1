<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Script\Gozoki;

#[AsCommand(
    name: 'Gozoki',
    description: 'Gozoki Script',
    hidden: false,
)]
class GozokiCommand extends Command
{
    private $Gozoki;

    public function __construct(Gozoki $Gozoki)
    {
        $this->Gozoki = $Gozoki;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
    
        $this->Gozoki->GozokiScript();

        return Command::SUCCESS;
    }
}
?>