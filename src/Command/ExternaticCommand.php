<?php
namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Script\Externatic;

#[AsCommand(
    name: 'Externatic',
    description: 'Externatic Script',
    hidden: false,
)]
class ExternaticCommand extends Command
{
    private $Externatic;

    public function __construct(Externatic $Externatic)
    {
        $this->Externatic = $Externatic;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
    
        $this->Externatic->ExternaticScript();

        return Command::SUCCESS;
    }
}