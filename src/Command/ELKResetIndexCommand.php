<?php

namespace App\Command;


use App\Service\ElasticClientService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'elk:reset-index',description: 'Reset elasticsearch index')]
class ELKResetIndexCommand extends Command
{

	private $elastic ;

	public function __construct( ElasticClientService $elastic )
	{

		$this->elastic = $elastic;
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->elastic->deleteIndex();
		$this->elastic->createIndex();
		return Command::SUCCESS;
	}
}
