<?php

namespace App\Command;

use App\Script\TestAddMetier;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Script\TestCvParse;
use Symfony\Component\Console\Input\InputArgument;

#[AsCommand(name: 'testAddMetier',description: 'Test count')]
class TestAddMetierCommand extends Command
{

    public function __construct(private TestAddMetier $testAddMetier)
    {
        $this->testAddMetier = $testAddMetier;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->addArgument('domain', InputArgument::REQUIRED, 'Domaine à utiliser pour la recherche de profils');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $domain = $input->getArgument('domain');
        
        $this->testAddMetier->setDomain($domain);
        $this->testAddMetier->testAddMetierScript();

        return Command::SUCCESS;
    }
}
