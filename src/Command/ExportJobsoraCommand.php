<?php

namespace App\Command;

use App\Script\ExportJobsora;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'ExportJobsora',description: 'start ExportJobsora script')]
class ExportJobsoraCommand extends Command
{

    private $ExportJobsora;

    public function __construct(ExportJobsora $ExportJobsora)
    {
        $this->ExportJobsora = $ExportJobsora;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $this->ExportJobsora->ExportJobsora();

        return Command::SUCCESS;
    }
}
