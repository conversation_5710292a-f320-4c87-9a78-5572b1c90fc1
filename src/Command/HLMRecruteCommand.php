<?php
namespace App\Command;

use App\Script\HLMRecrute;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'HLMR<PERSON>ru<PERSON>',
    description: 'HLMRecrute Script',
    hidden: false,
)]
class HLMRecruteCommand extends Command
{
    private $hlmrecrute;

    public function __construct(HLMRecrute $hlmrecrute)
    {
        $this->hlmrecrute = $hlmrecrute;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
    
        $this->hlmrecrute->HLMRecruteScript();

        return Command::SUCCESS;
    }
}

?>