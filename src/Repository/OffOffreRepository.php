<?php

namespace App\Repository;

use App\Entity\OffOffre;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Query\Parameter;
use DateTime;
use DateInterval;
use Doctrine\ORM\Mapping\OrderBy;
use Symfony\Component\Validator\Constraints\Date;

/**
 * @method OffOffre|null find($id, $lockMode = null, $lockVersion = null)
 * @method OffOffre|null findOneBy(array $criteria, array $orderBy = null)
 * @method OffOffre[]    findAll()
 * @method OffOffre[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OffOffreRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OffOffre::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(OffOffre $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(OffOffre $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    public function getSimilarOffers(string $intitule, int $limit = 5, $id, $geo_ville)
    {
        return $this->createQueryBuilder('o')
            ->select('o.id_offre', 'o.intitule', 'o.geo_ville', 'o.mission', 'c.libelle AS contrat', 'e.logo', 'e.raison_sociale')
            // ->from('App\Entity\OffOffre', 'o')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'c.id_type_contrat = o.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\UserUser', 'd', 'WITH', 'd.id_user = o.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
            ->where('o.intitule LIKE :intitule')
            ->setParameter('intitule', '%' . $intitule . '%')
            ->andWhere('o.id_offre != :id') 
            ->setParameter('id', $id) 
            ->andWhere('o.geo_ville = :geo_ville')
            ->setParameter('geo_ville', $geo_ville)
            ->andWhere('o.statut = 1')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    // On permet la recherche, s'il n'y en a pas, on récupère toutes les offres.
    public function candidatOffreSearch($mot_cle, $id_type_contrat, $geo_ville)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $array = [$mot_cle, $id_type_contrat, $geo_ville];
        $date = new DateTime();
        $month = new DateInterval('P61D');
        $date_valides = date_sub($date, $month);


/*
 //On instancie sphinx
 $cl = new SphinxClient();
 $host = SETTING_SPHINX_HOST;
 $port = SETTING_SPHINX_PORT;



 $index = "offres"; //Indexe de recherche
 //$ranker = SPH_RANK_PROXIMITY_BM25;
 $ranker = SPH_RANK_PROXIMITY;
 $mode = SPH_MATCH_ALL; //SPH_MATCH_ALL: tous les mots , SPH_MATCH_ANY : au moins un mot
 //$mode = SPH_MATCH_EXTENDED2; //SPH_MATCH_ALL: tous les mots , SPH_MATCH_ANY : au moins un mot
 $cl->SetServer($host, $port);
 $cl->SetConnectTimeout(1);
 $cl->SetMatchMode($mode);
 $cl->SetRankingMode($ranker);
 $cl->setLimits(0, 5000, 5000);
 //$cl->setLimits(0, 500, 5000);
 //On fait la recherche
 //Pondération
 $poids = array(
     'intitule'                  => 10000,
     'reference'                 => 1000,
     'mission'                   => 500,
     'profil'                    => 500,
     'raison_sociale'            => 10000,
     'presentation_societe'      => 0,
     'lib_secteur'               => 5000,
 );

 $cl->setFieldWeights($poids);

 $q = $parameters['mot_cle']; //Champ à rechercher

 if (is_array($q)) {
     $motcles = end($q);
 } else {
     $motcles =$q;
 }

 // suppresion des mots clés de moins de 2 lettres
 $tab_mot_cle = explode(" ",$motcles);
 $motcles='';
 foreach ($tab_mot_cle as $mot)
 {

     if(strlen($mot)>2)
         $motcles .= $mot.' ';
 }
 $motcles = substr($motcles,0,-1);

 $res = $cl->Query($motcles, $index);

 if ($res === false) {
     echo "Query failed: " . $cl->GetLastError() . ".\n";
 }
 if (isset($res["matches"])) {
     $id_offres_mot_cle = array_keys($res["matches"]);
 } else {
     $id_offres_mot_cle = array(0);
 }

 $c->add(OffOffrePeer :: ID_OFFRE, $id_offres_mot_cle, Criteria :: IN);
 $c->addAsColumn('myordersphinx', "FIELD(" . OffOffrePeer :: ID_OFFRE . ", " . implode(',', $id_offres_mot_cle) . ")");
}*/



        switch ($array) {
                // Recherche par mot clé
            case (!empty($array[0]) && empty($array[1]) && empty($array[2])):
                $qb->select('a', 'b.libelle AS contrat', 'b.id_type_contrat', 'e.logo', 'e.raison_sociale', 'f.libelle AS departement', 'g.libelle AS region', 'i.libelle AS secteur', 'i.libelle_alternatif1 AS secteur_alternatif', 'i.id_secteur_activite')
                    ->from('App\Entity\OffOffre', 'a')
                    ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville = c.libelle')
                    ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
                    ->join('App\Entity\UserUser', 'd', 'WITH', 'a.user_user_id_user = d.id_user')
                    ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
                    ->join('App\Entity\GeoDepartement', 'f', 'WITH', 'c.geo_departement_id_departement = f.id_departement')
                    ->join('App\Entity\GeoListeRegion', 'g', 'WITH', 'c.geo_liste_region_id_region = g.id_region')
                    ->join('App\Entity\OffLienOffreListeSecteurActivite', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
                    ->join('App\Entity\OffListeSecteurActivite', 'i', 'WITH', 'h.off_liste_secteur_activite_id_secteur_activite = i.id_secteur_activite')
                    ->where('a.intitule LIKE :mot_cle')
                    ->orWhere('i.libelle LIKE :mot_cle')
                    ->setParameters(
                        new ArrayCollection([
                            new Parameter('mot_cle', '%' . $mot_cle . '%'),
                        ])
                    )
                    ->andWhere('a.statut = 1')
                    ->orderBy('a.date_creation', 'DESC');
                break;

                // Recherche par localisation
            case (empty($array[0]) && empty($array[1]) && !empty($array[2])):
                $qb->select('a', 'b.libelle AS contrat', 'b.id_type_contrat', 'e.logo', 'e.raison_sociale', 'f.libelle AS departement', 'g.libelle AS region', 'i.libelle AS secteur', 'i.libelle_alternatif1 AS secteur_alternatif', 'i.id_secteur_activite')
                    ->distinct()
                    ->from('App\Entity\OffOffre', 'a')
                    ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
                    ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
                    ->join('App\Entity\UserUser', 'd', 'WITH', 'a.user_user_id_user = d.id_user')
                    ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
                    ->join('App\Entity\GeoDepartement', 'f', 'WITH', 'c.geo_departement_id_departement = f.id_departement')
                    ->join('App\Entity\GeoListeRegion', 'g', 'WITH', 'c.geo_liste_region_id_region = g.id_region')
                    ->join('App\Entity\OffLienOffreListeSecteurActivite', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
                    ->join('App\Entity\OffListeSecteurActivite', 'i', 'WITH', 'h.off_liste_secteur_activite_id_secteur_activite = i.id_secteur_activite')
                    ->where('a.geo_ville LIKE :localisation')
                    ->orWhere('f.libelle LIKE :localisation')
                    ->orWhere('g.libelle LIKE :localisation')
                    ->setParameters(
                        new ArrayCollection([
                            new Parameter('localisation', '%' . $geo_ville . '%'),
                        ])
                    )
                    ->andWhere('a.statut = 1')
                    ->orderBy('a.date_creation', 'DESC');
                break;

                // Recherche par type de contrat
            case (empty($array[0]) && !empty($array[1]) && empty($array[2])):
                $qb->select('a', 'b.libelle AS contrat', 'b.id_type_contrat', 'e.logo', 'e.raison_sociale', 'f.libelle AS departement', 'g.libelle AS region', 'i.libelle AS secteur', 'i.libelle_alternatif1 AS secteur_alternatif', 'i.id_secteur_activite')
                    ->from('App\Entity\OffOffre', 'a')
                    ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
                    ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
                    ->join('App\Entity\UserUser', 'd', 'WITH', 'a.user_user_id_user = d.id_user')
                    ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
                    ->join('App\Entity\GeoDepartement', 'f', 'WITH', 'c.geo_departement_id_departement = f.id_departement')
                    ->join('App\Entity\GeoListeRegion', 'g', 'WITH', 'c.geo_liste_region_id_region = g.id_region')
                    ->join('App\Entity\OffLienOffreListeSecteurActivite', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
                    ->join('App\Entity\OffListeSecteurActivite', 'i', 'WITH', 'h.off_liste_secteur_activite_id_secteur_activite = i.id_secteur_activite')
                    ->where('a.off_liste_type_contrat_id_type_contrat LIKE :id_type_contrat')
                    ->setParameters(
                        new ArrayCollection([
                            new Parameter('id_type_contrat', $id_type_contrat),
                        ])
                    )
                    ->andWhere('a.statut = 1')
                    ->orderBy('a.date_creation', 'DESC');
                break;

                // Recherche par mot clé && type de contrat
            case (!empty($array[0]) && !empty($array[1]) && empty($array[2])):
                $qb->select('a', 'b.libelle AS contrat', 'b.id_type_contrat', 'e.logo', 'e.raison_sociale', 'f.libelle AS departement', 'g.libelle AS region', 'i.libelle AS secteur', 'i.libelle_alternatif1 AS secteur_alternatif', 'i.id_secteur_activite')
                    ->from('App\Entity\OffOffre', 'a')
                    ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
                    ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')

                    ->where('a.off_liste_type_contrat_id_type_contrat = :id_type_contrat')

                    ->join('App\Entity\UserUser', 'd', 'WITH', 'a.user_user_id_user = d.id_user')
                    ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
                    ->join('App\Entity\GeoDepartement', 'f', 'WITH', 'c.geo_departement_id_departement = f.id_departement')
                    ->join('App\Entity\GeoListeRegion', 'g', 'WITH', 'c.geo_liste_region_id_region = g.id_region')
                    ->join('App\Entity\OffLienOffreListeSecteurActivite', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
                    ->join('App\Entity\OffListeSecteurActivite', 'i', 'WITH', 'h.off_liste_secteur_activite_id_secteur_activite = i.id_secteur_activite')
                    ->where('a.intitule LIKE :mot_cle')
                    ->orWhere('i.libelle LIKE :mot_cle')
                    ->andwhere('a.off_liste_type_contrat_id_type_contrat = :id_type_contrat')
                    ->setParameters(
                        new ArrayCollection([
                            new Parameter('mot_cle', '%' . $mot_cle . '%'),
                            new Parameter('id_type_contrat', $id_type_contrat),
                        ])
                    )
                    ->andWhere('a.statut = 1')
                    ->orderBy('a.date_creation', 'DESC');
                break;

                // Recherche par mot clé && par localisation
            case (!empty($array[0]) && empty($array[1]) && !empty($array[2])):
                $qb->select('a', 'b.libelle AS contrat', 'b.id_type_contrat', 'e.logo', 'e.raison_sociale', 'f.libelle AS departement', 'g.libelle AS region', 'i.libelle AS secteur', 'i.libelle_alternatif1 AS secteur_alternatif', 'i.id_secteur_activite')
                    ->from('App\Entity\OffOffre', 'a')
                    ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
                    ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
                    ->join('App\Entity\UserUser', 'd', 'WITH', 'a.user_user_id_user = d.id_user')
                    ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
                    ->join('App\Entity\GeoDepartement', 'f', 'WITH', 'c.geo_departement_id_departement = f.id_departement')
                    ->join('App\Entity\GeoListeRegion', 'g', 'WITH', 'c.geo_liste_region_id_region = g.id_region')
                    ->join('App\Entity\OffLienOffreListeSecteurActivite', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
                    ->join('App\Entity\OffListeSecteurActivite', 'i', 'WITH', 'h.off_liste_secteur_activite_id_secteur_activite = i.id_secteur_activite')
                    ->where('a.geo_ville LIKE :localisation')
                    ->orWhere('f.libelle LIKE :localisation')
                    ->orWhere('g.libelle LIKE :localisation')
                    ->andWhere('a.intitule LIKE :mot_cle')
                    ->orWhere('i.libelle LIKE :mot_cle')
                    ->setParameters(
                        new ArrayCollection([
                            new Parameter('mot_cle', '%' . $mot_cle . '%'),
                            new Parameter('localisation', '%' . $geo_ville . '%'),
                        ])
                    )
                    ->andWhere('a.statut = 1')
                    ->orderBy('a.date_creation', 'DESC');
                break;

                // Recherche par type contrat && localisation
            case (!empty($array[0]) && !empty($array[1]) && !empty($array[2])):
                $qb->select('a', 'b.libelle AS contrat', 'b.id_type_contrat', 'e.logo', 'e.raison_sociale', 'f.libelle AS departement', 'g.libelle AS region', 'i.libelle AS secteur', 'i.libelle_alternatif1 AS secteur_alternatif', 'i.id_secteur_activite')
                    ->from('App\Entity\OffOffre', 'a')
                    ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
                    ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
                    ->join('App\Entity\UserUser', 'd', 'WITH', 'a.user_user_id_user = d.id_user')
                    ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
                    ->join('App\Entity\GeoDepartement', 'f', 'WITH', 'c.geo_departement_id_departement = f.id_departement')
                    ->join('App\Entity\GeoListeRegion', 'g', 'WITH', 'c.geo_liste_region_id_region = g.id_region')
                    ->join('App\Entity\OffLienOffreListeSecteurActivite', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
                    ->join('App\Entity\OffListeSecteurActivite', 'i', 'WITH', 'h.off_liste_secteur_activite_id_secteur_activite = i.id_secteur_activite')
                    ->where('a.geo_ville LIKE :localisation')
                    ->orWhere('f.libelle LIKE :localisation')
                    ->orWhere('g.libelle LIKE :localisation')
                    ->andwhere('a.off_liste_type_contrat_id_type_contrat = :id_type_contrat')
                    ->setParameters(
                        new ArrayCollection([
                            new Parameter('id_type_contrat', $id_type_contrat),
                            new Parameter('localisation', '%' . $geo_ville . '%'),
                        ])
                    )
                    ->andWhere('a.statut = 1')
                    ->orderBy('a.date_creation', 'DESC');
                break;

                // Recherche complète
            case (!empty($array[0]) && !empty($array[1]) && !empty($array[2])):
                $qb->select('a', 'b.libelle AS contrat', 'b.id_type_contrat', 'e.logo', 'e.raison_sociale', 'f.libelle AS departement', 'g.libelle AS region', 'i.libelle AS secteur', 'i.libelle_alternatif1 AS secteur_alternatif', 'i.id_secteur_activite')
                    ->from('App\Entity\OffOffre', 'a')
                    ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
                    ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
                    ->join('App\Entity\UserUser', 'd', 'WITH', 'a.user_user_id_user = d.id_user')
                    ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
                    ->join('App\Entity\GeoDepartement', 'f', 'WITH', 'c.geo_departement_id_departement = f.id_departement')
                    ->join('App\Entity\GeoListeRegion', 'g', 'WITH', 'c.geo_liste_region_id_region = g.id_region')
                    ->join('App\Entity\OffLienOffreListeSecteurActivite', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
                    ->join('App\Entity\OffListeSecteurActivite', 'i', 'WITH', 'h.off_liste_secteur_activite_id_secteur_activite = i.id_secteur_activite')
                    ->where('a.geo_ville LIKE :localisation')
                    ->orWhere('f.libelle LIKE :localisation')
                    ->orWhere('g.libelle LIKE :localisation')
                    ->andwhere('a.off_liste_type_contrat_id_type_contrat = :id_type_contrat')
                    ->andWhere('a.intitule LIKE :mot_cle')
                    ->orWhere('i.libelle LIKE :mot_cle')
                    ->setParameters(
                        new ArrayCollection([
                            new Parameter('mot_cle', '%' . $mot_cle . '%'),
                            new Parameter('id_type_contrat', $id_type_contrat),
                            new Parameter('localisation', '%' . $geo_ville . '%'),
                        ])
                    )
                    ->andWhere('a.statut = 1')
                    ->orderBy('a.date_creation', 'DESC');
                break;

                // Recherche par défaut
            default:
                $qb->select('a', 'b.libelle AS contrat', 'b.id_type_contrat', 'e.logo', 'e.raison_sociale', 'f.libelle AS departement', 'g.libelle AS region', 'i.libelle AS secteur', 'i.libelle_alternatif1 AS secteur_alternatif', 'i.id_secteur_activite')
                    ->from('App\Entity\OffOffre', 'a')
                    ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
                    ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
                    ->join('App\Entity\UserUser', 'd', 'WITH', 'a.user_user_id_user = d.id_user')
                    ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
                    ->join('App\Entity\GeoDepartement', 'f', 'WITH', 'c.geo_departement_id_departement = f.id_departement')
                    ->join('App\Entity\GeoListeRegion', 'g', 'WITH', 'c.geo_liste_region_id_region = g.id_region')
                    ->join('App\Entity\OffLienOffreListeSecteurActivite', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
                    ->join('App\Entity\OffListeSecteurActivite', 'i', 'WITH', 'h.off_liste_secteur_activite_id_secteur_activite = i.id_secteur_activite')
                    ->setParameters(
                        new ArrayCollection([
                            new Parameter(':date', $date_valides),
                    ]))
                    ->where('a.statut = 1')
                    ->andWhere('a.date_soumission > :date')
                    ->orderBy('a.date_creation', 'DESC');
        }

        $query = $qb->getQuery();

        return $query->execute();
    }


    // Détails d'une offre
    public function candidatOffreDetails(int $id)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select(
            'a',
            'b.libelle AS libelleContrat',
            'b.id_type_contrat',
            'd.id_entreprise',
            'd.presentation_societe',
            'd.logo',
            'd.raison_sociale',
            'd.site_web',
            'e.adresse1',
            'e.code_postal',
            'e.nom_ville',
        )
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\UserUser', 'c', 'WITH', 'a.user_user_id_user = c.id_user')
            ->join('App\Entity\EntrEntreprise', 'd', 'WITH', 'c.entr_entreprise_id_entreprise = d.id_entreprise')
            ->join('App\Entity\GeoAdresse', 'e', 'WITH', 'd.geo_adresse_id_adresse = e.id_adresse')

            // // Pour avoir le secteur d'activité
            // ->join('App\Entity\OffLienOffreListeSousSecteurActivite', 'j', 'WITH', 'j.off_offre_id_offre = a.id_offre')
            // ->join('App\Entity\OffListeSousSecteurActivite', 'k', 'WITH', 'k.id_sous_secteur_activite = j.off_liste_sous_secteur_activite_id_sous_secteur_activite')

            // Pour avoir la fourchette de rémunération
            // ->join('App\Entity\OffFourchetteRemuneration', 'e', 'WITH', 'a.off_fourchette_remuneration_id_fourchette_remuneration = e.id_fourchette_remuneration')

            // Pour avoir l'expérience requise
            // ->join('App\Entity\OffLienOffreListeExperienceRequise', 'f', 'WITH', 'f.off_offre_id_offre = a.id_offre')
            // ->join('App\Entity\OffListeExperienceRequise', 'g', 'WITH', 'g.id_experience_requise = f.off_liste_experience_requise_id_experience_requise')

            // Pour avoir la formation requise
            // ->join('App\Entity\OffLienOffreListeTypeFormation', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
            // ->join('App\Entity\OffListeTypeFormation', 'i', 'WITH', 'i.id_type_formation = h.off_liste_type_formation_id_type_formation')

            ->where('a.id_offre = :id')
            ->setParameter(':id', $id)
            ;

        $query = $qb->getQuery();

        return $query->getOneOrNullResult();
    }
    /*
    public function GetListVilleFromMetier(string $metier)
    {
        $metier = str_replace("Offre d'emploi ", "", $metier);
        $em = $this->getEntityManager();
        $connection = $em->getConnection();

        $sql = "SELECT DISTINCT oo.geo_ville 
            FROM off_offre oo
            WHERE oo.intitule LIKE :metier
            AND oo.date_creation >= :date
            AND oo.geo_ville IS NOT NULL
            LIMIT 50";

        $fixedDate = '2024-01-01'; // ou utiliser new \DateTime('2024-01-01') si la date doit être générée dynamiquement

        $stmt = $connection->executeQuery($sql, [
            'metier' => '%' . $metier . '%',
            'date' => $fixedDate
        ]);
        return $stmt->fetchAllAssociative();
    }
*/





    public function candidatOffresParSecteurAvancee(array $array_of_id)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a', 'd.libelle AS contrat', 'd.id_type_contrat', 'f.logo', 'f.raison_sociale', 'h.libelle AS secteur', 'h.libelle_alternatif1 AS secteur_alternatif', 'i.libelle AS region', 'j.libelle AS departement', 'h.id_secteur_activite')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
            ->join('App\Entity\GeoDepartement', 'b', 'WITH', 'c.geo_departement_id_departement = b.id_departement')
            ->join('App\Entity\OffListeTypeContrat', 'd', 'WITH', 'd.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\UserUser', 'e', 'WITH', 'a.user_user_id_user = e.id_user')
            ->join('App\Entity\EntrEntreprise', 'f', 'WITH', 'f.id_entreprise = e.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'g', 'WITH', 'g.off_offre_id_offre = a.id_offre')
            ->join('App\Entity\OffListeSecteurActivite', 'h', 'WITH', 'g.off_liste_secteur_activite_id_secteur_activite = h.id_secteur_activite')
            ->join('App\Entity\GeoListeRegion', 'i', 'WITH', 'c.geo_liste_region_id_region = i.id_region')
            ->join('App\Entity\GeoDepartement', 'j', 'WITH', 'c.geo_departement_id_departement = j.id_departement')
            ->where('g.off_liste_secteur_activite_id_secteur_activite IN (:arrayOfId)')
            ->andWhere('a.statut = 1')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('arrayOfId', $array_of_id),
                ])
            );

        $query = $qb->getQuery();

        return $query->execute();
    }

    public function candidatOffreContrat($id_type_contrat)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a', 'b.libelle AS contrat', 'b.id_type_contrat', 'e.logo', 'e.raison_sociale', 'f.libelle AS departement', 'g.libelle AS region', 'i.libelle AS secteur', 'i.libelle_alternatif1 AS secteur_alternatif', 'i.id_secteur_activite')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
            ->join('App\Entity\UserUser', 'd', 'WITH', 'a.user_user_id_user = d.id_user')
            ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
            ->join('App\Entity\GeoDepartement', 'f', 'WITH', 'c.geo_departement_id_departement = f.id_departement')
            ->join('App\Entity\GeoListeRegion', 'g', 'WITH', 'c.geo_liste_region_id_region = g.id_region')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
            ->join('App\Entity\OffListeSecteurActivite', 'i', 'WITH', 'h.off_liste_secteur_activite_id_secteur_activite = i.id_secteur_activite')
            ->where('a.off_liste_type_contrat_id_type_contrat LIKE :id_type_contrat')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('id_type_contrat', $id_type_contrat),
                ])
            )
            ->andWhere('a.statut = 1')
            ->orderBy('a.date_creation', 'DESC');

        $query = $qb->getQuery();

        return $query->execute();
    }

    public function emploiOffresParSecteur($secteur)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a', 'd.libelle AS contrat', 'd.id_type_contrat', 'f.logo', 'f.raison_sociale', 'h.libelle AS secteur', 'h.libelle_alternatif1 AS secteur_alternatif', 'i.libelle AS region', 'j.libelle AS departement', 'h.id_secteur_activite')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
            ->join('App\Entity\GeoDepartement', 'b', 'WITH', 'c.geo_departement_id_departement = b.id_departement')
            ->join('App\Entity\OffListeTypeContrat', 'd', 'WITH', 'd.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\UserUser', 'e', 'WITH', 'a.user_user_id_user = e.id_user')
            ->join('App\Entity\EntrEntreprise', 'f', 'WITH', 'f.id_entreprise = e.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'g', 'WITH', 'g.off_offre_id_offre = a.id_offre')
            ->join('App\Entity\OffListeSecteurActivite', 'h', 'WITH', 'g.off_liste_secteur_activite_id_secteur_activite = h.id_secteur_activite')
            ->join('App\Entity\GeoListeRegion', 'i', 'WITH', 'c.geo_liste_region_id_region = i.id_region')
            ->join('App\Entity\GeoDepartement', 'j', 'WITH', 'c.geo_departement_id_departement = j.id_departement')
            ->where('h.id_secteur_activite = :secteur')
            ->andWhere('a.statut = 1')
            ->setParameter('secteur', $secteur);

        $query = $qb->getQuery();

        return $query->execute();
    }

    public function emploiOffresParDepartement($departement)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select(
            'a',
            'd.libelle AS contrat',
            'd.id_type_contrat',
            'f.logo',
            'f.raison_sociale',
            'h.libelle AS secteur',
            'h.libelle_alternatif1 AS secteur_alternatif',
            'i.libelle AS region',
            'j.libelle AS departement',
            'h.id_secteur_activite',
            'b.id_departement'
        )
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
            ->join('App\Entity\GeoDepartement', 'b', 'WITH', 'c.geo_departement_id_departement = b.id_departement')
            ->join('App\Entity\OffListeTypeContrat', 'd', 'WITH', 'd.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\UserUser', 'e', 'WITH', 'a.user_user_id_user = e.id_user')
            ->join('App\Entity\EntrEntreprise', 'f', 'WITH', 'f.id_entreprise = e.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'g', 'WITH', 'g.off_offre_id_offre = a.id_offre')
            ->join('App\Entity\OffListeSecteurActivite', 'h', 'WITH', 'g.off_liste_secteur_activite_id_secteur_activite = h.id_secteur_activite')
            ->join('App\Entity\GeoListeRegion', 'i', 'WITH', 'c.geo_liste_region_id_region = i.id_region')
            ->join('App\Entity\GeoDepartement', 'j', 'WITH', 'c.geo_departement_id_departement = j.id_departement')
            ->where('b.libelle LIKE :departement')
            ->andWhere('a.statut = 1')
            ->setParameter('departement', $departement);

        $query = $qb->getQuery();

        return $query->execute();
    }

    public function emploiOffresParRegion($region)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select(
            'a',
            'd.libelle AS contrat',
            'd.id_type_contrat',
            'f.logo',
            'f.raison_sociale',
            'h.libelle AS secteur',
            'h.libelle_alternatif1 AS secteur_alternatif',
            'i.libelle AS region',
            'j.libelle AS departement',
            'h.id_secteur_activite',
            'i.id_region'
        )
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
            ->join('App\Entity\GeoDepartement', 'b', 'WITH', 'c.geo_departement_id_departement = b.id_departement')
            ->join('App\Entity\OffListeTypeContrat', 'd', 'WITH', 'd.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\UserUser', 'e', 'WITH', 'a.user_user_id_user = e.id_user')
            ->join('App\Entity\EntrEntreprise', 'f', 'WITH', 'f.id_entreprise = e.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'g', 'WITH', 'g.off_offre_id_offre = a.id_offre')
            ->join('App\Entity\OffListeSecteurActivite', 'h', 'WITH', 'g.off_liste_secteur_activite_id_secteur_activite = h.id_secteur_activite')
            ->join('App\Entity\GeoListeRegion', 'i', 'WITH', 'c.geo_liste_region_id_region = i.id_region')
            ->join('App\Entity\GeoDepartement', 'j', 'WITH', 'c.geo_departement_id_departement = j.id_departement')
            ->where('i.libelle LIKE :region')
            ->andWhere('a.statut = 1')
            ->setParameter('region', $region);

        $query = $qb->getQuery();

        return $query->execute();
    }


    public function emploiOffresParVille($ville)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a', 'd.libelle AS contrat', 'd.id_type_contrat', 'f.logo', 'f.raison_sociale', 
        'h.libelle AS secteur', 'h.libelle_alternatif1 AS secteur_alternatif', 'i.libelle AS region',
         'j.libelle AS departement', 'h.id_secteur_activite', 'i.id_region')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
            ->join('App\Entity\GeoDepartement', 'b', 'WITH', 'c.geo_departement_id_departement = b.id_departement')
            ->join('App\Entity\OffListeTypeContrat', 'd', 'WITH', 'd.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\UserUser', 'e', 'WITH', 'a.user_user_id_user = e.id_user')
            ->join('App\Entity\EntrEntreprise', 'f', 'WITH', 'f.id_entreprise = e.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'g', 'WITH', 'g.off_offre_id_offre = a.id_offre')
            ->join('App\Entity\OffListeSecteurActivite', 'h', 'WITH', 'g.off_liste_secteur_activite_id_secteur_activite = h.id_secteur_activite')
            ->join('App\Entity\GeoListeRegion', 'i', 'WITH', 'c.geo_liste_region_id_region = i.id_region')
            ->join('App\Entity\GeoDepartement', 'j', 'WITH', 'c.geo_departement_id_departement = j.id_departement')
            ->where('a.geo_ville LIKE :region')
            ->andWhere('a.statut = 1')
            ->setParameter('region', '%'.$ville.'%');

        $query = $qb->getQuery();

        return $query->execute();
    }

    // FONCTION SCRIPT
    public function ToolsServicegetSupprimerOffres($idsOffresToConserve, $id_user)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a')
            ->from('App\Entity\OffOffre', 'a')
            ->where($qb->expr()->notIn('a.id_offre', ':idsOffresToConserve'))
            ->andWhere('a.user_user_id_user = :id_user')
            ->andWhere('a.statut != -2')
            ->setParameter('idsOffresToConserve', $idsOffresToConserve)
            ->setParameter('id_user', $id_user);

        $query = $qb->getQuery();

        return $query->getResult();
    }


    public function getOffresTries($id_user, $id_offres_valides)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $statutCondition = $id_user == 1041540 ? 'a.statut = 1' : 'a.statut != -2';
        
        $qb->select('a.intitule',
                    'a.id_offre',
                    'a.date_creation',
                    'a.date_soumission',
                    'a.statut',
                    'a.off_liste_type_contrat_id_type_contrat as id_type_contrat',
                    'a.reference',
                    'a.com_offre_commerciale_id_offre_commerciale as id_offre_commerciale',
                    'b.libelle as type_offre',
                    )
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = b.id_type_contrat')
            ->where('a.user_user_id_user = (:id_user)')
            //->andWhere('a.com_offre_commerciale_id_offre_commerciale IN (:id_offres_valides)')
            ->andWhere($statutCondition)
            ->orderBy('a.date_soumission', 'DESC')
            ->setParameter('id_user', $id_user)
            //->setParameter('id_offres_valides', $id_offres_valides)
            ->setMaxResults(100)
            
        ;
                
        $query = $qb->getQuery();
        $candid_offre = $query->getScalarResult();
        return ($candid_offre);
    }
    public function getOffresPerWeek()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('a.intitule',
            'a.off_liste_type_contrat_id_type_contrat as id_type_contrat',
            'a.geo_ville',
            'a.date_creation',
            'a.statut'
        )
            ->from('App\Entity\OffOffre', 'a')
            ->where('a.date_creation >= :date_semaine')
            ->andWhere('a.statut = 1')
            ->setParameter('date_semaine', new \DateTime('-7 days'));

        return $qb->getQuery()->getResult();
    }
    public function getVilleOffresPerWeek()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select(
            'DISTINCT a.geo_ville'
        )
            ->from('App\Entity\OffOffre', 'a')
            ->where('a.date_creation >= :date_semaine')
            ->andWhere('a.statut = 1')
            ->setParameter('date_semaine', new \DateTime('-7 days'));

        return $qb->getQuery()->getResult();
    }



    public function offreSearchRecrut($id_user, $array_param)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a.intitule',
                    'a.id_offre',
                    'a.date_creation',
                    'a.date_soumission',
                    'a.statut',
                    'a.off_liste_type_contrat_id_type_contrat as id_type_contrat',
                    'a.reference',
                    'a.com_offre_commerciale_id_offre_commerciale as id_offre_commerciale',
                    'b.libelle as type_offre',
                    )
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = b.id_type_contrat')
            ->where('a.user_user_id_user = :id_user');

            if($array_param["mc"]){
                $qb->andWhere('a.intitule LIKE :ref OR a.reference LIKE :ref');
                $qb->setParameter('ref', "%".$array_param['mc']."%");
            }


            
            if($array_param["etat_offre"] == 2) {
                $qb->andWhere('a.statut = 0');
            } elseif($array_param["etat_offre"] == 1) {
                $qb->andWhere('a.statut = 1');
            }else {
                if($id_user == 1041540) {
                    $qb->andWhere('a.statut = 1');
                }
            }

            if($array_param["type_offre"]){
                $qb->andWhere('a.off_liste_type_contrat_id_type_contrat = :contrat');
                $qb->setParameter('contrat', $array_param['type_offre']);
            } 
            
            $qb->setParameter('id_user', $id_user)
            ->setMaxResults(100);    

            
            if($array_param["tri_offre"]){
                switch ($array_param["tri_offre"]) {
                    case 3:
                        $qb->orderBy('a.intitule', 'ASC');
                        break;
                    case 4:
                        $qb->orderBy('a.intitule', 'DESC');
                        break;
                    case 5:
                        $qb->orderBy('a.date_creation', 'DESC');
                        break;
                    case 6:
                        $qb->orderBy('a.date_creation', 'ASC');
                        break;       
                }
            }
         
        
                
        $query = $qb->getQuery();
        $candid_offre = $query->getScalarResult();
        return ($candid_offre);
    }

    public function ScriptSuppressionOffres($key)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        
        $qb->select('a')
            ->from('App\Entity\OffOffre', 'a')
            ->where('a.user_user_id_user = :key')
            ->andWhere('a.statut != -2')
            ->setParameter('key', $key);
            
        $query = $qb->getQuery();
        return $query->execute();
    }

    public function getOfferDatas($table, $collumn, $data, $wanted)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select("a.$wanted")
            ->from("App\Entity\\$table", 'a')
            ->where("a.$collumn = $data")
            ;
        $query = $qb->getQuery();
        $result = $query->execute();

        if (count($result) == 1)
            $result = $result[0][$wanted];
        else if (count($result) > 1) {
            $arr_to_str = null;
            for ($i = 0; $i < count($result); $i++) {
                $arr_to_str .= $result[$i][$wanted];
                if ($i != count($result) - 1)
                    $arr_to_str .= "_";
            }
            $result = $arr_to_str;
        } else
            $result = null;

        return $result;
    }

    public function getNbOffresValidesByType($id_user, $type)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('count(a.id_offre)')
            ->setParameter(':id_user', $id_user)
            ->from("App\Entity\OffOffre", 'a')
            ->join("App\Entity\OffListeTypeContrat", 'b', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = b.id_type_contrat')
            ->where('a.user_user_id_user = :id_user')
            ->andWhere("a.statut != -2")
            ->andWhere("b.type_general = :type")
            ->setParameter(':type', $type)
            ;
        $query = $qb->getQuery();
        $result = $query->getSingleScalarResult();

        return $result;
    }

    public function getOffresZoomSectoriel($id_secteur)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'b', 'WITH', 'a.id_offre = b.off_offre_id_offre')
            ->join('App\Entity\OffListeSecteurActivite', 'c', 'WITH', 'b.off_liste_secteur_activite_id_secteur_activite = c.id_secteur_activite')
            ->where('c.id_secteur_activite = :id_secteur')
            ->setParameter('id_secteur', $id_secteur);

        $query = $qb->getQuery();
        
        return $query->execute();
    }

    public function getNbOffres()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->where('a.statut = 1')
            ->andWhere('a.sitemap_date_fin_validite > :date')
            ->setParameter('date', new \DateTime('now'));
        
        return $qb->getQuery()->getSingleScalarResult();
    }

    public function getNbOffresRepMail()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->where('a.statut = 1')
            ->andWhere('a.sitemap_date_fin_validite > :date')
            ->andWhere('a.rep_mail != :mail')
            ->setParameter('mail', '')
            ->setParameter('date', new \DateTime('now'));
        
        return $qb->getQuery()->getSingleScalarResult();
    }

    public function getNbOffresRepUrl()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->where('a.statut = 1')
            ->andWhere('a.sitemap_date_fin_validite > :date')
            ->andWhere('a.rep_url != :url')
            ->setParameter('url', '')
            ->setParameter('date', new \DateTime('now'));
        
        return $qb->getQuery()->getSingleScalarResult();
    }

    public function getNbOffresStage()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->where('a.statut = 1')
            ->andWhere('a.off_liste_type_contrat_id_type_contrat IN (5,7,8,9)');
       
        return $qb->getQuery()->getSingleScalarResult();
    }

    public function getNbOffresAlternance()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->where('a.statut = 1')
            ->andWhere("a.off_liste_type_contrat_id_type_contrat = 6");
       
        return $qb->getQuery()->getSingleScalarResult();
    }

    public function getNbOffresInterim()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->where('a.statut = 1')
            ->andWhere('a.off_liste_type_contrat_id_type_contrat = 4');
       
        return $qb->getQuery()->getSingleScalarResult();
    }

    public function getNbOffresCabinetRecrut()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->where('a.statut = 1')
            ->andWhere('a.off_liste_type_contrat_id_type_contrat = 2');
       
        return $qb->getQuery()->getSingleScalarResult();
    }

    public function repartitionByEntr()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        
        $qb->select('c.raison_sociale' , 'b.id_user', 'count(a.id_offre) as nbr')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\UserUser', 'b', 'WITH', 'a.user_user_id_user = b.id_user')
            ->join('App\Entity\EntrEntreprise', 'c', 'WITH', 'c.id_entreprise = b.entr_entreprise_id_entreprise')
            ->where('a.statut = 1')
            ->groupBy('a.user_user_id_user')
            ->orderBy('nbr', 'DESC')      
        ;
                
        $query = $qb->getQuery();
        $off_nbr = $query->getArrayResult();
        return ($off_nbr);
    }

    public function offFrance()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        
        $qb->select('c.raison_sociale', 'b.id_user','count(a.id_offre) as nbr')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\UserUser', 'b', 'WITH', 'a.user_user_id_user = b.id_user')
            ->join('App\Entity\EntrEntreprise', 'c', 'WITH', 'c.id_entreprise = b.entr_entreprise_id_entreprise')
            ->where("a.geo_ville = 'France'")
            ->andWhere('a.statut = 1')
            ->groupBy('a.user_user_id_user')
            ->orderBy('nbr', 'DESC')      
        ;
                
        $query = $qb->getQuery();
        $off_france = $query->getArrayResult();
        return ($off_france);
    }

    public function getNbOffresBTP()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'b', 'WITH', 'a.id_offre = b.off_offre_id_offre')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = c.id_type_contrat')
            ->where('b.off_liste_secteur_activite_id_secteur_activite IN (4, 11, 12, 18, 46)')
            ->andWhere('a.statut = 1')
            ->andWhere('a.date_creation > :date')
            ->orWhere('a.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2')
            ->andWhere('a.date_creation > :date2')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('date', new \DateTime('-1 month')),
                    new Parameter('date2', new \DateTime('-1 year')),
                ])
            );
        
        return $qb->getQuery()->getSingleScalarResult();
    }

    public function getNbOffresByCodeSecteur(array $codes): array
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('b.off_liste_secteur_activite_id_secteur_activite AS code, COUNT(a.id_offre) AS count')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'b', 'WITH', 'a.id_offre = b.off_offre_id_offre')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = c.id_type_contrat')
            ->where('b.off_liste_secteur_activite_id_secteur_activite IN (:codes)')
            ->andWhere('a.statut = 1')
            ->andWhere('a.date_creation > :date')
            ->andWhere('a.date_creation > :date2')
            ->groupBy('b.off_liste_secteur_activite_id_secteur_activite')
            ->setParameters([
                'codes' => $codes,
                'date' => new \DateTime('-1 month'),
                'date2' => new \DateTime('-1 year'),
            ]);

        $results = $qb->getQuery()->getResult();

        // Convert the results into an associative array [code => count]
        $offreCounts = [];
        foreach ($results as $result) {
            $offreCounts[$result['code']] = $result['count'];
        }

        return $offreCounts;
    }
    public function getNbOffresDistribution()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'b', 'WITH', 'a.id_offre = b.off_offre_id_offre')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = c.id_type_contrat')
            ->where('b.off_liste_secteur_activite_id_secteur_activite IN (16, 17, 47, 48)')
            ->andWhere('a.statut = 1')
            ->andWhere('a.date_creation > :date')
            ->orWhere('a.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2')
            ->andWhere('a.date_creation > :date2')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('date', new \DateTime('-1 month')),
                    new Parameter('date2', new \DateTime('-1 year')),
                ])
            );

        return $qb->getQuery()->getSingleScalarResult();
    }
    public function getNbOffresIndustrie()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'b', 'WITH', 'a.id_offre = b.off_offre_id_offre')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = c.id_type_contrat')
            ->where('b.off_liste_secteur_activite_id_secteur_activite IN (5, 2, 11, 20, 22, 24, 29, 39, 23)')
            ->andWhere('a.statut = 1')
            ->andWhere('a.date_creation > :date')
            ->orWhere('a.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2')
            ->andWhere('a.date_creation > :date2')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('date', new \DateTime('-1 month')),
                    new Parameter('date2', new \DateTime('-1 year')),
                ])
            );

        return $qb->getQuery()->getSingleScalarResult();
    }
    public function getNbOffresInformatique()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'b', 'WITH', 'a.id_offre = b.off_offre_id_offre')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = c.id_type_contrat')
            ->where('b.off_liste_secteur_activite_id_secteur_activite IN (10, 11, 19, 27)')
            ->andWhere('a.statut = 1')
            ->andWhere('a.date_creation > :date')
            ->orWhere('a.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2')
            ->andWhere('a.date_creation > :date2')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('date', new \DateTime('-1 month')),
                    new Parameter('date2', new \DateTime('-1 year')),
                ])
            );

        return $qb->getQuery()->getSingleScalarResult();
    }

    public function getNbOffresAlternance2()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'b', 'WITH', 'a.id_offre = b.off_offre_id_offre')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = c.id_type_contrat')
            ->andWhere('a.statut = 1')
            ->andWhere('a.off_liste_type_contrat_id_type_contrat = 6')
            ->andWhere('a.date_creation > :date')
            ->orWhere('a.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2')
            ->andWhere('a.date_creation > :date2')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('date', new \DateTime('-1 month')),
                    new Parameter('date2', new \DateTime('-1 year')),
                ])
            );

        return $qb->getQuery()->getSingleScalarResult();
    }
    public function getNbOffresStage2()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'b', 'WITH', 'a.id_offre = b.off_offre_id_offre')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = c.id_type_contrat')
            ->andWhere('a.statut = 1')
            ->andWhere('a.off_liste_type_contrat_id_type_contrat = 8')
            ->andWhere('a.date_creation > :date')
            ->orWhere('a.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2')
            ->andWhere('a.date_creation > :date2')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('date', new \DateTime('-1 month')),
                    new Parameter('date2', new \DateTime('-1 year')),
                ])
            );

        return $qb->getQuery()->getSingleScalarResult();
    }
    public function getNbOffresRestauration()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'b', 'WITH', 'a.id_offre = b.off_offre_id_offre')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = c.id_type_contrat')
            ->where('b.off_liste_secteur_activite_id_secteur_activite IN (17,28)')
            ->andWhere('a.statut = 1')
            ->andWhere('a.date_creation > :date')
            ->orWhere('a.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2')
            ->andWhere('a.date_creation > :date2')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('date', new \DateTime('-1 month')),
                    new Parameter('date2', new \DateTime('-1 year')),
                ])
            );

        return $qb->getQuery()->getSingleScalarResult();
    }

    // public function getNbOffresBTP()
    // {
    //     $em = $this->getEntityManager();
    //     $qb = $em->createQueryBuilder();

    //     $qb->select('count(a.id_offre)')
    //         ->from('App\Entity\OffOffre', 'a')
    //         ->where('a.statut = 1');

    //     return $qb->getQuery()->getSingleScalarResult();
    // }

    public function getOffreMomentBtpBouygues()
    {
        // Prendre une offre aléatoire pour Bouygues
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->setParameter('id_entreprise', '17756')
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }


        public function getOffreMomentBtpHorizonJob()
    {
        // Prendre une offre aléatoire pour NGE
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->andWhere('oo.sitemap_carriere_btp = 1')
            ->setParameter('id_entreprise', '55648')
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }

        public function getOffreMomentBtpBaudin()
    {
        // Prendre une offre aléatoire pour SPIE
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();


        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->setParameter('id_entreprise', '55606')
            ->setMaxResults(3);

        $query = $qb->getQuery(); 
        $results = $query->getResult();
        return $results;
    }

    public function getOffreMomentBtpNgeIng()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->andWhere('oo.intitule LIKE :ingenieur OR oo.intitule LIKE :ingenieurAlt')
            ->andWhere('oo.intitule NOT LIKE :stage AND oo.intitule NOT LIKE :alternance AND oo.intitule NOT LIKE :stages')
            ->setParameter('id_entreprise', '55650')
            ->setParameter('ingenieur', '%poste de directeur%')
            ->setParameter('ingenieurAlt', '%responsable%')
            ->setParameter('stage', '%stage%')
            ->setParameter('alternance', '%alternance%')
            ->setParameter('stages', '%stages%')
            ->setMaxResults(6);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }

    public function getOffreMomentMichaelPage()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->andWhere('oo.intitule LIKE :ingenieur OR oo.intitule LIKE :ingenieurAlt')
            ->andWhere('oo.intitule NOT LIKE :stage AND oo.intitule NOT LIKE :alternance AND oo.intitule NOT LIKE :stages')
            ->setParameter('id_entreprise', '53419')
            ->setParameter('ingenieur', '%poste de directeur%')
            ->setParameter('ingenieurAlt', '%responsable%')
            ->setParameter('stage', '%stage%')
            ->setParameter('alternance', '%alternance%')
            ->setParameter('stages', '%stages%')
            ->setMaxResults(6);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }
    public function getOffreMomentRestauration()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->andWhere('oo.intitule LIKE :ingenieur OR oo.intitule LIKE :ingenieurAlt')
            ->andWhere('oo.intitule NOT LIKE :stage AND oo.intitule NOT LIKE :alternance AND oo.intitule NOT LIKE :stages')
            ->setParameter('id_entreprise', '55587')
            ->setParameter('ingenieur', '%poste de directeur%')
            ->setParameter('ingenieurAlt', '%responsable%')
            ->setParameter('stage', '%stage%')
            ->setParameter('alternance', '%alternance%')
            ->setParameter('stages', '%stages%')
            ->setMaxResults(6);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }
    public function getOffreMomentISCOD()
    {
        // Prendre une offre aléatoire pour Bouygues
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->setParameter('id_entreprise', '49545')
            ->andWhere($qb->expr()->in('oo.off_liste_type_contrat_id_type_contrat', [6, 7, 8]))
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }
    private function sendElasticsearchRequest(array $body): array
    {
        $url = rtrim($_ENV['ELK_URL'], '/') . '/direct-emploi-dev2/_search';
        $auth = $_ENV['ELK_BASIC_AUTH_USER'] . ':' . $_ENV['ELK_BASIC_AUTH_PASSWORD'];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
        ]);
        curl_setopt($ch, CURLOPT_USERPWD, $auth);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Disable SSL verification (if needed)

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new \Exception('cURL error: ' . curl_error($ch));
        }
        curl_close($ch);

        // Log the full response for debugging
        //error_log('Elasticsearch Response: ' . $response);

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Invalid JSON response from Elasticsearch');
        }

        if (isset($decodedResponse['error'])) {
            throw new \Exception('Elasticsearch error: ' . $decodedResponse['error']['reason']);
        }

        return $decodedResponse;
    }
    public function getOffreMomentByIdEntreprise($identreprise, $site): array
    {
        $startDate = (new \DateTime('-15 days'))->setTime(0, 0, 0)->format(DATE_ATOM);
        $endDate = (new \DateTime('now'))->setTime(23, 59, 59)->format(DATE_ATOM);


        // Définition des filtres secteur_activite en fonction du site
        $secteurActiviteFilters = [];
        switch ($site) {
            case 4:
                $secteurActiviteFilters = [4, 11, 12, 18, 46];
                break;
            case 5:
                $secteurActiviteFilters = [16, 17, 47, 48];
                break;
            case 6:
                $secteurActiviteFilters = [2, 5, 11, 20, 22, 24, 29, 39, 23];
                break;
            case 7:
                $secteurActiviteFilters = [10, 11, 19, 27];
                break;
            case 8:
                $secteurActiviteFilters = [26, 38];
                break;
            case 9:
                $secteurActiviteFilters = [17, 28];
                break;
            default:
                return [];
        }

        // Construction de la requête Elasticsearch
        $query = [
            'query' => [
                'bool' => [
                    'must' => [
                        ['term' => ['entreprise.id_entreprise' => $identreprise]],
                        ['range' => ['date_soumission' => ['gte' => $startDate, 'lte' => $endDate]]],
                        ['terms' => ['id_type_contrat' => [2]]],
                        ['terms' => ['secteur_activite.id_secteur_activite' => $secteurActiviteFilters]],
                    ],
                ],
            ],
            'size' => 3,
            '_source' => ['id_offre'],
        ];

        // Envoi de la requête Elasticsearch
        $response = $this->sendElasticsearchRequest($query);

        // Extraction des ID des offres
        $offerIds = [];
        if (isset($response['hits']['hits']) && is_array($response['hits']['hits'])) {
            foreach ($response['hits']['hits'] as $hit) {
                if (isset($hit['_source']['id_offre'])) {
                    $offerIds[] = $hit['_source']['id_offre'];
                }
            }
        }
        // Récupération des offres en base de données à partir des IDs
        $offers = [];
        foreach ($offerIds as $offerId) {
            $offer = $this->findOneBy(['id_offre' => $offerId]);
            if ($offer !== null) { // Vérification que l'offre existe
                $offers[] = $offer;
            }
        }

        return $offers;

    }



    public function getOffreMomentByIdEntrepriseEtudiant($identreprise): array
    {
        // Get the start and end of the last 7 days (aligned with Elasticsearch)
        $startDate = (new \DateTime('-7 days'))->setTime(0, 0, 0)->format('Y-m-d\TH:i:s\Z');
        $endDate = (new \DateTime('now'))->setTime(23, 59, 59)->format('Y-m-d\TH:i:s\Z');

        $query = [
            'query' => [
                'bool' => [
                    'must' => [
                        ['term' => ['entreprise.id_entreprise' => $identreprise]],
                        ['range' => ['date_soumission' => ['gte' => $startDate, 'lte' => $endDate]]],
                        ['terms' => ['id_type_contrat' => [6, 8]]], // Filtering alternance & stage contracts
                    ],
                ],
            ],
            'size' => 3, // Limit to 3 results
            '_source' => ['id_offre', 'intitule', 'entreprise.id_entreprise'], // Fields to return
        ];

        $response = $this->sendElasticsearchRequest($query);

        // Handle the response safely
        $offers = [];
        if (!empty($response['hits']['hits'])) {
            foreach ($response['hits']['hits'] as $hit) {
                $offers[] = [
                    'id_offre' => $hit['_source']['id_offre'] ?? null,
                    'intitule' => $hit['_source']['intitule'] ?? null,
                    'id_entreprise' => $hit['_source']['entreprise']['id_entreprise'] ?? null,
                ];
            }
        }

        return $offers;
    }



    public function getOffreMomentByIdEntrepriseRestauration($identreprise)
    {
        // Prendre une offre aléatoire pour Bouygues
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'lsa', 'WITH', 'oo.id_offre = lsa.off_offre_id_offre')
            ->where('ee.id_entreprise = :id_entreprise')
            ->andWhere('lsa.off_liste_secteur_activite_id_secteur_activite IN (:secteurs)')
            ->setParameter('id_entreprise', $identreprise)
            ->setParameter('secteurs', [17, 28])
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }
    public function getOffreMomentByIdEntrepriseDistribution($identreprise)
    {
        // Prendre une offre aléatoire pour Bouygues
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'lsa', 'WITH', 'oo.id_offre = lsa.off_offre_id_offre')
            ->where('ee.id_entreprise = :id_entreprise')
            ->andWhere('lsa.off_liste_secteur_activite_id_secteur_activite IN (:secteurs)')
            ->setParameter('id_entreprise', $identreprise)
            ->setParameter('secteurs', [16, 17, 47, 48])
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }


    public function getOffreMomentByIdEntrepriseCDICDD($identreprise)
    {
        // Prendre une offre aléatoire pour Bouygues
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->setParameter('id_entreprise', $identreprise)
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }
    public function getOffreMomentByIdEntrepriseAlternance($identreprise): array
    {
        // Define the Elasticsearch query
        $query = [
            'query' => [
                'bool' => [
                    'must' => [
                        ['term' => ['entreprise.id_entreprise' => $identreprise]], // Filter by entreprise ID
                        ['term' => ['id_type_contrat' => 6]], // Filter by alternance contracts (id_type_contrat = 6)
                    ],
                ],
            ],
            'size' => 3, // Limit to 3 results
            '_source' => ['id_offre', 'intitule', 'entreprise.id_entreprise'], // Fields to return
        ];

        // Send the request to Elasticsearch
        $response = $this->sendElasticsearchRequest($query);

        // Safely handle the response
        $offers = [];
        if (isset($response['hits']['hits']) && is_array($response['hits']['hits'])) {
            foreach ($response['hits']['hits'] as $hit) {
                if (isset($hit['_source'])) {
                    $offers[] = [
                        'id_offre' => $hit['_source']['id_offre'] ?? null,
                        'intitule' => $hit['_source']['intitule'] ?? null,
                        'id_entreprise' => $hit['_source']['entreprise']['id_entreprise'] ?? null,
                    ];
                }
            }
        }

        return $offers;
    }


    public function getOffreMomentOrano()
    {
        // Prendre une offre aléatoire pour NGE
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->andWhere('oo.sitemap_carriere_btp = 1')
            ->setParameter('id_entreprise', '55291')
            ->andWhere($qb->expr()->in('oo.off_liste_type_contrat_id_type_contrat', [6, 7, 8]))
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }

    public function getOffreMomentBouyguesConstruction()
    {
        // Prendre une offre aléatoire pour SPIE
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();


        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->setParameter('id_entreprise', '52146')
            ->andWhere($qb->expr()->in('oo.off_liste_type_contrat_id_type_contrat', [6, 7, 8]))
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();
        return $results;
    }
    public function findByOffreMomentDirectOrange() {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('o.id_offre', 'o.intitule', 'e.logo')
            ->from('App\Entity\OffOffre', 'o')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'u.entr_entreprise_id_entreprise = e.id_entreprise')
            ->where('e.id_entreprise = :id_entreprise')
            ->setParameter('id_entreprise', '25718')
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }

    public function findByOffreMomentDirectAdzuna() {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('o.id_offre', 'o.intitule', 'e.logo')
            ->from('App\Entity\OffOffre', 'o')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'u.entr_entreprise_id_entreprise = e.id_entreprise')
            ->where('e.id_entreprise = :id_entreprise')
            ->setParameter('id_entreprise', '26012')
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }

    public function findByOffreMomentDirectMichaelPage() {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('o.id_offre', 'o.intitule', 'e.logo')
            ->from('App\Entity\OffOffre', 'o')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'u.entr_entreprise_id_entreprise = e.id_entreprise')
            ->where('e.id_entreprise = :id_entreprise')
            ->setParameter('id_entreprise', '53419')
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }

    // Carriere Medicale
    public function getNbOffresMedicale()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('count(a.id_offre)')
            ->from('App\Entity\OffOffre', 'a')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'b', 'WITH', 'a.id_offre = b.off_offre_id_offre')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'a.off_liste_type_contrat_id_type_contrat = c.id_type_contrat')
            ->where('b.off_liste_secteur_activite_id_secteur_activite IN (26,38)')
            ->andWhere('a.statut = 1')
            ->andWhere('a.date_creation > :date')
            ->orWhere('a.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2')
            ->andWhere('a.date_creation > :date2')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('date', new \DateTime('-1 month')),
                    new Parameter('date2', new \DateTime('-1 year')),
                ])
            );

        return $qb->getQuery()->getSingleScalarResult();
    }

    public function getoffresMedicale(int $id) {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('o.id_offre', 'o.intitule', 'e.logo')
            ->from('App\Entity\OffOffre', 'o')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'u.entr_entreprise_id_entreprise = e.id_entreprise')
            ->where('e.id_entreprise = :id_entreprise')
            ->setParameter('id_entreprise', $id)
            ->setMaxResults(3);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }
    public function getOffreMomentMedicale()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('oo.id_offre', 'oo.intitule')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'oo.user_user_id_user = uu.id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'uu.entr_entreprise_id_entreprise = ee.id_entreprise')
            ->where('ee.id_entreprise = :id_entreprise')
            ->andWhere('oo.intitule NOT LIKE :stage AND oo.intitule NOT LIKE :alternance AND oo.intitule NOT LIKE :stages AND oo.intitule NOT LIKE :Inf')
            ->setParameter('id_entreprise', '55669')
            ->setParameter('stage', '%stage%')
            ->setParameter('alternance', '%Infirmier%')
            ->setParameter('stages', '%stages%')
            ->setParameter('Inf', '%infirmier%')
            ->setMaxResults(9);

        $query = $qb->getQuery();
        $results = $query->getResult();

        return $results;
    }

    public function findCandidatesByOffresUserAndDates($id_user, $startDate, $endDate)
    {
        $queryBuilder = $this->createQueryBuilder('o')
            ->select('o.intitule', 'r.nom', 'r.prenom', 'r.email', 'r.cv', 'c.cv')
            ->leftJoin('App\Entity\OffReponseOffreNonIdentifie', 'r', 'WITH', 'o.id_offre = r.off_offre_id_offre')
            ->leftJoin('App\Entity\OffCandidature', 'c', 'WITH', 'o.id_offre = c.off_offre_id_offre')
            
            ->where('o.user_user_id_user = :id_user')
            ->andWhere('r.date_candidature BETWEEN :start_date AND :end_date')
            ->setParameter('id_user', $id_user)
            ->setParameter('start_date', $startDate)
            ->setParameter('end_date', $endDate);

        return $queryBuilder
            ->getQuery()
            ->getResult();
    }

    /* Direct Emploi Mobile */
    public function getOfferById(int $id)
    {
        return $this->createQueryBuilder('o')
            ->select(
                'o.id_offre as idOffre', 'o.intitule as intitule','o.reference','o.date_debut', 'o.duree_contrat as dureeContrat','o.statut', 'o.is_handicap as isHandicap', 'o.rep_url as repUrl', 'o.mission as mission','o.profil', 'c.libelle AS contrat', 'e.logo as logo','e.presentation_societe',
                'e.raison_sociale as raisonSociale', 's.libelle AS secteur', 'de.libelle as departement','py.libelle as pays','r.libelle as region','ex.libelle as experience','g.code_postal ','g.nom_ville as ville','f.libelle','o.date_creation'
            )
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'c.id_type_contrat = o.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\UserUser', 'd', 'WITH', 'd.id_user = o.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
            ->leftJoin('App\Entity\OffLienOffreListeSecteurActivite', 'l', 'WITH', 'l.off_offre_id_offre = o.id_offre')
            ->leftJoin('App\Entity\OffListeSecteurActivite', 's', 'WITH', 's.id_secteur_activite = l.off_liste_secteur_activite_id_secteur_activite')
            ->leftJoin('App\Entity\OffLienOffreGeoDepartement', 'ld', 'WITH', 'ld.off_offre_id_offre = o.id_offre')
            ->leftJoin('App\Entity\GeoDepartement', 'de', 'WITH', 'de.id_departement = ld.geo_departement_id_departement')
            ->leftJoin('App\Entity\OffLienOffreGeoListePays', 'lp', 'WITH', 'lp.off_offre_id_offre = o.id_offre')
            ->leftJoin('App\Entity\GeoListePays', 'py', 'WITH', 'py.id_pays = lp.geo_liste_pays_id_pays')
            ->leftJoin('App\Entity\OffLienOffreGeoListeRegion', 'lr', 'WITH', 'lr.off_offre_id_offre = o.id_offre')
            ->leftJoin('App\Entity\GeoListeRegion', 'r', 'WITH', 'r.id_region = lr.geo_liste_region_id_region')
            ->leftJoin('App\Entity\OffLienOffreListeExperienceRequise', 'le', 'WITH', 'le.off_offre_id_offre = o.id_offre')
            ->leftJoin('App\Entity\OffListeExperienceRequise', 'ex', 'WITH', 'ex.id_experience_requise = le.off_liste_experience_requise_id_experience_requise')
            ->leftJoin('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = e.geo_adresse_id_adresse')
            ->leftJoin('App\Entity\OffFourchetteRemuneration', 'f', 'WITH', 'f.id_fourchette_remuneration = o.off_fourchette_remuneration_id_fourchette_remuneration')
            ->where('o.id_offre = :id')
            ->setParameter('id', $id)
            ->andWhere('o.statut = 1') // Keep only active offers
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function getSimilarOffersMobile(string $intitule, int $limit = 5, $id, $geo_ville)
    {
        return $this->createQueryBuilder('o')
            ->select('o.id_offre as id', 'o.reference','o.intitule as title', 'o.geo_ville as ville', 'o.mission', 'c.libelle AS contractType', 'e.logo as logo', 'e.raison_sociale as companyName','o.date_creation as dateSoumission')
            // ->from('App\Entity\OffOffre', 'o')
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'c.id_type_contrat = o.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\UserUser', 'd', 'WITH', 'd.id_user = o.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
            ->where('o.intitule LIKE :intitule')
            ->setParameter('intitule', '%' . $intitule . '%')
            ->andWhere('o.id_offre != :id')
            ->setParameter('id', $id)
            ->andWhere('o.geo_ville = :geo_ville')
            ->setParameter('geo_ville', $geo_ville)
            ->andWhere('o.statut = 1')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function getOffersByIds(array $ids)
    {
        return $this->createQueryBuilder('o')
            ->select(
                'o.id_offre as idOffre', 'o.intitule as intitule','o.reference','o.date_debut', 'o.duree_contrat as dureeContrat','o.statut', 'o.is_handicap as isHandicap', 'o.rep_url as repUrl', 'o.mission as mission','o.profil', 'c.libelle AS contrat', 'e.logo as logo','e.presentation_societe',
                'e.raison_sociale as raisonSociale', 's.libelle AS secteur', 'de.libelle as departement','py.libelle as pays','r.libelle as region','ex.libelle as experience','g.code_postal ','g.nom_ville as ville','f.libelle','o.date_creation'
            )
            ->join('App\Entity\OffListeTypeContrat', 'c', 'WITH', 'c.id_type_contrat = o.off_liste_type_contrat_id_type_contrat')
            ->join('App\Entity\UserUser', 'd', 'WITH', 'd.id_user = o.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
            ->leftJoin('App\Entity\OffLienOffreListeSecteurActivite', 'l', 'WITH', 'l.off_offre_id_offre = o.id_offre')
            ->leftJoin('App\Entity\OffListeSecteurActivite', 's', 'WITH', 's.id_secteur_activite = l.off_liste_secteur_activite_id_secteur_activite')
            ->leftJoin('App\Entity\OffLienOffreGeoDepartement', 'ld', 'WITH', 'ld.off_offre_id_offre = o.id_offre')
            ->leftJoin('App\Entity\GeoDepartement', 'de', 'WITH', 'de.id_departement = ld.geo_departement_id_departement')
            ->leftJoin('App\Entity\OffLienOffreGeoListePays', 'lp', 'WITH', 'lp.off_offre_id_offre = o.id_offre')
            ->leftJoin('App\Entity\GeoListePays', 'py', 'WITH', 'py.id_pays = lp.geo_liste_pays_id_pays')
            ->leftJoin('App\Entity\OffLienOffreGeoListeRegion', 'lr', 'WITH', 'lr.off_offre_id_offre = o.id_offre')
            ->leftJoin('App\Entity\GeoListeRegion', 'r', 'WITH', 'r.id_region = lr.geo_liste_region_id_region')
            ->leftJoin('App\Entity\OffLienOffreListeExperienceRequise', 'le', 'WITH', 'le.off_offre_id_offre = o.id_offre')
            ->leftJoin('App\Entity\OffListeExperienceRequise', 'ex', 'WITH', 'ex.id_experience_requise = le.off_liste_experience_requise_id_experience_requise')
            ->leftJoin('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = e.geo_adresse_id_adresse')
            ->leftJoin('App\Entity\OffFourchetteRemuneration', 'f', 'WITH', 'f.id_fourchette_remuneration = o.off_fourchette_remuneration_id_fourchette_remuneration')
            ->where('o.id_offre IN (:ids)')
            ->andWhere('o.statut = 1') // Keep only active offers
            ->setParameter('ids', $ids)
            ->getQuery()
            ->getArrayResult();
    }
    public function getRandomSameOffre(string $title, string $ville,string $contrat, int $limit = 5)
    {
        return $this->createQueryBuilder('oo')
            ->select('oo')
            ->where('oo.geo_ville LIKE :ville')
            ->andWhere('oo.intitule LIKE :title')
            ->andWhere('oo.statut = 1')
            ->andWhere('oo.off_liste_type_contrat_id_type_contrat = :contrat')
            ->groupBy('oo.user_user_id_user')
            ->setParameter('contrat', $contrat)
            ->setParameter('title', '%' . $title . '%')
            ->setParameter('ville', '%' . $ville . '%')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function getVolumeOffres()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('ee.id_entreprise', 
                    'ee.raison_sociale', 
                    'COUNT(oo.id_offre) AS nombre_offres', 
                    'ee.entr_profil_recruteur_id_profil_recruteur AS position',)
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'uu.id_user = oo.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'ee.id_entreprise = uu.entr_entreprise_id_entreprise')
            ->where('oo.statut = 1')
            ->groupBy('ee.id_entreprise')
            ->orderBy('position', 'ASC');

        return $qb->getQuery()->getResult();
    }

    public function getVolumeOffresStage()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('ee.id_entreprise', 
                    'ee.raison_sociale', 
                    'COUNT(oo.id_offre) AS nombre_offres', 
                    'ee.entr_profil_recruteur_id_profil_recruteur_stage AS position',)
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'uu.id_user = oo.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'ee.id_entreprise = uu.entr_entreprise_id_entreprise')
            ->where('oo.statut = 1')
            ->andWhere('oo.off_liste_type_contrat_id_type_contrat IN (5,6,7,8,9)')
            ->groupBy('ee.id_entreprise')
            ->orderBy('position', 'ASC');

        return $qb->getQuery()->getResult();
    }

    public function getOffres_SendToFranceTravail()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('
        oo.id_offre AS par_ref_offre,
        oo.geo_ville AS ville,
        oo.mission AS mission,
        CONCAT(\'https://www.directemploi.com/candidatOffre/\', oo.id_offre, \'?utm_source=pole-emploi&utm_medium=referral&utm_campaign=Pôle-Emploi\') AS par_url_offre,
        \'directemploi\' AS par_cle,
        \'directemploi\' AS par_nom,
        oo.intitule AS libelle_metier_ogr,
        \'D\' AS exp_cle,
        \'Débutant accepté\' AS exp_libelle,
        CASE 
            WHEN oo.off_liste_type_contrat_id_type_contrat = 1 THEN \'CDD\'
            WHEN oo.off_liste_type_contrat_id_type_contrat = 2 THEN \'CDI\'
            WHEN oo.off_liste_type_contrat_id_type_contrat = 4 THEN \'INTERIM\'
            WHEN oo.off_liste_type_contrat_id_type_contrat = 3 THEN \'Freelance/Indépendant\'
            WHEN oo.off_liste_type_contrat_id_type_contrat = 6 THEN \'Alternance\'
            ELSE \'\' 
        END AS tco_cle,
        \'E1\' AS ntc_cle,
        oo.date_soumission AS off_date_creation,
        ee.presentation_societe AS description_entreprise,
        oo.date_debut AS date_debut,
        oo.duree_contrat AS duree_contrat
    ')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'uu.id_user = oo.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'ee.id_entreprise = uu.entr_entreprise_id_entreprise')
            ->where('oo.statut = 1')
            ->andWhere('oo.off_liste_type_contrat_id_type_contrat IN (1, 2, 3, 4, 6)') // Inclure CDD, CDI, INTERIM, Freelance, Alternance
            ->andWhere('oo.off_liste_type_contrat_id_type_contrat != 1 OR (oo.date_debut IS NOT NULL AND oo.duree_contrat IS NOT NULL)') // Si CDD, vérifier que les champs ne sont pas vides
            ->andWhere('ee.entr_profil_recruteur_id_profil_recruteur IN (-2, -1, 1, 2, 3, 4)') // Inclure les profils de recruteurs spécifiques
            ->andWhere('LENGTH(oo.mission) > 50') // Mission de plus de 50 caractères
            ->andWhere('oo.date_soumission > :date_limit') // Date soumission moins de 90 jours
            ->andWhere('uu.id_user NOT IN (:excluded_user_ids)')
            ->groupBy('oo.id_offre')
            ->orderBy('ee.entr_profil_recruteur_id_profil_recruteur', 'ASC')
            ->setParameter('date_limit', new \DateTime('-90 days'))
            ->setParameter('excluded_user_ids', [1137316,1203594]); // ⬅️ Liste d'exclusion


        $offres = $qb->getQuery()->getResult();
        return $offres;
    }



    public function getOffresForJobted()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('
        oo.id_offre AS id,
        oo.intitule AS title,
        CONCAT(\'https://www.directemploi.com/candidatOffre/\', oo.id_offre) AS url,
        oo.mission AS description,
        oo.geo_ville AS city,
        \'\' AS state,                -- Replace with actual region if available
        \'FR\' AS country,
        ee.raison_sociale AS company,
        oo.date_soumission AS date,
        \'Direct Emploi\' AS publisher,
        \'https://www.directemploi.com/\' AS publisherUrl
    ')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'uu.id_user = oo.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'ee.id_entreprise = uu.entr_entreprise_id_entreprise')
            ->where('oo.statut = 1')
            ->andWhere('oo.off_liste_type_contrat_id_type_contrat IN (1, 2, 3, 4, 6)')
            ->andWhere('ee.entr_profil_recruteur_id_profil_recruteur IN (-2, -1, 1, 2, 3, 4)') // Inclure les profils de recruteurs spécifiques
            ->groupBy('oo.id_offre')
            ->orderBy('oo.date_soumission', 'DESC');

        return $qb->getQuery()->getResult();
    }

    public function getOffresForJora()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('
        oo.id_offre AS id,
        oo.intitule AS title,
        CONCAT(\'https://www.directemploi.com/candidatOffre/\', oo.id_offre) AS url,
        oo.mission AS description,
        oo.geo_ville AS city,
        \'\' AS state,                -- Replace with actual region if available
        \'FR\' AS country,
        ee.raison_sociale AS company,
        oo.date_soumission AS date,
        \'Direct Emploi\' AS publisher,
        \'https://www.directemploi.com/\' AS publisherUrl
    ')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'uu.id_user = oo.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'ee.id_entreprise = uu.entr_entreprise_id_entreprise')
            ->where('oo.statut = 1')
            ->andWhere('oo.off_liste_type_contrat_id_type_contrat IN (1, 2, 3, 4, 6)')
            ->andWhere('ee.entr_profil_recruteur_id_profil_recruteur IN (-2, -1, 1, 2, 3, 4)') // Inclure les profils de recruteurs spécifiques
            ->groupBy('oo.id_offre')
            ->orderBy('oo.date_soumission', 'DESC');

        return $qb->getQuery()->getResult();
    }
    public function getOffresForJooble()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('
        oo.id_offre AS id,
        oo.intitule AS title,
        CONCAT(\'https://www.directemploi.com/candidatOffre/\', oo.id_offre) AS url,
        oo.mission AS description,
        oo.geo_ville AS city,
        \'\' AS state,                -- Replace with actual region if available
        \'FR\' AS country,
        ee.raison_sociale AS company,
        oo.date_soumission AS date,
        \'Direct Emploi\' AS publisher,
        \'https://www.directemploi.com/\' AS publisherUrl
    ')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'uu.id_user = oo.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'ee.id_entreprise = uu.entr_entreprise_id_entreprise')
            ->where('oo.statut = 1')
            ->andWhere('oo.off_liste_type_contrat_id_type_contrat IN (1, 2, 3, 4, 6)')
            ->andWhere('ee.entr_profil_recruteur_id_profil_recruteur IN (-2, -1, 1, 2, 3, 4)') // Inclure les profils de recruteurs spécifiques
            ->groupBy('oo.id_offre')
            ->orderBy('oo.date_soumission', 'DESC');

        return $qb->getQuery()->getResult();
    }

    public function getOffresForJobsora()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('
        oo.id_offre AS job_id,
        CONCAT(\'https://www.directemploi.com/candidatOffre/\', oo.id_offre) AS url,
        oo.intitule AS name,
        oo.geo_ville AS region,
        \'FR\' AS country,
        oo.mission AS description,
        oo.date_soumission AS pubdate,
        oo.date_soumission AS updated,
        ee.presentation_societe AS company,
        ee.logo AS company_logo,
        CASE 
            WHEN oo.off_liste_type_contrat_id_type_contrat = 1 THEN \'Full-time\'
            WHEN oo.off_liste_type_contrat_id_type_contrat = 2 THEN \'Part-time\'
            WHEN oo.off_liste_type_contrat_id_type_contrat = 3 THEN \'Contract\'
            WHEN oo.off_liste_type_contrat_id_type_contrat = 4 THEN \'Internship\'
            WHEN oo.off_liste_type_contrat_id_type_contrat = 6 THEN \'Temporary\'
            ELSE \'\' 
        END AS jobtype,
        uu.email AS email
    ')
            ->from('App\Entity\OffOffre', 'oo')
            ->join('App\Entity\UserUser', 'uu', 'WITH', 'uu.id_user = oo.user_user_id_user')
            ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'ee.id_entreprise = uu.entr_entreprise_id_entreprise')
            ->where('oo.statut = 1')
            ->andWhere('oo.off_liste_type_contrat_id_type_contrat IN (1, 2, 3, 4, 6)')
            ->andWhere('ee.entr_profil_recruteur_id_profil_recruteur IN (-2, -1, 1, 2, 3, 4)') // Inclure les profils de recruteurs spécifiques
            ->groupBy('oo.id_offre')
            ->orderBy('oo.date_soumission', 'DESC');

        return $qb->getQuery()->getResult();
    }





    // public function getVolumeCandidatures()
    // {
    //     $em = $this->getEntityManager();
    //     $qb = $em->createQueryBuilder();

    //     $firstDayOfMonth = new \DateTime('first day of this month');
    //     $lastDayOfMonth = new \DateTime('last day of this month');

    //     $qb->select('ee.id_entreprise', 'ee.raison_sociale', 'COUNT(oo.id_offre) AS nombre_offres', 'ee.entr_profil_recruteur_id_profil_recruteur AS position', '(COUNT(DISTINCT orn.id_reponse_offre_non_identifie) + COUNT(DISTINCT oc.id_candidature)) AS nombre_candidatures')
    //         ->from('App\Entity\OffOffre', 'oo')
    //         ->join('App\Entity\UserUser', 'uu', 'WITH', 'uu.id_user = oo.user_user_id_user')
    //         ->join('App\Entity\EntrEntreprise', 'ee', 'WITH', 'ee.id_entreprise = uu.entr_entreprise_id_entreprise')
    //         ->leftJoin('App\Entity\OffReponseOffreNonIdentifie', 'orn', 'WITH', 'orn.date_candidature BETWEEN :firstDayOfMonth AND :lastDayOfMonth')
    //         ->leftJoin('App\Entity\OffCandidature', 'oc', 'WITH', 'oc.date_candidature BETWEEN :firstDayOfMonth AND :lastDayOfMonth')
    //         ->where('oo.statut = 1')
    //         ->groupBy('ee.id_entreprise')
    //         ->orderBy('position', 'ASC')
    //         ->setParameter('firstDayOfMonth', $firstDayOfMonth)
    //         ->setParameter('lastDayOfMonth', $lastDayOfMonth);

    //     return $qb->getQuery()->getResult();
    // }
    public function getStatistiquesCvParOffre(int $userId, \DateTime $startDate, \DateTime $endDate): array
    {
        $qb = $this->createQueryBuilder('o');

        $qb->select('o.intitule, COUNT(r.id_reponse_offre_non_identifie) AS nombre_cv')
            ->innerJoin('App\Entity\OffReponseOffreNonIdentifie', 'r', 'WITH', 'o.id_offre = r.off_offre_id_offre')
            ->where('o.user_user_id_user = :userId')
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->andX(
                        $qb->expr()->isNotNull('o.date_soumission'),
                        $qb->expr()->between('o.date_soumission', ':startDate', ':endDate')
                    ),
                    $qb->expr()->andX(
                        $qb->expr()->isNull('o.date_soumission'),
                        $qb->expr()->between('o.date_creation', ':startDate', ':endDate')
                    )
                )
            )
            ->groupBy('o.intitule')
            ->setParameter('userId', $userId)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate);

        return $qb->getQuery()->getResult();
    }





}
