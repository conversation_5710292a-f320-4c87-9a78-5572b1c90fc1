<?php

namespace App\Repository;

use App\Entity\EntrEntreprise;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Query\Parameter;
use App\Entity\UserUser;
use App\Entity\ComSouscriptionCommerciale;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Symfony\Component\VarDumper\VarDumper;

/**
 * @method EntrEntreprise|null find($id, $lockMode = null, $lockVersion = null)
 * @method EntrEntreprise|null findOneBy(array $criteria, array $orderBy = null)
 * @method EntrEntreprise[]    findAll()
 * @method EntrEntreprise[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EntrEntrepriseRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EntrEntreprise::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(EntrEntreprise $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(EntrEntreprise $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }


    public function GetLogoEntrepriseSiteSpecialse(array $secteurs)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        // Requête sans pagination ni filtrage par lettre
        $qb->select('DISTINCT p.id_entreprise', 'p.logo','p.raison_sociale')
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'lsa', 'WITH', 'lsa.off_offre_id_offre = o.id_offre')
            ->where('lsa.off_liste_secteur_activite_id_secteur_activite IN (:secteurs)')
            ->andWhere('o.statut = 1')
            ->andWhere("p.raison_sociale NOT LIKE 'slash%'")
            ->andWhere(
                $qb->expr()->orX(
                    'o.date_creation > :threeMonthsAgo',
                    $qb->expr()->andX(
                        'o.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2',
                        'o.date_creation > :oneYearAgo'
                    )
                )
            )
            ->setParameter('secteurs', $secteurs)
            ->setParameter('threeMonthsAgo', new \DateTime('-3 months'))
            ->setParameter('oneYearAgo', new \DateTime('-1 year'))
            ->orderBy('p.raison_sociale', 'ASC');

        return $qb->getQuery()->getResult();
    }


    public function getEntreprisesByTypeDomain($type, $domain)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->distinct()
            ->from('App\Entity\EntrEntreprise', 'p')
            // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
            ->join('App\Entity\EntrLienEntrepriseOffListeSecteurActivite', 'a', 'WITH', 'a.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->where('o.statut = 1')
            ->andWhere("a.off_liste_secteur_activite_id_secteur_activite = $domain")
            ->andWhere("p.type_entreprise = $type")
            ->orderBy('p.raison_sociale', 'ASC')
        ;

        $query = $qb->getQuery();
        $result = $query->execute();

        return $result;
    }

    public function getEntreprisesByDomain($value)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->distinct()
            ->from('App\Entity\EntrEntreprise', 'p')
            // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
            ->join('App\Entity\EntrLienEntrepriseOffListeSecteurActivite', 'a', 'WITH', 'a.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->where('o.statut = 1')
            ->andWhere("a.off_liste_secteur_activite_id_secteur_activite = $value")
            ->orderBy('p.raison_sociale', 'ASC')
        ;

        $query = $qb->getQuery();
        $result = $query->execute();

        return $result;
    }

    public function getEntreprisesByType($value)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->distinct()
            ->from('App\Entity\EntrEntreprise', 'p')
            // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->where('o.statut = 1')
            ->andWhere("p.type_entreprise = $value")
            ->orderBy('p.raison_sociale', 'ASC')
        ;

        $query = $qb->getQuery();
        $result = $query->execute();

        return $result;
    }

    public function entrepriseListeEntreprises($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $contratExpr = $qb->expr()->orX(
            $qb->expr()->eq('o.off_liste_type_contrat_id_type_contrat', 6),
            $qb->expr()->eq('o.off_liste_type_contrat_id_type_contrat', 8)
        );

        if($letter == '') {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->orderBy('p.raison_sociale', 'ASC')
            ;

        } elseif($letter == '0-9') {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->andWhere('p.raison_sociale LIKE ?0')
                ->orWhere('p.raison_sociale LIKE ?1')
                ->orWhere('p.raison_sociale LIKE ?2')
                ->orWhere('p.raison_sociale LIKE ?3')
                ->orWhere('p.raison_sociale LIKE ?4')
                ->orWhere('p.raison_sociale LIKE ?5')
                ->orWhere('p.raison_sociale LIKE ?6')
                ->orWhere('p.raison_sociale LIKE ?7')
                ->orWhere('p.raison_sociale LIKE ?8')
                ->orWhere('p.raison_sociale LIKE ?9')
                ->setParameters(
                    new ArrayCollection([
                        new Parameter('0', '0'.'%'),
                        new Parameter('1', '1'.'%'),
                        new Parameter('2', '2'.'%'),
                        new Parameter('3', '3'.'%'),
                        new Parameter('4', '4'.'%'),
                        new Parameter('5', '5'.'%'),
                        new Parameter('6', '6'.'%'),
                        new Parameter('7', '7'.'%'),
                        new Parameter('8', '8'.'%'),
                        new Parameter('9', '9'.'%'),
                    ]))
                ->orderBy('p.raison_sociale', 'ASC');
        } else {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere('p.raison_sociale LIKE :letter')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->setParameter('letter', $letter.'%')
                ->orderBy('p.raison_sociale', 'ASC');
        }

        $query = $qb->getQuery();
        $result = $query->execute();

        return $result;
    }

    public function entrepriseListeEntreprisesAlternance($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $contratExpr = $qb->expr()->orX(
            $qb->expr()->eq('o.off_liste_type_contrat_id_type_contrat', 6),
        );

        if($letter == '') {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->andWhere($contratExpr)
                ->orderBy('p.raison_sociale', 'ASC')
            ;

        } elseif($letter == '0-9') {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere($contratExpr)
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->andWhere('p.raison_sociale LIKE ?0')
                ->orWhere('p.raison_sociale LIKE ?1')
                ->orWhere('p.raison_sociale LIKE ?2')
                ->orWhere('p.raison_sociale LIKE ?3')
                ->orWhere('p.raison_sociale LIKE ?4')
                ->orWhere('p.raison_sociale LIKE ?5')
                ->orWhere('p.raison_sociale LIKE ?6')
                ->orWhere('p.raison_sociale LIKE ?7')
                ->orWhere('p.raison_sociale LIKE ?8')
                ->orWhere('p.raison_sociale LIKE ?9')
                ->setParameters(
                    new ArrayCollection([
                        new Parameter('0', '0'.'%'),
                        new Parameter('1', '1'.'%'),
                        new Parameter('2', '2'.'%'),
                        new Parameter('3', '3'.'%'),
                        new Parameter('4', '4'.'%'),
                        new Parameter('5', '5'.'%'),
                        new Parameter('6', '6'.'%'),
                        new Parameter('7', '7'.'%'),
                        new Parameter('8', '8'.'%'),
                        new Parameter('9', '9'.'%'),
                    ]))
                ->orderBy('p.raison_sociale', 'ASC');
        } else {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere('p.raison_sociale LIKE :letter')
                ->andWhere($contratExpr)
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->setParameter('letter', $letter.'%')
                ->orderBy('p.raison_sociale', 'ASC');
        }

        $query = $qb->getQuery();
        $result = $query->execute();

        return $result;
    }
    public function entrepriseListeEntreprisesEtudiant($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $contratExpr = $qb->expr()->orX(
            $qb->expr()->eq('o.off_liste_type_contrat_id_type_contrat', 6),
            $qb->expr()->eq('o.off_liste_type_contrat_id_type_contrat', 8)
        );

        if($letter == '') {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->andWhere($contratExpr)
                ->orderBy('p.raison_sociale', 'ASC')
            ;

        } elseif($letter == '0-9') {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere($contratExpr)
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->andWhere('p.raison_sociale LIKE ?0')
                ->orWhere('p.raison_sociale LIKE ?1')
                ->orWhere('p.raison_sociale LIKE ?2')
                ->orWhere('p.raison_sociale LIKE ?3')
                ->orWhere('p.raison_sociale LIKE ?4')
                ->orWhere('p.raison_sociale LIKE ?5')
                ->orWhere('p.raison_sociale LIKE ?6')
                ->orWhere('p.raison_sociale LIKE ?7')
                ->orWhere('p.raison_sociale LIKE ?8')
                ->orWhere('p.raison_sociale LIKE ?9')
                ->setParameters(
                    new ArrayCollection([
                        new Parameter('0', '0'.'%'),
                        new Parameter('1', '1'.'%'),
                        new Parameter('2', '2'.'%'),
                        new Parameter('3', '3'.'%'),
                        new Parameter('4', '4'.'%'),
                        new Parameter('5', '5'.'%'),
                        new Parameter('6', '6'.'%'),
                        new Parameter('7', '7'.'%'),
                        new Parameter('8', '8'.'%'),
                        new Parameter('9', '9'.'%'),
                    ]))
                ->orderBy('p.raison_sociale', 'ASC');
        } else {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere('p.raison_sociale LIKE :letter')
                ->andWhere($contratExpr)
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->setParameter('letter', $letter.'%')
                ->orderBy('p.raison_sociale', 'ASC');
        }

        $query = $qb->getQuery();
        $result = $query->execute();

        return $result;
    }
    public function entrepriseListeEntreprisesAdmin($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        if($letter == '') {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond', 'p.presentation_societe','p.tag')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->orderBy('p.raison_sociale', 'ASC')
            ;

        } elseif($letter == '0-9') {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->andWhere('p.raison_sociale LIKE ?0')
                ->orWhere('p.raison_sociale LIKE ?1')
                ->orWhere('p.raison_sociale LIKE ?2')
                ->orWhere('p.raison_sociale LIKE ?3')
                ->orWhere('p.raison_sociale LIKE ?4')
                ->orWhere('p.raison_sociale LIKE ?5')
                ->orWhere('p.raison_sociale LIKE ?6')
                ->orWhere('p.raison_sociale LIKE ?7')
                ->orWhere('p.raison_sociale LIKE ?8')
                ->orWhere('p.raison_sociale LIKE ?9')
                ->setParameters(
                    new ArrayCollection([
                        new Parameter('0', '0'.'%'),
                        new Parameter('1', '1'.'%'),
                        new Parameter('2', '2'.'%'),
                        new Parameter('3', '3'.'%'),
                        new Parameter('4', '4'.'%'),
                        new Parameter('5', '5'.'%'),
                        new Parameter('6', '6'.'%'),
                        new Parameter('7', '7'.'%'),
                        new Parameter('8', '8'.'%'),
                        new Parameter('9', '9'.'%'),
                    ]))
                ->orderBy('p.raison_sociale', 'ASC');
        } else {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere('p.raison_sociale LIKE :letter')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->setParameter('letter', $letter.'%')
                ->orderBy('p.raison_sociale', 'ASC');
        }

        $query = $qb->getQuery();
        $result = $query->execute();

        return $result;
    }
    public function entrepriseListeEntreprisesELK($entreprise)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('p.id_entreprise', 'p.raison_sociale')
            ->distinct()
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->where('o.statut = 1')
            ->andWhere('p.raison_sociale = :entreprise') // Ajout de la condition
            ->setParameter('entreprise', $entreprise)
            ->orderBy('p.raison_sociale', 'ASC');

        $query = $qb->getQuery();
        $result = $query->getResult(); // Utilisation de getResult()

        return !empty($result) ? $result : 0; // Retourne 0 si aucun résultat
    }

    /*
    public function entrepriseListeEntreprisesCarriereBTP($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        // Requête avec pagination et filtrage
        $qb->select('DISTINCT p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'lsa', 'WITH', 'lsa.off_offre_id_offre = o.id_offre')
            ->where('lsa.off_liste_secteur_activite_id_secteur_activite IN (:secteurs)')
            ->andWhere('o.statut = 1')
            ->andWhere("p.raison_sociale NOT LIKE 'slash%'")
            ->andWhere(
                $qb->expr()->orX(
                    'o.date_creation > :threeMonthsAgo',
                    $qb->expr()->andX(
                        'o.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2',
                        'o.date_creation > :oneYearAgo'
                    )
                )
            )
            ->setParameter('secteurs', [4, 11, 12, 18, 46])
            ->setParameter('threeMonthsAgo', new \DateTime('-3 months'))
            ->setParameter('oneYearAgo', new \DateTime('-1 year'))
            ->orderBy('p.raison_sociale', 'ASC');

        if ($letter === '0-9') {
            $qb->andWhere(
                $qb->expr()->orX(
                    'p.raison_sociale LIKE :zero',
                    'p.raison_sociale LIKE :one',
                    'p.raison_sociale LIKE :two',
                    'p.raison_sociale LIKE :three',
                    'p.raison_sociale LIKE :four',
                    'p.raison_sociale LIKE :five',
                    'p.raison_sociale LIKE :six',
                    'p.raison_sociale LIKE :seven',
                    'p.raison_sociale LIKE :eight',
                    'p.raison_sociale LIKE :nine'
                )
            )
                ->setParameters([
                    'zero' => '0%',
                    'one' => '1%',
                    'two' => '2%',
                    'three' => '3%',
                    'four' => '4%',
                    'five' => '5%',
                    'six' => '6%',
                    'seven' => '7%',
                    'eight' => '8%',
                    'nine' => '9%'
                ]);
        } elseif ($letter !== '') {
            $qb->andWhere('p.raison_sociale LIKE :letter')
                ->setParameter('letter', $letter . '%');
        }

        return $qb->getQuery();
    }
    */

    public function entrepriseListeEntreprisesCarriereBTP(array $ids)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('p')
            ->from('App\Entity\EntrEntreprise', 'p')
            ->where('p.id_entreprise IN (:ids)')
            ->setParameter('ids', $ids)
            ->orderBy('p.raison_sociale', 'ASC');

        return $qb->getQuery();
    }

    public function entrepriseListeEntreprisesCarriereDistribution($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        // Requête avec pagination et filtrage
        $qb->select('DISTINCT p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'lsa', 'WITH', 'lsa.off_offre_id_offre = o.id_offre')
            ->where('lsa.off_liste_secteur_activite_id_secteur_activite IN (:secteurs)')
            ->andWhere('o.statut = 1')
            ->andWhere("p.raison_sociale NOT LIKE 'slash%'")
            ->andWhere(
                $qb->expr()->orX(
                    'o.date_creation > :threeMonthsAgo',
                    $qb->expr()->andX(
                        'o.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2',
                        'o.date_creation > :oneYearAgo'
                    )
                )
            )
            ->setParameter('secteurs', [16, 17, 47, 48])
            ->setParameter('threeMonthsAgo', new \DateTime('-3 months'))
            ->setParameter('oneYearAgo', new \DateTime('-1 year'))
            ->orderBy('p.raison_sociale', 'ASC');

        if ($letter === '0-9') {
            $qb->andWhere(
                $qb->expr()->orX(
                    'p.raison_sociale LIKE :zero',
                    'p.raison_sociale LIKE :one',
                    'p.raison_sociale LIKE :two',
                    'p.raison_sociale LIKE :three',
                    'p.raison_sociale LIKE :four',
                    'p.raison_sociale LIKE :five',
                    'p.raison_sociale LIKE :six',
                    'p.raison_sociale LIKE :seven',
                    'p.raison_sociale LIKE :eight',
                    'p.raison_sociale LIKE :nine'
                )
            )
                ->setParameters([
                    'zero' => '0%',
                    'one' => '1%',
                    'two' => '2%',
                    'three' => '3%',
                    'four' => '4%',
                    'five' => '5%',
                    'six' => '6%',
                    'seven' => '7%',
                    'eight' => '8%',
                    'nine' => '9%'
                ]);
        } elseif ($letter !== '') {
            $qb->andWhere('p.raison_sociale LIKE :letter')
                ->setParameter('letter', $letter . '%');
        }

        return $qb->getQuery();
    }
    public function entrepriseListeEntreprisesCarriereIndustrie($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        // Requête avec pagination et filtrage
        $qb->select('DISTINCT p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'lsa', 'WITH', 'lsa.off_offre_id_offre = o.id_offre')
            ->where('lsa.off_liste_secteur_activite_id_secteur_activite IN (:secteurs)')
            ->andWhere('o.statut = 1')
            ->andWhere("p.raison_sociale NOT LIKE 'slash%'")
            ->andWhere(
                $qb->expr()->orX(
                    'o.date_creation > :threeMonthsAgo',
                    $qb->expr()->andX(
                        'o.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2',
                        'o.date_creation > :oneYearAgo'
                    )
                )
            )
            ->setParameter('secteurs', [2,5,11,20,22,24,29,39,23])
            ->setParameter('threeMonthsAgo', new \DateTime('-3 months'))
            ->setParameter('oneYearAgo', new \DateTime('-1 year'))
            ->orderBy('p.raison_sociale', 'ASC');

        if ($letter === '0-9') {
            $qb->andWhere(
                $qb->expr()->orX(
                    'p.raison_sociale LIKE :zero',
                    'p.raison_sociale LIKE :one',
                    'p.raison_sociale LIKE :two',
                    'p.raison_sociale LIKE :three',
                    'p.raison_sociale LIKE :four',
                    'p.raison_sociale LIKE :five',
                    'p.raison_sociale LIKE :six',
                    'p.raison_sociale LIKE :seven',
                    'p.raison_sociale LIKE :eight',
                    'p.raison_sociale LIKE :nine'
                )
            )
                ->setParameters([
                    'zero' => '0%',
                    'one' => '1%',
                    'two' => '2%',
                    'three' => '3%',
                    'four' => '4%',
                    'five' => '5%',
                    'six' => '6%',
                    'seven' => '7%',
                    'eight' => '8%',
                    'nine' => '9%'
                ]);
        } elseif ($letter !== '') {
            $qb->andWhere('p.raison_sociale LIKE :letter')
                ->setParameter('letter', $letter . '%');
        }

        return $qb->getQuery();
    }
    public function entrepriseListeEntreprisesCarriereInformatique($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        // Requête avec pagination et filtrage
        $qb->select('DISTINCT p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'lsa', 'WITH', 'lsa.off_offre_id_offre = o.id_offre')
            ->where('lsa.off_liste_secteur_activite_id_secteur_activite IN (:secteurs)')
            ->andWhere('o.statut = 1')
            ->andWhere("p.raison_sociale NOT LIKE 'slash%'")
            ->andWhere(
                $qb->expr()->orX(
                    'o.date_creation > :threeMonthsAgo',
                    $qb->expr()->andX(
                        'o.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2',
                        'o.date_creation > :oneYearAgo'
                    )
                )
            )
            ->setParameter('secteurs', [10,11,19,27])
            ->setParameter('threeMonthsAgo', new \DateTime('-3 months'))
            ->setParameter('oneYearAgo', new \DateTime('-1 year'))
            ->orderBy('p.raison_sociale', 'ASC');

        if ($letter === '0-9') {
            $qb->andWhere(
                $qb->expr()->orX(
                    'p.raison_sociale LIKE :zero',
                    'p.raison_sociale LIKE :one',
                    'p.raison_sociale LIKE :two',
                    'p.raison_sociale LIKE :three',
                    'p.raison_sociale LIKE :four',
                    'p.raison_sociale LIKE :five',
                    'p.raison_sociale LIKE :six',
                    'p.raison_sociale LIKE :seven',
                    'p.raison_sociale LIKE :eight',
                    'p.raison_sociale LIKE :nine'
                )
            )
                ->setParameters([
                    'zero' => '0%',
                    'one' => '1%',
                    'two' => '2%',
                    'three' => '3%',
                    'four' => '4%',
                    'five' => '5%',
                    'six' => '6%',
                    'seven' => '7%',
                    'eight' => '8%',
                    'nine' => '9%'
                ]);
        } elseif ($letter !== '') {
            $qb->andWhere('p.raison_sociale LIKE :letter')
                ->setParameter('letter', $letter . '%');
        }

        return $qb->getQuery();
    }
    public function entrepriseListeEntreprisesCarriereMedicale($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        // Requête avec pagination et filtrage
        $qb->select('DISTINCT p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'lsa', 'WITH', 'lsa.off_offre_id_offre = o.id_offre')
            ->where('lsa.off_liste_secteur_activite_id_secteur_activite IN (:secteurs)')
            ->andWhere('o.statut = 1')
            ->andWhere("p.raison_sociale NOT LIKE 'slash%'")
            ->andWhere(
                $qb->expr()->orX(
                    'o.date_creation > :threeMonthsAgo',
                    $qb->expr()->andX(
                        'o.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2',
                        'o.date_creation > :oneYearAgo'
                    )
                )
            )
            ->setParameter('secteurs', [26,38])
            ->setParameter('threeMonthsAgo', new \DateTime('-3 months'))
            ->setParameter('oneYearAgo', new \DateTime('-1 year'))
            ->orderBy('p.raison_sociale', 'ASC');

        if ($letter === '0-9') {
            $qb->andWhere(
                $qb->expr()->orX(
                    'p.raison_sociale LIKE :zero',
                    'p.raison_sociale LIKE :one',
                    'p.raison_sociale LIKE :two',
                    'p.raison_sociale LIKE :three',
                    'p.raison_sociale LIKE :four',
                    'p.raison_sociale LIKE :five',
                    'p.raison_sociale LIKE :six',
                    'p.raison_sociale LIKE :seven',
                    'p.raison_sociale LIKE :eight',
                    'p.raison_sociale LIKE :nine'
                )
            )
                ->setParameters([
                    'zero' => '0%',
                    'one' => '1%',
                    'two' => '2%',
                    'three' => '3%',
                    'four' => '4%',
                    'five' => '5%',
                    'six' => '6%',
                    'seven' => '7%',
                    'eight' => '8%',
                    'nine' => '9%'
                ]);
        } elseif ($letter !== '') {
            $qb->andWhere('p.raison_sociale LIKE :letter')
                ->setParameter('letter', $letter . '%');
        }

        return $qb->getQuery();
    }
    public function entrepriseListeEntreprisesCarriereRestaurant($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        // Requête avec pagination et filtrage
        $qb->select('DISTINCT p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'lsa', 'WITH', 'lsa.off_offre_id_offre = o.id_offre')
            ->where('lsa.off_liste_secteur_activite_id_secteur_activite IN (:secteurs)')
            ->andWhere('o.statut = 1')
            ->andWhere("p.raison_sociale NOT LIKE 'slash%'")
            ->andWhere(
                $qb->expr()->orX(
                    'o.date_creation > :threeMonthsAgo',
                    $qb->expr()->andX(
                        'o.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2',
                        'o.date_creation > :oneYearAgo'
                    )
                )
            )
            ->setParameter('secteurs', [17,28])
            ->setParameter('threeMonthsAgo', new \DateTime('-3 months'))
            ->setParameter('oneYearAgo', new \DateTime('-1 year'))
            ->orderBy('p.raison_sociale', 'ASC');

        if ($letter === '0-9') {
            $qb->andWhere(
                $qb->expr()->orX(
                    'p.raison_sociale LIKE :zero',
                    'p.raison_sociale LIKE :one',
                    'p.raison_sociale LIKE :two',
                    'p.raison_sociale LIKE :three',
                    'p.raison_sociale LIKE :four',
                    'p.raison_sociale LIKE :five',
                    'p.raison_sociale LIKE :six',
                    'p.raison_sociale LIKE :seven',
                    'p.raison_sociale LIKE :eight',
                    'p.raison_sociale LIKE :nine'
                )
            )
                ->setParameters([
                    'zero' => '0%',
                    'one' => '1%',
                    'two' => '2%',
                    'three' => '3%',
                    'four' => '4%',
                    'five' => '5%',
                    'six' => '6%',
                    'seven' => '7%',
                    'eight' => '8%',
                    'nine' => '9%'
                ]);
        } elseif ($letter !== '') {
            $qb->andWhere('p.raison_sociale LIKE :letter')
                ->setParameter('letter', $letter . '%');
        }

        return $qb->getQuery();
    }

    public function entrepriseListeEntreprisesDirectAlternance($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        // Requête avec pagination et filtrage
        $qb->select('DISTINCT p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
            ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
            ->join('App\Entity\OffLienOffreListeSecteurActivite', 'lsa', 'WITH', 'lsa.off_offre_id_offre = o.id_offre')
            ->where('o.statut = 1')
            ->andWhere('o.off_liste_type_contrat_id_type_contrat = 6') // Contrat d'alternance
            ->andWhere("p.raison_sociale NOT LIKE 'slash%'")
            ->andWhere(
                $qb->expr()->orX(
                    'o.date_creation > :threeMonthsAgo',
                    $qb->expr()->andX(
                        'o.com_liste_type_offre_commerciale_id_type_offre_commerciale = 2',
                        'o.date_creation > :oneYearAgo'
                    )
                )
            )
            ->setParameter('threeMonthsAgo', new \DateTime('-3 months'))
            ->setParameter('oneYearAgo', new \DateTime('-1 year'))
            ->orderBy('p.raison_sociale', 'ASC');

        if ($letter === '0-9') {
            $qb->andWhere(
                $qb->expr()->orX(
                    'p.raison_sociale LIKE :zero',
                    'p.raison_sociale LIKE :one',
                    'p.raison_sociale LIKE :two',
                    'p.raison_sociale LIKE :three',
                    'p.raison_sociale LIKE :four',
                    'p.raison_sociale LIKE :five',
                    'p.raison_sociale LIKE :six',
                    'p.raison_sociale LIKE :seven',
                    'p.raison_sociale LIKE :eight',
                    'p.raison_sociale LIKE :nine'
                )
            )
                ->setParameters([
                    'zero' => '0%',
                    'one' => '1%',
                    'two' => '2%',
                    'three' => '3%',
                    'four' => '4%',
                    'five' => '5%',
                    'six' => '6%',
                    'seven' => '7%',
                    'eight' => '8%',
                    'nine' => '9%'
                ]);
        } elseif ($letter !== '') {
            $qb->andWhere('p.raison_sociale LIKE :letter')
                ->setParameter('letter', $letter . '%');
        }

        return $qb->getQuery();
    }
    public function entrepriseDetailsOffres($id) {
        if ($id==36106)
        {
            $max =3300;
        }else
            $max=100;
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $qb->select('c.geo_ville, c.id_offre, c.intitule, c.rep_url, c.user_user_id_user, c.date_soumission, d.libelle as type_contrat, f.libelle as region, g.libelle as fourchette_remu, i.libelle as departement')
            //$qb->select('c.geo_ville, c.id_offre, c.intitule, c.date_soumission, d.libelle as type_contrat')
            ->from('App\Entity\EntrEntreprise', 'a')
            ->join('App\Entity\UserUser', 'b', 'WITH', 'a.id_entreprise = b.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffOffre', 'c', 'WITH', 'b.id_user = c.user_user_id_user')
            ->join('App\Entity\OffListeTypeContrat', 'd', 'WITH', 'c.off_liste_type_contrat_id_type_contrat = d.id_type_contrat')
            ->leftJoin('App\Entity\OffLienOffreGeoListeRegion', 'e', 'WITH', 'c.id_offre = e.off_offre_id_offre')
            ->leftJoin('App\Entity\GeoListeRegion', 'f', 'WITH', 'e.geo_liste_region_id_region = f.id_region')
            ->leftJoin('App\Entity\OffFourchetteRemuneration', 'g', 'WITH', 'c.off_fourchette_remuneration_id_fourchette_remuneration = g.id_fourchette_remuneration' )
            ->leftJoin('App\Entity\OffLienOffreGeoDepartement', 'h', 'WITH', 'c.id_offre = h.off_offre_id_offre' )
            ->leftJoin('App\Entity\GeoDepartement', 'i', 'WITH', 'h.geo_departement_id_departement = i.id_departement' )
            ->where('a.id_entreprise = :id')
            ->andwhere('c.statut = 1')
            ->orderBy('c.date_soumission', 'DESC')
            ->setMaxResults($max)
            ->setParameters(
                new ArrayCollection([
                    new Parameter('id', $id),
                ])
            );
        $query = $qb->getQuery();
        return $query->execute();
    }

    public function entrepriseListeOffres(int $id): array
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('c.geo_ville, c.id_offre, c.intitule, c.rep_url, c.user_user_id_user, c.date_soumission, d.libelle as type_contrat, f.libelle as region, g.libelle as fourchette_remu, i.libelle as departement')
            ->from('App\Entity\EntrEntreprise', 'a')
            ->join('App\Entity\UserUser', 'b', 'WITH', 'a.id_entreprise = b.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffOffre', 'c', 'WITH', 'b.id_user = c.user_user_id_user')
            ->join('App\Entity\OffListeTypeContrat', 'd', 'WITH', 'c.off_liste_type_contrat_id_type_contrat = d.id_type_contrat')
            ->leftJoin('App\Entity\OffLienOffreGeoListeRegion', 'e', 'WITH', 'c.id_offre = e.off_offre_id_offre')
            ->leftJoin('App\Entity\GeoListeRegion', 'f', 'WITH', 'e.geo_liste_region_id_region = f.id_region')
            ->leftJoin('App\Entity\OffFourchetteRemuneration', 'g', 'WITH', 'c.off_fourchette_remuneration_id_fourchette_remuneration = g.id_fourchette_remuneration')
            ->leftJoin('App\Entity\OffLienOffreGeoDepartement', 'h', 'WITH', 'c.id_offre = h.off_offre_id_offre')
            ->leftJoin('App\Entity\GeoDepartement', 'i', 'WITH', 'h.geo_departement_id_departement = i.id_departement')
            ->where('a.id_entreprise = :id')
            ->andWhere('c.statut = 1')
            ->orderBy('c.intitule', 'ASC') // Sorting alphabetically by 'intitule'
            ->setParameter('id', $id);

        $query = $qb->getQuery();
        return $query->getResult();
    }


    public function getCountOffersByEntreprise($id) {

        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('COUNT(c.id_offre) as nb_offres')
            ->from('App\Entity\EntrEntreprise', 'a')
            ->join('App\Entity\UserUser', 'b', 'WITH', 'a.id_entreprise = b.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffOffre', 'c', 'WITH', 'b.id_user = c.user_user_id_user')
            ->where('a.id_entreprise = :id')
            ->andwhere('c.statut = 1')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('id', $id),
                ])
            );

        $query = $qb->getQuery();

        return $query->execute();
    }

    public function getEntrepriseById($id)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a.site_web',
            'a.raison_sociale',
            'a.presentation_societe',
            'b.adresse1',
            'b.adresse2',
            'b.code_postal',
            'b.nom_ville',
        )
            ->setParameter('id', $id)
            ->from('App\Entity\EntrEntreprise', 'a')
            ->where('a.id_entreprise = :id')
            ->join('App\Entity\GeoAdresse', 'b', 'WITH', 'a.geo_adresse_id_adresse = b.id_adresse')
        ;

        $query = $qb->getQuery();
        // $result = $query->getOneOrNullResult();
        $result = $query->execute();

        return ($result);
    }

    public function getOneEntrepriseById($id)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a.site_web',
            'a.raison_sociale',
            'a.presentation_societe',
            'b.adresse1',
            'b.adresse2',
            'b.code_postal',
            'b.nom_ville',
        )
            ->setParameter('id', $id)
            ->from('App\Entity\EntrEntreprise', 'a')
            ->where('a.id_entreprise = :id')
            ->join('App\Entity\GeoAdresse', 'b', 'WITH', 'a.geo_adresse_id_adresse = b.id_adresse')
        ;

        $query = $qb->getQuery();
        $result = $query->getOneOrNullResult();

        return ($result);
    }

    public function getEntrepriseProposantStages() {

        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a')
            ->from('App\Entity\EntrEntreprise', 'a')
            ->join('App\Entity\UserUser', 'b', 'WITH', 'a.id_entreprise = b.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffOffre', 'c', 'WITH', 'b.id_user = c.user_user_id_user')
            ->where('c.off_liste_type_contrat_id_type_contrat = 8')
            ->setMaxResults(6);

        $query = $qb->getQuery();

        return $query->execute();
    }

    public function getEntrepriseProposantAlternances() {

        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a')
            ->from('App\Entity\EntrEntreprise', 'a')
            ->join('App\Entity\UserUser', 'b', 'WITH', 'a.id_entreprise = b.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffOffre', 'c', 'WITH', 'b.id_user = c.user_user_id_user')
            ->where('c.off_liste_type_contrat_id_type_contrat = 6')
            ->setMaxResults(6);

        $query = $qb->getQuery();

        return $query->execute();
    }

    public function getEntreprisesById($ids) {

        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a')
            ->setParameter('ids', $ids)
            ->from('App\Entity\EntrEntreprise', 'a')
            ->where('a.id_entreprise IN (:ids)')
        ;

        $query = $qb->getQuery();

        return $query->execute();
    }
    public function getOneEntrepriseByIdPublicite($ids) {

        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('a')
            ->setParameter('ids', $ids)
            ->from('App\Entity\EntrEntreprise', 'a')
            ->where('a.id_entreprise = :ids')
        ;

        $query = $qb->getQuery();

        return $query->execute();
    }

    public function getEntreprises() {

        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();
        $arr = array(-2, -1, 1);

        $qb->select('a')
            ->setParameter(':arr', $arr)
            ->from('App\Entity\EntrEntreprise', 'a')
            ->join('App\Entity\UserUser', 'b', 'WITH', 'a.id_entreprise = b.entr_entreprise_id_entreprise')
            ->join('App\Entity\OffOffre', 'c', 'WITH', 'b.id_user = c.user_user_id_user')
            ->where('a.entr_profil_recruteur_id_profil_recruteur IN (:arr)')
            ->andwhere('a.logo IS NOT NULL')
            ->andwhere('a.agence_interim = 1')
            ->andwhere('c.statut = 1')
            ->setMaxResults(8)
            ->distinct()
        ;

        $query = $qb->getQuery();

        return $query->execute();
    }

    public function findClientsInfo($user_id)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $result = $qb->select(
            'a.id_entreprise',
            'a.logo',
            'a.raison_sociale',
            'a.entr_profil_recruteur_id_profil_recruteur',
            'a.entr_profil_recruteur_id_profil_recruteur_stage',
            'b.nom',
            'b.prenom',
            'b.email',
            'c.date_debut_souscription',
            'c.date_fin_souscription',
            'e.libelle AS diffusion'
        )
            ->addSelect('SUM(CASE WHEN d.statut = -2 THEN 1 ELSE 0 END) AS offre_delete')
            ->addSelect('SUM(CASE WHEN d.statut = -1 THEN 1 ELSE 0 END) AS offre_draft')
            ->addSelect('SUM(CASE WHEN d.statut = 0 THEN 1 ELSE 0 END) AS offre_offline')
            ->addSelect('SUM(CASE WHEN d.statut = 1 THEN 1 ELSE 0 END) AS offre_online')
            /*->addSelect('SUM(CASE WHEN f.date_candidature >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH) THEN 1 ELSE 0 END) AS this_month')
            ->addSelect('SUM(CASE WHEN f.date_candidature >= DATE_SUB(CURRENT_DATE(), INTERVAL 2 MONTH) AND f.date_candidature < DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH) THEN 1 ELSE 0 END) AS last_month')
            ->addSelect('SUM(CASE WHEN f.date_candidature >= DATE_SUB(CURRENT_DATE(), INTERVAL 3 MONTH) AND f.date_candidature < DATE_SUB(CURRENT_DATE(), INTERVAL 2 MONTH) THEN 1 ELSE 0 END) AS last_last_month')
            */->from('App\Entity\EntrEntreprise', 'a')
            ->join('App\Entity\UserUser', 'b', 'WITH', 'a.id_entreprise = b.entr_entreprise_id_entreprise')
            ->leftJoin('App\Entity\ComSouscriptionCommerciale', 'c', 'WITH', 'b.id_user = c.user_user_id_user')
            ->leftJoin('App\Entity\OffOffre', 'd', 'WITH', 'b.id_user = d.user_user_id_user')
            ->leftJoin('App\Entity\UserTypeDiffusion', 'e', 'WITH', 'b.type_diffusion = e.id_type_diffusion')
            //->leftJoin('App\Entity\OffCandidature', 'f', 'WITH', 'd.id_offre = f.off_offre_id_offre')
            ->where('b.id_user = :user_id')
            ->setParameter('user_id', $user_id)
            ->groupBy(
                'a.id_entreprise',
                'a.logo',
                'a.raison_sociale',
                'a.entr_profil_recruteur_id_profil_recruteur',
                'a.entr_profil_recruteur_id_profil_recruteur_stage',
                'b.nom',
                'b.prenom',
                'b.email',
                'c.date_debut_souscription',
                'c.date_fin_souscription',
                'e.libelle'
            )
            //->setMaxResults(20)
            ->getQuery()
            ->execute();

        return $result;
    }

    public function findClientsInfoForUserIds(array $userIds)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $results = $qb
            ->select(
                'a.id_entreprise',
                'a.logo',
                'a.raison_sociale',
                'a.entr_profil_recruteur_id_profil_recruteur',
                'a.entr_profil_recruteur_id_profil_recruteur_stage',
                'b.nom',
                'b.prenom',
                'b.email',
                'c.date_debut_souscription',
                'c.date_fin_souscription',
                'e.libelle AS diffusion',
                'SUM(CASE WHEN d.statut = -2 THEN 1 ELSE 0 END) AS offre_delete',
                'SUM(CASE WHEN d.statut = -1 THEN 1 ELSE 0 END) AS offre_draft',
                'SUM(CASE WHEN d.statut = 0 THEN 1 ELSE 0 END) AS offre_offline',
                'SUM(CASE WHEN d.statut = 1 THEN 1 ELSE 0 END) AS offre_online'
            )
            ->from('App\Entity\EntrEntreprise', 'a')
            ->join('App\Entity\UserUser', 'b', 'WITH', 'a.id_entreprise = b.entr_entreprise_id_entreprise')
            ->leftJoin('App\Entity\ComSouscriptionCommerciale', 'c', 'WITH', 'b.id_user = c.user_user_id_user')
            ->leftJoin('App\Entity\OffOffre', 'd', 'WITH', 'b.id_user = d.user_user_id_user')
            ->leftJoin('App\Entity\UserTypeDiffusion', 'e', 'WITH', 'b.type_diffusion = e.id_type_diffusion')
            ->where($qb->expr()->in('b.id_user', $userIds))
            ->groupBy(
                'a.id_entreprise',
                'a.logo',
                'a.raison_sociale',
                'a.entr_profil_recruteur_id_profil_recruteur',
                'a.entr_profil_recruteur_id_profil_recruteur_stage',
                'b.nom',
                'b.prenom',
                'b.email',
                'c.date_debut_souscription',
                'c.date_fin_souscription',
                'e.libelle'
            )
            ->getQuery()
            ->execute();

        return $results;
    }

    public function entrepriseOrderFilter($qb, $order)
    {
        switch ($order) {
            case 1: // order by position asc
                $qb->leftJoin(UserUser::class, 'uu', 'WITH', 'uu.idUser = u.idUser')
                    ->addOrderBy(EntrEntreprise::class . '.entrProfilRecruteurIdProfilRecruteur', 'ASC')
                    ->addOrderBy('uu.nbOffresEmploi', 'DESC');
                break;
            case 2: // order by position desc
                $qb->leftJoin(UserUser::class, 'uu', 'WITH', 'uu.idUser = u.idUser')
                    ->addOrderBy(EntrEntreprise::class . '.entrProfilRecruteurIdProfilRecruteur', 'DESC')
                    ->addOrderBy('uu.nbOffresEmploi', 'DESC');
                break;
            case 3: // order by Nbr offres en ligne ASC
                $qb->leftJoin(UserUser::class, 'uu', 'WITH', 'uu.idUser = u.idUser')
                    ->addOrderBy('uu.nbOffresEmploi', 'ASC');
                //$this->classe_nb_offre = true;
                break;
            case 4: // order by Nbr offres en ligne DESC
                $qb->leftJoin(UserUser::class, 'uu', 'WITH', 'uu.idUser = u.idUser')
                    ->addOrderBy('uu.nbOffresEmploi', 'DESC');
                //$this->classe_nb_offre = true;
                break;
            case 5: // order by Nbr de candidatures mail mois en cours ASC
                $qb->leftJoin(UserUser::class, 'uu', 'WITH', 'uu.idUser = u.idUser')
                    ->andWhere($qb->expr()->gte('uu.nbOffresEmploi', 0))
                    ->addOrderBy('uu.nbCandidaturesMailRecente', 'ASC');
                //$this->classe_nb_mail = true;
                break;
            case 6: // order by Nbr de candidatures mail mois en cours DESC
                $qb->leftJoin(UserUser::class, 'uu', 'WITH', 'uu.idUser = u.idUser')
                    ->andWhere($qb->expr()->gte('uu.nbOffresEmploi', 0))
                    ->addOrderBy('uu.nbCandidaturesMailRecente', 'DESC');
                //$this->classe_nb_mail = true;
                break;
            case 7: // order by Nbr de candidatures redirection mois en cours ASC
                $qb->leftJoin(UserUser::class, 'uu', 'WITH', 'uu.idUser = u.idUser')
                    ->andWhere($qb->expr()->gte('uu.nbOffresEmploi', 0))
                    ->addOrderBy('uu.nbCandidaturesRecente', 'ASC');
                //$this->classe_nb_redirect = true;
                break;
            case 8: // order by Nbr de candidatures redirection mois en cours DESC
                $qb->leftJoin(UserUser::class, 'uu', 'WITH', 'uu.idUser = u.idUser')
                    ->andWhere($qb->expr()->gte('uu.nbOffresEmploi', 0))
                    ->addOrderBy('uu.nbCandidaturesRecente', 'DESC');
                //$this->classe_nb_redirect = true;
                break;
            default:
                break;
        }
        return ($qb);
    }

    public function findEntreprisesAndClients($subType, $diff, $searchMail = null, $searchUser = null, $searchEntreprise = null, $page = 1, $perPage = 20)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $offset = ($page - 1) * $perPage;

        $qb
            ->select(
                'entr.id_entreprise',
                'entr.logo',
                'entr.raison_sociale',
                'entr.entr_profil_recruteur_id_profil_recruteur',
                'u.nom',
                'u.prenom',
                'u.email',
                'sc.date_fin_souscription',
                'utd.libelle AS diffusion',
                // 'SUM(CASE WHEN off.statut = -2 THEN 1 ELSE 0 END) AS offre_delete',
                // 'SUM(CASE WHEN off.statut = -1 THEN 1 ELSE 0 END) AS offre_draft',
                // 'SUM(CASE WHEN off.statut = 0 THEN 1 ELSE 0 END) AS offre_offline',
                'SUM(CASE WHEN off.statut = 1 THEN 1 ELSE 0 END) AS offre_online',
                // 'us.nb_offres_emploi AS nb_offres_emploi',
                // 'us.nb_offres_stage AS nb_offres_stage',
                // 'us.nb_offres_alternance AS nb_offres_alternance',
                'us.nb_offres_emploi_recente AS nb_offres_emploi_recente',
                'us.nb_offres_stage_recente AS nb_offres_stage_recente',
                'us.nb_offres_alternance_recente AS nb_offres_alternance_recente',
                'us.nb_candidatures_recente AS nb_candidatures_recente',
                'us.nb_candidatures_mail_recente AS nb_candidatures_mail_recente',
                'us.nb_candidatures_ancienne AS nb_candidatures_ancienne',
                'us.nb_candidatures_mail_ancienne AS nb_candidatures_mail_ancienne',
                'us.nb_candidatures_2mois AS nb_candidatures_2mois',
                'us.nb_candidatures_mail_2mois AS nb_candidatures_mail_2mois',
                'us.nb_candidatures_3mois AS nb_candidatures_3mois',
                'us.nb_candidatures_mail_3mois AS nb_candidatures_mail_3mois',
                'SUM(CASE WHEN tc.type_general = 2 THEN 1 ELSE 0 END) AS nb_offres_stage',
                'SUM(CASE WHEN tc.type_general = 3 THEN 1 ELSE 0 END) AS nb_offres_alternance',
            )
            //->distinct()
            ->from('App\Entity\UserUser', 'u')
            ->where('u.user_liste_type_id_type = :userListeTypeIdType')
            ->setMaxResults($perPage)
            ->setFirstResult($offset)
            ->setParameter('userListeTypeIdType', 2)
            ->leftJoin('App\Entity\UserTypeDiffusion', 'utd', 'WITH', 'u.type_diffusion = utd.id_type_diffusion')
            ->leftJoin('App\Entity\ComSouscriptionCommerciale', 'sc', 'WITH', 'u.id_user = sc.user_user_id_user')
            ->leftJoin('App\Entity\EntrEntreprise', 'entr', 'WITH', 'u.entr_entreprise_id_entreprise = entr.id_entreprise')
            ->leftJoin('App\Entity\OffOffre', 'off', 'WITH', 'u.id_user = off.user_user_id_user')
            ->leftJoin('App\Entity\UserStats', 'us', 'WITH', 'u.id_user = us.user_user_id_user')
            ->leftJoin('App\Entity\OffListeTypeContrat', 'tc', 'WITH', 'off.off_liste_type_contrat_id_type_contrat = tc.id_type_contrat')
            //->leftJoin('App\Entity\UserTypeDiffusion', 'utd', 'WITH', 'u.type_diffusion = utd.id_type_diffusion')
            ->groupBy(
                'entr.id_entreprise',
                'entr.logo',
                'entr.raison_sociale',
                'entr.entr_profil_recruteur_id_profil_recruteur',
                'u.nom',
                'u.prenom',
                'u.email',
                'sc.date_fin_souscription',
                'utd.libelle',
            );

        $subTypeMap = [
            'illimite' => [31, 33, 34, 40, 41, 44, 45, 47, 48],
            'limite' => [46, 43, 42, 41, 38, 39, 1, 2, 5, 6, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 60, 61, 50, 51],
            'gratuit' => [32, 52, 62],
        ];

        if (isset($subTypeMap[$subType])) {
            $qb
                ->andWhere('sc.com_offre_commerciale_id_offre_commerciale IN (:idOffreCommerciale)')
                ->andWhere('sc.date_fin_souscription > :currentDate')
                ->setParameter('idOffreCommerciale', $subTypeMap[$subType])
                ->setParameter('currentDate', new \DateTime());
        } else { // echu forcément
            $qb
                ->andWhere('sc.date_fin_souscription < :currentDate')
                ->andWhere('sc.date_debut_souscription IS NOT NULL')
                ->setParameter('currentDate', new \DateTime());
        }

        if ($diff) {
            $qb
                ->andWhere('utd.id_type_diffusion = :id_diffusion')
                ->setParameter('id_diffusion', $diff);
        }
        if ($searchMail) {
            $orConditions = $qb->expr()->orX();
            $orConditions->add(
                $qb
                    ->expr()->like('u.email', ':searchMail')
            );
            $qb
                ->setParameter('searchMail', '%' . $searchMail . '%')
                ->andWhere($orConditions);
        }
        if ($searchUser) {
            $explode_temp = explode(" ", $searchUser);
            $orConditions = $qb->expr()->orX();
            foreach ($explode_temp as $valueOr) {
                $orConditions->add(
                    $qb->expr()->like('u.nom', ':valueOr')
                );
                $orConditions->add(
                    $qb->expr()->like('u.prenom', ':valueOr')
                );
                $qb->setParameter('valueOr', '%' . $valueOr . '%');
            }
            $qb->andWhere($orConditions);
        }
        if ($searchEntreprise) {
            $searchEntreprise = urldecode($searchEntreprise);
            $searchTerms = explode(' ', $searchEntreprise);
            $orConditions = $qb->expr()->orX();
            foreach ($searchTerms as $term) {
                $orConditions->add(
                    $qb->expr()->like('entr.raison_sociale', ':entrepriseTerm')
                );
                $qb->setParameter('entrepriseTerm', '%' . $term . '%');
            }
            $qb->andWhere($orConditions);
        }

        $query = $qb->getQuery();
        $results = $query->getScalarResult();

        $totalCount = $qb->select('COUNT(DISTINCT entr.id_entreprise)')->getQuery()->getSingleScalarResult();

        return [
            'results' => $results,
            'totalCount' => $totalCount,
        ];
    }

    public function fetchCompanyByName($name, $limit)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb
            ->select(
                'entr.id_entreprise',
                'entr.logo',
                'entr.raison_sociale'
            )
            ->from('App\Entity\EntrEntreprise', 'entr');

        $name = urldecode($name);
        $searchTerms = explode(' ', $name);

        $orConditions = $qb->expr()->orX();
        foreach ($searchTerms as $i => $term) {
            $parameterName = 'entrepriseTerm' . $i;
            $orConditions->add(
                $qb->expr()->like("entr.raison_sociale", ':' . $parameterName)
            );
            $qb->setParameter($parameterName, '%' . $term . '%');
        }

        $qb
            ->andWhere($orConditions)
            ->setMaxResults($limit)
        ;
        $query = $qb->getQuery();
        $results = $query->getScalarResult();

        return $results;
    }


    // public function entrepriseListeEntreprisesBTP()
    // {
    //     $letter="aaaaaaaa";
    //     $em = $this->getEntityManager();
    //     $qb = $em->createQueryBuilder();

    //     if($letter == '') {
    //         $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
    //             ->distinct()
    //             ->from('App\Entity\EntrEntreprise', 'p')
    //             // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
    //             ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
    //             ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
    //             ->where('o.statut = 1')
    //             ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
    //             ->orderBy('p.raison_sociale', 'ASC')
    //         ;

    //     } elseif($letter == '0-9') {
    //         $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
    //             ->distinct()
    //             ->from('App\Entity\EntrEntreprise', 'p')
    //             // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
    //             ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
    //             ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
    //             ->where('o.statut = 1')
    //             ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
    //             ->andWhere('p.raison_sociale LIKE ?0')
    //             ->orWhere('p.raison_sociale LIKE ?1')
    //             ->orWhere('p.raison_sociale LIKE ?2')
    //             ->orWhere('p.raison_sociale LIKE ?3')
    //             ->orWhere('p.raison_sociale LIKE ?4')
    //             ->orWhere('p.raison_sociale LIKE ?5')
    //             ->orWhere('p.raison_sociale LIKE ?6')
    //             ->orWhere('p.raison_sociale LIKE ?7')
    //             ->orWhere('p.raison_sociale LIKE ?8')
    //             ->orWhere('p.raison_sociale LIKE ?9')
    //             ->setParameters(
    //                 new ArrayCollection([
    //                     new Parameter('0', '0'.'%'),
    //                     new Parameter('1', '1'.'%'),
    //                     new Parameter('2', '2'.'%'),
    //                     new Parameter('3', '3'.'%'),
    //                     new Parameter('4', '4'.'%'),
    //                     new Parameter('5', '5'.'%'),
    //                     new Parameter('6', '6'.'%'),
    //                     new Parameter('7', '7'.'%'),
    //                     new Parameter('8', '8'.'%'),
    //                     new Parameter('9', '9'.'%'),
    //                 ]))
    //             ->orderBy('p.raison_sociale', 'ASC');
    //     } else {
    //         $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
    //             ->distinct()
    //             ->from('App\Entity\EntrEntreprise', 'p')
    //             ->join('App\Entity\EntrLienEntrepriseOffListeSecteurActivite', 's', 'WITH', 's.entr_entreprise_id_entreprise = p.id_entreprise')
    //             ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
    //             ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
    //             ->where('o.statut = 1')
    //             ->where('s.off_liste_secteur_activite_id_secteur_activite IN (4, 11, 12, 18, 46)')
    //             ->andWhere('p.raison_sociale LIKE :letter')
    //             ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
    //             ->setParameter('letter', $letter.'%')
    //             ->orderBy('p.raison_sociale', 'ASC');
    //     }

    //     $query = $qb->getQuery();
    //     $result = $query->execute();

    //     return $result;
    // }

    public function getEntrepriseListeInterim()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->distinct()
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\EntrLienEntrepriseOffListeSecteurActivite', 's', 'WITH', 's.entr_entreprise_id_entreprise = p.id_entreprise')
            ->where('s.off_liste_secteur_activite_id_secteur_activite IN (4, 11, 12, 18, 46)')
            ->andWhere('p.actif = 1')
            ->andWhere('p.agence_interim = 1')
            ->orderBy('p.raison_sociale', 'ASC');

        $query = $qb->getQuery();
        $result = $query->execute();

        return $result;
    }

    public function getEntrepriseListeCabinetRecrutement()
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
            ->distinct()
            ->from('App\Entity\EntrEntreprise', 'p')
            ->join('App\Entity\EntrLienEntrepriseOffListeSecteurActivite', 's', 'WITH', 's.entr_entreprise_id_entreprise = p.id_entreprise')
            ->where('s.off_liste_secteur_activite_id_secteur_activite IN (4, 11, 12, 18, 46)')
            ->andWhere('p.actif = 1')
            ->andWhere('p.cabinet_recruteur = 1')
            ->orderBy('p.raison_sociale', 'ASC');

        $query = $qb->getQuery();
        $result = $query->execute();

        return $result;
    }

    // public function entrepriseListeEntreprisesBTP($letter)
    // {
    //     $em = $this->getEntityManager();
    //     $qb = $em->createQueryBuilder();

    //     $qb->select('DISTINCT p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
    //         ->from('App\Entity\EntrEntreprise', 'p')
    //         ->join('App\Entity\EntrLienEntrepriseOffListeSecteurActivite', 'eleolsa', 'WITH', 'eleolsa.entrEntreprise = p.id_entreprise')
    //         ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entrEntreprise = p.id_entreprise')
    //         ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.userUser = u.id_user')
    //         ->where($qb->expr()->in('eleolsa.offListeSecteurActivite', [4, 11, 12, 18, 46]))
    //         ->andWhere('p.actif = 1')
    //         ->andWhere('o.statut = 1');

    //     if ($letter != '') {
    //         if ($letter == '0-9') {
    //             $orConditions = [];
    //             for ($i = 0; $i <= 9; $i++) {
    //                 $orConditions[] = $qb->expr()->like('p.raisonSociale', ':letter_' . $i);
    //                 $qb->setParameter('letter_' . $i, $i . '%');
    //             }
    //             $qb->andWhere($qb->expr()->orX(...$orConditions));
    //         } else {
    //             $qb->andWhere('p.raisonSociale LIKE :letter')
    //                 ->setParameter('letter', $letter . '%');
    //         }
    //     }

    //     $qb->andWhere($qb->expr()->notLike('p.raisonSociale', 'slash%'))
    //         ->orderBy('p.raisonSociale', 'ASC');

    //     $query = $qb->getQuery();
    //     $result = $query->execute();

    //     return $result;
    // }

    public function entrepriseListeEntreprisesBTP($letter)
    {
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();

        if($letter == '') {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->join('App\Entity\EntrLienEntrepriseOffListeSecteurActivite', 's', 'WITH', 's.entr_entreprise_id_entreprise = p.id_entreprise')
                ->where('o.statut = 1')
                ->andWhere('s.off_liste_secteur_activite_id_secteur_activite IN (4, 11, 12, 18, 46)')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->andWhere('p.actif = 1')
                ->orderBy('p.raison_sociale', 'ASC')
            ;

        } elseif($letter == '0-9') {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->where('o.statut = 1')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->andWhere('p.raison_sociale LIKE ?0')
                ->orWhere('p.raison_sociale LIKE ?1')
                ->orWhere('p.raison_sociale LIKE ?2')
                ->orWhere('p.raison_sociale LIKE ?3')
                ->orWhere('p.raison_sociale LIKE ?4')
                ->orWhere('p.raison_sociale LIKE ?5')
                ->orWhere('p.raison_sociale LIKE ?6')
                ->orWhere('p.raison_sociale LIKE ?7')
                ->orWhere('p.raison_sociale LIKE ?8')
                ->orWhere('p.raison_sociale LIKE ?9')
                ->setParameters(
                    new ArrayCollection([
                        new Parameter('0', '0'.'%'),
                        new Parameter('1', '1'.'%'),
                        new Parameter('2', '2'.'%'),
                        new Parameter('3', '3'.'%'),
                        new Parameter('4', '4'.'%'),
                        new Parameter('5', '5'.'%'),
                        new Parameter('6', '6'.'%'),
                        new Parameter('7', '7'.'%'),
                        new Parameter('8', '8'.'%'),
                        new Parameter('9', '9'.'%'),
                    ]))
                ->orderBy('p.raison_sociale', 'ASC');
        } else {
            $qb->select('p.id_entreprise', 'p.raison_sociale', 'p.logo', 'p.logo_fond')
                ->distinct()
                ->from('App\Entity\EntrEntreprise', 'p')
                // ->join('App\Entity\GeoAdresse', 'g', 'WITH', 'g.id_adresse = p.geo_adresse_id_adresse')
                ->join('App\Entity\UserUser', 'u', 'WITH', 'u.entr_entreprise_id_entreprise = p.id_entreprise')
                ->join('App\Entity\OffOffre', 'o', 'WITH', 'o.user_user_id_user = u.id_user')
                ->join('App\Entity\EntrLienEntrepriseOffListeSecteurActivite', 's', 'WITH', 's.entr_entreprise_id_entreprise = p.id_entreprise')
                ->where('o.statut = 1')
                ->andWhere('s.off_liste_secteur_activite_id_secteur_activite IN (4, 11, 12, 18, 46)')
                ->andWhere('p.raison_sociale LIKE :letter')
                ->andWhere("p.raison_sociale NOT LIKE 'slash%' ")
                ->andWhere('p.actif = 1')
                ->setParameter('letter', $letter.'%')
                ->orderBy('p.raison_sociale', 'ASC');
        }

        $query = $qb->getQuery();
        $result = $query->execute();

        return $result;
    }

    public function createSuiviClientQuery($searchTerm = null)
    {
        $queryBuilder = $this->createQueryBuilder('e')
            ->select('e.raison_sociale', 'e.entr_profil_recruteur_id_profil_recruteur AS position', 'u.nom', 
                    'u.prenom', 'u.email', 'u.type_diffusion', 'us.date_fin_abo', 'us.nb_offres_emploi', 
                    'us.nb_offres_stage', 'us.nb_offres_alternance', 'us.nb_offres_emploi_recente', 
                    'us.nb_offres_stage_recente', 'us.nb_offres_alternance_recente', 'us.nb_candidatures_recente', 
                    'us.nb_candidatures_mail_recente', 'us.nb_candidatures_ancienne', 'us.nb_candidatures_mail_ancienne', 
                    'us.nb_candidatures_2mois', 'us.nb_candidatures_mail_2mois', 'us.nb_candidatures_3mois', 
                    'us.nb_candidatures_mail_3mois')
            ->leftJoin('App\Entity\UserUser', 'u', 'WITH', 'e.id_entreprise = u.entr_entreprise_id_entreprise')
            ->leftJoin('App\Entity\UserStats', 'us', 'WITH', 'u.id_user = us.user_user_id_user');
            // ->groupBy('e.raison_sociale');

            if ($searchTerm !== null) {
                $queryBuilder->andWhere('e.raison_sociale LIKE :searchTerm')
                            ->setParameter('searchTerm', '%'.$searchTerm.'%');
            }

            $query = $queryBuilder->getQuery();
            return $query;
    }



    /* Direct Emploi Mobile */
    public function findByName(string $name): ?string
    {
        $result = $this->createQueryBuilder('e')
            ->select('e.raison_sociale')
            ->andWhere('e.raison_sociale LIKE :name')
            ->setParameter('name', $name )
            ->setMaxResults(1) // Limit the result to 1
            ->getQuery()
            ->getOneOrNullResult();

        return $result ? $result['raison_sociale'] : null;
    }

}