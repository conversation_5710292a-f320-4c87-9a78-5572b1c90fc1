<?php

namespace App\Repository;

use App\Entity\CvCentreInteret;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CvCentreInteret|null find($id, $lockMode = null, $lockVersion = null)
 * @method CvCentreInteret|null findOneBy(array $criteria, array $orderBy = null)
 * @method CvCentreInteret[]    findAll()
 * @method CvCentreInteret[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CvCentreInteretRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CvCentreInteret::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(CvCentreInteret $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(CvCentreInteret $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    // /**
    //  * @return CvCentreInteret[] Returns an array of CvCentreInteret objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('c.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?CvCentreInteret
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
