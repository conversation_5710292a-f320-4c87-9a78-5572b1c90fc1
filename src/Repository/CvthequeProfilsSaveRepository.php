<?php

namespace App\Repository;

use App\Entity\CvthequeProfilsSave;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Query\Parameter;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\Expr\Join;

/**
 * @extends ServiceEntityRepository<CvthequeProfilsSave>
 *
 * @method CvthequeProfilsSave|null find($id, $lockMode = null, $lockVersion = null)
 * @method CvthequeProfilsSave|null findOneBy(array $criteria, array $orderBy = null)
 * @method CvthequeProfilsSave[]    findAll()
 * @method CvthequeProfilsSave[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CvthequeProfilsSaveRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CvthequeProfilsSave::class);
    }

    public function save(CvthequeProfilsSave $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(CvthequeProfilsSave $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return CvthequeProfilsSave[] Returns an array of CvthequeProfilsSave objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('c.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?CvthequeProfilsSave
//    {
//        return $this->createQueryBuilder('c')
//            ->andWhere('c.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }

   public function findAllByUser($user_user)
   {
    $em = $this->getEntityManager();
    $qb = $em->createQueryBuilder();            
    $qb->select('c.date_creation as date_creation', 
    'c.id_profils_save as profil_save', 
    'a.nom as nom', 
    'a.prenom as prenom', 
    'a.id_profils as id_profils', 
    'd.libelle as domaine', 
    'e.libelle as departement', 
    'f.libelle as region')
        ->from('App\Entity\CvthequeProfilsSave', 'c')
            
            ->leftJoin('App\Entity\CvtequeProfils', 'a', Join::WITH, 'a.id_profils = c.id_cvtheque_profils')
            ->leftJoin('App\Entity\OffListeSecteurActivite', 'd', Join::WITH, 'a.off_liste_secteur_activite_id_secteur_activite = d.id_secteur_activite')
            ->leftJoin('App\Entity\GeoDepartement', 'e', Join::WITH, 'a.id_departement = e.id_departement')
            ->leftJoin('App\Entity\GeoListeRegion', 'f', Join::WITH, 'a.id_region = f.id_region')
            ->andWhere('c.user_user_id_user = :user_user')
            ->setParameter('user_user', $user_user);
            $query = $qb->getQuery();
            $result = $query->execute();

            return $result;
   }


    public function saveNewProfil($id_profils, $user_user)
    {

        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();            
        $qb->select('a')
            ->from('App\Entity\CvthequeProfilsSave', 'a')
            ->where('a.user_user_id_user = :user_user')
            ->andWhere('a.id_cvtheque_profils = :id_profils')
            // ->andWhere('a.is_anonym = 0')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('id_profils', $id_profils),
                    new Parameter('user_user', $user_user)
                ]));
            
            $query = $qb->getQuery();
            $result = $query->getOneOrNullResult();

            if(!$result) {
                $em = $this->getEntityManager();
                $save_profil = new CvthequeProfilsSave();
                $save_profil->setUserUserIdUser($user_user);
                $save_profil->setIdCvthequeProfils($id_profils);
                $save_profil->setDateCreation(new \DateTime());
                $em->persist($save_profil);
                $em->flush();
            }   


        return true;
    }

    public function deleteProfilsSave($id_profils_save, $user_user)
    {
    
        $em = $this->getEntityManager();
        $qb = $em->createQueryBuilder();            
        $qb->select('a')
            ->from('App\Entity\CvthequeProfilsSave', 'a')
            ->where('a.user_user_id_user = :user_user')
            ->andWhere('a.id_profils_save = :id_profils')
            // ->andWhere('a.is_anonym = 0')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('id_profils', $id_profils_save),
                    new Parameter('user_user', $user_user)
                ]));
            
            $query = $qb->getQuery();
            $result = $query->getOneOrNullResult();

            if($result){
                $em->remove($result);
                $em->flush();
            }
            

    }


        // public function findOneByIdProfils($id_profils, $user_user): ?CvthequeProfilsSave
        // {

        //     return $this->createQueryBuilder('c')
        //         ->andWhere('c.exampleField = :val')
        //         ->setParameter('val', $id_profils)
        //         ->getQuery()
        //         ->getOneOrNullResult()
        //     ;
        // }

}
