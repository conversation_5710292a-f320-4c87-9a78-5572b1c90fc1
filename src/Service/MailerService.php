<?php

namespace App\Service;

use App\Entity\CommonMessage;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Mime\Address;

class MailerService
{

    public function __construct(private MailerInterface $mailer, private ManagerRegistry $doctrine, private Filesystem $filesystem)
    {

    }
    public function sendPasswordResetEmail(string $recipientEmail, string $resetUrl, ?string $recipientName = 'Utilisateur'): void
    {
        // Sujet de l'e-mail
        $subject = 'Réinitialisation de votre mot de passe';

        // Corps de l'e-mail
        $bodyHtml = $this->buildPasswordResetBody($resetUrl, $recipientName);

        // Création de l'objet Email
        $email = (new Email())
            ->from(new Address('<EMAIL>', 'Direct Emploi'))
            ->to(new Address($recipientEmail, $recipientName))
            ->subject($subject)
            ->html($bodyHtml);

        // Envoi de l'e-mail
        $this->mailer->send($email);
    }

    private function buildPasswordResetBody(string $resetUrl, string $recipientName): string
    {
        // Construire le corps de l'e-mail en HTML
        return sprintf(
            '<p>Bonjour %s,</p>
            <p>Vous avez demandé à réinitialiser votre mot de passe. Veuillez cliquer sur le lien ci-dessous pour réinitialiser votre mot de passe :</p>
            <p><a href="%s">Réinitialiser mon mot de passe</a></p>
            <p>Si vous n\'avez pas demandé cette réinitialisation, veuillez ignorer cet e-mail.</p>',
            htmlspecialchars($recipientName),
            htmlspecialchars($resetUrl)
        );
    }

    public function envoiMailSimple($label, $email_recrut, $email, $replacementArray, $lm = null, $cv = null, $sender_name = null, $is_loged = null, $candid_mail = null): void {
        $sujet = $this->doctrine->getRepository(CommonMessage::class)->findOneBy(
            ['label' => 'SUJET_'.$label]
        );

        // switch (sfConfig::get('app_application')) {
        //     case 'carriere_btp':
        //         $linkLogo = 'https://www.carriere-btp.com/images/carrierebtp.jpg';
        //         break;
        //     case 'carriere_distribution':
        //         $linkLogo = 'https://www.carriere-distribution.com/images/carrieredistribution.jpg';
        //         break;
        //     case 'carriere_industrie':
        //         $linkLogo = 'https://www.carriere-industrie.com/images/carriereindustrie.jpg';
        //         break;
        //     case 'carriere_informatique':
        //         $linkLogo = 'https://www.carriere-informatique.com/images/carriereinformatique.jpg';
        //         break;
        //     case 'carriere_medicale':
        //         $linkLogo = 'https://www.carriere-medicale.com/images/carrieremedicale.jpg';
        //         break;
        //     case 'carriere_restauration':
        //         $linkLogo = 'https://www.carriere-restauration.com/images/carriererestauration.jpg';
        //         break;
        //     case 'direct_alternance':
        //         $linkLogo = 'https://www.directalternance.com/images/directalternance.jpg';
        //         break;
        //     case 'direct_emploi':
        //         $linkLogo = 'https://www.directemploi.com/images/directemploi.jpg';
        //         break;
        //     case 'direct_etudiant':
        //         $linkLogo = 'https://www.directetudiant.com/images/directetudiant.jpg';
        //         break;
        // }

        $linkLogo = 'https://www.directemploi.com/images/directemploi.jpg';
        $msg = $this->doctrine->getRepository(CommonMessage::class)->findOneBy(['label' => $label])->getValue();


        $corps = str_replace('##LINK_LOGO##', $linkLogo,
                    str_replace(array_keys($replacementArray),
                        array_values($replacementArray),
                        $msg)
                );


            $fromAdress = "<EMAIL>";

        $bodyTxt = strip_tags($corps);
        $bodyHtml = nl2br($corps);

        if (strpos("SUJET_MAIL_CANDIDATURE", $sujet->getLabel()) >= 0) {
            if ("SUJET_MAIL_CANDIDATURE" == $sujet->getLabel()) {
                $subject = "Nouvelle candidature sur votre offre";
            } elseif ("SUJET_MAIL_CANDIDATURE_CANDIDAT_NON_CONNECTE") {
                $subject = "Confirmation de votre candidature sur Direct Emploi";
            } elseif ("SUJET_MAIL_CANDIDATURE_CANDIDAT") {
                $subject = "Confirmation de votre candidature sur Direct Emploi";
            } else {
                 $subject = strip_tags($label);
            }
        } else {
            $subject = strip_tags($label);
        }

        if ($cv)
            $cv_path = ($is_loged == true ? $_SERVER['DOCUMENT_ROOT'] . '/uploads_test/cv_cand_save/' . $cv : $_SERVER['DOCUMENT_ROOT'] . '/uploads_test/temp/' . $cv);
        if ($lm)
            $lm_path = ($is_loged == true ? $_SERVER['DOCUMENT_ROOT'] . '/uploads_test/lm_cand_save/' . $lm : $_SERVER['DOCUMENT_ROOT'] . '/uploads_test/temp/' . $lm);

        $newEmail = (new Email())
            ->from(new Address($fromAdress, 'Direct Emploi'))
            ->to($email);

            if($email_recrut != "cand"){
                $newEmail->from(new Address($fromAdress, $sender_name));
                $newEmail->replyTo($candid_mail);
            }

            $newEmail->subject($subject)
            ->text($bodyTxt)
            ->html($bodyHtml);
            if ($cv) {
                $extension = explode('.', $cv);
                if (count($extension) > 1) {
                    $newEmail->attach(fopen($cv_path, 'r'), 'CV de ' . $sender_name . '.' . $extension[1]);
                }
            }
            if ($lm) {
                $extension = explode('.', $lm);
                if (count($extension) > 1) {
                    $newEmail->attach(fopen($lm_path, 'r'), 'Lettre de motivation de' . $sender_name . '.' . $extension[1]);
                }
            }
        $this->mailer->send($newEmail);
	}

    public function sendMail($mailSender, $mailContent, $mailSubject): void
    {
        $ourAdress = "<EMAIL>";
        $contentAdresse = "\n\nSujet: " . $mailSubject . "\nEnvoyé par: " . $mailSender;

        $newEmail = (new Email())
            ->from($mailSender)
            ->to($ourAdress)
            ->subject($mailSubject)
            ->text($mailContent . $contentAdresse)
            ;

        $this->mailer->send($newEmail);
	}

    public function sendMailCvt($mailSender, $bodyTxt, $bodyHtml, $mailSubject): void
    {
        $ourAdress = "<EMAIL>";
        $contentAdresse = "\n\nSujet: " . $mailSubject . "\nEnvoyé par: " . $mailSender;

        $newEmail = (new Email())
            ->from($ourAdress)
            ->to($mailSender)
            ->subject($mailSubject)
            ->text($bodyTxt)
            ->html($bodyHtml);
            ;

        $this->mailer->send($newEmail);
	}

    //Envoi mail ATS spécifique pour WorkAndYou
    public function sendMailWY($email, $subject, $lm = null, $cv = null, $sender_name = null, $is_loged = null, $offoffre, $userData): void {

        // Définir l'adresse de l'expéditeur avec le nom
        $fromAddress = new Address("<EMAIL>", "Direct Emploi");

        // Construire le corps de l'email en HTML avec les variables dynamiques
        $bodyHtml = '
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    Bonjour <br /><br />
    Nous vous informons qu\'un candidat a postulé à votre offre <strong>' . htmlspecialchars($offoffre->getIntitule(), ENT_QUOTES, 'UTF-8') . '</strong>.
    <br /><br />
    Référence : ' . htmlspecialchars($offoffre->getReference(), ENT_QUOTES, 'UTF-8') . '<br />
    Lien de l\'offre : <a href="https://www.directemploi.com/candidatOffre/' . urlencode($offoffre->getIdOffre()) . '">Voir l\'offre</a>
    <br /><br />
    Nom : ' . htmlspecialchars($userData['nom'], ENT_QUOTES, 'UTF-8') . ';<br />
    Prénom : ' . htmlspecialchars($userData['prenom'], ENT_QUOTES, 'UTF-8') . ';<br />
    Email : <a href="mailto:' . htmlspecialchars($userData['email'], ENT_QUOTES, 'UTF-8') . '">' . htmlspecialchars($userData['email'], ENT_QUOTES, 'UTF-8') . '</a>;<br />
    <br />
    Vous pouvez retrouver tous vos CVs sur votre compte recruteur <a href="https://www.directemploi.com/espaceRecruteur">www.directemploi.com/espaceRecruteur</a>
    <br /><br />
    Cordialement,<br />
    <a href="https://www.directemploi.com">www.directemploi.com</a>
    <div hspace="streak-pt-mark" style="max-height:1px">
        <img alt="" style="width:0px;max-height:0px;overflow:hidden" src="https://workandyou.nicoka.com//api/messages/tracker/?m=16739&uid=<EMAIL>">
    </div>
    ';

        // Créer une instance de l'email
        $newEmail = (new Email())
            ->from($fromAddress)
            ->to($email)
            ->subject($subject)
            ->html($bodyHtml)
            ->text(strip_tags($bodyHtml)); // Optionnel : version texte de l'email

        // Ajouter l'en-tête personnalisé X-Nicoka-Id
        $newEmail->getHeaders()->addTextHeader('X-Nicoka-Id', '<EMAIL>');

        // Attacher le CV si fourni
        if ($cv) {
            $cv_path = ($is_loged
                ? $_SERVER['DOCUMENT_ROOT'] . '/uploads_test/cv_cand_save/' . $cv
                : $_SERVER['DOCUMENT_ROOT'] . '/uploads_test/temp/' . $cv
            );
            if (file_exists($cv_path)) {
                $newEmail->attachFromPath($cv_path, 'CV de ' . htmlspecialchars($sender_name, ENT_QUOTES, 'UTF-8'));
            }
        }

        // Attacher la Lettre de Motivation si fournie
        if ($lm) {
            $lm_path = ($is_loged
                ? $_SERVER['DOCUMENT_ROOT'] . '/uploads_test/lm_cand_save/' . $lm
                : $_SERVER['DOCUMENT_ROOT'] . '/uploads_test/temp/' . $lm
            );
            if (file_exists($lm_path)) {
                $newEmail->attachFromPath($lm_path, 'Lettre de motivation de ' . htmlspecialchars($sender_name, ENT_QUOTES, 'UTF-8'));
            }
        }

        // Envoyer l'email
        try {
            $this->mailer->send($newEmail);
        } catch (\Exception $e) {

        }
    }

    public function sendMailPdfWellJob($bodyTxt, $bodyHtml): void
    {
        $ourAdress = "<EMAIL>";
        $to = "<EMAIL>";
        //$contentAdresse = "\n\nSujet: " . "Cv to PDF welljob" . "\nEnvoyé par: " . $mailSender;
        $mailSubject = "Cv to PDF welljob";

        $newEmail = (new Email())
            ->from($ourAdress)
            ->to($to)
            ->subject($mailSubject)
            ->text($bodyTxt)
            ->html($bodyHtml);
            ;

        $this->mailer->send($newEmail);
	}
    /*
    public function sendMailWY($email, $subject, $lm = null, $cv = null, $sender_name = null, $is_loged = null): void {
        // Validation des entrées
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return;
        }

        $fromAddress = "<EMAIL>";

        $bodyTxt = strip_tags($subject);
        $bodyHtml = nl2br($subject);

        $subject = $bodyTxt;

        try {
            $newEmail = (new Email())
                ->from($fromAddress)
                ->to("<EMAIL>")
                ->subject($subject)
                ->text($bodyTxt)
                ->html($bodyHtml);

            // Attachement des fichiers
            $this->attachFile($newEmail, $cv, $is_loged, 'cv_cand_save', 'CV de ', $sender_name);
            $this->attachFile($newEmail, $lm, $is_loged, 'lm_cand_save', 'Lettre de motivation de', $sender_name);

            $this->mailer->send($newEmail);
        } catch (\Exception $e) {

        }
    }

    private function attachFile($email, $file, $is_loged, $folder, $prefix, $sender_name) {
        if ($file) {
            $filePath = $this->getFilePath($file, $is_loged, $folder);
            if (file_exists($filePath)) {
                $extension = pathinfo($file, PATHINFO_EXTENSION);
                $email->attach(fopen($filePath, 'r'), $prefix . $sender_name . '.' . $extension);
            }
        }
    }

    private function getFilePath($file, $is_loged, $folder) {
        $basePath = $is_loged ? '/uploads_test/' . $folder . '/' : '/uploads_test/temp/';
        return $_SERVER['DOCUMENT_ROOT'] . $basePath . $file;
    }

*/

}

?>
