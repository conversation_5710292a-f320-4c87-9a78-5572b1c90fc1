<?php

namespace App\Entity;

use App\Repository\CvtequeProfilsN2Repository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CvtequeProfilsN2Repository::class)]
class CvtequeProfilsN2
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $cv_cv_id_cv;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $id_reponse_offre_non_identifie;

    #[ORM\Column(type: 'text', nullable: true)]
    private $email;

    #[ORM\Column(type: 'text', nullable: true)]
    private $prenom;

    #[ORM\Column(type: 'text', nullable: true)]
    private $nom;

    #[ORM\Column(type: 'text', nullable: true)]
    private $nom_fichier_cv_stockage;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $off_liste_secteur_activite_id_secteur_activite;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $user_disponibilite_id_disponibilite;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $user_situation_activite_id_situation_activite;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $ec_ecole_id_ecole;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $ec_organisme_formation_id_organisme_formation;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $id_ec_session_formation;

    #[ORM\Column(type: 'text', nullable: true)]
    private $etablissement_autre;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $off_liste_type_formation_id_type_formation;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $annee_obtention_diplome;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $id_region;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $id_departement;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $id_metier;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $id_off_fourchette_remuneration;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $is_visible;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $is_anonyme;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $date_modification;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $date_creation_profil;

    #[ORM\Column(type: 'text', nullable: true)]
    private $mots_cles;

    #[ORM\Column(type: 'text', nullable: true)]
    private $geo_ville;

    #[ORM\Column(type: 'text', nullable: true)]
    private $lib_metier;

    #[ORM\Column(type: 'text', nullable: true)]
    private $lib_off_liste_secteur_activite;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $off_liste_type_contrat_id_type_contrat;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCvCvIdCv(): ?int
    {
        return $this->cv_cv_id_cv;
    }

    public function setCvCvIdCv(?int $cv_cv_id_cv): self
    {
        $this->cv_cv_id_cv = $cv_cv_id_cv;

        return $this;
    }

    public function getIdReponseOffreNonIdentifie(): ?int
    {
        return $this->id_reponse_offre_non_identifie;
    }

    public function setIdReponseOffreNonIdentifie(?int $id_reponse_offre_non_identifie): self
    {
        $this->id_reponse_offre_non_identifie = $id_reponse_offre_non_identifie;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getPrenom(): ?string
    {
        return $this->prenom;
    }

    public function setPrenom(?string $prenom): self
    {
        $this->prenom = $prenom;

        return $this;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(?string $nom): self
    {
        $this->nom = $nom;

        return $this;
    }

    public function getNomFichierCvStockage(): ?string
    {
        return $this->nom_fichier_cv_stockage;
    }

    public function setNomFichierCvStockage(?string $nom_fichier_cv_stockage): self
    {
        $this->nom_fichier_cv_stockage = $nom_fichier_cv_stockage;

        return $this;
    }

    public function getOffListeSecteurActiviteIdSecteurActivite(): ?int
    {
        return $this->off_liste_secteur_activite_id_secteur_activite;
    }

    public function setOffListeSecteurActiviteIdSecteurActivite(?int $off_liste_secteur_activite_id_secteur_activite): self
    {
        $this->off_liste_secteur_activite_id_secteur_activite = $off_liste_secteur_activite_id_secteur_activite;

        return $this;
    }

    public function getUserDisponibiliteIdDisponibilite(): ?int
    {
        return $this->user_disponibilite_id_disponibilite;
    }

    public function setUserDisponibiliteIdDisponibilite(?int $user_disponibilite_id_disponibilite): self
    {
        $this->user_disponibilite_id_disponibilite = $user_disponibilite_id_disponibilite;

        return $this;
    }

    public function getUserSituationActiviteIdSituationActivite(): ?int
    {
        return $this->user_situation_activite_id_situation_activite;
    }

    public function setUserSituationActiviteIdSituationActivite(?int $user_situation_activite_id_situation_activite): self
    {
        $this->user_situation_activite_id_situation_activite = $user_situation_activite_id_situation_activite;

        return $this;
    }

    public function getEcEcoleIdEcole(): ?int
    {
        return $this->ec_ecole_id_ecole;
    }

    public function setEcEcoleIdEcole(?int $ec_ecole_id_ecole): self
    {
        $this->ec_ecole_id_ecole = $ec_ecole_id_ecole;

        return $this;
    }

    public function getEcOrganismeFormationIdOrganismeFormation(): ?int
    {
        return $this->ec_organisme_formation_id_organisme_formation;
    }

    public function setEcOrganismeFormationIdOrganismeFormation(?int $ec_organisme_formation_id_organisme_formation): self
    {
        $this->ec_organisme_formation_id_organisme_formation = $ec_organisme_formation_id_organisme_formation;

        return $this;
    }

    public function getIdEcSessionFormation(): ?int
    {
        return $this->id_ec_session_formation;
    }

    public function setIdEcSessionFormation(?int $id_ec_session_formation): self
    {
        $this->id_ec_session_formation = $id_ec_session_formation;

        return $this;
    }

    public function getEtablissementAutre(): ?string
    {
        return $this->etablissement_autre;
    }

    public function setEtablissementAutre(?string $etablissement_autre): self
    {
        $this->etablissement_autre = $etablissement_autre;

        return $this;
    }

    public function getOffListeTypeFormationIdTypeFormation(): ?int
    {
        return $this->off_liste_type_formation_id_type_formation;
    }

    public function setOffListeTypeFormationIdTypeFormation(?int $off_liste_type_formation_id_type_formation): self
    {
        $this->off_liste_type_formation_id_type_formation = $off_liste_type_formation_id_type_formation;

        return $this;
    }

    public function getAnneeObtentionDiplome(): ?int
    {
        return $this->annee_obtention_diplome;
    }

    public function setAnneeObtentionDiplome(?int $annee_obtention_diplome): self
    {
        $this->annee_obtention_diplome = $annee_obtention_diplome;

        return $this;
    }

    public function getIdRegion(): ?int
    {
        return $this->id_region;
    }

    public function setIdRegion(?int $id_region): self
    {
        $this->id_region = $id_region;

        return $this;
    }

    public function getIdDepartement(): ?int
    {
        return $this->id_departement;
    }

    public function setIdDepartement(?int $id_departement): self
    {
        $this->id_departement = $id_departement;

        return $this;
    }

    public function getIdMetier(): ?int
    {
        return $this->id_metier;
    }

    public function setIdMetier(?int $id_metier): self
    {
        $this->id_metier = $id_metier;

        return $this;
    }

    public function getIdOffFourchetteRemuneration(): ?int
    {
        return $this->id_off_fourchette_remuneration;
    }

    public function setIdOffFourchetteRemuneration(?int $id_off_fourchette_remuneration): self
    {
        $this->id_off_fourchette_remuneration = $id_off_fourchette_remuneration;

        return $this;
    }

    public function getIsVisible(): ?int
    {
        return $this->is_visible;
    }

    public function setIsVisible(?int $is_visible): self
    {
        $this->is_visible = $is_visible;

        return $this;
    }

    public function getIsAnonyme(): ?int
    {
        return $this->is_anonyme;
    }

    public function setIsAnonyme(?int $is_anonyme): self
    {
        $this->is_anonyme = $is_anonyme;

        return $this;
    }

    public function getDateModification(): ?\DateTimeInterface
    {
        return $this->date_modification;
    }

    public function setDateModification(?\DateTimeInterface $date_modification): self
    {
        $this->date_modification = $date_modification;

        return $this;
    }

    public function getDateCreationProfil(): ?\DateTimeInterface
    {
        return $this->date_creation_profil;
    }

    public function setDateCreationProfil(?\DateTimeInterface $date_creation_profil): self
    {
        $this->date_creation_profil = $date_creation_profil;

        return $this;
    }

    public function getMotsCles(): ?string
    {
        return $this->mots_cles;
    }

    public function setMotsCles(?string $mots_cles): self
    {
        $this->mots_cles = $mots_cles;

        return $this;
    }

    public function getGeoVille(): ?string
    {
        return $this->geo_ville;
    }

    public function setGeoVille(?string $geo_ville): self
    {
        $this->geo_ville = $geo_ville;

        return $this;
    }

    public function getLibMetier(): ?string
    {
        return $this->lib_metier;
    }

    public function setLibMetier(?string $lib_metier): self
    {
        $this->lib_metier = $lib_metier;

        return $this;
    }

    public function getLibOffListeSecteurActivite(): ?string
    {
        return $this->lib_off_liste_secteur_activite;
    }

    public function setLibOffListeSecteurActivite(?string $lib_off_liste_secteur_activite): self
    {
        $this->lib_off_liste_secteur_activite = $lib_off_liste_secteur_activite;

        return $this;
    }

    public function getOffListeTypeContratIdTypeContrat(): ?int
    {
        return $this->off_liste_type_contrat_id_type_contrat;
    }

    public function setOffListeTypeContratIdTypeContrat(?int $off_liste_type_contrat_id_type_contrat): self
    {
        $this->off_liste_type_contrat_id_type_contrat = $off_liste_type_contrat_id_type_contrat;

        return $this;
    }
}
