<?php

namespace App\Entity;

use App\Repository\CvCentreInteretRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CvCentreInteretRepository::class)]
class CvCentreInteret
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id_centre_interet;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $nom_centre_interet;

    #[ORM\Column(type: 'text', nullable: true)]
    private $description_centre_interet;

    #[ORM\Column(type: 'integer')]
    private $cv_cv_id_cv;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $ordre_affichage;

    public function __construct()
    {
    }

    public function getIdCentreInteret(): ?int
    {
        return $this->id_centre_interet;
    }

    public function setIdCentreInteret(?int $id_centre_interet): self
    {
        $this->id_centre_interet = $id_centre_interet;

        return $this;
    }

    public function getNomCentreInteret(): ?string
    {
        return $this->nom_centre_interet;
    }

    public function setNomCentreInteret(?string $nom_centre_interet): self
    {
        $this->nom_centre_interet = $nom_centre_interet;

        return $this;
    }

    public function getDescriptionCentreInteret(): ?string
    {
        return $this->description_centre_interet;
    }

    public function setDescriptionCentreInteret(?string $description_centre_interet): self
    {
        $this->description_centre_interet = $description_centre_interet;

        return $this;
    }

    public function getCvCvIdCv(): ?int
    {
        return $this->cv_cv_id_cv;
    }

    public function setCvCvIdCv(?int $cv_cv_id_cv): self
    {
        $this->cv_cv_id_cv = $cv_cv_id_cv;

        return $this;
    }

    public function getOrdreAffichage(): ?int
    {
        return $this->ordre_affichage;
    }

    public function setOrdreAffichage(?int $ordre_affichage): self
    {
        $this->ordre_affichage = $ordre_affichage;

        return $this;
    }
}
