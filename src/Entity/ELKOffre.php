<?php

namespace App\Entity;

class ELKOffre {
    /**
     * @var $data
     */
    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function getId()
    {
        return $this->data->id ?? null;
    }

    public function getDateSoumission(){
        return $this->__get_date("date_soumission");
    }

    private function __get_date($prop){
        $st = $this->data->{$prop} ?? null;
        if($st){
            $dt = new \DateTime($st);
            return $dt;
        }
        return null;
    }

    public function getSecteurs()
    {
        $secteurs = [];
        if(isset($this->data->secteur_activite)){
            $secteurs = $this->data->secteur_activite;
            if(!is_array($secteurs)){
                return [$secteurs];
            }
        }
        return $secteurs;
    }

    public function __get($prop)
    {
        return $this->data->{$prop} ?? null;
    }

    public function __isset($prop){
        if(isset($this->data->{$prop}) || in_array($prop , [ "ville", "mission","secteur_activite"])){
            return true;
        }
        return false;
    }
}
