<?php

namespace App\Entity;

use App\Repository\MetierMetierRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: MetierMetierRepository::class)]
class MetierMetier
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id_metier;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $libelle_metier;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $domaine_id_domaine;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $form_cand;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $form_cvtheque;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $page_acceuil;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $zone_mc;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $zone_mc_domaine;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $zone_cadre;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $mc_recherche_avancee;

    public function getIdMetier(): ?int
    {
        return $this->id_metier;
    }

    public function getLibelleMetier(): ?string
    {
        return $this->libelle_metier;
    }

    public function setLibelleMetier(?string $libelle_metier): self
    {
        $this->libelle_metier = $libelle_metier;

        return $this;
    }

    public function getDomaineIdDomaine(): ?int
    {
        return $this->domaine_id_domaine;
    }

    public function setDomaineIdDomaine(?int $domaine_id_domaine): self
    {
        $this->domaine_id_domaine = $domaine_id_domaine;

        return $this;
    }

    public function getFormCand(): ?int
    {
        return $this->form_cand;
    }

    public function setFormCand(?int $form_cand): self
    {
        $this->form_cand = $form_cand;

        return $this;
    }

    public function getFormCvtheque(): ?int
    {
        return $this->form_cvtheque;
    }

    public function setFormCvtheque(?int $form_cvtheque): self
    {
        $this->form_cvtheque = $form_cvtheque;

        return $this;
    }

    public function getPageAcceuil(): ?int
    {
        return $this->page_acceuil;
    }

    public function setPageAcceuil(?int $page_acceuil): self
    {
        $this->page_acceuil = $page_acceuil;

        return $this;
    }

    public function getZoneMc(): ?int
    {
        return $this->zone_mc;
    }

    public function setZoneMc(?int $zone_mc): self
    {
        $this->zone_mc = $zone_mc;

        return $this;
    }

    public function getZoneMcDomaine(): ?int
    {
        return $this->zone_mc_domaine;
    }

    public function setZoneMcDomaine(?int $zone_mc_domaine): self
    {
        $this->zone_mc_domaine = $zone_mc_domaine;

        return $this;
    }

    public function getZoneCadre(): ?int
    {
        return $this->zone_cadre;
    }

    public function setZoneCadre(?int $zone_cadre): self
    {
        $this->zone_cadre = $zone_cadre;

        return $this;
    }

    public function getMcRechercheAvancee(): ?int
    {
        return $this->mc_recherche_avancee;
    }

    public function setMcRechercheAvancee(?int $mc_recherche_avancee): self
    {
        $this->mc_recherche_avancee = $mc_recherche_avancee;

        return $this;
    }
}
