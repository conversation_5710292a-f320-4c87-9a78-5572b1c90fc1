<?php

namespace App\Entity;

use App\Repository\EditLogHabillageRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EditLogHabillageRepository::class)]
class EditLogHabillage
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $date_log;

    #[ORM\ManyToMany(targetEntity: EditHabillage::class)]
    private $edit_habillage_id_habillage;

    public function __construct()
    {
        $this->edit_habillage_id_habillage = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDateLog(): ?\DateTimeInterface
    {
        return $this->date_log;
    }

    public function setDateLog(?\DateTimeInterface $date_log): self
    {
        $this->date_log = $date_log;

        return $this;
    }

    /**
     * @return Collection<int, EditHabillage>
     */
    public function getEditHabillageIdHabillage(): Collection
    {
        return $this->edit_habillage_id_habillage;
    }

    public function addEditHabillageIdHabillage(EditHabillage $editHabillageIdHabillage): self
    {
        if (!$this->edit_habillage_id_habillage->contains($editHabillageIdHabillage)) {
            $this->edit_habillage_id_habillage[] = $editHabillageIdHabillage;
        }

        return $this;
    }

    public function removeEditHabillageIdHabillage(EditHabillage $editHabillageIdHabillage): self
    {
        $this->edit_habillage_id_habillage->removeElement($editHabillageIdHabillage);

        return $this;
    }
}
