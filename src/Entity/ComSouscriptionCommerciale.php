<?php

namespace App\Entity;

use App\Repository\ComSouscriptionCommercialeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use App\Entity\ComListeTypeOffreCommerciale;
use App\Entity\ComOffreCommerciale;
use Doctrine\Persistence\ManagerRegistry;

#[ORM\Entity(repositoryClass: ComSouscriptionCommercialeRepository::class)]
class ComSouscriptionCommerciale
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id_souscription_commerciale;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $user_user_id_user;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $com_offre_commerciale_id_offre_commerciale;

    #[ORM\Column(type: 'date', nullable: true)]
    private $date_debut_souscription;

    #[ORM\Column(type: 'date', nullable: true)]
    private $date_fin_souscription;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $credit;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $com_type_paiement_id_type_paiement;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $com_lot_souscription_id_lot_souscription;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $promo_promotion_id_promotion;

    public function __construct()
    {
        // $this->com_offre_commerciale_id_offre_commerciale = new ArrayCollection();
        // $this->com_type_paiement_id_type_paiement = new ArrayCollection();
        // $this->com_lot_souscription_id_lot_souscription = new ArrayCollection();
        // $this->promo_promotion_id_promotion = new ArrayCollection();
    }

    public function getIdSouscriptionCommerciale(): ?int
    {
        return $this->id_souscription_commerciale;
    }

    public function getUserUserIdUser(): ?int
    {
        return $this->user_user_id_user;
    }

    public function setUserUserIdUser(?int $user_user_id_user): self
    {
        $this->user_user_id_user = $user_user_id_user;

        return $this;
    }


    public function getComOffreCommercialeIdOffreCommerciale(): ?int
    {
        return $this->com_offre_commerciale_id_offre_commerciale;
    }

    public function setComOffreCommercialeIdOffreCommerciale(?int $com_offre_commerciale_id_offre_commerciale): self
    {
        $this->com_offre_commerciale_id_offre_commerciale = $com_offre_commerciale_id_offre_commerciale;

        return $this;
    }


    // /**
    //  * @return Collection<int, UserUser>
    //  */
    // public function getUserUserId�User(): Collection
    // {
    //     return $this->user_user_id�_user;
    // }

    // public function addUserUserIdUser(UserUser $userUserIdUser): self
    // {
    //     if (!$this->user_user_id�_user->contains($userUserIdUser)) {
    //         $this->user_user_id�_user[] = $userUserIdUser;
    //     }

    //     return $this;
    // }

    // public function removeUserUserIdUser(UserUser $userUserIdUser): self
    // {
    //     $this->user_user_id�_user->removeElement($userUserIdUser);

    //     return $this;
    // }

    // /**
    //  * @return Collection<int, ComOffreCommerciale>
    //  */
    // public function getComOffreCommercialeIdOffreCommerciale(): Collection
    // {
    //     return $this->com_offre_commerciale_id_offre_commerciale;
    // }

    // public function addComOffreCommercialeIdOffreCommerciale(ComOffreCommerciale $comOffreCommercialeIdOffreCommerciale): self
    // {
    //     if (!$this->com_offre_commerciale_id_offre_commerciale->contains($comOffreCommercialeIdOffreCommerciale)) {
    //         $this->com_offre_commerciale_id_offre_commerciale[] = $comOffreCommercialeIdOffreCommerciale;
    //     }

    //     return $this;
    // }

    // public function removeComOffreCommercialeIdOffreCommerciale(ComOffreCommerciale $comOffreCommercialeIdOffreCommerciale): self
    // {
    //     $this->com_offre_commerciale_id_offre_commerciale->removeElement($comOffreCommercialeIdOffreCommerciale);

    //     return $this;
    // }

    public function getDateDebutSouscription(): ?\DateTimeInterface
    {
        return $this->date_debut_souscription;
    }

    public function setDateDebutSouscription(?\DateTimeInterface $date_debut_souscription): self
    {
        $this->date_debut_souscription = $date_debut_souscription;

        return $this;
    }

    public function getDateFinSouscription(): ?\DateTimeInterface
    {
        return $this->date_fin_souscription;
    }

    public function setDateFinSouscription(?\DateTimeInterface $date_fin_souscription): self
    {
        $this->date_fin_souscription = $date_fin_souscription;

        return $this;
    }

    public function getCredit(): ?int
    {
        return $this->credit;
    }

    public function setCredit(?int $credit): self
    {
        $this->credit = $credit;

        return $this;
    }

    // /**
    //  * @return Collection<int, ComTypePaiement>
    //  */
    // public function getComTypePaiementIdTypePaiement(): Collection
    // {
    //     return $this->com_type_paiement_id_type_paiement;
    // }

    // public function addComTypePaiementIdTypePaiement(ComTypePaiement $comTypePaiementIdTypePaiement): self
    // {
    //     if (!$this->com_type_paiement_id_type_paiement->contains($comTypePaiementIdTypePaiement)) {
    //         $this->com_type_paiement_id_type_paiement[] = $comTypePaiementIdTypePaiement;
    //     }

    //     return $this;
    // }

    // public function removeComTypePaiementIdTypePaiement(ComTypePaiement $comTypePaiementIdTypePaiement): self
    // {
    //     $this->com_type_paiement_id_type_paiement->removeElement($comTypePaiementIdTypePaiement);

    //     return $this;
    // }

    // /**
    //  * @return Collection<int, ComLotSouscription>
    //  */
    // public function getComLotSouscriptionIdLotSouscription(): Collection
    // {
    //     return $this->com_lot_souscription_id_lot_souscription;
    // }

    // public function addComLotSouscriptionIdLotSouscription(ComLotSouscription $comLotSouscriptionIdLotSouscription): self
    // {
    //     if (!$this->com_lot_souscription_id_lot_souscription->contains($comLotSouscriptionIdLotSouscription)) {
    //         $this->com_lot_souscription_id_lot_souscription[] = $comLotSouscriptionIdLotSouscription;
    //     }

    //     return $this;
    // }

    // public function removeComLotSouscriptionIdLotSouscription(ComLotSouscription $comLotSouscriptionIdLotSouscription): self
    // {
    //     $this->com_lot_souscription_id_lot_souscription->removeElement($comLotSouscriptionIdLotSouscription);

    //     return $this;
    // }

    // /**
    //  * @return Collection<int, PromoPromotion>
    //  */
    // public function getPromoPromotionIdPromotion(): Collection
    // {
    //     return $this->promo_promotion_id_promotion;
    // }

    // public function addPromoPromotionIdPromotion(PromoPromotion $promoPromotionIdPromotion): self
    // {
    //     if (!$this->promo_promotion_id_promotion->contains($promoPromotionIdPromotion)) {
    //         $this->promo_promotion_id_promotion[] = $promoPromotionIdPromotion;
    //     }

    //     return $this;
    // }

    // public function removePromoPromotionIdPromotion(PromoPromotion $promoPromotionIdPromotion): self
    // {
    //     $this->promo_promotion_id_promotion->removeElement($promoPromotionIdPromotion);

    //     return $this;
    // }

    public function getComTypePaiementIdTypePaiement(): ?int
    {
        return $this->com_type_paiement_id_type_paiement;
    }

    public function setComTypePaiementIdTypePaiement(?int $com_type_paiement_id_type_paiement): self
    {
        $this->com_type_paiement_id_type_paiement = $com_type_paiement_id_type_paiement;

        return $this;
    }


    public function getPromoPromotionIdPromotion(): ?int
    {
        return $this->promo_promotion_id_promotion;
    }

    public function setPromoPromotionIdPromotion(?int $promo_promotion_id_promotion): self
    {
        $this->promo_promotion_id_promotion = $promo_promotion_id_promotion;

        return $this;
    }

    public function getComLotSouscriptionIdLotSouscription(): ?int
    {
        return $this->com_lot_souscription_id_lot_souscription;
    }

    public function setComLotSouscriptionIdLotSouscription(?int $com_lot_souscription_id_lot_souscription): self
    {
        $this->com_lot_souscription_id_lot_souscription = $com_lot_souscription_id_lot_souscription;

        return $this;
    }
}
