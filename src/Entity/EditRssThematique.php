<?php

namespace App\Entity;

use App\Repository\EditRssThematiqueRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EditRssThematiqueRepository::class)]
class EditRssThematique
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $fichier_xml;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $titre;

    #[ORM\Column(type: 'text', nullable: true)]
    private $description;

    #[ORM\Column(type: 'smallint', nullable: true)]
    private $type_contrat;

    #[ORM\ManyToMany(targetEntity: EditApp::class)]
    private $edit_app_id_app;

    public function __construct()
    {
        $this->edit_app_id_app = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFichierXml(): ?string
    {
        return $this->fichier_xml;
    }

    public function setFichierXml(?string $fichier_xml): self
    {
        $this->fichier_xml = $fichier_xml;

        return $this;
    }

    public function getTitre(): ?string
    {
        return $this->titre;
    }

    public function setTitre(?string $titre): self
    {
        $this->titre = $titre;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getTypeContrat(): ?int
    {
        return $this->type_contrat;
    }

    public function setTypeContrat(?int $type_contrat): self
    {
        $this->type_contrat = $type_contrat;

        return $this;
    }

    /**
     * @return Collection<int, EditApp>
     */
    public function getEditAppIdApp(): Collection
    {
        return $this->edit_app_id_app;
    }

    public function addEditAppIdApp(EditApp $editAppIdApp): self
    {
        if (!$this->edit_app_id_app->contains($editAppIdApp)) {
            $this->edit_app_id_app[] = $editAppIdApp;
        }

        return $this;
    }

    public function removeEditAppIdApp(EditApp $editAppIdApp): self
    {
        $this->edit_app_id_app->removeElement($editAppIdApp);

        return $this;
    }
}
