<?php

namespace App\Entity;

use App\Repository\EditPageCadreRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EditPageCadreRepository::class)]
class EditPageCadre
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id_edit_page_cadre;

    #[ORM\Column(type: 'integer')]
    private $edit_app_id_app;

    #[ORM\Column(type: 'integer')]
    private $edit_article_id_article;

    public function __construct()
    {
        // $this->edit_app_id_app = new ArrayCollection();
        // $this->edit_article_id_article = new ArrayCollection();
    }

    public function getIdEditPageCadre(): ?int
    {
        return $this->id_edit_page_cadre;
    }

    public function setIdEditPageCadre(?string $id_edit_page_cadre): self
    {
        $this->id_edit_page_cadre = $id_edit_page_cadre;

        return $this;
    }

    public function getEditAppIdApp(): ?int
    {
        return $this->edit_app_id_app;
    }

    public function setEditAppIdApp(?string $edit_app_id_app): self
    {
        $this->edit_app_id_app = $edit_app_id_app;

        return $this;
    }

    public function getEditArticleIdArticle(): ?int
    {
        return $this->edit_article_id_article;
    }

    public function setEditArticleIdArticle(?string $edit_article_id_article): self
    {
        $this->edit_article_id_article = $edit_article_id_article;

        return $this;
    }

    // /**
    //  * @return Collection<int, EditApp>
    //  */
    // public function getEditAppIdApp(): Collection
    // {
    //     return $this->edit_app_id_app;
    // }

    // public function addEditAppIdApp(EditApp $editAppIdApp): self
    // {
    //     if (!$this->edit_app_id_app->contains($editAppIdApp)) {
    //         $this->edit_app_id_app[] = $editAppIdApp;
    //     }

    //     return $this;
    // }

    // public function removeEditAppIdApp(EditApp $editAppIdApp): self
    // {
    //     $this->edit_app_id_app->removeElement($editAppIdApp);

    //     return $this;
    // }

    // /**
    //  * @return Collection<int, EditArticle>
    //  */
    // public function getEditArticleIdArticle(): Collection
    // {
    //     return $this->edit_article_id_article;
    // }

    // public function addEditArticleIdArticle(EditArticle $editArticleIdArticle): self
    // {
    //     if (!$this->edit_article_id_article->contains($editArticleIdArticle)) {
    //         $this->edit_article_id_article[] = $editArticleIdArticle;
    //     }

    //     return $this;
    // }

    // public function removeEditArticleIdArticle(EditArticle $editArticleIdArticle): self
    // {
    //     $this->edit_article_id_article->removeElement($editArticleIdArticle);

    //     return $this;
    // }
}
