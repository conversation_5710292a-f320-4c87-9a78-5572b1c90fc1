<?php

namespace App\Entity;

use App\Repository\EditTypeArticleRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EditTypeArticleRepository::class)]
class EditTypeArticle
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id_type_article;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $libelle;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $slug;

    public function getIdTypeArticle(): ?int
    {
        return $this->id_type_article;
    }

    public function setIdTypeArticle(?int $id_type_article): self
    {
        $this->id_type_article = $id_type_article;

        return $this;
    }

    public function getLibelle(): ?string
    {
        return $this->libelle;
    }

    public function setLibelle(?string $libelle): self
    {
        $this->libelle = $libelle;

        return $this;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function setSlug(?string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }
}
