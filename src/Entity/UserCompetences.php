<?php

namespace App\Entity;

use App\Repository\UserCompetencesRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: UserCompetencesRepository::class)]
class UserCompetences
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id_competences;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private $liste_competences;
    
    #[ORM\Column(type: 'integer')]
    private $user_user_id_user;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $id_choix_langue_1;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $id_niveau_langue_1;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $id_choix_langue_2;

    #[ORM\Column(type: 'integer', nullable: true)]
    private $id_niveau_langue_2;

    #[ORM\Column(type: 'smallint', nullable: true)]
    private $permis_b;

    #[ORM\Column(type: 'smallint', nullable: true)]
    private $is_profil_visible;

    public function getIdCompetences(): ?int
    {
        return $this->id_competences;
    }

    public function setIdCompetences(?int $id_competences): self
    {
        $this->id_competences = $id_competences;

        return $this;
    }

    public function getUserUserIdUser(): ?int
    {
        return $this->user_user_id_user;
    }

    public function setUserUserIdUser(?int $user_user_id_user): self
    {
        $this->user_user_id_user = $user_user_id_user;

        return $this;
    }

    public function getListeCompetences(): ?string
    {
        return $this->liste_competences;
    }

    public function setListeCompetences(?string $liste_competences): self
    {
        $this->liste_competences = $liste_competences;

        return $this;
    }

    public function getIdChoixLangue1(): ?int
    {
        return $this->id_choix_langue_1;
    }

    public function setIdChoixLangue1(?int $id_choix_langue_1): self
    {
        $this->id_choix_langue_1 = $id_choix_langue_1;

        return $this;
    }

    public function getIdNiveauLangue1(): ?int
    {
        return $this->id_niveau_langue_1;
    }

    public function setIdNiveauLangue1(?int $id_niveau_langue_1): self
    {
        $this->id_niveau_langue_1 = $id_niveau_langue_1;

        return $this;
    }

    public function getIdChoixLangue2(): ?int
    {
        return $this->id_choix_langue_2;
    }

    public function setIdChoixLangue2(?int $id_choix_langue_2): self
    {
        $this->id_choix_langue_2 = $id_choix_langue_2;

        return $this;
    }

    public function getIdNiveauLangue2(): ?int
    {
        return $this->id_niveau_langue_2;
    }

    public function setIdNiveauLangue2(?int $id_niveau_langue_2): self
    {
        $this->id_niveau_langue_2 = $id_niveau_langue_2;

        return $this;
    }

    public function getPermisB(): ?int
    {
        return $this->permis_b;
    }

    public function setPermisB(?int $permis_b): self
    {
        $this->permis_b = $permis_b;

        return $this;
    }

    public function getIsProfilVisible(): ?int
    {
        return $this->is_profil_visible;
    }

    public function setIsProfilVisible(?int $is_profil_visible): self
    {
        $this->is_profil_visible = $is_profil_visible;

        return $this;
    }
}
