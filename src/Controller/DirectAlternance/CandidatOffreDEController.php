<?php

namespace App\Controller\DirectAlternance;

use App\Entity\AdminOffMultStats;
use App\Entity\CommonMessage;
use App\Entity\CvCv;
use App\Entity\DomaineMetier;
use App\Entity\EditReferencement;
use App\Entity\EntrEntreprise;
use App\Entity\GeoAdresse;
use App\Entity\GeoDepartement;
use App\Entity\GeoListePays;
use App\Entity\GeoListeRegion;
use App\Entity\OffCandidature;
use App\Entity\OffLienOffreGeoDepartement;
use App\Entity\OffLienOffreGeoListeRegion;
use App\Entity\OffLienOffreListeSecteurActivite;
use App\Entity\OffListeSecteurActivite;
use App\Entity\OffListeTypeContrat;
use App\Entity\OffListeTypeFormation;
use App\Entity\OffOffre;
use App\Entity\OffReponseOffreNonIdentifie;
use App\Entity\OffStatistique;
use App\Entity\RechercheAvancee;
use App\Entity\SaveMailCandiNonLog;
use App\Entity\SeoEditReferencement;
use App\Entity\UserUser;
use App\Form\carriereBTP\CandidatOffreELKRechercheBTPType;
use App\Form\CandidatOffreELKRechercheType;
use App\Form\DirectEtudiant\OffreCandidatureBTPType;
use App\Form\DirectEtudiant\SubmitCandidatureType;
use App\Form\OffreCandidatureType;
use App\Helpers\Utils;
use App\Repository\CvCvRepository;
use App\Repository\GeoAdresseRepository;
use App\Repository\MetierMetierRepository;
use App\Repository\UserSituationRepository;
use App\Repository\UserUserRepository;
use App\Service\ContratTypeCacheService;
use App\Service\ELKOffOffreService;
use App\Service\GeoCacheService;
use App\Service\MailerService;
use App\Service\myToolsService;
use App\Service\RechercheAvanceeCacheService;
use App\Service\SecteurActivityCacheService;
use App\Service\SimpleCacheService;
use App\Twig\AppExtension;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Cache\CacheItem;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Component\Security\Core\Security;

class CandidatOffreDEController extends AbstractController
{
    private EntityManagerInterface $entityManager;

    private $cache;
    private $requestStack;
    private $offreService ;
    private $managerRegistry;
    private $contratTypeCacheService;
    private $geoCacheService;
    private $secteurActivityCacheService;
    private $rechercheCacheService;
    private $editAppId;

    const SLUG_ALL_METIERS_SECTEURS_SUFFIXE = "metiers-secteurs";
    const SLUG_ALL_SECTEURS_SUFFIXE = "secteurs";
    const SLUG_METIER_SEPARATOR = "metier";
    const SLUG_METIERS_SEPARATOR = "metiers";
    const BASE_LIST_URL = "/AL/lists-offre/";

    const LOCALISATION_PARAM = "localisation";
    const LOCALISATION_FIELD_PARAM = "localisation_field";

    const QUERY_PARAM = "q";
    const QUERY_FIELD_PARAM = "q_field";

    function __construct(int $editAppId,Security $security,ManagerRegistry $doctrine, ELKOffOffreService $offOffreService,RechercheAvanceeCacheService $rechercheCacheService, ManagerRegistry $managerRegistry,
                         CacheInterface $cache,EntityManagerInterface $entityManager,  RequestStack $requestStack, GeoCacheService $geoCacheService, ContratTypeCacheService $contratTypeCacheService, SecteurActivityCacheService $secteurActivityCacheService)
    {
        $this->offreService = $offOffreService;
        $this->managerRegistry = $managerRegistry;
        $this->contratTypeCacheService = $contratTypeCacheService;
        $this->geoCacheService = $geoCacheService;
        $this->secteurActivityCacheService = $secteurActivityCacheService;
        $this->cache = $cache;
        $this->requestStack = $requestStack;
        $this->rechercheCacheService = $rechercheCacheService;
        $this->editAppId = $editAppId;
        $this->doctrine = $doctrine;
        $this->security = $security;
        $this->entityManager = $entityManager;

    }
    public function candidatureNoConnectedDoublon($form, $idoffre)
    {


        $OffReponseOffreNonIdentifie = new OffReponseOffreNonIdentifie();
        $OffReponseOffreNonIdentifie->setDateCandidature(new DateTime());
        $OffReponseOffreNonIdentifie->setNom($form->get('nom')->getData());
        $OffReponseOffreNonIdentifie->setPrenom($form->get('prenom')->getData());
        $OffReponseOffreNonIdentifie->setEmail($form->get('email')->getData());
        $OffReponseOffreNonIdentifie->setCp($form->get('code_postal')->getData());
        $OffReponseOffreNonIdentifie->setOffOffreIdOffre($idoffre);
        $OffReponseOffreNonIdentifie->setTel($form->get('mobile')->getData());
        $OffReponseOffreNonIdentifie->setOffListeSecteurActiviteIdSecteurActivite($form->get('domaine_activite')->getData()->getIdSecteurActivite());
        $OffReponseOffreNonIdentifie->setTriRecruteur(0);
        $OffReponseOffreNonIdentifie->setEcSessionFormationIdSessionFormation(null);
        $OffReponseOffreNonIdentifie->setEcEcoleIdEcole(null);
        $OffReponseOffreNonIdentifie->setEtablissementAutre(null);

        if ($form->has('metier_metier')) {
            $metierMetier = $form->get('metier_metier')->getData();
            if ($metierMetier instanceof App\Entity\DomaineMetier) {
                $OffReponseOffreNonIdentifie->setIdMetier($metierMetier->getIdMetier());
            }
        }

        if ($form->has('promo')) {
            $OffReponseOffreNonIdentifie->setAnneeSortie($form->get('promo')->getData());
        }

        if ($form->has('liste_formation')) {
            $OffReponseOffreNonIdentifie->setOffListeTypeFormationIdTypeFormation($form->get('liste_formation')->getData());
        } else {
            $OffReponseOffreNonIdentifie->setOffListeTypeFormationIdTypeFormation(23);
        }

        if ($form->get('civilite')->getData() == 1) {
            $OffReponseOffreNonIdentifie->setCivilite('M');
        } else {
            $OffReponseOffreNonIdentifie->setCivilite('F');
        }

        if ($form->has('nom_diplome')) {
            $OffReponseOffreNonIdentifie->setNomDiplome($form->get('nom_diplome')->getData());
        } else {
            $OffReponseOffreNonIdentifie->setNomDiplome(null);
        }

        if ($form->has('id_metier')) {
            $OffReponseOffreNonIdentifie->setIdMetier($form->get('id_metier')->getData());
        } else {
            $OffReponseOffreNonIdentifie->setIdMetier(null);
        }

        if ($form->has('metier_autre')) {
            $OffReponseOffreNonIdentifie->setMetierAutre($form->get('metier_autre') ? $form->get('metier_autre') : null);
        }

        if ($form->has('remuneration_souhaitee')) {
            $OffReponseOffreNonIdentifie->setIdOffFourchetteRemuneration($form->get('remuneration_souhaitee')->getData());
        } else {
            $OffReponseOffreNonIdentifie->setIdOffFourchetteRemuneration(null);
        }

        $brochureFile = $form->get('cv')->getData();

        if ($brochureFile && $brochureFile->isReadable()) { // Vérifie si le fichier existe et est lisible
            $originalFilename = pathinfo($brochureFile->getClientOriginalName(), PATHINFO_FILENAME);
            $random_id = rand(1000, 9999);
            $nom_filename = $random_id . date('YmdHis');

            // Vérifie si l'extension peut être devinée, sinon utilise une extension par défaut
            $extension = $brochureFile->guessExtension() ?: 'pdf';
            $newFilename = $nom_filename . '-' . uniqid() . '.' . $extension;

            $OffReponseOffreNonIdentifie->setCv($newFilename);

            try {
                $brochureFile->move(
                    $this->getParameter('temp'),
                    $newFilename
                );
            } catch (FileException $e) {
                // Gestion des erreurs lors du déplacement du fichier
            }

        }else{
            $OffReponseOffreNonIdentifie->setCv("DoublonCV.pdf");

        }
        if ($form->get('lm')->getData()) { // Si l'utilisateur a joint un fichier
            $brochureFile = $form->get('lm')->getData();

            if ($brochureFile && $brochureFile->isReadable()) { // Vérifie si le fichier existe et est lisible
                $originalFilename = pathinfo($brochureFile->getClientOriginalName(), PATHINFO_FILENAME);
                $random_id = '_' . rand(1000, 9999);
                $nom_filename = $random_id . date('YmdHis');

                // Vérifie si l'extension peut être devinée, sinon utilise une extension par défaut
                $extension = $brochureFile->guessExtension() ?: 'pdf';
                $newFilename = $nom_filename . '-' . uniqid() . '.' . $extension;

                $OffReponseOffreNonIdentifie->setLm($newFilename);

                try {
                    $brochureFile->move(
                        $this->getParameter('temp'),
                        $newFilename
                    );
                } catch (FileException $e) {
                    // Gestion des erreurs lors du déplacement du fichier
                }
            } else {
                $OffReponseOffreNonIdentifie->setLm("DoublonLM.pdf");

            }
        }


        // $OffReponseOffreNonIdentifie->setIdMetier($form->get('metier_metier')->getData()->getIdMetier());

        if ($form->get('metier_metier')->getData() instanceof DomaineMetier) {
            $OffReponseOffreNonIdentifie->setIdMetier($form->get('metier_metier')->getData()->getIdMetier());
        }
        else {
            $OffReponseOffreNonIdentifie->setIdMetier(370);
        }

        $this->entityManager->persist($OffReponseOffreNonIdentifie);
        $this->entityManager->flush();

    }
    #[Route('/AL/candidatOffre', name: 'app_AL_candidat1')]
    public function searchElk(int $id=null, Request $request, Session $session=null, UserInterface $user=null, ELKOffOffreService $offOffreService): Response
    {
        $sentParams =  [
            "id" => $id,
            "session" => $session,
            "user" => $user,
        ];
        $searchParams = $request->query->all();
        return $this->displaySearchList($searchParams, $sentParams);
    }
    #[Route('/AL/candidatOffre/{id}', name: '_search_details_AL')]
    public function offreDetails(
        int $id,
        ManagerRegistry $doctrine,
        Request $request,
        Session $session,
        myToolsService $myToolsService,
        MailerService $mailerService,
        UrlGeneratorInterface $urlGenerator
    ): JsonResponse {
        //$baseURL = AppExtension::forceHTTPS($this->generateUrl('directemploi', [], UrlGeneratorInterface::ABSOLUTE_URL));
        $offre = $doctrine->getRepository(OffOffre::class)->candidatOffreDetails($id);


        /**
         * @greg: offre[0] is $OffOffre why not use it ?
         */
        /**
         * @var OffOffre $OffOffre
         */
        $OffOffre = $doctrine->getRepository(OffOffre::class)->findOneBy(['id_offre' => $id]);

        $UserUser = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $OffOffre->getUserUserIdUser()]);
        $secteurLien = $doctrine->getRepository(OffLienOffreListeSecteurActivite::class)->findOneBy(['off_offre_id_offre' => $id]);
        $secteur = $doctrine->getRepository(OffListeSecteurActivite::class)->findOneBy(['id_secteur_activite' => $secteurLien->getOffListeSecteurActiviteIdSecteurActivite()]);

        $jsonLD = [
            "@context" => "https://schema.org/",
            "@type" => "JobPosting",
            "title" => $OffOffre->getIntitule(),
            "description" => Utils::purgeHTMLForJsonLD($OffOffre->getMission()."  ".$OffOffre->getProfil()),
            "mission"=>$OffOffre->getMission(),
            "profil"=>$OffOffre->getProfil(),
            "datePosted"=> $OffOffre->getDateSoumission() ? $OffOffre->getDateSoumission()->format('Y-m-d\TH:i:s\Z') : null,
            "identifier"=>[
                "@type"=> "PropertyValue",
                "name"=> "id_offre",
                "value"=> $OffOffre->getIdOffre()
            ],
            "jobLocation"=> [
                "@type"=> "Place",
                "address"=> [
                    "@type"=> "PostalAddress",
                    "addressCountry"=> "FR",
                    "addressLocality"=> $OffOffre->getGeoVille()
                ]
            ]
        ];

        if($typeContrat = $OffOffre->getOffListeTypeContratIdTypeContrat()){
            if($typeContrat == OffListeTypeContrat::TYPE_CDD){
                $jsonLD["employmentType"] = "TEMPORARY";
            } elseif ($typeContrat == OffListeTypeContrat::TYPE_FREELANCE) {
                $jsonLD["employmentType"] = "CONTRACTOR" ;
            }elseif ($typeContrat == OffListeTypeContrat::TYPE_STAGE || $typeContrat == OffListeTypeContrat::TYPE_STAGE_FIN_ETUDE) {
                $jsonLD["employmentType"] = "INTERN";
            }

        }
        if($offre['libelleSecteur'] ?? false){
            // hesitate with "industry" and "occupationalCategory"
            $jsonLD["occupationalCategory"] = $offre['libelleSecteur'];
        }

        if($offre["raison_sociale"] ?? false){
            $jsonLD["hiringOrganization"] = [
                "@type" => "Organization",
                "name" => $offre["raison_sociale"],
                //"logo"=> $baseURL.'/uploads/'.$offre["logo"],
                "description"=> Utils::purgeHTMLForJsonLD($offre["presentation_societe"])

            ];
        }
        /*
        if (!$myToolsService->isBot() && $OffOffre->getStatut() == 1) {
            self::statPrint($OffOffre, $UserUser);
            // return $this->redirectToRoute('directemploi');
        }*/

        $id_region = $doctrine->getRepository(OffLienOffreGeoListeRegion::class)->findOneBy(
            ['off_offre_id_offre' => $id]
        );

        if ($id_region) {
            $id_region = $id_region->getGeoListeRegionIdRegion();
        }

        //$id_region = 17;

        if ($id_region) {
            $region = $doctrine->getRepository(GeoListeRegion::class)->findOneBy(
                ['id_region' => $id_region]
            );
            if($region){
                $jsonLD["jobLocation"]["address"]["addressRegion"] = $region->getLibelle();
            }

        }

        $departement = $doctrine->getRepository(GeoDepartement::class)->findOneBy(
            ['id_departement' => $doctrine->getRepository(OffLienOffreGeoDepartement::class)->findOneBy(
                ['off_offre_id_offre' => $id]
            )]
        );
        if ($departement) {
            $departement = $departement->getGeoDepartementIdDepartement();
        }
        /**
         * @greg: $departement seems to be null
         */
        if ($OffOffre->getDateDebut() != null) {
            if (is_string($OffOffre->getDateDebut())) {
                $date_debut = $OffOffre->getDateDebut();
            } elseif (preg_match("/[0-9]{4}-[0-9]{2}-[0-9]{2}/", $OffOffre->getDateDebut())) {
                $date_debut = date_format($OffOffre->getDateDebut(), "d/m/Y");
            } else {
                $date_debut = $OffOffre->getDateDebut();
            }
        } else {
            $date_debut = null;
        }

        $id_recruteur = $OffOffre->getUserUserIdUser();
        $user_offre = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $id_recruteur]);

        $form = $this->createForm(OffreCandidatureBTPType::class);
        $form->handleRequest($request);

        if ($OffOffre->getRepUrl() && $user_offre->getTypeDiffusion() != 5) {
            // TO REMOVE
            if(strpos($OffOffre->getRepUrl(), 'http') === false){
                $url_response = 'https://'.$OffOffre->getRepUrl();
            } else {
                $url_response = $OffOffre->getRepUrl();
            }
        }
        /*
       // $red_direct = $user_offre->getRedirectionAuto();

        if ($offre[0]->getRepUrl() && ($red_direct == 1)) {
            $url_direct = $offre[0]->getRepUrl();
        } else {
            $url_direct = null;
        }
*/
        if (!isset($region)) {
            $region = null;
        }
        if ($request->isXmlHttpRequest()) {
            $id = (int) $request->request->get('offId');
            $select = (int) $request->request->get('select');

            $target = $doctrine->getRepository(OffOffre::class)->findOneBy(['id_offre' => $id]);
            $select == 1 ? $target->setStatut(1) : $target->setStatut(0);
            $entityManager = $doctrine->getManager();
            $entityManager->persist($target);
            $entityManager->flush();
            $data = ["select" => $select];
            return new JsonResponse($data);
        }
        /*
         *
                return $this->render('candidat_offre/offreDetails.html.twig', [
            'url_direct' => $url_direct,
            'offreDetails' => $offre,
            "jsonLD"=> $jsonLD,
            'dateDebut' => $date_debut,
            'region' => $region,
            'departement' => $departement,
            'secteur' => $secteur->getLibelle(),
            'formulaireCandidature' => $form->createView()
        ]);*/
        $responseData = [
            'url_direct' => $url_response ?? null,
            'offreDetails' => $offre,
            'jsonLD' => $jsonLD,
            'dateDebut' => $date_debut,
            'region' => $region,
            'departement' => $departement,
            'secteur' => $secteur->getLibelle(),
        ];
        return new JsonResponse($responseData);
    }
    #[Route('/Al/view/{id}', name: '_view_search_details_Al')]
    public function viewOffreDetails(
        int $id,
        ManagerRegistry $doctrine,
        Request $request,
        Session $session,
        myToolsService $myToolsService,
        MailerService $mailerService,
        UrlGeneratorInterface $urlGenerator
    ): Response {
        if ($request->query->get('intitule') != null)
        {
            $intitule = $request->query->get('intitule');
        }else
        {
            $intitule=null;
        }

        $baseURL = AppExtension::forceHTTPS($this->generateUrl('directemploi', [], UrlGeneratorInterface::ABSOLUTE_URL));
        $offre = $doctrine->getRepository(OffOffre::class)->candidatOffreDetails($id);


        /**
         * @greg: offre[0] is $OffOffre why not use it ?
         */
        /**
         * @var OffOffre $OffOffre
         */
        $OffOffre = $doctrine->getRepository(OffOffre::class)->findOneBy(['id_offre' => $id]);
        if (!$OffOffre) {
            return $this->redirectToRoute('directemploi');
        }
        $UserUser = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $OffOffre->getUserUserIdUser()]);
        $secteurLien = $doctrine->getRepository(OffLienOffreListeSecteurActivite::class)->findOneBy(['off_offre_id_offre' => $id]);
        if (!$secteurLien) {
            $secteurLien = new OffLienOffreListeSecteurActivite();
            $secteurLien->setOffOffreIdOffre($id);
            $secteurLien->setOffListeSecteurActiviteIdSecteurActivite(31);
            $entityManager = $doctrine->getManager();
            $entityManager->persist($secteurLien);
            $entityManager->flush();
        }
        $secteur = $doctrine->getRepository(OffListeSecteurActivite::class)->findOneBy(['id_secteur_activite' => $secteurLien->getOffListeSecteurActiviteIdSecteurActivite()]);

        $jsonLD = [
            "@context" => "https://schema.org/",
            "@type" => "JobPosting",
            "title" => $OffOffre->getIntitule(),
            "description" => Utils::purgeHTMLForJsonLD($OffOffre->getMission()."  ".$OffOffre->getProfil()),
            "mission"=>$OffOffre->getMission(),
            "profil"=>$OffOffre->getProfil(),
            "datePosted"=> $OffOffre->getDateSoumission() ? $OffOffre->getDateSoumission()->format('Y-m-d\TH:i:s\Z') : null,
            "identifier"=>[
                "@type"=> "PropertyValue",
                "name"=> "id_offre",
                "value"=> $OffOffre->getIdOffre()
            ],
            "jobLocation"=> [
                "@type"=> "Place",
                "address"=> [
                    "@type"=> "PostalAddress",
                    "addressCountry"=> "FR",
                    "addressLocality"=> $OffOffre->getGeoVille()
                ]
            ]
        ];

        if($typeContrat = $OffOffre->getOffListeTypeContratIdTypeContrat()){
            if($typeContrat == OffListeTypeContrat::TYPE_CDD){
                $jsonLD["employmentType"] = "TEMPORARY";
            } elseif ($typeContrat == OffListeTypeContrat::TYPE_FREELANCE) {
                $jsonLD["employmentType"] = "CONTRACTOR" ;
            }elseif ($typeContrat == OffListeTypeContrat::TYPE_STAGE || $typeContrat == OffListeTypeContrat::TYPE_STAGE_FIN_ETUDE) {
                $jsonLD["employmentType"] = "INTERN";
            }

        }
        if($offre['libelleSecteur'] ?? false){
            // hesitate with "industry" and "occupationalCategory"
            $jsonLD["occupationalCategory"] = $offre['libelleSecteur'];
        }

        if($offre["raison_sociale"] ?? false){
            $jsonLD["hiringOrganization"] = [
                "@type" => "Organization",
                "name" => $offre["raison_sociale"],
                "logo"=> $baseURL.'/uploads/'.$offre["logo"],
                "description"=> Utils::purgeHTMLForJsonLD($offre["presentation_societe"])

            ];
        }

        if (!$myToolsService->isBot() && $OffOffre->getStatut() == 1) {
            self::statPrint($OffOffre, $UserUser);
            // return $this->redirectToRoute('directemploi');
        }

        $id_region = $doctrine->getRepository(OffLienOffreGeoListeRegion::class)->findOneBy(
            ['off_offre_id_offre' => $id]
        );

        if ($id_region) {
            $id_region = $id_region->getGeoListeRegionIdRegion();
        }

        //$id_region = 17;

        if ($id_region) {
            $region = $doctrine->getRepository(GeoListeRegion::class)->findOneBy(
                ['id_region' => $id_region]
            );
            if($region){
                $jsonLD["jobLocation"]["address"]["addressRegion"] = $region->getLibelle();
            }

        }

        $departement = $doctrine->getRepository(GeoDepartement::class)->findOneBy(
            ['id_departement' => $doctrine->getRepository(OffLienOffreGeoDepartement::class)->findOneBy(
                ['off_offre_id_offre' => $id]
            )]
        );
        if ($departement) {
            $departement = $departement->getGeoDepartementIdDepartement();
        }
        /**
         * @greg: $departement seems to be null
         */
        if ($OffOffre->getDateDebut() != null) {
            if (is_string($OffOffre->getDateDebut())) {
                $date_debut = $OffOffre->getDateDebut();
            } elseif (preg_match("/[0-9]{4}-[0-9]{2}-[0-9]{2}/", $OffOffre->getDateDebut())) {
                $date_debut = date_format($OffOffre->getDateDebut(), "d/m/Y");
            } else {
                $date_debut = $OffOffre->getDateDebut();
            }
        } else {
            $date_debut = null;
        }

        $id_recruteur = $OffOffre->getUserUserIdUser();
        $user_offre = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $id_recruteur]);

        // $red_direct = $user_offre->getRedirectionAuto();

        if ($OffOffre->getRepUrl() && $user_offre->getTypeDiffusion() != 5) {
            // TO REMOVE
            if(strpos($OffOffre->getRepUrl(), 'http') === false){
                $url_response = 'https://'.$OffOffre->getRepUrl();
            } else {
                $url_response = $OffOffre->getRepUrl();
            }
        }

        if (!isset($region)) {
            $region = null;
        }
        if ($request->isXmlHttpRequest()) {
            $id = (int) $request->request->get('offId');
            $select = (int) $request->request->get('select');

            $target = $doctrine->getRepository(OffOffre::class)->findOneBy(['id_offre' => $id]);
            $select == 1 ? $target->setStatut(1) : $target->setStatut(0);
            $entityManager = $doctrine->getManager();
            $entityManager->persist($target);
            $entityManager->flush();
            $data = ["select" => $select];
            return new JsonResponse($data);
        }
        $idoffre = $id;
        // return $this->render('candidat_offre/offreDetails.html.twig', [
        //         'url_direct' => $url_direct,
        //         'offreDetails' => $offre,
        //         "jsonLD"=> $jsonLD,
        //         'dateDebut' => $date_debut,
        //         'region' => $region,
        //         'departement' => $departement,
        //         'secteur' => $secteur->getLibelle(),
        //         'formulaireCandidature' => $form->createView(),
        //         'intitule'=>$intitule,
        //         'idoffre' => $idoffre,
        //     ]);
        $responseData = [
            'url_direct' => $url_response ?? null,
            'offreDetails' => $offre,
            'jsonLD' => $jsonLD,
            'dateDebut' => $date_debut,
            'region' => $region,
            'departement' => $departement,
            'secteur' => $secteur->getLibelle(),
        ];
        return new JsonResponse($responseData);
    }

    public function displaySearchList($searchParams, $sentParams = [], $fixedParams=[], $options = [])
    {
        //dd($sentParams["id"], $sentParams["session"], $sentParams["user"], $sentParams);

        $found = !empty($sentParams) ? 1 : 0;
        if ($found == 0 || ($sentParams["id"] == null)) {
            $allParams = $searchParams + $fixedParams;
        } else {
            $allParams = $searchParams["search_params"] + $fixedParams;
        }

        $array_secteurs = array(
            array('1-20', 'Achat / Logistique / Transport'),
            array('30-15', 'Administratif / Secrétariat / Public'),
            array('29', 'Agriculture / Agronomie / Alimentaire'),
            array('38', 'Aide à la personne / Social'),
            array('3', 'Banque / Assurance / Mutuelle'),
            array('4-12', 'BTP / Environnement / Génie civil'),
            array('5', 'Chimie / Matériaux / Plasturgie'),
            array('16-47-48', 'Commerces / Grande distribution / Retail'),
            array('6-13', 'Commercial / Vente / Export'),
            array('8', 'Conseil / Organisation / Stratégie'),
            array('11-27', "Electronique / Electricité / Télécom"),
            array('14-32', "Finance / Audit / Comptabilité"),
            array('17', 'Hôtellerie / Restauration'),
            array('46-18', 'Immobilier / Urbanisme / Architecture'),
            array('2-39-24-23', 'Industrie / Qualité / Sécurité / Auto'),
            array('19-10', "Informatique / Multimédia / Internet"),
            array('21-7-45-44', 'Marketing / Communication / Médias'),
            array('22', 'Production / Mécanique / Maintenance'),
            array('25-9', "RH / Formation / Juridique / Fiscalité"),
            array('26', "Santé / Paramédical / Pharmacie"),
            array('28', 'Tourisme / Loisirs / Sport / Culture'),
        );

        $array_voyelle = array("A","E","I","O","Y","U");

        $lib_comp = array();
        $lib_comp['ville'] = array_key_exists("ville",$allParams) ? " dans la ville ". (in_array($allParams['ville'][0],$array_voyelle) ? "d'": "de ") . ucfirst(strtolower($allParams['ville'])) : "";
        $lib_comp["departement"] = array_key_exists("departement",$allParams) ? " dans le departement " . ucfirst(strtolower($allParams['departement'])) : "";
        $lib_comp["region"] = array_key_exists("region",$allParams) ? " dans la région " . ucfirst(strtolower($allParams['region'])) : "";
        // $lib_comp["secteur"] = array_key_exists("secteur",$allParams) ? " du domaine " . str_replace(array(" - "," , ", ", ", " ,"), " / " ,ucfirst(strtolower($allParams['secteur']))) : "";
        $lib_comp["mc"] = array_key_exists("q",$allParams) ? " sur le mot-clé " . ucfirst(strtolower($allParams['q'])) : "";

        if(isset($options["slugParts"]['secteur'])){

            $id_secteur = explode("-",$options['slugParts']['secteur'])[0];
            foreach($array_secteurs as $secteur) {

                $key_a = "'".$secteur[0]."-";
                $key_b = "-".$secteur[0]."'";
                $key_c = "-".$secteur[0]."-";

                if(strpos($key_a, $id_secteur)) {
                    $lib_comp["secteur"] = " du domaine " . $secteur[1];
                    break;
                } elseif(strpos($key_b,$id_secteur)) {
                    $lib_comp["secteur"] = " du domaine " . $secteur[1];
                    break;
                } elseif(strpos($key_c,$id_secteur)) {
                    $lib_comp["secteur"] = " du domaine " . $secteur[1];
                    break;
                } else {
                    $lib_comp["secteur"] = "";
                }
            }
        } else {

            $lib_comp["secteur"] = "";
        }


        foreach ($allParams as $key => $value){

            if(!$value){
                unset($allParams[$key]);
            }
        }
        if(!isset($allParams["q"])){
            if(isset($allParams["secteur"])){
                $allParams["q"] = $allParams["secteur"];
                $allParams["q_field"] = "secteur";
                unset($allParams["secteur"]);
            }
        }
        if(!isset($allParams["localisation"])){
            foreach (["region","departement","ville"] as $field){
                if(!isset($allParams["localisation"]) && isset($allParams[$field] )){
                    $allParams["localisation"] = $allParams[$field];
                    $allParams["localisation_field"] = $field;
                }
                unset($allParams[$field]);
            }
        }else if(isset($allParams["localisation_field"])){
            $localisationField = $allParams["localisation_field"];
            $posLocalisation = array_search($localisationField , ["region", "departement", "ville"]);
            foreach (["region", "departement", "ville"] as $pos => $field){
                if(isset($allParams[$field])){
                    if($pos < $posLocalisation){
                        unset($allParams[$field]);
                    }else{
                        $allParams["localisation"] = $allParams[$field];
                        $allParams["localisation_field"] = $field;
                        unset($allParams[$field]);
                    }
                }
            }
        }

        $request = $this->requestStack->getMainRequest();
        $uri = $request->getRequestUri();

        $searchForm =  $this->createForm(CandidatOffreELKRechercheBTPType::class, $allParams );
        $metiers = null;
        if($rechercheAvancee = $this->getRechecheIfMatchSecteurs($allParams)){

            if($this->rechercheCacheService->data["metiers_by_id"][$rechercheAvancee->getIdRechercheAvancee()] ?? false){
                $metiers = $this->rechercheCacheService->data["metiers_by_id"][$rechercheAvancee->getIdRechercheAvancee()];
            }

        }

        unset($allParams["_token"]);
        $editRef = null;
        if($uri){
            $repoRef = $this->managerRegistry->getRepository(EditReferencement::class);
            $editRef = $this->cache->get("edit_referencement_".str_replace(["/", "(", ")","@"],"|",$uri),function() use ($repoRef, $uri){
                return $repoRef->findOneBy(["url"=>$uri]);
            });
        }

        $limit = 15;
        if ($found == 1 && $sentParams["id"])
        {
            $limit = 5;
        }

       list($listOffres, $elkResults) = $this->offreService->searchOffreForPage($allParams, 1, $limit);

       $pageCount = $listOffres->getPageCount();

        $isLastPage = ($pageCount <= 1) || ($pageCount == $listOffres->getPage());
        $breacrumb = $this->getBreadcrumpSearch($allParams, $request);

        $myDomaine = "";
        $myLocalisationType = "";
        $myLocalisation = "";
        $myContrat = "";

        if (isset($allParams["q"]))
            $myDomaine = $allParams["q"];
        if (isset($allParams["localisation"])) {
            $myLocalisation = $allParams["localisation"];

            if(isset($localisationField)){
                $myLocalisationType = $localisationField;
            }
        }

        if (isset($allParams["contrat"])) {
            $selectedValue = $allParams["contrat"];
            $options = [
                "" => "Contrat",
                "2" => "CDI",
                "1" => "CDD",
                "4" => "Interim",
                "3" => "FreeLance / Indépendant",
                "6" => "Alternance",
                "8" => "Stage"
            ];
            $myContrat = isset($options[$selectedValue]) ? $options[$selectedValue] : "Unknown option";
        }

        $title_seo = "Emploi";
        $title_seo .= $myDomaine ? " " . $myDomaine : "";
        $title_seo .= $myLocalisation ? ", " . $myLocalisation : ", en France";
        $title_seo .= $myContrat ? " en " . $myContrat : ", en CDI/CDD/Interim/FreeLance/indépendent/Alternance/Stage";

        if ($found == 1) {
            if ($sentParams["id"]) {
                $locs = ["departement", "region"]; // ce limiter a ces 3 type d'emplacement ("ville" par defaut)
                // $locs = ["departement", "region", true]; // rechers offres avec meme caracteristiques partout en France
                for ($i = 0; $i < count($locs) && $pageCount == 0; $i++) {
                    $allParams['localisation_field'] = $locs[$i];
                    $searchParams['search_params'] = $allParams;

                    list($listOffres, $elkResults) = $this->offreService->searchOffreForPage($allParams, $request->query->getInt("page", 1), $limit);

                    $pageCount = $listOffres->getPageCount();
                }
                if ($pageCount == 0)
                    return $this->redirectToRoute('app_AL_submitCandidat', ["id" => $sentParams["id"]]);
            }
        }
        if ($found == 1 && $sentParams["id"] != null) {
            $twig =  [
                'listeOffres' => $listOffres,
                'breadcrumb' => $breacrumb,
                'moreResults' => $isLastPage ? $this->extendsSearchLocation($searchParams):null,
            ];
        } else {
            $twig =  [
                'myDomaine' => $myDomaine,
                'myLocalisationType' => $myLocalisationType,
                'myLocalisation' => $myLocalisation,
                'myContrat' => $myContrat,
                //'listeOffres' => $listOffres,
                'editRef' => $editRef,
                'canonical' => $breacrumb[count($breacrumb)-1]["link"],
                'breadcrumb' => $breacrumb,
                'moreResults' => $isLastPage ? $this->extendsSearchLocation($searchParams):null,
                "rechercheAvancee" => $rechercheAvancee,
                'seoEditReferencement' => isset($options["slugParts"]) ? $this->getSeoEditReferencement($options["slugParts"]):null,
                'sideMenus'=> $this->generateSideMenu(true, $allParams, $metiers, $elkResults["aggs"]),
                'searchForm' => $searchForm->createView(),
                'connected' => ($found == 1 && $sentParams["user"]) ? 1 : 0,
                'title_seo' => $title_seo,
            ];
        }
        if($options["template_options"] ?? false){
            $twig =  $twig + $options["template_options"];
        }
        $twig['lib_comp'] = $lib_comp;
        if ($found == 1) {
            if ($sentParams["id"]) {

                $newOffers = [];
                foreach ($listOffres as $offer) {
                    foreach ($offer as $data) {
                        $newOffers[] = $data;
                    }
                }

                $sentParams["session"]->set("offers", $newOffers);
                $sentParams["session"]->set("user_datas", $searchParams["global"]["user_data"]);

                return $this->redirectToRoute('app_candidat_offres_similaires',
                    [
                        "id" => $sentParams["id"],
                    ]
                );
            }
        }
        return $this->render('DirectAlternance/candidat_offre/index.html.twig', $twig);
    }
    public function getRechecheIfMatchSecteurs($params){
        if(isset($params["q"]) && isset($params["q_field"]) && $params["q_field"] == "secteur"){
            $secteurs = $this->secteurActivityCacheService->secteurListToSecteurs($params["q"]);
            $ids = [];
            foreach ($secteurs as $secteur){
                $ids[] = $secteur["id"];

            }
            if(count($ids)){
                return $this->rechercheCacheService->getRechercheForSecteurIds($ids);
            }
        }
    }

    public function getBreadcrumpSearch($params, $request){
        $baseURL = $this->generateUrl("directemploi", [], UrlGeneratorInterface::ABSOLUTE_URL);
        $breadcrumb = [
            ["link"=> $baseURL, "title" => "Accueil"],
            ["link"=> $this->generateUrl("app_candidat_search", [], UrlGeneratorInterface::ABSOLUTE_URL), "title" => " Offres d'Emploi"],

        ];
        $titles = [];
        $slugField = [];
        if (isset($params["localisation_field"]) && isset($params["localisation"])) {
            $params[$params["localisation_field"]] = $params["localisation"];
        }

        if($params["q_field"] ?? false){
            $params[$params["q_field"]] = $params["q"];
        }



        if($params["metier"] ?? false){
            if(count($titles)){
                $titles [] = self::SLUG_METIER_SEPARATOR;
            }
            $titles [] = SimpleCacheService::getLegacySlug($params["metier"]);
            $slugField [] = "metier";
        }else if($params["recherche_avance_id"] ?? false ){
            /**
             * @var $rechercheAvance RechercheAvancee
             */
            $rechercheAvance = $this->rechercheCacheService->getById($params["recherche_avance_id"]);
            if($rechercheAvance){

                $slugField [] = "secteur";
                $titles[]= $rechercheAvance->getTitre();
            }

        }else if($params["secteur"] ?? false ){

            $titles[ ] = $params["secteur"];
            $slugField [] = "secteur";

        }else if($params["q"] ?? false){

            $titles [] = $params["q"];
            $slugField [] = "secteur";
        }

        if($params["localisation_field"] ?? false){
            foreach(["ville","departement","region", "pays"] as $field){
                if($params[$field] ?? false){
                    $titles[] = $params[$field];
                    $slugField [] = $field;
                    break;
                }
            }
        }else if($params["localisation"] ?? false){
            $titles[] = $params["localisation"];
            $slugField [] = "localisation";
        }

        if(isset($params["contrat"])){
            if(is_numeric($params["contrat"])){
                $contrat = $this->contratTypeCacheService->getById($params["contrat"]);
                if($contrat){
                    $titles [] = $contrat["label"];
                    $slugField [] = "contrat";
                }
            }else{
                $titles [] = $params["contrat"];
                $slugField [] = "contrat";
            }
        }
        if(isset($params["id_type_contrat"])){
            $slug [] = $this->contratTypeCacheService->data["by_id"][$params["id_type_contrat"]]["slug"] ;
            $slugField [] = "contrat";
        }




        $title = "Toutes les Offres";
        if(count($titles)>0){
            $title = implode(" - ", $titles);
        }
        $page = ($request->get("page") && $request->get("page") != 1) ? "?page=". $request->get("page"): "";

        $breadcrumb [] = ["link"=>$baseURL.substr($this->searchParamsToLegacyUrl($params).$page, 1), "title" => $title];
        return $breadcrumb;

    }

    public function extendsSearchLocation($searchParams)
    {
        if (isset($searchParams["localisation_field"])) {
            if ($searchParams["localisation_field"] === "ville") {
                $department = $this->geoCacheService->getDepartementForVilleLabel($searchParams["localisation"]);
                if ($department) {
                    $newParams = array_merge($searchParams, [
                        "localisation" => $department["label"],
                        "localisation_field" => "departement",
                        "page" => 1
                    ]);
                    return [
                        "location" => $department["label"],
                        "url" => $this->searchParamsToURL($newParams)
                    ];
                } else {
                    return [
                        "error" => "Aucun département trouvé pour la ville spécifiée."
                    ];
                }
            } elseif ($searchParams["localisation_field"] === "departement") {
                $region = $this->geoCacheService->getRegionForDepartementLabel($searchParams["localisation"]);
                if ($region) {
                    $newParams = array_merge($searchParams, [
                        "localisation" => $region["label"],
                        "localisation_field" => "region",
                        "page" => 1
                    ]);
                    return [
                        "location" => $region["label"],
                        "url" => $this->searchParamsToURL($newParams)
                    ];
                } else {
                    return [
                        "error" => "Aucune région trouvée pour le département spécifié."
                    ];
                }
            } elseif ($searchParams["localisation_field"] === "region") {
                $country = $this->geoCacheService->getCountryForRegionLabel($searchParams["localisation"]);
                if ($country) {
                    $newParams = array_merge($searchParams, [
                        "localisation" => $country["label"],
                        "localisation_field" => "pays",
                        "page" => 1
                    ]);
                    return [
                        "location" => $country["label"],
                        "url" => $this->searchParamsToURL($newParams)
                    ];
                } else {
                    return [
                        "error" => "Aucun pays trouvé pour la région spécifiée."
                    ];
                }
            }
        }
        return null;
    }

    public function getSeoEditReferencement($slugParts)
    {
        $seoRepo = $this->managerRegistry->getRepository(SeoEditReferencement::class);
        foreach(["secteur", "metier", "ville", "localisation"] as $field){
            if($slugParts [$field] ?? FALSE){
                $slug = $slugParts [$field];

                if($field == "secteur"){
                    $slug = "metiers-".$slug;
                } elseif($field == "metier"){
                    $slug = "metier-".str_replace(" ", "-", $slug);
                }

                $cacheKey = str_replace(["(",")"],"|", $slug);
                $seo = $this->cache->get("seo_edit_referencement_".$cacheKey, function (CacheItem $item ) use ($seoRepo, $slug){
                    return $seoRepo->findOneBy(["edit_app_id_app"=>$this->editAppId, "url"=>$slug]);
                });

                if($seo){
                    return $seo;
                }
            }
        }
        return null;
    }

    public function generateSideMenu($fromElk, $params, $metiers=[], $listItems = [], $newURLFormat = true){
        $menu = [];
        $hasContratSelected  = isset($params["contrat"]) || isset($params["id_contrat_type"]);
        $menu [] = ["label"=>"Toutes les offres d'emploi par Contrat", "items"=>$this->generateMenuLinksContrat($fromElk && !$hasContratSelected,  $params, $listItems["contrat"] ?? false, $newURLFormat)];

        if(isset($params["localisation_field"]) && isset($params["localisation"]) && $params["localisation"] && $params["localisation_field"]){
            $field = $params["localisation_field"];
            $params[$field] = $params["localisation"] ;
        }

        if($params["ville"] ?? false ){
            //$ville = $this->geoCacheService->data["ville_by_label"][$params["ville"]];
            //villes adjacentes
            /*$departement = $this->geoCacheService->getDepartementForVilleLabel($params["ville"]);
            if($departement){
                $newParms = $params;
                unset($newParms["ville"]);
                unset($newParms["localisation"]);
                unset($newParms["localisation_field"]);
                $newParms["departement"] = $departement["label"];
                $menu[] =  ["label"=> "Villes", "items"=>$this->generateMenuLinksVille(false , $newParms, $listItems["ville"] ?? false,$newURLFormat)];

            }*/
        }elseif($params["departement"] ?? false){
            $menu[] =  ["label"=> "Toutes les offres d'emploi dans votre Ville", "items"=>$this->generateMenuLinksVille($fromElk , $params, $listItems["ville"] ?? false, $newURLFormat)];
        }elseif($params["region"] ?? false){
            $menu[] =  ["label"=> "Toutes les offres d'emploi dans votre Département", "items"=>$this->generateMenuLinksDepartement($fromElk, $params, $listItems["departement"] ?? false,$newURLFormat)];
        }else{
            $menu[] =  ["label"=> "Toutes les offres d'emploi dans votre Région", "items"=>$this->generateMenuLinksRegion($fromElk, $params, $listItems["region"] ?? false, $newURLFormat)];
        }
        if($metiers){
            $menu [] = ["label"=>"Toutes les offres d'emploi par Metiers", "items"=>$this->generateMenuLinksMetiers($params,$metiers,  $newURLFormat)];
        }else{
            $request = $this->requestStack->getMainRequest();
            if(str_contains($request->getRequestUri(),"-metiers-secteurs") || !str_contains($request->getRequestUri(), "-".self::SLUG_METIER_SEPARATOR."-")
                && !str_contains($request->getRequestUri(), "-".self::SLUG_METIERS_SEPARATOR."-") && (!isset($params["q"]) || ! $params["q"]) ){

                $menu [] = ["label"=>"Toutes les offres d'emploi par Domaine", "items"=>$this->generateMenuLinksSecteurs($fromElk, $params,$listItems["secteur"] ?? false, $newURLFormat)];

            }
        }
        return $menu;
    }

    public function generateMenuLinksMetiers($params, $metiers,  $newURLFormat = true){
        $contrats = $this->contratTypeCacheService->getContratsWithOrdre();
        $links = [];
        foreach($metiers  as $metier){
            $url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["metier"=>$metier])
                : $this->searchParamsToURL(array_merge($params , ["metier"=>$metier]));

            $links[] = ["label"=>$metier, "url"=>$url];
        }
        return $links;
    }
    public function generateMenuLinksContrat($fromElk, $params, $items, $newURLFormat = true){
        $links = [];
        //contrat and id_type_contrat are supported => confusion
        $contratField = isset($params["contrat"]) ? "contrat" : (isset($params["id_type_contrat"]) ? "id_type_contrat" : "contrat") ;

        if($fromElk){
            foreach ($items as $item ){
                $contrat = $this->contratTypeCacheService->getByLabel($item["label"]);
                $url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl(array_merge($params , [$contratField=>$contrat["label"]]))
                    : $this->searchParamsToURL(array_merge($params , [$contratField=>$contrat["id"]]));

                $links[] = ["label"=>$contrat["label"] , "url"=>$url, "count"=>$item["count"]];
            }
        }else{
            $contrats = $this->contratTypeCacheService->getContratsWithOrdre();

            foreach($contrats  as $contrat){
                $url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl(array_merge($params , [$contratField=>$contrat["id"]]))
                    : $this->searchParamsToURL(array_merge($params , [$contratField=>$contrat["id"]]));

                $links[] = ["label"=>$contrat["label"], "url"=>$url];
            }
        }

        return $links;
    }

    public function generateMenuLinksDepartement($fromElk, $params, $items , $newURLFormat = true){
        $region = $params["region"];
        unset($params["region"]);

        $links = [];

        if($fromElk){
            foreach ($items as $departement){
                $url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["departement"=>$departement["label"]])
                    : $this->searchParamsToURL(array_merge($params , ["localisation"=>$departement["label"], "localisation_field"=>"departement"]));

                $links[] = ["label"=>$departement["label"], "url"=>$url, "count"=>$departement["count"]];
            }
        }else{
            $departements = $this->geoCacheService->getDepartementListForRegionLabel($region);
            foreach($departements  as $departement){
                $url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["departement"=>$departement["label"]])
                    : $this->searchParamsToURL(array_merge($params , ["localisation"=>$departement["label"], "localisation_field"=>"departement"]));

                $links[] = ["label"=>$departement["label"], "url"=>$url];
            }
        }

        return $links;
    }

    public function generateMenuLinksSecteurs($fromElk, $params, $items, $newURLFormat = true){

        $links = [];
        if($fromElk) {

            /**
            @note: Try to regroup secteurs with secteur group defined in recherche_avancee
             */
            $secteurGroups = [];
            $secteurs = [];
            foreach ($items as $k=> $item){
                /**
                 * @var $secteur array
                 */
                $secteur = $this->secteurActivityCacheService->getByLabel($item["label"]);
                if($secteur){
                    $items[$k]["id"] = $secteur["id"];
                    $group = $this->rechercheCacheService->getBySecteur($items[$k]["id"]);
                    if($group){
                        $groupId = $group->getIdRechercheAvancee();
                        if(!isset($secteurGroups[$groupId])){
                            $secteurGroups[$groupId] = ["count"=>$item["count"], "label"=>$group->getTitre(), "type"=>"recherche_avance_id", "id"=>$groupId];
                        }else{
                            $secteurGroups[$groupId]["count"] = $secteurGroups[$groupId]["count"] + $item["count"];
                        }
                    }else{
                        $item["type"] = "secteur";
                        $secteurs[] = $item;
                    }
                }
            }
            $secteurs = array_merge($secteurs, array_values($secteurGroups));
            usort($secteurs, function ($a, $b){
                return  $b["count"] - $a["count"] ;
            });

            foreach($secteurs as $secteur){
                $secteurParams =($secteur["type"] == "recherche_avance_id") ?  ["recherche_avance_id" => $secteur["id"]] : ["secteur"=> $secteur["label"]];
                $url = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + $secteurParams)
                    : $this->searchParamsToURL(array_merge($params, ["q" => $secteur["label"], "q_field" => "secteur"]));

                $links[] = ["label" => $secteur["label"], "url" => $url, "count"=>$secteur["count"]];
            }
        }else{
            $secteurs = $this->secteurActivityCacheService->data["by_id"];
            foreach ($secteurs as $secteur) {
                $url = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["secteur" => $secteur["label"]])
                    : $this->searchParamsToURL(array_merge($params, ["q" => $secteur["label"], "q_field" => "secteur"]));

                $links[] = ["label" => $secteur["label"], "url" => $url];
            }
        }

        return $links;
    }

    public function generateMenuLinksVille($fromELk, $params, $items, $newURLFormat = true){
        $links = [];
        if($fromELk){
            //$departement = $this->geoCacheService->getDepartementByLabel($params["departement"];

            unset($params["departement"]);

            foreach($items  as $ville){
                $url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["ville"=>$ville["label"]])
                    : $this->searchParamsToURL(array_merge($params, ["localisation"=>$ville["label"], "localisation_field"=>"ville"]));

                $links[] = ["label"=>$ville["pretty"] ?? $ville["label"], "url"=>$url, "count"=>$ville["count"]];
            }
        }else{
            $villes = $this->geoCacheService->getMainVillesForDepartementLabel($params["departement"]);
            unset($params["departement"]);

            foreach($villes  as $ville){
                $url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["ville"=>$ville["label"]])
                    : $this->searchParamsToURL(array_merge($params, ["localisation"=>$ville["label"], "localisation_field"=>"ville"]));

                $links[] = ["label"=>$ville["pretty"] ?? $ville["label"], "url"=>$url];
            }
        }

        return $links;
    }

    public function generateMenuLinksRegion($fromElk, $params, $items , $newURLFormat = true){
        $links = [];
        if($fromElk){
            foreach($items as $region){
                $url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["region"=>$region["label"]])
                    : $this->searchParamsToURL(array_merge($params + ["localisation"=>$region["label"], "localisation_field"=>"region"]));

                $links[] = ["label"=>$region["label"], "url"=>$url, "count"=>$region["count"]];
            }
        }else{
            $regions = $this->geoCacheService->getRegions();

            foreach($regions as $region){
                $url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["region"=>$region["label"]])
                    : $this->searchParamsToURL(array_merge($params + ["localisation"=>$region["label"], "localisation_field"=>"region"]));

                $links[] = ["label"=>$region["label"], "url"=>$url];
            }
        }

        return $links;
    }


    public function searchParamsToLegacyUrl($params)
    {
        $slug = [];
        $slugField = [];
        if (($params["localisation_field"] ?? false) && ($params["localisation"] ?? false)) {
            $params[$params["localisation_field"]] = $params["localisation"];
        }
        if($params["q_field"] ?? false){
            $params[$params["q_field"]] = $params["q"];
        }
        if(isset($params["contrat"])){
            if(is_numeric($params["contrat"])){
                $contrat = $this->contratTypeCacheService->getById($params["contrat"]);
                if($contrat){
                    $slug [] = $contrat["slug"];
                    $slugField [] = "contrat";
                }
            }else{
                $contratSlug =  $this->contratTypeCacheService->getSlugByLabel($params["contrat"]);
                if($contratSlug){
                    $slug [] = $contratSlug;
                    $slugField [] = "contrat";
                }
            }


        }
        if(isset($params["id_type_contrat"])){
            $slug [] = $this->contratTypeCacheService->data["by_id"][$params["id_type_contrat"]]["slug"] ;
            $slugField [] = "contrat";
        }

        foreach(["ville","departement","region", "pays"] as $field){
            if($params[$field] ?? false){
                if($field == "ville"){
                    $villeSlug = SimpleCacheService::getLegacySlug($params[$field]);
                    $ville = $this->geoCacheService->getFirstVilleBySlug($villeSlug);
                    if($ville){
                        $id_departement = $ville["id_departement"];
                        $departement = $this->geoCacheService->getDepartementById($id_departement);
                        $slug[] = $villeSlug;
                        $slug[] = "(". $departement["code_postal"].")";
                    }
                    //$this->geoCacheService->get
                }else{
                    $slug[] = SimpleCacheService::getLegacySlug($params[$field]);
                }

                $slugField [] = $field;
                break;
            }
        }
        if($params["metier"] ?? false){
            if(count($slug)){
                $slug [] = self::SLUG_METIER_SEPARATOR;
            }
            $slug [] = SimpleCacheService::getLegacySlug($params["metier"]);
            $slugField [] = "metier";
        }else if($params["recherche_avance_id"] ?? false ){
            /**
             * @var $rechercheAvance RechercheAvancee
             */
            $rechercheAvance = $this->rechercheCacheService->getById($params["recherche_avance_id"]);
            if($rechercheAvance){
                if(count($slug)){
                    $slug [] = self::SLUG_METIERS_SEPARATOR;
                }
                $slugField [] = "secteur";
                $slug[]=implode("-", $rechercheAvance->getIdSecteurActivites()). "-".SimpleCacheService::getLegacySlug($rechercheAvance->getTitre());
            }

        }else if($params["secteur"] ?? false ){
            if(count($slug)){
                $slug [] = self::SLUG_METIERS_SEPARATOR;
            }
            //can be multiple
            if(strpos($params["secteur"], ",") !== false){
                $secteurLabels = explode( ",",$params["secteur"]);
                $secteurSlugs = [];
                $secteurIds = [];
                foreach ($secteurLabels as $label){
                    $label = trim($label);
                    $secteur = $this->secteurActivityCacheService->getBySlug(SimpleCacheService::getLegacySlug($label));
                    if($secteur){
                        $secteurIds[] = $secteur["id"];
                        $secteurSlugs[] = str_replace("--","-",$secteur["slug"]);
                    }
                }
                sort($secteurIds, SORT_NUMERIC);
                $slug[] = implode("-",$secteurIds)."-".implode("-", $secteurSlugs);
            }else{
                $slug [] = SimpleCacheService::getLegacySlug($params["secteur"]);



            }
            $slugField [] = "secteur";

        }/*elseif(isset($params["ville"]) || isset($params["departement"]) || isset($params["region"]) || isset($params["pays"])) {
			$slug [] = self::SLUG_ALL_METIERS_SECTEURS_SUFFIXE;
		}*/ else if(isset($params["q"])  && $params["q"]){
            if(count($slug)){
                $slug [] = self::SLUG_METIER_SEPARATOR;
            }
            $slug [] = SimpleCacheService::getLegacySlug($params["q"]);
            $slug [] = self::SLUG_ALL_SECTEURS_SUFFIXE;
            $slugField [] = "secteur";
        }
        if(!in_array("secteur",$slugField) && !in_array("metier",$slugField)){
            $slug [] = self::SLUG_ALL_METIERS_SECTEURS_SUFFIXE;
        }
        if(count($slugField) == 0){
            return self::BASE_LIST_URL;
        }
        return self::BASE_LIST_URL .implode("-", $slug);
        /*if(count($slugField) > 1 ){

        }else{
            return self::BASE_LIST_URL .$slugField[0]."/".implode("-",$slug);
        }*/

    }

    public function searchParamsToURL ($params){
        return self::BASE_LIST_URL."?".http_build_query($params);
    }
    public function clearSessionDatas($session, $datas=[])
    {
        foreach($datas as $data) {
            if ($session->get($data) != null)
                $session->remove($data);
        }
    }

    #[Route('/AL/submitCandidat/{id}', name: 'app_AL_submitCandidat')]
    public function submitCandidat(Session $session,ELKOffOffreService $elkOffOffreService,MetierMetierRepository $metierMetierRepository,UrlGeneratorInterface $urlGenerator,MailerService $mailerService,Request $request,myToolsService $myToolsService,$id,ManagerRegistry $doctrine,CvCvRepository $cvCvRepository,UserUserRepository $userRepository,UserSituationRepository $situationRepository,GeoAdresseRepository $adresseRepository, ELKOffOffreService $offOffreService): Response
    {

        $baseURL = AppExtension::forceHTTPS($this->generateUrl('directemploi', [], UrlGeneratorInterface::ABSOLUTE_URL));
        $offre = $doctrine->getRepository(OffOffre::class)->candidatOffreDetails($id);
        $OffOffre = $doctrine->getRepository(OffOffre::class)->findOneBy(['id_offre' => $id]);
        $compte=$doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $OffOffre->getUserUserIdUser()]);
        $entreprise=$doctrine->getRepository(EntrEntreprise::class)->findOneBy(['id_entreprise' => $compte->getEntrEntrepriseIdEntreprise()]);

        //Tag = 1 Est une redirection Tag = 0 Est un formulaire
        if ($entreprise->getTag()==1 && $OffOffre->getRepUrl() != null) {
            return $this->redirect($OffOffre->getRepUrl());
        }
        $excludedIds = [
            1255997, 1255971, 1245988, 1255189, 1255224, 872634, 1255828,
            1255197, 1255223, 991195, 984813, 1255823, 1255226, 1255216,
            1256039, 1255768, 1255840, 1255703, 860993, 1255221, 1255767,
            1255198, 1255196, 1255965, 1255253, 1255184, 1064982, 1095565,
            1255227, 1255383, 1254911, 977102, 1255825, 909244, 1255700,
            1255250, 1255721, 846965, 1256004, 1047570, 1248694, 1013221,
            933277, 1116637, 1050164, 909713, 4
        ];

        $UserUser = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $OffOffre->getUserUserIdUser()]);
        if ($OffOffre->getRepMail() === "") {
            // Redirection, type_click = 1
            return $this->redirect($OffOffre->getRepUrl());
        } else {
            // Vue du formulaire, type_click = 2
            // Rendu du formulaire ou autre logique
        }
        $defaultData = [];
        if ($this->getUser())
        {
            $user=$userRepository->findOneBy(['id_user' => $this->getUser()->getIdUser()]);
            $Geoadresse = $adresseRepository->findOneBy(['id_adresse'=>$user->getGeoAdresseIdAdresse()]);
            $usersituation = $situationRepository->findOneBy(['user_user_id_user'=>$user->getIdUser()]);
            $metier = $metierMetierRepository->findOneBy(['id_metier'=>$usersituation->getIdMetier()]);

            $defaultData = [
                'email' => $user->getEmail(),
                'nom' => $user->getNom(),
                'prenom' => $user->getPrenom(),
                'telephone' => $user->getTelephone(),
                'code_postal' => $Geoadresse->getCodePostal(),
                'formation_level' => $usersituation->getOffListeTypeFormationIdTypeFormation(),
                'domaine_activite' => $usersituation->getOffListeTypeFormationIdTypeFormation(),
                'metier_metier' => $usersituation->getIdMetier(),
                'ec_ecole' => $usersituation->getEcEcoleIdEcole(),
                'anneeObtentionDiplome' => $usersituation->getAnneeObtentionDiplome(),
            ];
            $Cv = $cvCvRepository->findBy(['user_user_id_user'=>$this->getUser()->getIdUser()]);
        }else
        {
            $user=null;
            $Cv=null;
            $Geoadresse=null;
            $metier=null;
            $usersituation=null;
        }
        /**
         * @greg: offre[0] is $OffOffre why not use it ?
         */
        /**
         * @var OffOffre $OffOffre
         */
        $jsonLD = [
            "@context" => "https://schema.org/",
            "@type" => "JobPosting",
            "title" => $OffOffre->getIntitule(),
            "description" => Utils::purgeHTMLForJsonLD($OffOffre->getMission()."  ".$OffOffre->getProfil()),
            "datePosted"=> $OffOffre->getDateSoumission() ? $OffOffre->getDateSoumission()->format('Y-m-d\TH:i:s\Z') : null,
            "identifier"=>[
                "@type"=> "PropertyValue",
                "name"=> "id_offre",
                "value"=> $OffOffre->getIdOffre()
            ],
            "jobLocation"=> [
                "@type"=> "Place",
                "address"=> [
                    "@type"=> "PostalAddress",
                    "addressCountry"=> "FR",
                    "addressLocality"=> $OffOffre->getGeoVille()
                ]
            ]
        ];
        if($typeContrat = $OffOffre->getOffListeTypeContratIdTypeContrat()){
            if($typeContrat == OffListeTypeContrat::TYPE_CDD){
                $jsonLD["employmentType"] = "TEMPORARY";
            } elseif ($typeContrat == OffListeTypeContrat::TYPE_FREELANCE) {
                $jsonLD["employmentType"] = "CONTRACTOR" ;
            }elseif ($typeContrat == OffListeTypeContrat::TYPE_STAGE || $typeContrat == OffListeTypeContrat::TYPE_STAGE_FIN_ETUDE) {
                $jsonLD["employmentType"] = "INTERN";
            }

        }
        if($offre['libelleSecteur'] ?? false){
            // hesitate with "industry" and "occupationalCategory"
            $jsonLD["occupationalCategory"] = $offre['libelleSecteur'];
        }

        if($offre["raison_sociale"] ?? false){
            $jsonLD["hiringOrganization"] = [
                "@type" => "Organization",
                "name" => $offre["raison_sociale"],
                //"logo"=> $baseURL.'/uploads/'.$offre["logo"],
                "description"=> Utils::purgeHTMLForJsonLD($offre["presentation_societe"])

            ];
        }
        if (!$myToolsService->isBot() && $OffOffre->getStatut() == 1) {
            self::statPrint($OffOffre, $UserUser);
            // return $this->redirectToRoute('directemploi');
        }

        $id_region = $doctrine->getRepository(OffLienOffreGeoListeRegion::class)->findOneBy(
            ['off_offre_id_offre' => $id]
        );

        if ($id_region) {
            $id_region = $id_region->getGeoListeRegionIdRegion();
        }

        //$id_region = 17;

        if ($id_region) {
            $region = $doctrine->getRepository(GeoListeRegion::class)->findOneBy(
                ['id_region' => $id_region]
            );
            if($region){
                $jsonLD["jobLocation"]["address"]["addressRegion"] = $region->getLibelle();
            }

        }
        $departement = $doctrine->getRepository(GeoDepartement::class)->findOneBy(
            ['id_departement' => $doctrine->getRepository(OffLienOffreGeoDepartement::class)->findOneBy(
                ['off_offre_id_offre' => $id]
            )]
        );
        if ($departement) {
            $departement = $departement->getGeoDepartementIdDepartement();
        }
        /**
         * @greg: $departement seems to be null
         */
        if ($OffOffre->getDateDebut() != null) {
            if (is_string($OffOffre->getDateDebut())) {
                $date_debut = $OffOffre->getDateDebut();
            } elseif (preg_match("/[0-9]{4}-[0-9]{2}-[0-9]{2}/", $OffOffre->getDateDebut())) {
                $date_debut = date_format($OffOffre->getDateDebut(), "d/m/Y");
            } else {
                $date_debut = $OffOffre->getDateDebut();
            }
        } else {
            $date_debut = null;
        }
        $id_recruteur = $OffOffre->getUserUserIdUser();
        $user_offre = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $id_recruteur]);
        $cvChoices = [];
        if ($Cv !=null)
        {


        foreach ($Cv as $cvs) {
            $cvChoices[$cvs->getIdCv()] = $cvs->getTitre();
        }
        }
        // Pass the default data to the form
        $form = $this->createForm(SubmitCandidatureType::class);
        if($user!=null)
        {
            $form->get('mobile')->setData($user->getTelephone());
        }
        if($Geoadresse!=null)
        {
            $form->get('code_postal')->setData($Geoadresse->getCodePostal());
        }
        if($usersituation!=null) {
            $form->get('metier_metier')->setData($usersituation->getIdMetier());
        }
        $form->handleRequest($request);


        if($form->isSubmitted())
        {
            self::statsCandidature($OffOffre->getIdOffre(), $user);

            $date = date('d-m-Y');
            $OffOffre = $doctrine->getRepository(OffOffre::class)->findOneBy(['id_offre' => $id]);

            if (!$OffOffre) {
                throw $this->createNotFoundException();
            }
            $civilite_field = $form->get('user_civilite_id_user_civilite')->getData();
            $prenom_field = $form->get('prenom')->getData();
            $nom_field = $form->get('nom')->getData();
            $email_field = $form->get('email')->getData();

            if($form->get('telephone')->getData()!=null)
            {
                $tel_field = $form->get('telephone')->getData();
            }elseif($user!=null)
            {
                $tel_field=$user->getTelephone();
            }else
            {
                $tel_field=null;
            }
            if($form->get('code_postal')->getData()!=null)
            {
                $cp_field = $form->get('code_postal')->getData();
            }elseif($Geoadresse!=null)
            {
                $cp_field=$Geoadresse->getCodePostal();
            }else
            {
                $cp_field=null;
            }
            if($form->get('metier_metier')->getData()!=null)
            {
                $metier_field = $form->get('metier_metier')->getData();
            }elseif($user!=null)
            {
                $metier_field=$metier;
            }else
            {
                $metier_field=null;
            }
            $formation_field = $form->get('liste_formation')->getData();
            //$promo_field = $form->get('promo')->getData();
            $domaine_field = $form->get('domaine_activite')->getData();
            if ($OffOffre->getIsAnonym()) {
                $txt_entreprise = "Anonyme";
            } else {
                $id_entreprise = $user_offre->getEntrEntrepriseIdEntreprise();

                $txt_entreprise = $doctrine->getRepository(EntrEntreprise::class)->findOneBy(
                    ['id_entreprise' => $id_entreprise]
                )->getRaisonSociale();
            }

                if ($this->getUser() && in_array("ROLE_CANDID", $this->getUser()->getRoles())) {
                    $OffCandidature = self::candidatureConnected($session, $form, $OffOffre);
                    $email_recrut = "cand";

                    if ($OffOffre->getRepMail()) {
                        $mailerService->envoiMailSimple('MAIL_CANDIDATURE_CANDIDAT', $email_recrut, $email_field, array(
                                "##PRENOM##" => $form->get('prenom')->getData(),
                                "##LINK_COMPTE##" => $urlGenerator->generate('app_espace_candidat', [], UrlGeneratorInterface::ABSOLUTE_URL),
                                "##TITRE##" => $OffOffre->getIntitule(),
                                "##REFERENCE##" => $OffOffre->getReference(),
                                "##ENTREPRISE##" => $txt_entreprise,
                                "##DATE##" => $date
                            )
                        );
                    }
                } else {
                    $OffReponseOffreNonIdentifie = self::candidatureNoConnected($form, $OffOffre);

                    $email_recrut = "cand";
                    if ($OffOffre->getRepMail()) {
                        $mailerService->envoiMailSimple('MAIL_CANDIDATURE_CANDIDAT_NON_CONNECTE', $email_recrut, $email_field, array(
                            "##PRENOM##" => $form->get('prenom')->getData(),
                            "##TITRE##" => $OffOffre->getIntitule(),
                            "##REFERENCE##" => $OffOffre->getReference(),
                            "##ENTREPRISE##" => $txt_entreprise,
                            "##DATE##" => $date), null, null, null, null
                        );
                    }


                }
    
            if($OffOffre->getUserUserIdUser() == '1066521'){
                $this->addFlash('msg', 'Votre candidature a été prise en compte 1');
                self::clearSessionDatas($session, ["cv_name", "lm_name"]);
                return $this->redirectToRoute('app_AL_submitCandidat', ['id' => $id]);
            }
            if ($OffOffre->getRepUrl() && $user_offre->getTypeDiffusion() != 5) {
                // TO REMOVE
                if(strpos($OffOffre->getRepUrl(), 'http') === false){
                    $url_response = 'https://'.$OffOffre->getRepUrl();
                } else {
                    $url_response = $OffOffre->getRepUrl();
                }
                return $this->redirect($url_response);


            } else {

                if ($OffOffre->getRepMail() && $OffOffre->getRepMail() != '') {
                    $email = $OffOffre->getRepMail();
                } else {
                    $email = $user_offre->getEmail();
                }

                if (isset($OffCandidature) && $OffCandidature) {

                    $prenom = $user_offre->getPrenom();
                    $tel = $user_offre->getTelephone();
                    $nom = $user_offre->getNom();
                    $date = $OffCandidature->getDateCandidature()->format('d-m-Y');

                    $cp = $doctrine->getRepository(GeoAdresse::class)->findOneBy(
                        ['id_adresse' => $user_offre->getGeoAdresseIdAdresse()]
                    )->getCodePostal();

                    $Corps_message = $doctrine->getRepository(CommonMessage::class)->findOneBy(
                        ['label' => 'MAIL_CANDIDATURE']
                    )->getValue();

                    $cv = $OffCandidature->getCv();
                    if ($OffCandidature->getLm()) {
                        $lm = $OffCandidature->getLm();
                    } else {
                        $lm = null;
                    }

                    if ($OffOffre->getIntitule()) {
                        $intitule_offre_mail = $OffOffre->getIntitule();
                    }

                    if ($OffOffre->getReference()) {
                        $reference_offre_mail = $OffOffre->getReference();
                    }

                    //Envoi mail ATS spécifique pour WorkAndYou
                    if($OffOffre->getUserUserIdUser() == 1095928){
                        $subject = $form->get('nom')->getData() ." - ". $form->get('prenom')->getData() ." - ". $OffOffre->getReference() . " - Direct Emploi";
                        $mailerService->sendMailWY($email, $subject,$lm, $cv, $form->get('prenom')->getData() . ' ' . $form->get('nom')->getData(), true);
                    } else {

                        $mailerService->envoiMailSimple('MAIL_CANDIDATURE',$email_recrut, $email, array(
                            "##OFFRE##" => $intitule_offre_mail,
                            "##REFERENCE##" => $reference_offre_mail,
                            "##NOM##" => $form->get('nom')->getData(),
                            "##PRENOM##" => $form->get('prenom')->getData(),
                            "##EMAIL##" => $email_field,
                            "##URL_OFFRE##" => $urlGenerator->generate('app_AL_submitCandidat', ['id' => $id], UrlGeneratorInterface::ABSOLUTE_URL)),
                            $lm, $cv, $form->get('prenom')->getData() . ' ' . $form->get('nom')->getData(), true
                        );
                    }


                } elseif (isset($OffReponseOffreNonIdentifie) && $OffReponseOffreNonIdentifie) {

                    $tel = $OffReponseOffreNonIdentifie->getTel();
                    $prenom = $OffReponseOffreNonIdentifie->getPrenom();
                    $nom = $OffReponseOffreNonIdentifie->getNom();
                    $date = $OffReponseOffreNonIdentifie->getDateCandidature()->format('d-m-Y');
                    $cp = $OffReponseOffreNonIdentifie->getCp();
                    $annee_sortie = $OffReponseOffreNonIdentifie->getAnneeSortie();

                    $TypeFormation = $doctrine->getRepository(OffListeTypeFormation::class)->findOneBy(
                        ['id_type_formation' => $OffReponseOffreNonIdentifie->getOffListeTypeFormationIdTypeFormation()]
                    );

                    if ($TypeFormation) {
                        $formation = $TypeFormation->getLibelle();
                    } else {
                        $formation = "";
                    }

                    $Corps_message = $doctrine->getRepository(CommonMessage::class)->findOneBy(
                        ['label' => 'MAIL_CANDIDATURE']
                    )->getValue();

                    $Corps_message = str_replace("##PROMO##", $annee_sortie, $Corps_message);
                    $Corps_message = str_replace("##FORMATION##", $formation, $Corps_message);


                    $cv = $OffReponseOffreNonIdentifie->getCv();
                    $lm = $OffReponseOffreNonIdentifie->getLm();

                    if ($OffOffre->getIntitule()) {
                        $intitule_offre_mail = $OffOffre->getIntitule();
                    }

                    if ($OffOffre->getReference()) {
                        $reference_offre_mail = $OffOffre->getReference();
                    }

                    //Envoi mail ATS spécifique pour WorkAndYou
                    if($OffOffre->getUserUserIdUser() == 1095928){
                        $subject = $form->get('nom')->getData() ." - ". $form->get('prenom')->getData() ." - ". $OffOffre->getReference() . " - Direct Emploi";
                        $mailerService->sendMailWY($email, $subject,$lm, $cv, $form->get('prenom')->getData() . ' ' . $form->get('nom')->getData());
                    }
                    else {
                        // Variables pour l'offre par défaut
                        $intitule_offre_mail = $OffOffre->getIntitule();
                        $reference_offre_mail = $OffOffre->getReference();
                        $url_offre = $urlGenerator->generate('app_candidat_search_details', ['id' => $OffOffre->getIdOffre()], UrlGeneratorInterface::ABSOLUTE_URL);
                        $email_recrut = $OffOffre->getRepMail();

                        // Envoi de l'offre par défaut
                        $mailerService->envoiMailSimple(
                            'MAIL_CANDIDATURE',
                            $email_recrut,
                            $email,
                            [
                                "##OFFRE##" => $intitule_offre_mail,
                                "##REFERENCE##" => $reference_offre_mail,
                                "##NOM##" => $form->get('nom')->getData(),
                                "##PRENOM##" => $form->get('prenom')->getData(),
                                "##EMAIL##" => $email_field,
                                "##URL_OFFRE##" => $url_offre
                            ],
                            $lm,
                            $cv,
                            $form->get('prenom')->getData() . ' ' . $form->get('nom')->getData(),
                            false,
                            $form->get('email')->getData()
                        );
/*
                        // Envoi des offres similaires
                        $title = $OffOffre->getIntitule();
                        $ville = $OffOffre->getGeoVille();
                        $command = "python3 /var/www/directemploi/www/direct_emploi_s6/src/AI/find_similar_jobs.py " . escapeshellarg($title) . " " . escapeshellarg($ville);
                        $output = shell_exec($command);
                        $results = json_decode($output, true);

                        if ($results) {
                            $sentJobs = []; // Tableau pour stocker les IDs des offres déjà envoyées
                            foreach ($results as $job) {
                                $jobId = $job['id'];

                                // Vérifie si l'ID de l'offre a déjà été envoyé
                                if (in_array($jobId, $sentJobs)) {
                                    continue; // Passe à l'offre suivante si elle est déjà envoyée
                                }

                                // Récupérer l'offre complète en utilisant le repository avec l'ID
                                $OffOffre1 = $doctrine->getRepository(OffOffre::class)->findOneBy(['id_offre' => $jobId]);

                                if ($OffOffre1) {
                                    // Ajouter l'ID de l'offre à la liste des offres envoyées
                                    //$sentJobs[] = $jobId;
                                    // Appel de la méthode `candidatureNoConnected` pour l'offre similaire
                                    $this->candidatureNoConnectedDoublon($form, $OffOffre1->getIdOffre());

                                    $intitule_offre_mail = $OffOffre1->getIntitule();
                                    $reference_offre_mail = $OffOffre1->getReference();
                                    $url_offre = $urlGenerator->generate('app_candidat_search_details', ['id' => $jobId], UrlGeneratorInterface::ABSOLUTE_URL);
                                    $email_recrut = $OffOffre1->getRepMail();
                                    if ($email_recrut !=""){
                                        // Envoi de l'email pour chaque offre similaire
                                        $mailerService->envoiMailSimple(
                                            'MAIL_CANDIDATURE',
                                            $email_recrut,
                                            $email,
                                            [
                                                "##OFFRE##" => $intitule_offre_mail,
                                                "##REFERENCE##" => $reference_offre_mail,
                                                "##NOM##" => $form->get('nom')->getData(),
                                                "##PRENOM##" => $form->get('prenom')->getData(),
                                                "##EMAIL##" => $email_field,
                                                "##URL_OFFRE##" => $url_offre
                                            ],
                                            $lm,
                                            $cv,
                                            $form->get('prenom')->getData() . ' ' . $form->get('nom')->getData(),
                                            false,
                                            $form->get('email')->getData()
                                        );
                                    }
                                } else {
                                    // Gérer les cas où l'offre n'a pas été trouvée dans la base de données
                                    continue;
                                }
                            }
                        } else {
                            // Gérer les cas où aucun résultat n'a été trouvé
                            echo "Aucune offre similaire trouvée.";
                        }
*/
                    }
                }
            }
            if($user_offre->getTypeDiffusion() == 5) {
                $cv_mime_type = mime_content_type($this->getParameter('temp') . $cv);
                $cv_contenu = file_get_contents($this->getParameter('temp') . $cv, true);
                $cv_base64 = base64_encode($cv_contenu); //."==\n";

                $lm_mime_type = mime_content_type($this->getParameter('temp') . $lm);
                $lm_contenu = file_get_contents($this->getParameter('temp') . $lm, true);
                $lm_base64 = base64_encode($lm_contenu); //."==\n";

                $url_api = $OffOffre->getRepUrl();

                if ($lm) {
                    $tableau_pour_json = array(
                        'version' => "1.0",
                        'first_name' => $prenom,
                        'last_name' => $nom,
                        'email' => $email,
                        'mobile_phone' => $tel,
                        'zip_code' => $cp,
                        'country' => 'FR',
                        'cv' => array(
                            'name' => $cv,
                            'mime_type' => $cv_mime_type,
                            'content' => $cv_base64,
                        ),
                        'cover' => array(
                            'name' => $lm,
                            'mime_type' => $lm_mime_type,
                            'content' => $lm_base64,
                        ),
                    );
                } else {
                    $tableau_pour_json = array(
                        'version' => "1.0",
                        'first_name' => $prenom,
                        'last_name' => $nom,
                        'email' => $email,
                        'mobile_phone' => $tel,
                        'zip_code' => $cp,
                        'country' => 'FR',
                        'cv' => array(
                            'name' => $cv,
                            'mime_type' => $cv_mime_type,
                            'content' => $cv_base64,
                        ),
                    );
                }

                $json = json_encode($tableau_pour_json);

                $curl = curl_init($url_api);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLINFO_HEADER_OUT, true);
                curl_setopt($curl, CURLOPT_POST, true);
                curl_setopt($curl, CURLOPT_POSTFIELDS, $json);

                //  Set HTTP Header for POST request
                curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
                $result = curl_exec($curl);
                curl_close($curl);
            }

            $globalInfo = [
                /*"id" => [
                    "candidat" => ($this->security->getUser()->getIdUser()) ? $this->security->getUser()->getIdUser() : -1,
                    "recruteur" => $offre[0]->getUserUserIdUser(),
                    "entreprise" => $offre["id_entreprise"],
                    "offre" => $offre[0]->getIdOffre(),
                ],*/
                "user_data" => array(
                    "civilite" => $civilite_field,
                    "prenom" => $prenom_field,
                    "nom" => $nom_field,
                    "mobile" => $tel_field,
                    "email" => $email_field,
                    "cp" => (int) $cp_field,
                    "formation" => $formation_field,
                   // "promo" => (int) $promo_field,
                    "secteur" => array(
                        "id" => $domaine_field->getIdSecteurActivite() ? $domaine_field->getIdSecteurActivite() : -1,
                        "value" => $domaine_field->getLibelle() ? self::findDomaineName($domaine_field) : -1,
                    ),
                    "metier" => array(
                        "id" => ($metier_field == null) ? -1 : $metier_field->getIdMetier(),
                        "value" => ($metier_field == null) ? -1 : $metier_field->getLibelle(),
                    ),
                    "documents" => array(
                        "cv" => $session->get("cv_name") ? $session->get("cv_name") : 0,
                        "lm" => $session->get("lm_name") ? $session->get("lm_name") : 0,
                    ),
                ),
                /*"offre_data" => [
                     "secteur" => [
                         "id" => $secteurLien->getOffListeSecteurActiviteIdSecteurActivite(),
                         "value" => $secteur->getLibelle(),
                     ],
                     "localisation" => [
                         "id"=> ($region->getIdRegion()) ? $region->getIdRegion() : 0,
                         "region" => $region->getLibelle(),
                         "value" => $offre[0]->getGeoVille(),
                     ],
                     "contrat" =>[
                         "id" => $offre[0]->getOffListeTypeContratIdTypeContrat(),
                         "value"=> $offre["libelleContrat"],
                     ]
                 ],*/
            ];
            $secteurLien = $doctrine->getRepository(OffLienOffreListeSecteurActivite::class)->findOneBy(['off_offre_id_offre' => $id]);
            $secteur = $doctrine->getRepository(OffListeSecteurActivite::class)->findOneBy(['id_secteur_activite' => $secteurLien->getOffListeSecteurActiviteIdSecteurActivite()]);

            $localisationFetch = [
                "q" => self::findDomaineName($secteur),
                "localisation" => ($offre[0]->getGeoVille() != null) ? strtoupper($offre[0]->getGeoVille()) : $region->getLibelle(),
                "localisation_field" => "ville" || "departement" || "region", // A FIX
                "contrat" => "2",//(string) $offre[0]->getOffListeTypeContratIdTypeContrat(),
            ];
            $this->addFlash('msg', 'Votre candidature a été prise en compte ');
            /*  SI UTILISATEUR N'A PAS DE COMPTE REDIRIGEZ LE*/


            /*
            if (!($this->getUser())) {
                self::clearSessionDatas($session, ["cv_name", "lm_name"]);
                return $this->redirectToRoute('app_candidat_search_details_DE', ['id' => $id]);
            }
            */


            return $this->redirectToRoute('app_AL_candidat_search',
                [
                    'id' => $id,
                    'global' => $globalInfo,
                    'search_params' => array (
                        "q" => self::findDomaineName($secteur),
                        "localisation" => ($offre[0]->getGeoVille() != null) ? strtoupper($offre[0]->getGeoVille()) : $region->getLibelle(),
                        "localisation_field" => "ville", //par defaut puis change si besoin au cours de la recherche
                        //"contrat" => (string) $offre[0]->getOffListeTypeContratIdTypeContrat(),//"2",
                        "contrat" => $offre[0]->getOffListeTypeContratIdTypeContrat() ? (string) $offre[0]->getOffListeTypeContratIdTypeContrat() : "2",
                    )
                ]
            );
        }
        $red_direct = $user_offre->getRedirectionAuto();
        if ($offre[0]->getRepUrl() && ($red_direct == 1)) {
            $url_direct = $offre[0]->getRepUrl();
        } else {
            $url_direct = null;
        }

        if (!isset($region)) {
            $region = null;
        }
        if ($request->isXmlHttpRequest()) {
            $id = (int) $request->request->get('offId');
            $select = (int) $request->request->get('select');
            $target = $doctrine->getRepository(OffOffre::class)->findOneBy(['id_offre' => $id]);
            $select == 1 ? $target->setStatut(1) : $target->setStatut(0);
            $entityManager = $doctrine->getManager();
            $entityManager->persist($target);
            $entityManager->flush();
            $data = ["select" => $select];
            return new JsonResponse($data);
        }
        return $this->render('DirectAlternance/candidat_offre/offre-form.html.twig', [
            'form' => $form->createView(),
            'user' => $user ?? null,
            'situation' => $usersituation ?? null,
            'cv' => $Cv ?? null,
            'cvChoices' => $cvChoices,
            'offreDetails' => $offre,

        ]);
    }


    public function statsOffresMultiples($user_id, $offre_id)
    {
        $entrepriseID = $this->doctrine->getRepository(UserUser::class)->findOneBy([
            'id_user'=>
                $this->doctrine->getRepository(OffOffre::class)
                    ->findOneBy(['id_offre'=>$offre_id])->getUserUserIdUser()
        ])->getEntrEntrepriseIdEntreprise();

        $stats = new AdminOffMultStats();
        $stats->setUserCandidID($user_id);
        $stats->setIdOffre($offre_id);
        $stats->setIdEntreprise($entrepriseID);
        $stats->setCountApply(1);
        $stats->setDateCreation(new DateTime('now'));
        $this->entityManager->persist($stats);
        $this->entityManager->flush($stats);
    }

    public function statsCandidature(int $id_offre , $user_user) {

        //$result = $this->doctrine->getRepository(OffStatistique::class)->findOneById($id_offre);

        //if (!$result) {
        $OffStatistique = new OffStatistique();
        $OffStatistique->setOffOffreIdOffre($id_offre);

        if ($user_user and $user_user->getEntrEntrepriseIdEntreprise()) {

            $OffStatistique->setEntrEntrepriseIdEntreprise($user_user->getEntrEntrepriseIdEntreprise());
            $OffStatistique->setDateLog(new DateTime());
            $OffStatistique->setTypeLog('2');

            $entityManager = $this->doctrine->getManager();
            $entityManager->persist($OffStatistique);
            $entityManager->flush();
        }
        //}
    }

    public function statPrint($OffOffre, $UserUser)
    {
        $stat_print = new OffStatistique();
        $stat_print->setOffOffreIdOffre($OffOffre->getIdOffre());
        $stat_print->setEntrEntrepriseIdEntreprise($UserUser->getEntrEntrepriseIdEntreprise());
        $stat_print->setDateLog(new DateTime());
        $stat_print->setTypeLog('1');
        $entityManager = $this->doctrine->getManager();
        $entityManager->persist($stat_print);
        $entityManager->flush();
    }

    public function candidatureConnected($session, $form, $OffOffre)
    {
        $email_cand = $this->security->getUser()->getEmail();
        $OffCandidature = $this->doctrine->getRepository(OffCandidature::class)->findOneBy(
            ['user_user_id_user' => $this->security->getUser()->getIdUser(), 'off_offre_id_offre' => $OffOffre->getIdOffre()]
        );
        $entityManager = $this->doctrine->getManager();

        if (!$OffCandidature) {
            $OffCandidature = new OffCandidature();
            $OffCandidature->setUserUserIdUser($this->security->getUser()->getIdUser());
            $OffCandidature->setOffOffreIdOffre($OffOffre->getIdOffre());
            $OffCandidature->setNbCandidature(1);

        } else {
            $OffCandidature->setNbCandidature($OffCandidature->getNbCandidature() + 1);
        }

        $OffCandidature->setDateCandidature(new DateTime());
        $OffCandidature->setSaisie('');
        $OffCandidature->setOffListeTypeSuiviIdTypeSuivi(1);
        $OffCandidature->setTriRecruteur(0);

        $entityManager->persist($OffCandidature);
        $entityManager->flush();
        if ($form->get('cvexist')->getData() == "Sélectionner un cv")
        {
            $CvCv = new CvCv();
            $CvCv->setDateCreation(new \DateTime());
            $CvCv->setIsVisible(1);
            $CvCv->setIsAnonym(0);
            $CvCv->setDateModification(new \DateTime());
            $CvCv->setUserUserIdUser($this->security->getUser()->getIdUser());
            $CvCv->setTitre('Cv de ' . $this->security->getUser()->getNom() . ' ' . $this->security->getUser()->getPrenom());
            $CvCv->setIntitulePoste('');


            $brochureFile = $form->get('cv')->getData();
            $originalFilename = pathinfo($brochureFile->getClientOriginalName(), PATHINFO_FILENAME);
            $nom_filename = $this->security->getUser()->getIdUser() . date('YmdHis');
            $newFilename = $nom_filename . '-' . uniqid() . '.' . $brochureFile->guessExtension();
            $session->set("cv_name", $newFilename);

            try {
                $brochureFile->move(
                    $this->getParameter('cv_cand_save'),
                    $newFilename
                );
            } catch (FileException $e) {
                // ... handle exception if something happens during file upload
            }

            $CvCv->setNomFichierCvOriginal($originalFilename);
            $CvCv->setNomFichierCvStockage($newFilename);
            $CvCv->setOffFourchetteRemunerationIdFourchetteRemunerationSouhaite(0);
            $CvCv->setOffFourchetteRemunerationIdFourchetteRemunerationActuel(0);
            $CvCv->setEtat(0);
            $entityManager->persist($CvCv);
            $entityManager->flush();

            $OffCandidature->setCv($newFilename);
        }else
        {
            $cv = $this->managerRegistry->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $this->getUser()->getIdUser(),'titre'=>$form->get('cvexist')->getData()]);
            $OffCandidature->setCv($cv->getNomFichierCvStockage());
        }


        if ($form->get('lm')->getData()) {
            $brochureFile = $form->get('lm')->getData();
            $originalFilename = pathinfo($brochureFile->getClientOriginalName(), PATHINFO_FILENAME);

            $newFilename = 'lm' . $this->security->getUser()->getIdUser() . date('YmdHis') . '.' . $brochureFile->guessExtension();
            $session->set("lm_name", $newFilename);
            //On met le fichier dans un dossier temporaire
            try {
                $brochureFile->move(
                    $this->getParameter('lm_cand_save'),
                    $newFilename
                );
            } catch (FileException $e) {
                // ... handle exception if something happens during file upload
            }

            $OffCandidature->setLm($newFilename);
        }

        $entityManager->persist($OffCandidature);
        $entityManager->flush();
        return $OffCandidature;
    }

    public function candidatureNoConnected($form, $OffOffre)
    {

        /* Enregistrement de l'adresse email pour relance */
        $SaveMailCandiNonLog = $this->doctrine->getRepository(SaveMailCandiNonLog::class)->findOneBy(
            ['email' => $form->get('email')->getData()]
        );

        if (!$SaveMailCandiNonLog) {
            $SaveMailCandiNonLog = new SaveMailCandiNonLog;
            $SaveMailCandiNonLog->setEmail($form->get('email')->getData());
        }
        $SaveMailCandiNonLog->setEditAppIdApp(1);
        $SaveMailCandiNonLog->setPrenom($form->get('prenom')->getData());
        $SaveMailCandiNonLog->setOffListeTypeContratIdTypeContrat($OffOffre->getOffListeTypeContratIdTypeContrat());

        $this->entityManager->persist($SaveMailCandiNonLog);
        $this->entityManager->flush();


        $OffReponseOffreNonIdentifie = new OffReponseOffreNonIdentifie();
        $OffReponseOffreNonIdentifie->setDateCandidature(new DateTime());
        $OffReponseOffreNonIdentifie->setNom($form->get('nom')->getData());
        $OffReponseOffreNonIdentifie->setPrenom($form->get('prenom')->getData());
        $OffReponseOffreNonIdentifie->setEmail($form->get('email')->getData());
        $OffReponseOffreNonIdentifie->setCp($form->get('code_postal')->getData());
        $OffReponseOffreNonIdentifie->setOffOffreIdOffre($OffOffre->getIdOffre());
        $OffReponseOffreNonIdentifie->setTel($form->get('telephone')->getData());
        $OffReponseOffreNonIdentifie->setOffListeSecteurActiviteIdSecteurActivite($form->get('domaine_activite')->getData()->getIdSecteurActivite());
        $OffReponseOffreNonIdentifie->setTriRecruteur(0);
        $OffReponseOffreNonIdentifie->setEcSessionFormationIdSessionFormation(null);
        $OffReponseOffreNonIdentifie->setEcEcoleIdEcole(null);
        $OffReponseOffreNonIdentifie->setEtablissementAutre(null);

        if ($form->has('metier_metier')) {
            $OffReponseOffreNonIdentifie->setIdMetier((int)$form->get('metier_metier')->getData()->getIdMetier());
        }

        if ($form->has('promo')) {
            $OffReponseOffreNonIdentifie->setAnneeSortie($form->get('promo')->getData());
        }

        if ($form->has('liste_formation')) {
            $OffReponseOffreNonIdentifie->setOffListeTypeFormationIdTypeFormation($form->get('liste_formation')->getData());
        } else {
            $OffReponseOffreNonIdentifie->setOffListeTypeFormationIdTypeFormation(23);
        }

        if ($form->get('user_civilite_id_user_civilite')->getData() == 1) {
            $OffReponseOffreNonIdentifie->setCivilite('M');
        } else {
            $OffReponseOffreNonIdentifie->setCivilite('F');
        }

        if ($form->has('nom_diplome')) {
            $OffReponseOffreNonIdentifie->setNomDiplome($form->get('nom_diplome')->getData());
        } else {
            $OffReponseOffreNonIdentifie->setNomDiplome(null);
        }

        if ($form->has('id_metier')) {
            $OffReponseOffreNonIdentifie->setIdMetier($form->get('id_metier')->getData());
        } else {
            $OffReponseOffreNonIdentifie->setIdMetier(null);
        }

        if ($form->has('metier_autre')) {
            $OffReponseOffreNonIdentifie->setMetierAutre($form->get('metier_autre') ? $form->get('metier_autre') : null);
        }

        if ($form->has('remuneration_souhaitee')) {
            $OffReponseOffreNonIdentifie->setIdOffFourchetteRemuneration($form->get('remuneration_souhaitee')->getData());
        } else {
            $OffReponseOffreNonIdentifie->setIdOffFourchetteRemuneration(null);
        }


        $brochureFile = $form->get('cv')->getData();
        $originalFilename = pathinfo($brochureFile->getClientOriginalName(), PATHINFO_FILENAME);
        $random_id = rand(1000, 9999);
        $nom_filename = $random_id . date('YmdHis');
        $newFilename = $nom_filename . '-' . uniqid() . '.' . $brochureFile->guessExtension();
        $OffReponseOffreNonIdentifie->setCv($newFilename);

        try {
            $brochureFile->move(
                $this->getParameter('temp'),
                $newFilename
            );
        } catch (FileException $e) {
            // ... handle exception if something happens during file upload
        }

        if ($form->get('lm')->getData()) { // Si l'utilisateur a joint un fichier
            $brochureFile = $form->get('lm')->getData();
            $originalFilename = pathinfo($brochureFile->getClientOriginalName(), PATHINFO_FILENAME);
            $random_id = '_' . rand(1000, 9999);
            $nom_filename = $random_id . date('YmdHis');
            $newFilename = $nom_filename . '-' . uniqid() . '.' . $brochureFile->guessExtension();

            $OffReponseOffreNonIdentifie->setLm($newFilename);

            try {
                $brochureFile->move(
                    $this->getParameter('temp'),
                    $newFilename
                );
            } catch (FileException $e) {
                // ... handle exception if something happens during file upload
            }
        }

        $this->entityManager->persist($OffReponseOffreNonIdentifie);
        $this->entityManager->flush();

        return $OffReponseOffreNonIdentifie;
    }

    public function candidatureConnectedMultipleApply($userID, $docs, $OffOffre)
    {
        $cv_name = $docs["cv"] ? $docs["cv"] : 0;
        $lm_name = $docs["lm"] ? $docs["lm"] : null;

        $user_id = (int) $userID;
        $offre_id = $OffOffre->getIdOffre();

        $OffCandidature = $this->doctrine->getRepository(OffCandidature::class)->findOneBy(
            ['user_user_id_user' => $user_id, 'off_offre_id_offre' => $offre_id]
        );
        $entityManager = $this->doctrine->getManager();

        if (!$OffCandidature) {
            $OffCandidature = new OffCandidature();

            $OffCandidature->setUserUserIdUser($user_id);
            $OffCandidature->setOffOffreIdOffre($offre_id);
            $OffCandidature->setNbCandidature(1);

        } else {
            $OffCandidature->setNbCandidature($OffCandidature->getNbCandidature() + 1);
        }

        $OffCandidature->setDateCandidature(new DateTime());
        $OffCandidature->setSaisie('');
        $OffCandidature->setOffListeTypeSuiviIdTypeSuivi(1);
        $OffCandidature->setTriRecruteur(0);
        $OffCandidature->setCv($cv_name);
        ($lm_name != null) ? $OffCandidature->setLm($lm_name) : 0;

        $entityManager->persist($OffCandidature);
        $entityManager->flush();

        /*$CvCv = new CvCv();
        $CvCv->setDateCreation(new \DateTime());
        $CvCv->setIsVisible(1);
        $CvCv->setIsAnonym(0);
        $CvCv->setDateModification(new \DateTime());
        $CvCv->setUserUserIdUser($this->security->getUser()->getIdUser());
        $CvCv->setTitre('Cv de ' . $this->security->getUser()->getNom() . ' ' . $this->security->getUser()->getPrenom());
        $CvCv->setIntitulePoste('');


        /*$brochureFile = $docs["cv"];
        $originalFilename = pathinfo($brochureFile->getClientOriginalName(), PATHINFO_FILENAME);
        $nom_filename = $this->security->getUser()->getIdUser() . date('YmdHis');
        $newFilename = $docs["cv"];

        $CvCv->setNomFichierCvOriginal($newFilename);
        $CvCv->setNomFichierCvStockage($newFilename);
        $CvCv->setOffFourchetteRemunerationIdFourchetteRemunerationSouhaite(0);
        $CvCv->setOffFourchetteRemunerationIdFourchetteRemunerationActuel(0);
        $CvCv->setEtat(0);
        $entityManager->persist($CvCv);
        $entityManager->flush();*/

        return $OffCandidature;
    }


    public function findDomaineName($domaine_offListeSecteurActivite)
    {
        $domaine = $domaine_offListeSecteurActivite;

        if ($domaine->getLibelleAlternatif1() != null)
            return $domaine->getLibelleAlternatif1();
        if ($domaine->getLibelleAlternatif2() != null)
            return $domaine->getLibelleAlternatif2();
        if ($domaine->getLibelleAlternatif3() != null)
            return $domaine->getLibelleAlternatif3();
        if ($domaine->getMotsClesAssocies() != null)
            return trim(explode(";", $domaine->getMotsClesAssocies())[0]);
        return trim(explode("/", $domaine->getLibelle())[0]);
    }

    public function findPresentationEntreprise($entreprise, $doctrine)
    {
        $recruteur = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user'=>$entreprise->getUserUserIdUser()]);
        $id_entreprise = $recruteur->getEntrEntrepriseIdEntreprise();
        $findPresentation = $doctrine->getRepository(EntrEntreprise::class)->findOneBy(['id_entreprise'=>(int) $id_entreprise])->getPresentationSociete();

        return ($findPresentation);
    }

    public function filterOffresInfo($offres, $doctrine)
    {
        foreach ($offres as $offre) {
            $entreprise = $offre[0];
            $allInfo[] = [
                "checked" => 0,
                "id" => array(
                    "offre" => $entreprise->getIdOffre(),
                    "recruteur" => $entreprise->getUserUserIdUser(),
                ),
                "offre" => array(
                    "intitule" => $entreprise->getIntitule(),
                    "raison_sociale" => $offre["raison_sociale"],
                    "publication" => $entreprise->getDateSoumission(),
                    "reference" => $entreprise->getReference(),
                    "secteur_actvite" => array (
                        "secteur" => $offre["secteur"],
                        "id" => $offre["id_secteur_activite"],
                    ),
                    "localisation" => array(
                        "ville" => $entreprise->getGeoVille(),
                        "region" => "a trouver",
                    ),
                    "contrat" => array(
                        "id" => $offre["id_type_contrat"],
                        "type" => $offre["contrat"],
                        "debut" => $entreprise->getDateDebut(),
                        "duree" => $entreprise->getDureeContrat(),
                    ),
                    "text" => array(
                        "presentation" => self::findPresentationEntreprise($entreprise, $doctrine),
                        "mission" => $entreprise->getMission(),
                        "profil" => $entreprise->getProfil(),
                    ),
                ),
            ];
        }
        return $allInfo;
    }

    public function struct_home_search($string)
    {
        $from = array("'", 'É', 'È', 'é', 'ê', 'è', 'à', 'ô', ' - ', ' ', '-/');
        $to = array('-', 'e', 'e', 'e', 'e', 'e', 'a', 'o', '-', '-', '');
        $result = str_replace(array("--", "---"), array("-", "-"), str_ireplace($from, $to, strtolower($string)));
        return $result;
    }
}
