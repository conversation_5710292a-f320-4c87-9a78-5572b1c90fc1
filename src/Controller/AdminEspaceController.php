<?php

namespace App\Controller;

use App\Entity\ArticleForAllSiteDirectEmploi;
use App\Entity\EntrEntreprise;
use App\Entity\OffNombreClickParEntreprise;
use App\Repository\ArticleForAllSiteDirectEmploiRepository;
use App\Repository\ArticlesRouteRepository;
use App\Service\ELKOffOffreService;
use App\Service\ToolsService;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Console\Application;
use Symfony\Component\Console\CommandLoader\CommandLoaderInterface;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Process\Process;
use Symfony\Component\Security\Core\Security;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use Symfony\Component\HttpFoundation\Session\SessionInterface;

use Symfony\Component\HttpFoundation\JsonResponse;

use DateTime;
use App\Entity\EditArticle;
use App\Entity\AdminEditArticle;
use App\Form\AdminOffreFinder;
use App\Form\AdminActuUpdate;
use App\Form\AdminEditArticle as AdmEditArticle;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use App\Form\AccueilAdminArticleType;
use App\Form\AdminDateRangeCandidateType;
use App\Form\AdminEditArticleType;
use App\Form\SearchFormType;
use App\Form\StatsDateClickType;
use App\Repository\AdminEditArticleRepository;
use App\Repository\ComSouscriptionCommercialeRepository;
use App\Repository\CvtequeProfilsRepository;
use App\Repository\EditArticleRepository;
use App\Repository\EditThemeArticleRepository;
use App\Repository\EditTypeArticleRepository;
use App\Repository\EntrEntrepriseRepository;
use App\Repository\OffCandidatureRepository;
use App\Repository\OffListeSecteurActiviteRepository;
use App\Repository\OffOffreRepository;
use App\Repository\OffReponseOffreNonIdentifieRepository;
use App\Repository\OffStatistiqueRepository;
use App\Repository\UserUserRepository;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\String\Slugger\SluggerInterface;
use Doctrine\DBAL\Connection;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
#[Route('/admin', name: 'app_admin')]
class AdminEspaceController extends AbstractController
{
    private const COMMANDS = [
        'ABSOLIS' => 'ABSOLIS',
        'ADSN' => 'ADSN',
        'AG2R' => 'AG2R',
        'ASCENCIABUSINESSSCHOOL' => 'ASCENCIABUSINESSSCHOOL',
        'ASEmploi' => 'ASEmploi',
        'AboutirEmploi' => 'AboutirEmploi',
        'AbsolisSud' => 'AbsolisSud',
        'Actua' => 'Actua',
        'Adampartners' => 'Adampartners',
        'Adapei' => 'Adapei',
        'Adoma' => 'Adoma',
        'Adworks' => 'Adworks',
        'Adzuna' => 'Adzuna',
        'AdzunaAlternance' => 'AdzunaAlternance',
        'AdzunaDomTom' => 'AdzunaDomTom',
        'Aesio' => 'Aesio',
        'Agatha' => 'Agatha',
        'AlbertvilleInterim' => 'AlbertvilleInterim',
        'Aldi' => 'Aldi',
        'Alfainterim' => 'Alfainterim',
        'Alkine' => 'Alkine',
        'Allia' => 'Allia',
        'Alphyr' => 'Alphyr',
        'Altagile' => 'Altagile',
        'Altays' => 'Altays',
        'Alterego' => 'Alterego',
        'AlteregoInterim' => 'AlteregoInterim',
        'Alternative' => 'Alternative',
        'AnnecyInterim' => 'AnnecyInterim',
        'Apave' => 'Apave',
        'Aptic' => 'Aptic',
        'AquilaRH' => 'AquilaRH',
        'ArcelorMittal' => 'ArcelorMittal',
        'ArobaseInterim' => 'ArobaseInterim',
        'AsapWork' => 'AsapWork',
        'AssistancePubliqueHopitauxdeParis' => 'AssistancePubliqueHopitauxdeParis',
        'Atalian' => 'Atalian',
        'Auchan' => 'Auchan',
        'Aurys' => 'Aurys',
        'Axeo' => 'Axeo',
        'Azae' => 'Azae',
        'BPS' => 'BPS',
        'Beetween' => 'Beetween',
        'Boulanger' => 'Boulanger',
        'BouyguesConstruction' => 'BouyguesConstruction',
        'Brainfield' => 'Brainfield',
        'Briand' => 'Briand',
        'Broadbean' => 'Broadbean',
        'BuffaloGrill' => 'BuffaloGrill',
        'BurgerKing' => 'BurgerKing',
        'BusinessPeople' => 'BusinessPeople',
        'ByMyCar' => 'ByMyCar',
        'CCLD' => 'CCLD',
        'CDEL' => 'CDEL',
        'CHU_Rennes' => 'CHU_Rennes',
        'CRARH' => 'CRARH',
        'CTConsultant' => 'CTConsultant',
        'Carrefour' => 'Carrefour',
        'CarrefourRecrut' => 'CarrefourRecrut',
        'CarrouselEntreprise' => 'CarrouselEntreprise',
        'Casa' => 'Casa',
        'Casino' => 'Casino',
        'Cdchabitat' => 'Cdchabitat',
        'Celio' => 'Celio',
        'Cerba' => 'Cerba',
        'ChantiersAtlantique' => 'ChantiersAtlantique',
        'Charier' => 'Charier',
        'Chronopost' => 'Chronopost',
        'Citya' => 'Citya',
        'Clubofficine' => 'Clubofficine',
        'Coallia' => 'Coallia',
        'CollegeDeParis' => 'CollegeDeParis',
        'Colruyt' => 'Colruyt',
        'Conserto' => 'Conserto',
        'Cora' => 'Cora',
        'Costco' => 'Costco',
        'Cstb' => 'Cstb',
        'DPD' => 'DPD',
        'Dachser' => 'Dachser',
        'Danone' => 'Danone',
        'Darty' => 'Darty',
        'Dcs' => 'Dcs',
        'Decathlon' => 'Decathlon',
        'Dekra' => 'Dekra',
        'Dentego' => 'Dentego',
        'DepartementEssonne' => 'DepartementEssonne',
        'DepartementduRhone' => 'DepartementduRhone',
        'Domaliance' => 'Domaliance',
        'DominoRH' => 'DominoRH',
        'Dupon' => 'Dupon',
        'EDF' => 'EDF',
        'EDUSERVICES' => 'EDUSERVICES',
        'Ecole Conte' => 'Ecole Conte',
        'Effektiv' => 'Effektiv',
        'Elior' => 'Elior',
        'Elis' => 'Elis',
        'Elsan' => 'Elsan',
        'Emeis' => 'Emeis',
        'EmploiLR' => 'EmploiLR',
        'Enedis' => 'Enedis',
        'Engie' => 'Engie',
        'EtreEmploi' => 'EtreEmploi',
        'Eurodeal' => 'Eurodeal',
        'Even' => 'Even',
        'Excelliance' => 'Excelliance',
        'ExpertRH' => 'ExpertRH',
        'Expertnet' => 'Expertnet',
        'ExportJobsora' => 'ExportJobsora',
        'ExportJobted' => 'ExportJobted',
        'ExportJooble' => 'ExportJooble',
        'ExportJora' => 'ExportJora',
        'ExportPE' => 'ExportPE',
        'Externatic' => 'Externatic',
        'FHF' => 'FHF',
        'FIDRH' => 'FIDRH',
        'FLEURYMichon' => 'FLEURYMichon',
        'FSEF' => 'FSEF',
        'Fabre' => 'Fabre',
        'Fauche' => 'Fauche',
        'FayatXml' => 'FayatXml',
        'FlorianMantionOld' => 'FlorianMantionOld',
        'FlorianMantione' => 'FlorianMantione',
        'Framatone' => 'Framatone',
        'FranceTravail' => 'FranceTravail',
        'GIF' => 'GIF',
        'GROUPEPRINTEMPS' => 'GROUPEPRINTEMPS',
        'GSF' => 'GSF',
        'Galileo' => 'Galileo',
        'GeneralEmploi' => 'GeneralEmploi',
        'GerardPerrier' => 'GerardPerrier',
        'Ginger' => 'Ginger',
        'Gozoki' => 'Gozoki',
        'GrandVision' => 'GrandVision',
        'GrantThornton' => 'GrantThornton',
        'Groupama' => 'Groupama',
        'GroupeADP' => 'GroupeADP',
        'GroupeActive' => 'GroupeActive',
        'GroupeAtlantic' => 'GroupeAtlantic',
        'GroupeBarriere' => 'GroupeBarriere',
        'GroupeBaudinChateauneuf' => 'GroupeBaudinChateauneuf',
        'GroupeBertrand' => 'GroupeBertrand',
        'GroupeCasino' => 'GroupeCasino',
        'GroupeFB' => 'GroupeFB',
        'GroupeFed' => 'GroupeFed',
        'GroupeGroupama' => 'GroupeGroupama',
        'GroupeINTUIS' => 'GroupeINTUIS',
        'GroupeLeDuff' => 'GroupeLeDuff',
        'GroupeMorgan' => 'GroupeMorgan',
        'Groupehenner' => 'Groupehenner',
        'HERIGE' => 'HERIGE',
        'HLMRecrute' => 'HLMRecrute',
        'HORIZONJOB' => 'HORIZONJOB',
        'HOWDENS' => 'HOWDENS',
        'HarryHope' => 'HarryHope',
        'Hays' => 'Hays',
        'Hess' => 'Hess',
        'HistoireDor' => 'HistoireDor',
        'Ikks' => 'Ikks',
        'Implid' => 'Implid',
        'Indibat' => 'Indibat',
        'Indoors' => 'Indoors',
        'Iscod' => 'Iscod',
        'IteaRH' => 'IteaRH',
        'JeanRouyer' => 'JeanRouyer',
        'Job4You' => 'Job4You',
        'JobAffinity' => 'JobAffinity',
        'JobAndTalent' => 'JobAndTalent',
        'JobAndYou' => 'JobAndYou',
        'JobComputaCenter' => 'JobComputaCenter',
        'Joblift' => 'Joblift',
        'JubilInterim' => 'JubilInterim',
        'Keltis' => 'Keltis',
        'Kiloutou' => 'Kiloutou',
        'KingFisher' => 'KingFisher',
        'Korian' => 'Korian',
        'LCL' => 'LCL',
        'LM5P' => 'LM5P',
        'LSRH' => 'LSRH',
        'LaGrandeRecree' => 'LaGrandeRecree',
        'LaMaisonBleue' => 'LaMaisonBleue',
        'Lacoste' => 'Lacoste',
        'LagarderepoleHachetteLivre' => 'LagarderepoleHachetteLivre',
        'Laposte' => 'Laposte',
        'Lareleve' => 'Lareleve',
        'Leclerc' => 'Leclerc',
        'Liberkeys' => 'Liberkeys',
        'Lidl' => 'Lidl',
        'LynxRH' => 'LynxRH',
        'MACIF' => 'MACIF',
        'MINISO' => 'MINISO',
        'Malakof' => 'Malakof',
        'MarcOrian' => 'MarcOrian',
        'Mencorh' => 'Mencorh',
        'Menway' => 'Menway',
        'MichaelPage' => 'MichaelPage',
        'MichaelPageInterim' => 'MichaelPageInterim',
        'MisterTemp' => 'MisterTemp',
        'Mnt' => 'Mnt',
        'Momenti' => 'Momenti',
        'MonAssistantNumerique' => 'MonAssistantNumerique',
        'Monoprix' => 'Monoprix',
        'MyRecruteur' => 'MyRecruteur',
        'MyTalentPlug' => 'MyTalentPlug',
        'MyTalentPlug100' => 'MyTalentPlug100',
        'NGE' => 'NGE',
        'NantesMetropole' => 'NantesMetropole',
        'Natup' => 'Natup',
        'Neo2' => 'Neo2',
        'Neovity' => 'Neovity',
        'NextepHR' => 'NextepHR',
        'NextgenRH' => 'NextgenRH',
        'ONERA' => 'ONERA',
        'OffDuMomentsSites' => 'OffDuMomentsSites',
        'Optic2000' => 'Optic2000',
        'Orange' => 'Orange',
        'Orano' => 'Orano',
        'Orientaction' => 'Orientaction',
        'Oxance' => 'Oxance',
        'PROMOTRANS' => 'PROMOTRANS',
        'PagePersonnel' => 'PagePersonnel',
        'PagePersonnelNew' => 'PagePersonnelNew',
        'ParcAsterix' => 'ParcAsterix',
        'Petitforestier' => 'Petitforestier',
        'PhiSante' => 'PhiSante',
        'PlaneteInterim' => 'PlaneteInterim',
        'Plasticomnium' => 'Plasticomnium',
        'PolygoneRH' => 'PolygoneRH',
        'Printemps' => 'Printemps',
        'ProInterim' => 'ProInterim',
        'ProactiveRH' => 'ProactiveRH',
        'Proservia' => 'Proservia',
        'Pvcpfr' => 'Pvcpfr',
        'REVELYO' => 'REVELYO',
        'RSRH' => 'RSRH',
        'RecrutOr' => 'RecrutOr',
        'Recrutimmo' => 'Recrutimmo',
        'Renault' => 'Renault',
        'Resideetudes' => 'Resideetudes',
        'Rexel' => 'Rexel',
        'Rgis' => 'Rgis',
        'SAINTMACLOU' => 'SAINTMACLOU',
        'SANTESTAFFING' => 'SANTESTAFFING',
        'SIPCA' => 'SIPCA',
        'SOSInterim' => 'SOSInterim',
        'Sabena' => 'Sabena',
        'SaintGobain' => 'SaintGobain',
        'Samsic' => 'Samsic',
        'SamsicEmploi' => 'SamsicEmploi',
        'Santech' => 'Santech',
        'SatisSolution' => 'SatisSolution',
        'SeaTPI' => 'SeaTPI',
        'Securitas' => 'Securitas',
        'Seris' => 'Seris',
        'Servagroupe' => 'Servagroupe',
        'ServirRH' => 'ServirRH',
        'SimEmploi' => 'SimEmploi',
        'Sncf' => 'Sncf',
        'Sodexo' => 'Sodexo',
        'Sofitex' => 'Sofitex',
        'Solinki' => 'Solinki',
        'Solution30' => 'Solution30',
        'Spie' => 'Spie',
        'StartPeople' => 'StartPeople',
        'Steva' => 'Steva',
        'Studi' => 'Studi',
        'SuperU' => 'SuperU',
        'TIEUP' => 'TIEUP',
        'TalentDetection' => 'TalentDetection',
        'TalentNature' => 'TalentNature',
        'TalentPeople' => 'TalentPeople',
        'TalentsHandicap' => 'TalentsHandicap',
        'TarnoukRecrute' => 'TarnoukRecrute',
        'Temporis' => 'Temporis',
        'Terresdefrance' => 'Terresdefrance',
        'Tessi' => 'Tessi',
        'TradeSolutions' => 'TradeSolutions',
        'TransDev' => 'TransDev',
        'TransparenceRH' => 'TransparenceRH',
        'Tresor' => 'Tresor',
        'Viaposte' => 'Viaposte',
        'VichyInterim' => 'VichyInterim',
        'VitalisMedical' => 'VitalisMedical',
        'Viveris' => 'Viveris',
        'Vousfinancer' => 'Vousfinancer',
        'VoyageADAPTE' => 'VoyageADAPTE',
        'WhatsJob' => 'WhatsJob',
        'Winsearch' => 'Winsearch',
        'WolfInterim' => 'WolfInterim',
        'Wonderbox' => 'Wonderbox',
        'WorkandYou' => 'WorkandYou',
        'Wurth' => 'Wurth',
        'Yvelines' => 'Yvelines',
        'about' => 'about',
        'completion' => 'completion',
        'cvParseCandSave' => 'cvParseCandSave',
        'cvParseTemp' => 'cvParseTemp',
        'cvToSend' => 'cvToSend',
        'groupeA2micile' => 'groupeA2micile',
        'help' => 'help',
        'list' => 'list',
        'testAddMetier' => 'testAddMetier',
        'testCount' => 'testCount',
        'testCvParse' => 'testCvParse',
        'welljob' => 'welljob',
        'xFab' => 'xFab',
    ];
    private Security $security;
    private $logs;

    public function __construct(
        Security $security,
        AdminLogsController $logs,
        EntityManagerInterface $entityManager,
    )
    {
        $this->security = $security;
        $this->logs = $logs;
        $this->entityManager = $entityManager;


    }

    #[Route('/index', name: '_index')]
    public function index(  
        ManagerRegistry $doctrine,
        UserInterface $user = null,
        SessionInterface $session,
        OffOffreRepository $offOffreRepository,
        UserUserRepository $userUserRepository,
        CvtequeProfilsRepository $cvtequeProfilsRepository,
        ComSouscriptionCommercialeRepository $comSouscriptionCommercialeRepository,
        Request $request): Response
    {
        $roles = $user->getRoles();
        if (!(in_array("ROLE_ADMIN", $roles))) {
            return $this->redirectToRoute('directemploi');
        }

        $totalOffers = $offOffreRepository->getNbOffres();
        $stageOffers = $offOffreRepository->getNbOffresStage();
        $alternanceOffers = $offOffreRepository->getNbOffresAlternance();
        $repMailOffers = $offOffreRepository->getNbOffresRepMail();
        $repUrlOffers = $offOffreRepository->getNbOffresRepUrl();
        $totalCandidates = $userUserRepository->getNbCandidates();
        $profilCandidates = $cvtequeProfilsRepository->getProfilesAll();
        $profilRecruteurs = $userUserRepository->getNbRecruiters();
        $souscriptions = $comSouscriptionCommercialeRepository->getNbSouscriptions();


        return $this->render('admin_espace/index.html.twig', [
            'totalOffers' => $totalOffers,
            'stageOffers' => $stageOffers,
            'alternanceOffers' => $alternanceOffers,
            'repMailOffers' => $repMailOffers,
            'repUrlOffers' => $repUrlOffers,
            'totalCandidates' => $totalCandidates,
            'profilCandidates' => $profilCandidates,
            'profilRecruteurs' => $profilRecruteurs,
            'souscriptions' => $souscriptions,
            // 'controller_name' => 'AdminEspaceController',
            // 'offres' => $recentOffers,
            // 'articlesID' => $adminArticlesID,
            // 'articleInfo' => $articleInfo,
            // 'countArticles' => $count,
            // 'updateForm' => $updateForm->createView(),
            // 'searchForm' => $searchForm->createView(),
            // "selectedIndex" => $optionSelect ? $optionSelect : 0,
        ]);
    }

    #[Route('/gestion_carrousel', name: '_gestion_carrousel')]
    public function gestion_carrousel(Connection $connection, Request $request): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
        if ($request->isMethod('POST')) {
            $action = $request->request->get('action');
            $siteCode = $request->request->get('site_code');

            if ($action === 'add') {
                // Récupérer les entreprises sélectionnées (tableau)
                $idEntreprises = $request->request->all('id_entreprises'); // Utiliser all() ici
                foreach ($idEntreprises as $idEntreprise) {
                    $currentSites = $connection->fetchOne(
                        'SELECT site FROM carrousselForAllSiteDirectEmploi WHERE id_carroussel = ?',
                        [$idEntreprise]
                    );

                    $currentSitesArray = $currentSites ? json_decode($currentSites, true) : [];
                    if (!in_array($siteCode, $currentSitesArray)) {
                        $currentSitesArray[] = $siteCode;
                    }

                    $connection->update(
                        'carrousselForAllSiteDirectEmploi',
                        ['site' => json_encode($currentSitesArray)],
                        ['id_carroussel' => $idEntreprise]
                    );
                }
            } elseif ($action === 'remove') {
                // Supprimer une entreprise du site
                $idEntreprise = $request->request->get('id_entreprise');
                $currentSites = $connection->fetchOne(
                    'SELECT site FROM carrousselForAllSiteDirectEmploi WHERE id_carroussel = ?',
                    [$idEntreprise]
                );

                $currentSitesArray = $currentSites ? json_decode($currentSites, true) : [];
                $updatedSites = array_diff($currentSitesArray, [$siteCode]);

                $connection->update(
                    'carrousselForAllSiteDirectEmploi',
                    ['site' => json_encode(array_values($updatedSites))],
                    ['id_carroussel' => $idEntreprise]
                );
            }
        }

        $carrouselItems = $connection->fetchAllAssociative('SELECT * FROM carrousselForAllSiteDirectEmploi');

        foreach ($carrouselItems as &$item) {
            $item['site'] = $item['site'] ? json_decode($item['site'], true) : [];
        }

        return $this->render('admin_espace/gestion_carousel.html.twig', [
            'boites' => $carrouselItems,
        ]);
    }
    /**
     * @Route("/delete-from-caroussel", name="_delete_entr_from_caroussel", methods={"POST"})
     */
    public function deleteEntrFromCaroussel(Request $request): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
        // Get parameters from the request
        $idEntreprise = $request->request->get('id_entreprise');
        $siteCode = $request->request->get('site_code');

        // Fetch the current row from the database
        $row = $this->entityManager->getConnection()->fetchAssociative(
            'SELECT * FROM OffreDuMomentsForAllSiteDirectEmploi WHERE id_entreprise = ?',
            [$idEntreprise]
        );

        if (!$row) {
            return new Response('Entreprise not found', Response::HTTP_NOT_FOUND);
        }

        // Decode the JSON site field
        $sites = json_decode($row['site'], true);

        // Remove the site code from the sites array
        $sites = array_filter($sites, function ($site) use ($siteCode) {
            return $site !== $siteCode;
        });

        // Encode the updated sites array back to JSON
        $updatedSites = json_encode(array_values($sites));

        // Update the row in the database
        $this->entityManager->getConnection()->executeStatement(
            'UPDATE OffreDuMomentsForAllSiteDirectEmploi SET site = ? WHERE id_entreprise = ?',
            [$updatedSites, $idEntreprise]
        );

        return new Response('Site removed successfully', Response::HTTP_OK);
    }

    #[Route('/add-to-caroussel', name: '_add_entr_to_caroussel', methods: ['POST'])]
    public function addEntrToCaroussel(Request $request): JsonResponse
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
        // Get all form data
        $formData = $request->request->all();

        // Log the incoming request data for debugging
        error_log(print_r($formData, true));

        // Extract id_entreprises and site_code from the form data
        $idEntreprises = $formData['id_entreprises'] ?? []; // Use empty array as default
        $siteCode = $formData['site_code'] ?? null;

        // Validate the input
        if (empty($idEntreprises)) {
            return new JsonResponse(['success' => false, 'message' => 'Aucune entreprise sélectionnée'], Response::HTTP_BAD_REQUEST);
        }
        if (empty($siteCode)) {
            return new JsonResponse(['success' => false, 'message' => 'Code site manquant'], Response::HTTP_BAD_REQUEST);
        }

        // Loop through each selected entreprise
        foreach ($idEntreprises as $idEntreprise) {
            // Fetch the current row from the database
            $row = $this->entityManager->getConnection()->fetchAssociative(
                'SELECT * FROM OffreDuMomentsForAllSiteDirectEmploi WHERE id_carroussel = ?',
                [$idEntreprise]
            );

            if (!$row) {
                continue; // Skip if the entreprise is not found
            }

            // Decode the JSON site field
            $sites = json_decode($row['site'], true);

            // Add the site code to the sites array if it doesn't already exist
            if (!in_array($siteCode, $sites)) {
                $sites[] = $siteCode;
            }

            // Encode the updated sites array back to JSON
            $updatedSites = json_encode(array_values($sites));

            // Update the row in the database
            $this->entityManager->getConnection()->executeStatement(
                'UPDATE OffreDuMomentsForAllSiteDirectEmploi SET site = ? WHERE id_carroussel = ?',
                [$updatedSites, $idEntreprise]
            );
            // After updating the database, call the OffDuMomentsSites command
            // You can use exec or shell_exec to execute a command
            $command = 'php /var/www/directemploi/www/direct_emploi_s6/bin/console OffDuMomentsSites';
            exec($command, $output, $status);

            // Optionally log or inspect the output of the command
            error_log("Command output: " . implode("\n", $output));
            error_log("Command status: " . $status);
        }

        return new JsonResponse(['success' => true, 'message' => 'Sites ajoutés avec succès'], Response::HTTP_OK);
    }
    #[Route('/gestionOffreDuMoments', name: '_gestion_Offre_Du_Moments')]
    public function GestionOffreDuMoments(Connection $connection, Request $request, ManagerRegistry $doctrine,EntrEntrepriseRepository $entrEntrepriseRepository): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
        // Fetch all carrousel items from the database
        $carrouselItems = $connection->fetchAllAssociative('SELECT * FROM OffreDuMomentsForAllSiteDirectEmploi');
        $entrepriserepo = $doctrine->getRepository(EntrEntreprise::class);
        // Loop through each item in the carrouselItems array
        for ($i = 0; $i < count($carrouselItems); $i++) {
            $idEntreprise = $carrouselItems[$i]['id_entreprise']; // Get the id_entreprise from the current item

            // Fetch the corresponding EntrEntreprise entity
            $entreprise = $entrepriserepo->findOneBy(['id_entreprise' => $idEntreprise]);

            // If the entreprise is found, add the raison_sociale to the carrouselItems array
            if ($entreprise) {
                $carrouselItems[$i]['raison_sociale'] = $entreprise->getRaisonSociale();
            } else {
                // If the entreprise is not found, set raison_sociale to null or a default value
                $carrouselItems[$i]['raison_sociale'] = null;
            }
        }
        $carrouselItemsfordiplay=$carrouselItems;

        // Now $carrouselItems will have the "raison_sociale" field added to each item
        // Group carrousel items by site code
        $groupedCarrouselItems = [];
        foreach ($carrouselItems as $item) {
            $siteCodes = json_decode($item['site'], true) ?? []; // Decode the JSON site field
            foreach ($siteCodes as $code) {
                if (!isset($groupedCarrouselItems[$code])) {
                    $groupedCarrouselItems[$code] = [];
                }
                $groupedCarrouselItems[$code][] = $item;
            }
        }
        // Handle form submission
        if ($request->isMethod('POST')) {
            $idEntreprise = $request->request->get('id_entreprise');
            $action = $request->request->get('action');

            if ($action === 'remove' && $idEntreprise) {
                // Perform the update or delete operation on the database
                $connection->executeStatement(
                    'DELETE FROM carrousselForAllSiteDirectEmploi WHERE id_entreprise = :id_entreprise',
                    ['id_entreprise' => $idEntreprise]
                );

                // Add a flash message to confirm the deletion
                $this->addFlash('success', 'L\'entreprise a été supprimée avec succès.');

                // Redirect to the same page to refresh the list
                return $this->redirectToRoute('_gestion_Offre_Du_Moments');
            }
        }

        // Render the Twig template with the grouped carrousel items
        return $this->render('admin_espace/GestionOffreDuMoments.html.twig', [
            'groupedCarrouselItems' => $groupedCarrouselItems,
            'carrouselItemsfordiplay' => $carrouselItemsfordiplay,
        ]);
    }

    #[Route('/gestionDesArticles', name: '_gestion_article')]
    public function gestionDesArticles(
        Request $request,
        EditArticleRepository $articleRepository,
        PaginatorInterface $paginator,
        ManagerRegistry $doctrine
    ): Response {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
        // Définition des sites avec leurs codes, noms et icônes
        $sites = [
            1 => ['name' => 'Direct Emploi', 'icon' => 'https://www.directemploi.com/images/direct-emploi/new_logo.png'],
            2 => ['name' => 'Direct Etudiant', 'icon' => 'https://www.directetudiant.com/DirectEtudiant/Front/images/logo-black.png'],
            3 => ['name' => 'Direct Alternance', 'icon' => 'https://www.directalternance.com/DirectAlternance/Front/images/logo-white.png'],
            4 => ['name' => 'Carriere BTP', 'icon' => 'https://www.carriere-btp.com/carriereBTP/Front/images/logo%20noir%20avec%20text.png'],
            5 => ['name' => 'Carriere Distribution', 'icon' => 'https://www.carriere-distribution.com/carriereDistribution/Front/images/logo-black.png'],
            6 => ['name' => 'Carriere industrie', 'icon' => 'https://www.carriere-industrie.com/carriereIndustrie/Front/images/logoindustrie.png'],
            9 => ['name' => 'Carriere Restauration', 'icon' => 'https://www.carriere-restauration.com/carriereRestauration/Front/images/logo-black.png'],
            7 => ['name' => 'Carriere Informatique', 'icon' => 'https://www.carriere-informatique.com/carriereInformatique/Front/images/logo%20noir%20avec%20text.png'],
            8 => ['name' => 'Carriere medicale', 'icon' => 'https://www.carriere-medicale.com/carriereMedicale/Front/images/logo-black.png'],
        ];

        // Gestion de la soumission du formulaire pour la mise à jour des sites
        if ($request->isMethod('POST')) {
            $submittedSites = $request->request->all()['site'] ?? [];
            $entityManager = $doctrine->getManager();
            foreach ($submittedSites as $articleId => $siteCode) {
                $article = $articleRepository->find($articleId);
                if ($article && isset($sites[$siteCode])) {
                    $article->setSite((int)$siteCode);
                    $entityManager->persist($article);
                }
            }
            $entityManager->flush();

            $this->addFlash('success', 'Sites mis à jour avec succès.');
            return $this->redirectToRoute('app_admin_gestion_article');
        }

        // Récupérer les filtres depuis la requête (méthode GET)
        $filterTitle = $request->query->get('filter_title');

        // Construction du QueryBuilder avec filtrage
        $qb = $articleRepository->createQueryBuilder('a')
            ->where('a.is_hidden = 0')
            ->orderBy('a.date_creation', 'DESC');

        if ($filterTitle) {
            $qb->andWhere('a.titre LIKE :titre')
                ->setParameter('titre', '%' . $filterTitle . '%');
        }



        $query = $qb->getQuery();

        // Pagination
        $pagination = $paginator->paginate(
            $query,
            $request->query->getInt('page', 1),
            50
        );

        // Ajouter l'URL pour chaque article dans la pagination
        foreach ($pagination as $article) {
            $article->url_article = sprintf(
                'https://www.directemploi.com/article/%d/%s',
                $article->getIdArticle(),
                $this->slugify($article->getTitre())
            );
        }

        return $this->render('admin_espace/gestionDesArticles.html.twig', [
            'pagination' => $pagination,
            'sites' => $sites,
            // Nous passons également les filtres pour pouvoir les réafficher dans le formulaire
            'filter_title' => $filterTitle,
        ]);
    }
    #[Route('/gestionDesArticlesPageAccueil', name: '_gestion_article_PageAccueil')]
    public function gestionDesArticlesPageAccueil(
        ArticleForAllSiteDirectEmploiRepository $allSiteDirectEmploiRepository,
        EditArticleRepository $editArticleRepository
    ): Response {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
        // Récupérer tous les articles
        $allSiteDirectEmploi = $allSiteDirectEmploiRepository->findAll();

        // Définition des sites
        $sites = [
            1 => 'Direct Emploi',
            2 => 'Direct Etudiant',
            3 => 'Direct Alternance',
            4 => 'Carriere BTP',
            5 => 'Carriere Distribution',
            6 => 'Carriere Industrie',
            7 => 'Carriere Informatique',
            8 => 'Carriere Medical',
            9 => 'Carriere Restauration',
        ];

        // Créer un tableau pour stocker les articles associés à chaque site
        $siteArticles = [];

        foreach ($sites as $siteId => $siteName) {
            $articlesForSite = [];
            foreach ($allSiteDirectEmploi as $siteDirectEmploi) {
                if ($siteDirectEmploi->getSite() === $siteId) {
                    // Récupérer l'ID de l'article
                    $id_article = $siteDirectEmploi->getIdArticle();

                    // Rechercher l'article complet dans le repository EditArticle
                    $editArticle = $editArticleRepository->findOneBy(['id_article' => $id_article]);

                    // Si l'article est trouvé, ajouter ses informations
                    if ($editArticle) {
                        $articlesForSite[] = [
                            'id_article' => $siteDirectEmploi->getIdArticle(),
                            'titre' => $editArticle->getTitre()
                        ];
                    }
                }
            }

            // On limite à 3 articles sauf pour Direct Alternance (ID = 3)
            if ($siteId === 3) {
                $siteArticles[$siteName] = $articlesForSite;
            } else {
                $siteArticles[$siteName] = array_slice($articlesForSite, 0, 3);
            }
        }

        // Récupérer tous les articles pour les afficher dans la liste déroulante
        $allArticles = $editArticleRepository->findAll();
        $allArticleTitles = array_map(function($article) {
            return $article->getTitre();
        }, $allArticles);

        return $this->render('admin_espace/gestionDesArticlesPageAccueil.html.twig', [
            'siteArticles' => $siteArticles, // Passer les articles par site au template
            'sites' => $sites,
            'allArticleTitles' => $allArticleTitles // Passer tous les titres d'articles au template
        ]);
    }
    #[Route('/update-article-site', name: '_update_article_site', methods: ['POST'])]
    public function updateArticleSite(
        Request $request,
        ArticleForAllSiteDirectEmploiRepository $allSiteRepo,
        EditArticleRepository $editArticleRepo,
        EntityManagerInterface $em
    ): Response {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
        $siteId = $request->request->get('site_id');
        $oldArticleId = $request->request->get('old_article_id');
        $newArticleTitle = $request->request->get('new_article_title');

        // Trouver le nouvel article
        $newArticle = $editArticleRepo->findOneBy(['titre' => $newArticleTitle]);

        if (!$newArticle) {
            $this->addFlash('error', 'Article introuvable');
            return $this->redirectToRoute('app_admin_gestion_article_PageAccueil');
        }

        // Désactiver l'ancienne association
        $oldAssociation = $allSiteRepo->findOneBy([
            'id_article' => $oldArticleId,
            'site' => $siteId
        ]);

        if ($oldAssociation) {
            $oldAssociation->setSite(0);
            $em->persist($oldAssociation);
        }

        // Créer la nouvelle association
        $newAssociation = new ArticleForAllSiteDirectEmploi();
        $newAssociation
            ->setIdArticle($newArticle->getIdArticle())
            ->setSite($siteId);

        $em->persist($newAssociation);
        $em->flush();

        $this->addFlash('success', 'Mise à jour effectuée avec succès');
        return $this->redirectToRoute('app_admin_gestion_article_PageAccueil');
    }



// Helper function to slugify the title
    private function slugify(string $text): string
    {
        // Replace non-letter or non-digits by -
        $text = preg_replace('~[^\pL\d]+~u', '-', $text);

        // Transliterate
        $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);

        // Remove unwanted characters
        $text = preg_replace('~[^-\w]+~', '', $text);

        // Trim
        $text = trim($text, '-');

        // Remove duplicate -
        $text = preg_replace('~-+~', '-', $text);

        // Lowercase
        $text = strtolower($text);

        if (empty($text)) {
            return 'n-a';
        }

        return $text;
    }

    #[Route('/BoiteOutilsArticles', name: '_boite_outils_articles')]
    public function BoiteOutilsArticles(
        ArticlesRouteRepository $articlesRouteRepository,
        EditArticleRepository $editArticleRepository
    ): Response {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        // Récupérer tous les articles
        $allSiteDirectEmploi = $articlesRouteRepository->findAll();
        $allArticle = $editArticleRepository->findBy(['is_hidden' => 0]);

        // Prepare the data for the Twig template
        $articlesData = [];
        foreach ($allSiteDirectEmploi as $articleRoute) {
            // Fetch article objects based on idArticle
            $articleObjects = [];
            foreach ($articleRoute->getIdArticle() as $articleId) {
                $article = $editArticleRepository->find($articleId);
                if ($article) {
                    $articleObjects[] = $article;
                }
            }

            $articlesData[] = [
                'site' => $articleRoute->getSite(),
                'route' => $articleRoute->getRoute(),
                'articles' => $articleObjects, // Pass the article objects instead of IDs
            ];
        }
        return $this->render('admin_espace/BoiteOutilsArticles.html.twig', [
            'articlesData' => $articlesData,
        ]);
    }

    #[Route('/conseils_list', name: 'admin_conseil_list')]
    public function listeConseils(ConseilRepository $conseilRepository): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $conseils = $conseilRepository->findAll();

        return $this->render('admin_espace/conseils_liste.html.twig', [
            'conseils' => $conseils,
        ]);
    }

    #[Route('/get-available-articles', name: '_get_available_articles', methods: ['GET'])]
    public function getAvailableArticles(EditArticleRepository $editArticleRepository): JsonResponse
    {
        $articles = $editArticleRepository->findBy(['is_hidden' => 0]);
        $data = [];
        foreach ($articles as $article) {
            $data[] = [
                'id' => $article->getIdArticle(),
                'titre' => $article->getTitre(),
            ];
        }
        return $this->json($data);
    }
    #[Route('/update-article', name: '_update_article', methods: ['POST'])]
    public function updateArticle(
        Request $request,
        ArticlesRouteRepository $articlesRouteRepository,
        EditArticleRepository $editArticleRepository
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);

        // Debugging: Log the incoming data
        error_log('Incoming data: ' . print_r($data, true));

        // Find the row to update
        $articleRoute = $articlesRouteRepository->findOneBy([
            'site' => $data['site'],
            'route' => $data['route'],
        ]);

        if (!$articleRoute) {
            error_log('Article route not found.');
            return $this->json(['success' => false, 'message' => 'Article route not found.']);
        }

        // Debugging: Log the current state of the article route
        $idArticles = $articleRoute->getIdArticle();
        error_log('Current idArticles: ' . print_r($idArticles, true));
        error_log('Old article ID to find: ' . $data['oldArticleId']);

        // Convert oldArticleId to integer for strict comparison
        $oldArticleId = (int)$data['oldArticleId'];

        // Update the article ID
        $key = array_search($oldArticleId, $idArticles);
        if ($key !== false) {
            $idArticles[$key] = (int)$data['newArticleId']; // Ensure newArticleId is also an integer
            $articleRoute->setIdArticle($idArticles);

            // Debugging: Log the updated state of the article route
            error_log('After update: ' . print_r($articleRoute->getIdArticle(), true));

            // Save the changes
            $articlesRouteRepository->save($articleRoute, true);

            // Debugging: Verify the changes were saved
            $updatedArticleRoute = $articlesRouteRepository->find($articleRoute->getId());
            error_log('After save: ' . print_r($updatedArticleRoute->getIdArticle(), true));
        } else {
            error_log('Old article ID not found in the article route.');
            return $this->json(['success' => false, 'message' => 'Old article ID not found.']);
        }

        return $this->json(['success' => true]);
    }
    #[Route('/espace', name: '_espace')]
    public function espace(  ManagerRegistry $doctrine,
                            UserInterface $user = null,
                            SessionInterface $session,
                            Request $request): Response
    {
        $roles = $user->getRoles();
        if (!(in_array("ROLE_ADMIN", $roles)))
            return $this->redirectToRoute('directemploi');
        
        $optionSelect = $session->get("optionSelect");
        $searchForm = $this->createForm(AdminOffreFinder::class);
        $searchForm->handleRequest($request);
        
        $updateForm = $this->createForm(AdminActuUpdate::class);
        $updateForm->handleRequest($request);
        
        $offers = $doctrine->getRepository(EditArticle::class);
        $adminArticles = $doctrine->getRepository(AdminEditArticle::class);

        $optionSelect = $session->get("optionSelect") ? $session->get("optionSelect") : 0;
        $defaultDisplay = 1;

        $adminArticlesID = $adminArticles->getAdminArticles($optionSelect >= 0 ? $optionSelect : $defaultDisplay);
        $articleInfo = [];
        $count = 0;
        $msg[0] = null;
        
        //$adminArticlesID = array(3094, 3095, 3096); //    tests en local

        foreach ($adminArticlesID as $articleID) {
            $currentOffer = $offers->findOneBy(["id_article" => $articleID]);
            if ($articleID == 0 || $currentOffer == null) {
                $articleID = 0;
                $articleInfo[$articleID] = null;
                continue;
            }

            $pathImages = "/uploads/article/";
            $articleInfo[$articleID] = [
                'titre' => $currentOffer->getTitre(),
                'chapo' => $currentOffer->getChapo(),
                'mark' => '/uploads/admin/espace/blue_fav.png',
                'image' => $pathImages . $currentOffer->getImagePetite(),
                //'image' => "/uploads/admin/espace/".'dsdsd.jpg', //tests locals
                'id' => $currentOffer->getIdArticle(),
            ];
            $count++;
        }

        if ($request->isXmlHttpRequest()) {
            $count  = 0;
            if ($session->get("optionSelect"))
                $session->remove("optionSelect");
            $session->set("optionSelect", (int) $request->request->get('selectedValue'));

            $optionSelect = $session->get("optionSelect");
            $errorDisplay = 1000;
            $adminArticlesID = $adminArticles->getAdminArticles($optionSelect >= 0 ? $optionSelect : $errorDisplay);
            foreach ($adminArticlesID as $articleID) {
                $currentOffer = $offers->findOneBy(["id_article" => $articleID]);
                if ($articleID == 0 || $currentOffer == null) {
                    $articleID = 0;
                    $articleInfo[$articleID] = null;
                    continue;
                }
    
                $pathImages = "/uploads/article/";
                $articleInfo[$articleID] = [
                    'titre' => $currentOffer->getTitre(),
                    'chapo' => $currentOffer->getChapo(),
                    'mark' => '/uploads/admin/espace/blue_fav.png',
                    'image' => $pathImages . $currentOffer->getImagePetite(),
                    //'image' => "/uploads/admin/espace/".'dsdsd.jpg', //tests locals
                    'id' => $currentOffer->getIdArticle(),
                ];
                $count++;
            }

            $result = [
                "count" => $count,
                "articles" => $articleInfo,
                "articlesID" => $adminArticlesID,
                "selectedIndex" => (int) $optionSelect ? $optionSelect : (int) "0",
            ];
            return new JsonResponse($result);
        }

        
        if ($searchForm->isSubmitted() && $searchForm->isValid()) {
            if ($searchForm->has('annonceId') && null !== $searchForm->get('annonceId')->getData())
                $annonceID = $searchForm->get('annonceId')->getData();
            if ($annonceID > 0) {
                $myOffer = $offers->findOneBy(["id_article" => (int) $annonceID]);
                if ($myOffer != null) {
                    $this->addFlash('success', 'Offre trouvée');
                    return $this->render('admin_espace/index.html.twig', [
                        'controller_name' => 'AdminEspaceController',
                        'myOffer' => $myOffer,
                        'articlesID' => $adminArticlesID,
                        'articleInfo' => $articleInfo,
                        'countArticles' => $count,
                        'updateForm' => $updateForm->createView(),
                        'searchForm' => $searchForm->createView(),
                        "selectedIndex" => $optionSelect ? $optionSelect : 0,
                    ]);
                } else {
                    $msg[0] = "danger";
                    $msg[1] = "Offre inexistante";
                }
            }
        }

        $max = 3;
        $recentOffers = $offers->findRecentOffers($max);
        if ($msg[0] != null)
            $this->addFlash($msg[0], $msg[1]);
        return $this->render('admin_espace/index.html.twig', [
            'controller_name' => 'AdminEspaceController',
            'offres' => $recentOffers,
            'articlesID' => $adminArticlesID,
            'articleInfo' => $articleInfo,
            'countArticles' => $count,
            'updateForm' => $updateForm->createView(),
            'searchForm' => $searchForm->createView(),
            "selectedIndex" => $optionSelect ? $optionSelect : 0,
        ]);
    }

    #[Route('/update', name: '_update')]
    public function updateOffers(   ManagerRegistry $doctrine,
                                    EntityManagerInterface $entityManager,
                                    UserInterface $user = null,
                                    SessionInterface $session
                                ): Response
    {
        $roles = $user->getRoles();
        if (!(in_array("ROLE_ADMIN", $roles)))
            return $this->redirectToRoute('directemploi');
        
        $optionSelect = $session->get("optionSelect");
        $adminArticles = $doctrine->getRepository(AdminEditArticle::class);

        $colNames = ["id_article_first", "id_article_second", "id_article_third"];
        $btnName = ["articleIdHolderOne", "articleIdHolderTwo", "articleIdHolderThree"];
        
        if (!isset($_POST["admin_actu_update"]))
            return $this->redirectToRoute('app_admin_espace');
        
        $formValues = $_POST["admin_actu_update"];
        $newValues = [];
        $pos = 0;
        
        foreach ($btnName as $btn)
            if (isset($formValues[$btn]))
                $newValues[$colNames[$pos++]] = $formValues[$btn];

        $choix = [
            0 => 'Page Accueil - Actualité', 1 => 'Stage', 2 => 'Interim', 
            3 => 'Alternance', 4 => 'Recrutement', 5 => 'Handicap', 
            6 => 'Jeune Diplomé', 7 => 'Entreprise', 8 => 'Page Saisonnier',
            9 => 'Page Cadre'];

        $optionSelect = (int) $session->get("optionSelect") ? $session->get("optionSelect") : 0;
        $newArticles = $adminArticles->findOneBy(['edit_article_id_article' => $optionSelect]);
        $newArticles->setIdArticleFirst((int) $newValues["id_article_first"]);
        $newArticles->setIdArticleSecond((int) $newValues["id_article_second"]);
        $newArticles->setIdArticleThird((int) $newValues["id_article_third"]);
        $entityManager->persist($newArticles);
        $entityManager->flush();
        $this->logs->writeLogs(
            "logs", $this->logs->logsDataFormatter(
                $this->getUser()->getIdUser(),
                "edit_online",
                "articles", 
                implode(" | ", $newValues),
                $choix[$optionSelect]
        ));
        //$session->remove("optionSelect");

        return $this->redirectToRoute('app_admin_espace');
    }

    #[Route('/edit/{id}', name: '_edit_article')]
    public function editArticle(   ManagerRegistry $doctrine,
                                    EntityManagerInterface $entityManager,
                                    UserInterface $user = null,
                                    SessionInterface $session,
                                    Request $request,
                                    int $id=null
                                ): Response
    {
        $roles = $user->getRoles();
        if (!(in_array("ROLE_ADMIN", $roles)))
            return $this->redirectToRoute('directemploi');
        
        if ($id == null || $id == 0) {
            return $this->redirectToRoute('app_admin_espace');
        }

        $articles = $doctrine->getRepository(EditArticle::class);
        $articleEdit = $articles->findOneBy(['id_article' => $id]);

        if ($articleEdit == null) {
            $this->addFlash('danger', "Votre article est introuvable");
            return $this->redirectToRoute('app_admin_espace');
        }

        $form = $this->createForm(AdmEditArticle::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $formValues = $_POST["admin_edit_article"];
            $articleEdit->setDateModification(new DateTime());
            $articleEdit->setTitre($formValues["titre"] ? $formValues["titre"] : $articleEdit->getTitre());
            $articleEdit->setChapo($formValues["chapo"] ? $formValues["chapo"] : $articleEdit->getChapo());
            $articleEdit->setContenu($formValues["contenu"] ? $formValues["contenu"] : $articleEdit->getContenu());
            //$articleEdit->setImagePetite($formValues["image_data"] ? $formValues["image_data"] : $articleEdit->getImagePetite());
            $entityManager->flush();
            $this->logs->writeLogs(
                "logs", $this->logs->logsDataFormatter(
                    $this->getUser()->getIdUser(),
                    "edit_contenu", 
                    "articles", 
                    (string) $id,
                    "Admin | Actualité"
            ));
            return $this->redirectToRoute('app_admin_espace');
        }

        return $this->render('admin_espace/editArticle.html.twig', [
            'idArticle' => $id,
            'titre' => $articleEdit->getTitre(),
            'chapo' => $articleEdit->getChapo(),
            'contenu' => $articleEdit->getContenu(),
            'image' => $articleEdit->getImagePetite(),
            'date_creation' => $articleEdit->getDateCreation(),
            'date_modification' => $articleEdit->getDateModification(),
            'form' => $form->createView(),
        ]);
    }

    #[Route('/stats_candidatures', name: '_stats_candidatures')]
    public function test(UserInterface $user = null, Request $request, OffCandidatureRepository $offCandidatureRepository): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $form = $this->createForm(AdminDateRangeCandidateType::class);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $startDate = $data['startDate'];
            $endDate = $data['endDate'];

            $candidatures = $offCandidatureRepository->getCandidaturesByDateRange($startDate, $endDate);


            $totalRepMail = 0;
            $totalRepUrl = 0;

            foreach ($candidatures as $candidature) {
                if ($candidature['rep_mail'] != null) {
                    $totalRepMail++;
                }
                if ($candidature['rep_url'] != null) {
                    $totalRepUrl++;
                }
            }

            $totalCandidatures = count($candidatures);

            return $this->render('admin_espace/candidatures_par_date.html.twig', [
                'form' => $form->createView(),
                'totalCandidatures' => $totalCandidatures,
                'startDate' => $startDate,
                'endDate' => $endDate,
                'totalRepMail' => $totalRepMail,
                'totalRepUrl' => $totalRepUrl,
            ]);
        }

        return $this->render('admin_espace/candidatures_par_date.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/view-scripts', name: '_view_scripts')]
    public function viewScripts(Connection $connection): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        // Exécution de la requête SQL pour récupérer les logs, triés par nom de script (ordre alphabétique)
        $query = "SELECT * FROM script_logs ORDER BY script_name ASC, start_time DESC";
        $scripts = $connection->fetchAllAssociative($query);

        // Calculs
        $totalScripts = count($scripts);
        $totalSuccess = count(array_filter($scripts, fn($script) => $script['status'] == 1));
        $totalFailure = $totalScripts - $totalSuccess;
        $totalOffers = array_sum(array_column($scripts, 'offers_processed'));

        return $this->render('admin_espace/view_scripts_new.html.twig', [
            'scripts' => $scripts,
            'totalScripts' => $totalScripts,
            'totalSuccess' => $totalSuccess,
            'totalFailure' => $totalFailure,
            'totalOffers' => $totalOffers,
        ]);
    }

    #[Route('/launch-script', name: '_launch_script', methods: ['POST'])]
    public function launchScript(Request $request, LoggerInterface $logger): JsonResponse
    {
        $logger->info('LaunchScript method called.');

        // Check if the user has the ROLE_ADMIN role
        if (!$this->isGranted('ROLE_ADMIN')) {
            $logger->warning('Access denied: User does not have ROLE_ADMIN.');
            return new JsonResponse(['success' => false, 'message' => 'Access denied.'], 403);
        }

        // Decode the JSON payload
        $data = json_decode($request->getContent(), true);
        $logger->info('Request payload:', $data);

        // Get script name from the JSON payload
        $scriptName = $data['script_name'] ?? null;

        if (!$scriptName) {
            $logger->warning('Script name is missing in the payload.');
            return new JsonResponse(['success' => false, 'message' => 'Script name is required.'], 400);
        }

        // Find the corresponding Symfony command
        $commandName = $this->findCommandByName($scriptName);
        $logger->info('Command name found:', ['commandName' => $commandName]);

        if (!$commandName) {
            $logger->warning('No command found for script:', ['scriptName' => $scriptName]);
            return new JsonResponse([
                'success' => false,
                'message' => 'Aucune commande trouvée pour ce script.',
                'available_commands' => array_keys(self::COMMANDS),
            ], 404);
        }

        // Execute the command
        try {
            $logger->info('Executing command:', ['commandName' => $commandName]);
            $process = new Process(['php', 'bin/console', $commandName]);
            $process->run();

            if ($process->isSuccessful()) {
                $logger->info('Command executed successfully.');
                return new JsonResponse(['success' => true, 'message' => 'Script exécuté avec succès.']);
            } else {
                $logger->error('Command execution failed:', ['errorOutput' => $process->getErrorOutput()]);
                return new JsonResponse(['success' => false, 'message' => 'Erreur: ' . $process->getErrorOutput()], 500);
            }
        } catch (\Exception $e) {
            $logger->error('Command execution error:', ['exception' => $e->getMessage()]);
            return new JsonResponse(['success' => false, 'message' => 'Erreur d\'exécution: ' . $e->getMessage()], 500);
        }
    }
    private function findCommandByName(?string $scriptName): ?string
    {
        if (!$scriptName) {
            return null;
        }

        // Normalize script name for comparison
        $normalizedScriptName = $this->normalizeName($scriptName);

        $bestMatch = null;
        $bestScore = 0;

        // Search for a matching command in the array
        foreach (self::COMMANDS as $commandScriptName => $commandName) {
            $normalizedCommandScriptName = $this->normalizeName($commandScriptName);

            // Calculate similarity score
            similar_text($normalizedScriptName, $normalizedCommandScriptName, $score);

            // Keep track of the best match
            if ($score > $bestScore) {
                $bestScore = $score;
                $bestMatch = $commandName;
            }
        }

        // Only return a match if the similarity score is above a threshold (e.g., 80%)
        return $bestScore >= 80 ? $bestMatch : null;
    }

    /**
     * Normalize names for comparison (remove spaces, special chars, etc.).
     */
    private function normalizeName(string $name): string
    {
        // Remove all non-alphanumeric characters and convert to lowercase
        return strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $name));
    }



    /**
     * @Route("/ExportCompany", name="ExportCompany", methods={"GET", "POST"})
     */
    public function ExportCompany(ManagerRegistry $doctrine, Request $request): JsonResponse
    {
        // Retrieve the 'lettre' parameter from the POST request body (optional)
        $lettre = $request->request->get('lettre');

        // Fetch the list of companies from the repository
        $entrepriseRepository = $doctrine->getRepository(EntrEntreprise::class);

        $entrepriseList = $entrepriseRepository->entrepriseListeEntreprises($lettre);


        // Check if the result is empty
        if (empty($entrepriseList)) {
            return new JsonResponse([
                'success' => false,
                'message' => 'No companies found.',
            ], 404); // HTTP 404 Not Found
        }

        // Return the list of companies in a structured JSON response
        return new JsonResponse([
            'success' => true,
            'data' => $entrepriseList,
        ]);
    }
    /**
     * @Route("/run-script", name="run_script", methods={"POST"})
     */
    public function runScript(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $scriptName = $data['scriptName'] ?? null;

        if (!$scriptName) {
            return new JsonResponse(['success' => false, 'message' => 'Nom du script manquant.'], 400);
        }
        // Liste des scripts valides
        $validScripts = [
            "ABSOLIS",
            "ADAMPARTNERS",
            "ADSN",
            "AG2R",
            "ALFA",
            "APTIC",
            "ASCENCIA",
            "ASEmploi",
            "AboutirEmploi",
            "Absolis",
            "AbsolisSud",
            "Actua",
            "Adoma",
            "Adworks",
            "Adzuna",
            "AdzunaAlternance",
            "AdzunaDomTom",
            "Aesio",
            "Agatha",
            "AlbertvilleInterim",
            "Aldi",
            "Alkine",
            "Allia",
            "AlteregoInterim",
            "Alternativ",
            "AnnecyInterim",
            "Apave",
            "AquilaRH",
            "ArcelorMittal",
            "ArobaseInterim",
            "AsapWork",
            "AssistancePublique",
            "Atalian",
            "Auchan",
            "Aurys",
            "Axeo",
            "Azae",
            "BPS",
            "BouyguesConstruction",
            "Brainfield",
            "Briand",
            "Broadbean",
            "BuffaloGrill",
            "BurgerKing",
            "BusinessPeople",
            "ByMYCAR",
            "CASA",
            "CCLD",
            "CDEL",
            "CHRONOPOST",
            "CRARH",
            "CTConsultant",
            "CarrefourRecrut",
            "Casino",
            "Celio",
            "Cerba",
            "Chantiers",
            "Charier",
            "Citya",
            "Conserto",
            "Cora",
            "DACHSER",
            "DARTY",
            "DEKRA",
            "DPD",
            "Dcs",
            "Decathlon",
            "Dentego",
            "Departement",
            "DepartementEssonne",
            "Domaliance",
            "DominoRH",
            "Dupon",
            "EDF",
            "EDUSERVICES",
            "ELIS",
            "ENEDIS",
            "Elior",
            "Elsan",
            "Emeis",
            "EmploiLR",
            "Engie",
            "EtreEmploi",
            "Even",
            "Excelliance",
            "ExpertRH",
            "Expertnet",
            "Externatic",
            "FIDRH",
            "FSEF",
            "Fauche",
            "Fayat",
            "FayatXml",
            "FleuryMichon",
            "FlorianMantione",
            "Framatone",
            "FranceTravail",
            "GIF",
            "GROUPEINTUIS",
            "GROUPEPRINTEMPS",
            "GSF",
            "GeneralEmploi",
            "GerardPerrier",
            "Ginger",
            "Gozoki",
            "GrandVision",
            "GrantThornton",
            "Groupama",
            "GroupeADP",
            "GroupeActive",
            "GroupeBarriere",
            "GroupeBaudin",
            "GroupeBertrand",
            "GroupeClariane",
            "GroupeFB",
            "GroupeGroupama",
            "GroupeLeDuff",
            "GroupeMorgan",
            "HERIGE",
            "HESS",
            "HLMRecrute",
            "HORIZONJOB",
            "HOWDENS",
            "HarryHope",
            "HaryHope",
            "Hess",
            "HistoireDor",
            "Ikks",
            "Iscod",
            "Itea",
            "JeanRouyer",
            "Job4You",
            "JobAffinity",
            "JobAndYou",
            "JobandTalent",
            "Joblift",
            "Jubilinterim",
            "Keltis",
            "Kiloutou",
            "Korian",
            "LCL",
            "LECLERC",
            "LM5P",
            "LNA",
            "LSRH",
            "LaMaisonBleue",
            "LaReleve",
            "Lacoste",
            "LagarderepoleHachetteLivre",
            "Liberkeys",
            "Lidl",
            "LynxRH",
            "MACIF",
            "Malakof",
            "MarcOrian",
            "Menway",
            "MichaelPage",
            "MichaelPageInterim",
            "Mistertemp",
            "Momenti",
            "MonAssistantNumerique",
            "Monoprix",
            "MyRecruteur",
            "NEO2",
            "NGE",
            "Natup",
            "NextepHR",
            "OGF",
            "ONERA",
            "OXANCE",
            "OffDuMomentsSites",
            "Optic200",
            "Orange",
            "Orano",
            "PROMOTRANS",
            "PagePersonnelNew",
            "Planete",
            "Plasticomnium",
            "PolygoneRH",
            "ProInterim",
            "RecrutOr",
            "Recrutimmo",
            "Recrutop",
            "Renault",
            "Rexel",
            "SAINTMACLOU",
            "SANTESTAFFING",
            "SERIS",
            "SOLINKI",
            "SOLUTION30",
            "SOSInterim",
            "SUPER",
            "Sabena",
            "SaintGobain",
            "Samsic",
            "SamsicEmploi",
            "SatisSolution",
            "Securitas",
            "Servagroupe",
            "ServirRH",
            "Simemploi",
            "Sodexo",
            "Sofitex",
            "StartPeople",
            "Studi",
            "TALENTNATURE",
            "TalentPeople",
            "TalentsHandicap",
            "TarnoukRecrute",
            "Temporis",
            "TieUP",
            "TradeSolutions",
            "TransparenceRH",
            "Tresor",
            "VIVERIS",
            "Viaposte",
            "VichyInterim",
            "Vitalis",
            "Vousfinancer",
            "VoyageADAPTE",
            "WorkandYou",
            "Wurth",
            "Yvelines",
            "groupeA2micile",
            "implid",
            "lm5p",
            "rgis",
            "winsearch",
            "xFab"
        ];

        // Trouver la meilleure correspondance
        $closestScript = $this->findClosestMatch($scriptName, $validScripts);

        if (!$closestScript) {
            return new JsonResponse(['success' => false, 'message' => 'Aucune correspondance trouvée.'], 400);
        }

        $projectDir = $this->getParameter('kernel.project_dir');
        $command = sprintf('php %s/bin/console %s', $projectDir, escapeshellarg($closestScript));
        try {
            $process = new \Symfony\Component\Process\Process(explode(' ', $command));
            $process->setWorkingDirectory('/var/www/directemploi/www/direct_emploi_s6');
            $process->run();

            if (!$process->isSuccessful()) {
                throw new \Exception($process->getErrorOutput());
            }

            return new JsonResponse([
                'success' => true,
                'output' => $process->getOutput(),
                'script' => $closestScript
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'command' => $command,
                'closestScript' => $closestScript,
            ], 500);

        }

    }
    private function findClosestMatch(string $input, array $scripts): ?string
    {
        $closest = null;
        $shortestDistance = -1;

        foreach ($scripts as $script) {
            $levenshteinDistance = levenshtein(strtolower($input), strtolower($script));

            if ($shortestDistance === -1 || $levenshteinDistance < $shortestDistance) {
                $closest = $script;
                $shortestDistance = $levenshteinDistance;
            }
        }

        return ($shortestDistance <= 3) ? $closest : null;
    }




    #[Route('/cv-views', name: '_cv_views')]
    public function cvViews(OffReponseOffreNonIdentifieRepository $offReponseOffreNonIdentifieRepository, OffCandidatureRepository $offCandidatureRepository) 
    {

        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $offReponseViews = $offReponseOffreNonIdentifieRepository->getViews();
        $offCandidatureViews = $offCandidatureRepository->getViews();

        $views = array_merge($offReponseViews, $offCandidatureViews);
        $totalViews = [];
        foreach ($views as $view) {
            if (isset($totalViews[$view['raison_sociale']])) {
                $totalViews[$view['raison_sociale']] += $view['total_vues'];
            } else {
                $totalViews[$view['raison_sociale']] = $view['total_vues'];
            }
        }

        arsort($totalViews);

        
        return $this->render('admin_espace/cv_views.html.twig', [
            'views' => $totalViews,
        ]);
    }

    #[Route('/articles', name: '_articles')]
    public function articles(Request $request, EditArticleRepository $editArticleRepository, PaginatorInterface $paginator): Response
    {

        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $articles = $editArticleRepository->findAllByDateModif();

        // ajouter une section de recherche
        $form = $this->createForm(SearchFormType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $searchTerm = $form->get('searchTerm')->getData();
            $articles = $editArticleRepository->search($searchTerm);
        }


        $page = $request->query->getInt('page', 1);

        $articles = $paginator->paginate(
            $articles,
            $page,
            10
        );


        return $this->render('admin_espace/view_articles.html.twig', [
            'articles' => $articles,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/article/new', name: '_new_article')]
    public function newArticle(Request $request, EntityManagerInterface $entityManager): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $editArticle = new EditArticle();
        $articleForAllSiteDirectEmploi = new ArticleForAllSiteDirectEmploi();
        $form = $this->createForm(AdminEditArticleType::class, $editArticle);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Set fixed values for secteurActiviteId, typeArticleId, and themeArticleId
            $secteurActiviteId = 31;
            $typeArticleId = 3;
            $themeArticleId = 1;

            $user = $this->getUser()->getIdUser();

            // Set the fixed values
            $editArticle->setOffListeSecteurActiviteIdSecteurActivite($secteurActiviteId);
            $editArticle->setEditTypeArticleIdTypeArticle($typeArticleId);
            $editArticle->setEditThemeArticleIdThemeArticle($themeArticleId);
            $editArticle->setSite($form->get('site')->getData()); // Get the site from the form

            $imageFile = $form->get('image_petite')->getData();
            if ($imageFile) {
                $newFilename = uniqid().'.'.$imageFile->guessExtension();
                try {
                    $imageFile->move(
                        $this->getParameter('images_directory'),
                        $newFilename
                    );
                } catch (FileException $e) {
                    $this->addFlash('error', 'Erreur lors du téléchargement de l\'image : '.$e->getMessage());
                    return $this->redirectToRoute('_new_article');
                }
                $editArticle->setImagePetite($newFilename);
            }

            $editArticle->setUserUserIdUser($user);
            $editArticle->setDateModification(new DateTime());
            $editArticle->setDateCreation(new DateTime());
            $editArticle->setIsHidden(0);

            try {
                // Persist main article first
                $entityManager->persist($editArticle);
                $entityManager->flush();

                // Now create cross-site entry
                $crossEntry = new ArticleForAllSiteDirectEmploi();
                $crossEntry->setSite(0);
                $crossEntry->setIdArticle($editArticle->getIdArticle());

                $entityManager->persist($crossEntry);
                $entityManager->flush();

                $this->addFlash('success', 'Article créé avec succès!');
                return $this->redirectToRoute('app_admin_articles');

            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur de base de données : ' . $e->getMessage());
                // Rollback both entries if needed
                $entityManager->clear();
            }

            return $this->redirectToRoute('app_admin_articles');
        }

        return $this->render('admin_espace/new_article.html.twig', [
            'form' => $form->createView(),
        ]);
    }


    #[Route('/article/{id}', name: '_view_article')]
    public function article(EditArticle $editArticle): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        return $this->render('admin_espace/view_article.html.twig', [
            'article' => $editArticle,
        ]);
    }
    #[Route('/update/presentation/{id}', name: '_update_presentation_entreprise')]
    public function updatepresentation(
        $id,
        Request $request,
        EntrEntrepriseRepository $entrEntrepriseRepository,
        EntityManagerInterface $em
    ): Response {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $entreprise = $entrEntrepriseRepository->find($id);

        if (!$entreprise) {
            throw $this->createNotFoundException('Entreprise introuvable.');
        }

        // Si la requête est POST → on met à jour
        if ($request->isMethod('POST')) {
            $nouveauContenu = $request->request->get('presentation');

            $entreprise->setPresentationSociete($nouveauContenu);
            $em->persist($entreprise);
            $em->flush();

            $this->addFlash('success', 'Présentation mise à jour avec succès.');

            return $this->redirectToRoute('app_admin_update_presentation_entreprise', ['id' => $id]);
        }

        $contenuhtml = $entreprise->getPresentationSociete();

        return $this->render('admin_espace/edit_presentation_entreprise.html.twig', [
            'contenuhtml' => $contenuhtml,
        ]);
    }


    #[Route('/article/edit/{id}', name: '_edit_article_test')]
    public function editArticleAdmin($id, 
    Request $request, 
    EntityManagerInterface $entityManager, 
    EditArticleRepository $editArticleRepository, 
    OffListeSecteurActiviteRepository $offListeSecteurActiviteRepository,
    EditThemeArticleRepository $editThemeArticleRepository,
    EditTypeArticleRepository $editTypeArticleRepository): Response
    {

        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $editArticle = $editArticleRepository->find($id);
        if ($editArticle->getImagePetite())
        {
            $Oldimagename= $editArticle->getImagePetite();
        }else
        {
            $Oldimagename="";
        }
/*
        $secteurActiviteId = $editArticle->getOffListeSecteurActiviteIdSecteurActivite();
        $secteurActivite = $offListeSecteurActiviteRepository->find($secteurActiviteId);
        $themeArticleId = $editArticle->getEditThemeArticleIdThemeArticle();
        $themeArticle = $editThemeArticleRepository->find($themeArticleId);
        $typeArticleId = $editArticle->getEditTypeArticleIdTypeArticle();
        $typeArticle = $editTypeArticleRepository->find($typeArticleId);
        if (!$secteurActivite) {
            throw $this->createNotFoundException('Le secteur d\'activité associé à l\'article n\'existe pas');
        }
        if (!$themeArticle) {
            throw $this->createNotFoundException('Le thème de l\'article n\'existe pas');
        }
        if (!$typeArticle) {
            throw $this->createNotFoundException('Le type de l\'article n\'existe pas');
        }
        */


        $form = $this->createForm(AdminEditArticleType::class, $editArticle);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $editArticle->setDateModification(new DateTime());


            $imageFile = $form->get('image_petite')->getData();

            if ($imageFile) {
                $newFilename = uniqid().'.'.$imageFile->guessExtension();
                try {
                    $imageFile->move(
                        $this->getParameter('images_directory'),
                        $newFilename
                    );
                } catch (FileException $e) {
                    return $this->json(['error' => $e->getMessage()], 500);
                }
                $editArticle->setImagePetite($newFilename);
            }else
            {
                $editArticle->setImagePetite($Oldimagename);
            }

            $entityManager->flush();

            return $this->redirectToRoute('app_admin_articles');
        }

        return $this->render('admin_espace/edit_article.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/accueil-article', name: '_accueil_article')]
    public function accueilArticle(EntityManagerInterface $em, AdminEditArticleRepository $adminEditArticleRepository, Request $request, EditArticleRepository $editArticleRepository): Response
    {
        $article = $adminEditArticleRepository->findOneBy(['edit_article_id_article' => 0]);
        $firstArticle = $article->getIdArticleFirst();
        $secondArticle = $article->getIdArticleSecond();
        $thirdArticle = $article->getIdArticleThird();
        $accueilArticle = $editArticleRepository->findBy(['id_article' => [$firstArticle, $secondArticle, $thirdArticle]]);
        
        return $this->render('admin_espace/accueil_article.html.twig', [
            'articles' => $accueilArticle,
        ]);
    }

    #[Route('/accueil-article/edit', name: '_edit_accueil_article')]
    public function editAccueilArticle(Request $request, EntityManagerInterface $em, AdminEditArticleRepository $adminEditArticleRepository): Response
    {
        $article = $adminEditArticleRepository->findOneBy(['edit_article_id_article' => 0]);
        $form = $this->createForm(AccueilAdminArticleType::class, $article);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em->flush();
            return $this->redirectToRoute('app_admin_accueil_article');
        }

        return $this->render('admin_espace/edit_accueil_article.html.twig', [
            'form' => $form->createView(),
        ]);
    }


    #[Route('/upload-image', name: '_image_upload', methods: ['POST'])]
    public function upload(Request $request, SluggerInterface $slugger): Response
    {
        $file = $request->files->get('file');
        if ($file) {
            $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $safeFilename = $slugger->slug($originalFilename);
            $newFilename = $safeFilename.'-'.uniqid().'.'.$file->guessExtension();

            try {
                $file->move(
                    $this->getParameter('images_directory'),
                    $newFilename
                );

                return $this->json(['location' => '/uploads/article'.$newFilename]);
            } catch (FileException $e) {
                return $this->json(['error' => $e->getMessage()], 500);
            }
        }

        return $this->json(['error' => 'No file uploaded'], 400);
    }

    #[Route('/stats-entreprises', name: '_stats_entreprises')]
    public function statsEntreprises(OffStatistiqueRepository $offStatistiqueRepository, UserUserRepository $userUserRepository, PaginatorInterface $page, Request $request): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $form = $this->createForm(SearchFormType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $searchTerm = $form->get('searchTerm')->getData();
        } else {
            $searchTerm = '';
        }
    
        $users = $userUserRepository->getCompanies($searchTerm);

        // pagination
        $users = $page->paginate(
            $users,
            $request->query->getInt('page', 1), 
            10
        );
    
        return $this->render('admin_espace/stats_entreprises.html.twig', [
            'users' => $users,
            'form' => $form->createView(),
        ]);
    }
    #[Route('/stats-entreprises-ELK', name: '_stats_entreprises_ELK')]
    public function statsEntreprisesELK(
        ELKOffOffreService $elkOffOffreService,
        EntrEntrepriseRepository $entrEntrepriseRepository,
        Request $request,
        UserUserRepository $userRepository
    ): Response {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        // Récupérer le terme de recherche
        $search = $request->query->get('search', '');

        // Récupération des entreprises via ELK
        $entreprises = $elkOffOffreService->getEntreprisesList($search);

        // Tableau final pour stocker les données complètes des entreprises
        $result = [];

        foreach ($entreprises as $entreprise) {
            $entrepriseId = $entreprise['key']; // Récupérer l'ID de l'entreprise depuis Elasticsearch
            $dbEntreprise = $entrEntrepriseRepository->findOneBy(['id_entreprise' => $entrepriseId]);

            if ($dbEntreprise) {
                $Recruteur = $userRepository->findOneBy(['entr_entreprise_id_entreprise' => $dbEntreprise->getIdEntreprise()]);

                // Ajouter les données complètes de l'entreprise
                $result[] = [
                    'id' => $dbEntreprise->getIdEntreprise(),
                    'raison_sociale' => $dbEntreprise->getRaisonSociale(),
                    'telephone' => $dbEntreprise->getTelephone() ?? 'Non spécifié',
                    'fax' => $dbEntreprise->getFax() ?? 'Non spécifié',
                    'site_web' => $dbEntreprise->getSiteWeb() ?? 'Non spécifié',
                    'logo' => $dbEntreprise->getLogo(),
                    'presentation_societe' => $dbEntreprise->getPresentationSociete(),
                    'logo_fond' => $dbEntreprise->getLogoFond(),
                    'nomrecrut' => $Recruteur->getNom(),
                    'prenomrecrut' => $Recruteur->getPrenom(),
                ];
            }
        }
        // Passer les données au Twig
        return $this->render('admin_espace/new_stats_entreprises.html.twig', [
            'entreprises' => $result,
            'search' => $search,
        ]);
    }
    #[Route('/stats-entreprises-ELK/{id}', name: '_entreprise_offres_ELK')]
    public function entrepriseOffres(
        int $id,
        ELKOffOffreService $elkOffOffreService,
        EntrEntrepriseRepository $entrEntrepriseRepository
    ): Response {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        // Récupérer les détails de l'entreprise depuis la base de données
        $entreprise = $entrEntrepriseRepository->find($id);

        if (!$entreprise) {
            throw $this->createNotFoundException('Entreprise non trouvée.');
        }

        // Récupérer les offres de l'entreprise via Elasticsearch
        $offres = $elkOffOffreService->getOffresByEntreprise($id);

        // Passer les données au Twig
        return $this->render('admin_espace/new_stats_click.html.twig', [
            'entreprise' => $entreprise,
            'offres' => $offres,
        ]);
    }



    #[Route('/stats-entreprises/{id}', name: '_stats_entreprises_id')]
    public function statsClicks($id, Request $request, OffStatistiqueRepository $offStatistiqueRepository, EntrEntrepriseRepository $entrEntrepriseRepository, PaginatorInterface $page, RequestStack $requestStack): Response
    {
        $entreprise = $entrEntrepriseRepository->find($id);

        $form = $this->createForm(StatsDateClickType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $formData = $form->getData();
            $startDate = $formData['startDate'];
            $endDate = $formData['endDate'];
            $endDate->setTime(23, 59, 59); // 🔥 indispensable !
            $stats = $offStatistiqueRepository->getStatistiqueByCompany($id, $startDate, $endDate);
        } else {
            $stats = $offStatistiqueRepository->getStatistiqueByCompany($id, null, null);
        }
        // $stats = $page->paginate(
        //     $stats,
        //     $request->query->getInt('page', 1), 
        //     10
        // );

        // passer les stats a la session pour le telechargement en csv
        $session = $requestStack->getSession();
        $session->set('stats', $stats);

        return $this->render('admin_espace/stats_click.html.twig', [
            'stats' => $stats,
            'entreprise' => $entreprise,
            'form' => $form->createView(),
        ]);
    }

    // telecharger les stats obtenues en csv
    #[Route('/stats-entreprises/{id}/download', name: '_stats_entreprises_id_download')]
    public function downloadStats($id, Request $request, OffStatistiqueRepository $offStatistiqueRepository, EntrEntrepriseRepository $entrEntrepriseRepository, RequestStack $requestStack): Response
    {
        // Augmenter le temps d'exécution à 5 minutes (900 secondes)
        ini_set('max_execution_time', 900);

        // Récupérer les statistiques de la session
        $stats = $requestStack->getSession()->get('stats');

        // dump($stats);

        if (empty($stats)) {
            throw new \Exception('Aucune statistique trouvée en session.');
        }

        // Récupérer l'entreprise
        $entreprise = $entrEntrepriseRepository->find($id);

        // Calculer les totaux
        $totalAffichages = array_sum(array_column($stats, 'affichage_count'));
        $totalClics = array_sum(array_column($stats, 'click_count'));
        $totalLiens = array_sum(array_column($stats, 'link_count'));

        // Nom du fichier CSV
        $filename = $entreprise->getRaisonSociale() . '_stats.csv';

        // Initialiser la réponse
        $response = new Response();

        // Contenu du fichier CSV
        $content = "\xEF\xBB\xBF"; // UTF-8 BOM

        // Définir les options pour le format CSV
        $delimiter = ';';
        $enclosure = '"';

        // Créer le contenu du fichier CSV
        $content .= "Offre;Reference;Ville;Affichages;Clics;Liens;Date\n";
        foreach ($stats as $stat) {
            $dateLog = $stat['date_log'];
                if ($dateLog instanceof \DateTime) {
                    $formattedDate = $dateLog->format('Y-m-d'); 
                } elseif (is_numeric($dateLog)) { 
                    $formattedDate = (new \DateTime())->setTimestamp($dateLog)->format('Y-m-d');
                } else {
                    $formattedDate = (string)$dateLog;
                }
                $content .= '"' . $stat['intitule'] . '"' . $delimiter .
                            '"' . $stat['reference'] . '"' . $delimiter .
                            '"' . $stat['geo_ville'] . '"' . $delimiter .
                            $stat['affichage_count'] . $delimiter .
                            $stat['click_count'] . $delimiter .
                            $stat['link_count'] . $delimiter .
                            $formattedDate . "\n";
        }

        // Ajouter les totaux
        $content .= "\nTotal Affichages;;;" . $totalAffichages . "\n";
        $content .= "Total Clics;;;;" . $totalClics . "\n";
        $content .= "Total Liens;;;;;" . $totalLiens . "\n";

        // Définir le contenu de la réponse
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');
        $response->setContent($content);

        return $response;
    }

    #[Route('/cvs-candidatures', name: '_stats_cvs_candidatures')]
    public function cvsCandidatures(
        OffReponseOffreNonIdentifieRepository $offReponseOffreNonIdentifieRepository,
        OffCandidatureRepository $offCandidatureRepository,
        Request $request,
        PaginatorInterface $paginator
    ): Response {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $form = $this->createForm(AdminDateRangeCandidateType::class);
        $form->handleRequest($request);

        // Variables par défaut pour candidatures et total
        $totalCandidatures = [];
        $total = 0;

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $startDate = $data['startDate'];
            $endDate = $data['endDate'];

            // Candidatures des candidats non identifiés
            $candidaturesNonIdentifiees = $offReponseOffreNonIdentifieRepository->getNbCandidaturesByCompany($startDate, $endDate);

            // Candidatures des candidats identifiés
            $candidaturesIdentifiees = $offCandidatureRepository->getNbCandidaturesByCompany($startDate, $endDate);

            // Fusionner et agréger les candidatures
            $totalCandidatures = array_merge($candidaturesNonIdentifiees, $candidaturesIdentifiees);

            // Si on trouve une exactitude de raison sociale, on additionne les candidatures
            $totalCandidatures = array_reduce($totalCandidatures, function ($carry, $item) {
                if (isset($carry[$item['raison_sociale']])) {
                    $carry[$item['raison_sociale']] += $item['nombre_candidatures'];
                } else {
                    $carry[$item['raison_sociale']] = $item['nombre_candidatures'];
                }
                return $carry;
            }, []);

            arsort($totalCandidatures);

            // Calcul du total des candidatures
            $total = array_sum($totalCandidatures);
        }

        return $this->render('admin_espace/cvs_candidatures.html.twig', [
            'form' => $form->createView(),
            'candidatures' => $totalCandidatures, 
            'total' => $total,                   
        ]);
    }
    #[Route('/clicks-candidatures', name: '_stats_clicks_candidatures')]
    public function ClicksCandidatures(
        ELKOffOffreService $elkOffOffreService,
        EntrEntrepriseRepository $entrEntrepriseRepository,
        Request $request,
        PaginatorInterface $paginator
    ): Response {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $form = $this->createForm(AdminDateRangeCandidateType::class);
        $form->handleRequest($request);

        $totalViews = 0;
        $totalRedirects = 0;
        $result = [];

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $startDate = $data['startDate']->format('Y-m-d');
            $endDate = $data['endDate']->format('Y-m-d');

            // Requête pour récupérer les clics par entreprise et type de clic depuis Elasticsearch
            $clicksByEntreprise = $elkOffOffreService->getClicksByEntrepriseAndDateRange($startDate, $endDate);

            // Traitement des résultats et récupération des raisons sociales
            foreach ($clicksByEntreprise as $clickData) {
                $entrepriseId = $clickData['key'];
                $views = 0;
                $redirects = 0;

                foreach ($clickData['clicks_by_type']['buckets'] as $typeData) {
                    if ($typeData['key'] == 1) {
                        $redirects = $typeData['doc_count'];
                        $totalRedirects += $redirects;
                    } elseif ($typeData['key'] == 2) {
                        $views = $typeData['doc_count'];
                        $totalViews += $views;
                    }
                }

                // Récupérer la raison sociale de l'entreprise
                $entreprise = $entrEntrepriseRepository->findOneBy(['id_entreprise' => $entrepriseId]);
                if ($entreprise) {
                    $raisonSociale = $entreprise->getRaisonSociale();

                    $result[] = [
                        'raison_sociale' => $raisonSociale,
                        'nombre_views' => $views,
                        'nombre_redirects' => $redirects,
                    ];
                }
            }
        }
        usort($result, function ($a, $b) {
            return strcmp($a['raison_sociale'], $b['raison_sociale']);
        });
        return $this->render('admin_espace/click_candidatures.html.twig', [
            'form' => $form->createView(),
            'candidatures' => $result,
            'total_views' => $totalViews,
            'total_redirects' => $totalRedirects,
        ]);
    }



    #[Route('/increment-clicks', name: 'increment_clicks', methods: ['POST'])]
    public function incrementClicks(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $entrepriseId = $data['entrepriseId'] ?? null;

        if (!$entrepriseId) {
            return new JsonResponse(['error' => 'Missing entrepriseId'], JsonResponse::HTTP_BAD_REQUEST);
        }

        // Créer un nouvel enregistrement de clic pour cette entreprise
        $clickRecord = new OffNombreClickParEntreprise();
        $clickRecord->setEntrepriseId($entrepriseId);
        $clickRecord->setNombreClick(1);  // Initialisation du clic à 1 pour chaque nouveau clic

        // Sauvegarder le nouvel enregistrement
        $entityManager->persist($clickRecord);
        $entityManager->flush();

        return new JsonResponse(['success' => true, 'message' => 'Clic enregistré avec succès']);
    }

    #[Route('/list-users', name: '_list_users')]
    public function listUsers(UserUserRepository $userUserRepository, PaginatorInterface $paginator, Request $request): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
    
        $page = $request->query->getInt('page', 1);
        $limit = 10; 
    
        $query = $userUserRepository->findAllUsersWithEntr($page, $limit);
    
        $users = $paginator->paginate(
            $query,
            $page,
            $limit
        );
    
        return $this->render('admin_espace/list_users.html.twig', [
            'users' => $users,
        ]);
    }

    #[Route('/show-user/{id}', name: '_show_user')]
    public function showUser($id, UserUserRepository $userUserRepository, EntrEntrepriseRepository $entrEntrepriseRepository): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $user = $userUserRepository->find($id);

        return $this->render('admin_espace/show_user.html.twig', [
            'user' => $user,
        ]);
    }

    #[Route('/statistiques-cv', name: '_statistiques_cv')]
    public function statistiquesCv(UserUserRepository $userUserRepository, PaginatorInterface $page, Request $request): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $form = $this->createForm(SearchFormType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $searchTerm = $form->get('searchTerm')->getData();
        } else {
            $searchTerm = '';
        }
    
        $users = $userUserRepository->getCompanies($searchTerm);

        // pagination
        $users = $page->paginate(
            $users,
            $request->query->getInt('page', 1), 
            10
        );

        return $this->render('admin_espace/statistiques_cv.html.twig', [
            'users' => $users,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/statistiques-cv/{id}', name: '_statistiques_cv_id')]
    public function statistiquesCvId(OffReponseOffreNonIdentifieRepository $offReponseOffreNonIdentifieRepository, OffCandidatureRepository $offCandidatureRepository, Request $request): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $id = $request->get('id');

        $offReponseNonIdentifie = $offReponseOffreNonIdentifieRepository->getCandidaturesStatsByUser($id);
        $offCandidature = $offCandidatureRepository->getCandidaturesStatsByUser($id);

        $totalCandidatures = array_merge($offReponseNonIdentifie, $offCandidature);

        $filteredCandidatures = [];
        foreach ($totalCandidatures as $candidature) {
            // Construire une clé unique basée sur id_offre et email
            $key = $candidature['id_offre'] . '_' . $candidature['email'];

            // Vérifier si cette clé existe déjà dans $filteredCandidatures
            if (!isset($filteredCandidatures[$key])) {
                // Si la clé n'existe pas, ajouter cette candidature à $filteredCandidatures
                $filteredCandidatures[$key] = $candidature;
            }
        }

        // organiser par date de candidature décroissante
        usort($filteredCandidatures, function($a, $b) {
            return $b['date_candidature'] <=> $a['date_candidature'];
        });

        // Transformer le tableau associatif en un tableau numérique (si nécessaire)
        $filteredCandidatures = array_values($filteredCandidatures);

        return $this->render('admin_espace/statistiques_cv_id.html.twig', [
            'candidatures' => $filteredCandidatures,
        ]);
    }


    #[Route('/statistiques-cv/{id}/download', name: '_statistiques_cv_id_download')]
    public function downloadStatistiquesCvId(
        OffReponseOffreNonIdentifieRepository $offReponseOffreNonIdentifieRepository, 
        OffCandidatureRepository $offCandidatureRepository, 
        UserUserRepository $userUserRepository,
        Request $request
    ): Response {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $id = $request->get('id');

        $offReponseNonIdentifie = $offReponseOffreNonIdentifieRepository->getCandidaturesStatsByUser($id);
        $offCandidature = $offCandidatureRepository->getCandidaturesStatsByUser($id);

        $entreprise = $userUserRepository->findEntrWithUserId($id);

        $totalCandidatures = array_merge($offReponseNonIdentifie, $offCandidature);

        $filteredCandidatures = [];
        foreach ($totalCandidatures as $candidature) {
            // Construire une clé unique basée sur id_offre et email
            $key = $candidature['id_offre'] . '_' . $candidature['email'];

            // Vérifier si cette clé existe déjà dans $filteredCandidatures
            if (!isset($filteredCandidatures[$key])) {
                // Si la clé n'existe pas, ajouter cette candidature à $filteredCandidatures
                $filteredCandidatures[$key] = $candidature;
            }
        }

        // organiser par date de candidature décroissante
        usort($filteredCandidatures, function($a, $b) {
            return $b['date_candidature'] <=> $a['date_candidature'];
        });

        // Transformer le tableau associatif en un tableau numérique (si nécessaire)
        $filteredCandidatures = array_values($filteredCandidatures);

        // nettoyer date_candidature
        foreach ($filteredCandidatures as $key => $candidature) {
            $filteredCandidatures[$key]['date_candidature'] = $candidature['date_candidature']->format('d/m/Y H:i:s');
        }

        $filename = 'statistiques_cv_' . $entreprise['raison_sociale'] . '.csv';

        $response = new StreamedResponse(function() use ($filteredCandidatures) {
            $handle = fopen('php://output', 'w');

            fputs($handle, "\xEF\xBB\xBF");

            // Définir les options pour le format CSV
            $delimiter = ';';
            $enclosure = '"';

            // Écrire l'en-tête du CSV
            fputcsv($handle, [
                'Nom',
                'Prenom',
                'Email',
                'Id Offre',
                'Intitule',
                'Date de candidature',
            ], $delimiter, $enclosure);

            // Écrire les données des statistiques
            foreach ($filteredCandidatures as $stat) {
                fputcsv($handle, [
                    $stat['nom'],
                    $stat['prenom'],
                    $stat['email'],
                    $stat['id_offre'],
                    $stat['intitule'],
                    $stat['date_candidature'],
                ], $delimiter, $enclosure);
            }

            fclose($handle);
        });

        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');

        return $response;
    }

    #[Route('/statistiques-volumes', name: '_statistiques_volumes')]
    public function statistiquesVolumes(OffOffreRepository $offOffreRepository): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $volumesOffres = $offOffreRepository->getVolumeOffres();

        return $this->render('admin_espace/stats_volume.html.twig', [
            'volumes' => $volumesOffres,
        ]);
    }

    #[Route('/statistiques-volumes-stage', name: '_statistiques_volumes_stage')]
    public function statistiquesVolumesStage(OffOffreRepository $offOffreRepository): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $volumesOffres = $offOffreRepository->getVolumeOffresStage();

        return $this->render('admin_espace/stats_volume_stage.html.twig', [
            'volumes' => $volumesOffres,
        ]);
    }
    #[Route('/statistiques-vues-consultation/{page<\d+>?1}', name: '_statistiques_volumes_consultation')]
    public function statistiquesvuesconsultation(EntrEntrepriseRepository $entrEntrepriseRepository, Request $request, ManagerRegistry $doctrine, int $page = 1): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
        $entityManager = $doctrine->getManager();
        $limit = 300;
        $offset = ($page - 1) * $limit;

        // Récupérer les dates du formulaire (si soumises)
        $startDate = $request->query->get('start_date');
        $endDate = $request->query->get('end_date');

        // Construction de la condition WHERE selon les dates
        $dateCondition = '';
        $parameters = [];
        if ($startDate) {
            $dateCondition .= ' AND datecreation >= :startDate';
            $parameters['startDate'] = $startDate;
        }
        if ($endDate) {
            $dateCondition .= ' AND datecreation <= :endDate';
            $parameters['endDate'] = $endDate;
        }

        // Requête SQL pour récupérer les statistiques
        $sql = sprintf(
            'SELECT nom_entreprise, SUM(nbr_de_redirection) as total_redirections 
         FROM redirections
         WHERE 1=1 %s
         GROUP BY nom_entreprise
         ORDER BY total_redirections DESC
         LIMIT %d OFFSET %d',
            $dateCondition,
            $limit,
            $offset
        );
        $listOfEntrEntreprise = $entrEntrepriseRepository->entrepriseListeEntreprises('');
        $stats = $entityManager->getConnection()->executeQuery($sql, $parameters)->fetchAllAssociative();

        // Create a list of enterprise names from $listOfEntrEntreprise
        $entrepriseNames = array_map(function ($entreprise) {
            return $entreprise['raison_sociale'];
        }, $listOfEntrEntreprise);

        // Create a list of enterprise names from stats
        $statsNames = array_map(function ($stat) {
            return $stat['nom_entreprise'];
        }, $stats);

        // Find enterprises with no redirections
        $noRedirectionEnterprises = array_diff($entrepriseNames, $statsNames);

        // Add enterprises with no redirections to the stats array
        foreach ($noRedirectionEnterprises as $entrepriseName) {
            $stats[] = [
                'nom_entreprise' => $entrepriseName,
                'total_redirections' => 0
            ];
        }

        // Requête pour compter le nombre total de lignes (après filtrage)
        $countSql = sprintf(
            'SELECT COUNT(DISTINCT nom_entreprise) as total 
         FROM redirections
         WHERE 1=1 %s',
            $dateCondition
        );
        $totalStats = $entityManager->getConnection()->executeQuery($countSql, $parameters)->fetchOne();

        $totalPages = ceil($totalStats / $limit);

        return $this->render('admin_espace/stats_vues_consultation.html.twig', [
            'stats' => $stats,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'startDate' => $startDate,
            'endDate' => $endDate,
        ]);
    }

    #[Route('/FicheEntreprise', name: '_ficheEntreprise')]
    public function FicheEntreprise(EntrEntrepriseRepository $entrEntrepriseRepository, Request $request, ManagerRegistry $doctrine): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
        $entreprises = $entrEntrepriseRepository->entrepriseListeEntreprisesAdmin('');

        return $this->render('admin_espace/FicheEntreprise.html.twig', [
            'entreprises' => $entreprises,
        ]);
    }

    #[Route('/UpdateFicheEntreprise/{id}', name: '_update_ficheEntreprise', methods: ['POST'])]
    public function UpdateFicheEntreprise(EntrEntrepriseRepository $entrEntrepriseRepository, Request $request, ManagerRegistry $doctrine, $id): JsonResponse
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }
        $entreprise = $entrEntrepriseRepository->findOneBy(['id_entreprise' => $id]);
        $data = json_decode($request->getContent(), true);

        // Update the company description
        $entreprise->setPresentationSociete($data['description']);
        $entityManager = $doctrine->getManager();
        $entityManager->persist($entreprise);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Description updated successfully']);
    }

    #[Route('/GetFicheEntreprise/{id}', name: '_Get_ficheEntreprise', methods: ['POST'])]
    public function GetFicheEntreprise(EntrEntrepriseRepository $entrEntrepriseRepository, Request $request, ManagerRegistry $doctrine, $id): JsonResponse
    {
        $entreprise = $entrEntrepriseRepository->findOneBy(['id_entreprise' => $id]);
        return new JsonResponse(['status' => 'success', 'description' => $entreprise->getPresentationSociete()]);
    }

    #[Route('/GestionRedirectionFormulaire', name: '_GestionRedirectionFormulaire')]
    public function GestionRedirectionFormulaire(
        EntrEntrepriseRepository $entrEntrepriseRepository,
        Request $request,
        ManagerRegistry $doctrine
    ): Response {
        // Check if the request is an AJAX POST request
        if ($request->isXmlHttpRequest() && $request->isMethod('POST')) {
            $entityManager = $doctrine->getManager();
            $idEntreprise = $request->request->get('id_entreprise');
            $newTag = $request->request->get('tag');

            $entreprise = $entrEntrepriseRepository->find($idEntreprise);

            if ($entreprise) {
                $entreprise->setTag((int) $newTag);
                $entityManager->persist($entreprise);
                $entityManager->flush();

                return $this->json([
                    'success' => true,
                    'message' => 'Le tag a été mis à jour avec succès.'
                ]);
            }

            return $this->json([
                'success' => false,
                'message' => 'Entreprise introuvable.'
            ]);
        }

        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $entreprises = $entrEntrepriseRepository->entrepriseListeEntreprisesAdmin('');

        return $this->render('admin_espace/GestionRedirectionFormulaire.html.twig', [
            'entreprises' => $entreprises,
        ]);
    }

    #[Route('/get-offre-clicks', name: '_app_get_offre_clicks')]
    public function getOffreClicks(Request $request, ManagerRegistry $doctrine, OffOffreRepository $offOffreRepository): JsonResponse
    {
        $entreprise = $request->query->get('entreprise');
        $startDate = $request->query->get('start_date');
        $endDate = $request->query->get('end_date');

        if (!$entreprise) {
            return new JsonResponse(['success' => false, 'message' => 'Paramètre entreprise manquant'], 400);
        }

        $entityManager = $doctrine->getManager();

        // Vérification des dates et mise en format SQL
        $sql = "SELECT nom_offre, nbr_de_redirection, id_offre FROM redirections WHERE nom_entreprise = :entreprise";
        $params = ['entreprise' => $entreprise];

        if ($startDate && $endDate) {
            try {
                $dateStart = new \DateTime($startDate);
                $dateEnd = new \DateTime($endDate);

                $sql .= " AND DATE(datecreation) BETWEEN :start_date AND :end_date"; // Utilisation de la bonne colonne
                $params['start_date'] = $dateStart->format('Y-m-d');
                $params['end_date'] = $dateEnd->format('Y-m-d');
            } catch (\Exception $e) {
                return new JsonResponse(['success' => false, 'message' => 'Format de date invalide'], 400);
            }
        }

        $offres = $entityManager->getConnection()->executeQuery($sql, $params)->fetchAllAssociative();

        if (!$offres) {
            return new JsonResponse(['success' => false, 'message' => 'Aucune donnée trouvée'], 404);
        }

        // Ajouter la référence pour chaque offre
        foreach ($offres as &$offre) {
            $reference = $offOffreRepository->findOneBy(['id_offre' => $offre['id_offre']]);
            $offre['reference'] = $reference ? $reference->getReference() : null;
        }

        return new JsonResponse(['success' => true, 'offres' => $offres]);
    }





    #[Route('/statistiques-cvparoffre', name: '_statistiques_cvparoffre')]
    public function statistiquescvparoffre(Request $request, Connection $connection, OffOffreRepository $offOffreRepository): Response
    {
        // Récupérer les entreprises pour la liste déroulante
        $carrouselItems = $connection->fetchAllAssociative(
            "SELECT raison_sociale, id_entreprise FROM carrousselForAllSiteDirectEmploi"
        );

        // Préparer les options pour la liste déroulante
        $choices = [];
        foreach ($carrouselItems as $item) {
            $choices[$item['raison_sociale']] = $item['id_entreprise'];
        }

        // Créer le formulaire
        $form = $this->createFormBuilder()
            ->add('entreprise', ChoiceType::class, [
                'choices' => $choices,
                'label' => 'Sélectionnez une entreprise',
                'required' => true,
                'placeholder' => 'Choisir une entreprise...',
            ])
            ->add('start_date', DateType::class, [
                'label' => 'Date de début',
                'widget' => 'single_text',
                'required' => true,
            ])
            ->add('end_date', DateType::class, [
                'label' => 'Date de fin',
                'widget' => 'single_text',
                'required' => true,
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'Rechercher',
            ])
            ->getForm();

        $form->handleRequest($request);

        $stats = [];
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $idEntreprise = $data['entreprise'];
            $startDate = $data['start_date'];
            $endDate = $data['end_date'];

            // Récupérer l'id utilisateur correspondant à l'id entreprise
            $idUser = $connection->fetchOne(
                "SELECT id_user FROM user_user WHERE entr_entreprise_id_entreprise = :idEntreprise",
                ['idEntreprise' => $idEntreprise]
            );

            // Si un utilisateur est trouvé, récupérer les statistiques avec les dates fournies
            if ($idUser) {
                $stats = $offOffreRepository->getStatistiquesCvParOffre((int)$idUser, $startDate, $endDate);
            }
        }

        return $this->render('admin_espace/statistiquescvparoffre.html.twig', [
            'stats' => $stats,
            'form' => $form->createView(),
        ]);
    }






    #[Route('/statistiques-volumes-Francetravail', name: '_statistiques_volumes_Francetravail')]
    public function statistiquesVolumesFrancetravail(OffOffreRepository $offOffreRepository): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $volumesOffres = $offOffreRepository->getOffres_SendToFranceTravail();
        dd($volumesOffres);
        return $this->render('admin_espace/stats_volume.html.twig', [
            'volumes' => $volumesOffres,
        ]);
    }

    #[Route('/modifier-position-stage/{id}', name: '_modifier_position_stage', methods: ['POST'])]
    public function modifierPositionStage(int $id, Request $request, EntrEntrepriseRepository $entrEntrepriseRepository, EntityManagerInterface $entityManager): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $entreprise = $entrEntrepriseRepository->find($id);

        if (!$entreprise) {
            throw $this->createNotFoundException('Entreprise non trouvée.');
        }

        // Récupérer la nouvelle position depuis le formulaire
        $newPosition = $request->request->get('position');

        if ($newPosition) {
            $entreprise->setEntrProfilRecruteurIdProfilRecruteurStage($newPosition);
            $entityManager->persist($entreprise);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_admin_statistiques_volumes_stage');
    }

    #[Route('/modifier-position/{id}', name: '_modifier_position', methods: ['POST'])]
    public function modifierPosition(int $id, Request $request, EntrEntrepriseRepository $entrEntrepriseRepository, EntityManagerInterface $entityManager): Response
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('directemploi');
        }

        $entreprise = $entrEntrepriseRepository->find($id);

        if (!$entreprise) {
            throw $this->createNotFoundException('Entreprise non trouvée.');
        }

        // Récupérer la nouvelle position depuis le formulaire
        $newPosition = $request->request->get('position');

        if ($newPosition) {
            $entreprise->setEntrProfilRecruteurIdProfilRecruteur($newPosition); 
            $entityManager->persist($entreprise);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_admin_statistiques_volumes');
    }



}
