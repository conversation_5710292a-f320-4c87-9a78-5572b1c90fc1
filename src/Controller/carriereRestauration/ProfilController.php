<?php

namespace App\Controller\carriereRestauration;

use App\Entity\CvCv;
use App\Entity\CvListeChoixLangue;
use App\Entity\CvNiveauLangue;
use App\Entity\EcEcole;
use App\Entity\EcOrganismeFormation;
use App\Entity\EcSessionFormation;
use App\Entity\GeoAdresse;
use App\Entity\GeoDepartement;
use App\Entity\GeoListePays;
use App\Entity\GeoListeRegion;
use App\Entity\MetierMetier;
use App\Entity\OffFourchetteRemuneration;
use App\Entity\OffListeSecteurActivite;
use App\Entity\OffListeTypeContrat;
use App\Entity\OffListeTypeFormation;
use App\Entity\UserCompetences;
use App\Entity\UserDisponibilite;
use App\Entity\UserOffreVeille;
use App\Entity\UserSituation;
use App\Entity\UserSituationActivite;
use App\Entity\UserSituationExperience;
use App\Entity\UserUser;
use App\Form\carriereBTP\EditCompteBTPCandidatType;
use App\Form\carriereBTP\InscriptionCandidatBTPEtape3Type;
use App\Repository\CvCvRepository;
use App\Repository\EcEcoleRepository;
use App\Repository\EcSessionFormationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Util\Json;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\Parameter;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints\File;

class ProfilController extends AbstractController
{
    #[Route('/Res/espaceCandidat', name: 'app_Res_espaceCandidat')]
    public function index(ManagerRegistry $doctrine, EntityManagerInterface $entity,EcSessionFormationRepository $ecSessionFormationRepository ): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_Res_accueil');
        }
        $id_user = $this->getUser()->getIdUser();
        $user_situation = $doctrine->getRepository(UserSituation::class)->findOneBy(['user_user_id_user' => $id_user]);

        $situation = array();
        $user_competences = $doctrine->getRepository(UserCompetences::class)->findOneBy(['user_user_id_user' => $id_user]);
        $user_veille = $doctrine->getRepository(UserOffreVeille::class)->findOneBy(['user_user_id_user' => $id_user]);

        if ($user_situation)
        {
            $situation = array();
            $formation=$doctrine->getRepository(OffListeTypeFormation::class)->findOneBy(['id_type_formation'=>$user_situation->getOffListeTypeFormationIdTypeFormation()]);
            if ($user_situation->getOffListeTypeFormationIdTypeFormation()!=0)
            {


            if($formation->getIdTypeFormation()==22)
            {
                $etablissement=$doctrine->getRepository(EcOrganismeFormation::class)->findOneBy(['id_organisme_formation'=>$user_situation->getEcEcoleIdEcole()])->getNomOrganisme();
            }
            elseif(in_array($formation->getIdTypeFormation(),array(6,7,8)))
            {

                //$etablissement=$doctrine->getRepository(EcSessionFormation::class)->findBy(['id_session_formation'=>$user_situation->getEcSessionFormationIdSessionFormation()]);
                $etablissement=$ecSessionFormationRepository->findById($user_situation->getEcSessionFormationIdSessionFormation())->getNomFormation();
            }
            else
            {
                $etablissement=$doctrine->getRepository(EcEcole::class)->findOneBy(['id_ecole'=>$user_situation->getEcEcoleIdEcole()])->getNomEcole();

            }
            }
            //$etablissement=$doctrine->getRepository(EcSessionFormation::class)->findOneBy(['id_session_formation'=>$user_situation->getEcEcoleIdEcole()]);
            $acitivite=$doctrine->getRepository(UserSituationActivite::class)->findOneBy(['id_situation_activite'=>$user_situation->getUserSituationActiviteIdSituationActivite()]);
            if ($formation !=null)
            {
                $situation['formation'] = $formation->getLibelle() ?? null;
            }
            $situation['etablissement'] = $etablissement ?? null;
            $situation['annediplome'] = $user_situation->getAnneeObtentionDiplome();
            $situation['nomdiplome'] = $user_situation->getNomDiplome();
            if ($acitivite != null) {
                $situation['acitivite'] = $acitivite->getLibelle();

            }
            if ( $doctrine->getRepository(OffFourchetteRemuneration::class)->findOneBy(['id_fourchette_remuneration'=>$user_situation->getIdOffFourchetteRemuneration()]) != null) {
                $situation['salairesouahite'] = $doctrine->getRepository(OffFourchetteRemuneration::class)->findOneBy(['id_fourchette_remuneration'=>$user_situation->getIdOffFourchetteRemuneration()])->getLibelle();

            }
            if ( $user_situation->getIdMetier()!=null )
            {
                $situation['metier'] = $doctrine->getRepository(MetierMetier::class)->findOneBy(['id_metier'=>$user_situation->getIdMetier()])->getLibelleMetier();
            }
            if ( $user_situation->getOffListeSecteurActiviteIdSecteurActivite()!=null )
            {
                $situation['secteuractivite'] = $doctrine->getRepository(OffListeSecteurActivite::class)->findOneBy(['id_secteur_activite'=>$user_situation->getOffListeSecteurActiviteIdSecteurActivite()])->getLibelle();

            }
            if ($doctrine->getRepository(UserSituationExperience::class)->findOneBy(['id_situation_experience'=>$user_situation->getUserSituationExperienceIdSituationExperience()])!=null)
            {
                $situation['experience']= $doctrine->getRepository(UserSituationExperience::class)->findOneBy(['id_situation_experience'=>$user_situation->getUserSituationExperienceIdSituationExperience()])->getLibelle();
            }
            if ($doctrine->getRepository(UserDisponibilite::class)->findOneBy(['id_disponibilite'=>$user_situation->getUserDisponibiliteIdDisponibilite()])!= null)
            {
                $situation['dispo']= $doctrine->getRepository(UserDisponibilite::class)->findOneBy(['id_disponibilite'=>$user_situation->getUserDisponibiliteIdDisponibilite()])->getLibelle();

            }
            if ($doctrine->getRepository(GeoListeRegion::class)->findOneBy(['id_region'=>$user_situation->getUserSituationMobiliteIdSituationMobilite()])!= null)
            {
                $situation['mobilite']= $doctrine->getRepository(GeoListeRegion::class)->findOneBy(['id_region'=>$user_situation->getUserSituationMobiliteIdSituationMobilite()])->getLibelle();
            }
        }
        $competences = array();
        if ($user_competences) {
            $languageEntity1 = $doctrine->getRepository(CvListeChoixLangue::class)->findOneBy(['id_choix_langue' => $user_competences->getIdChoixLangue1()]);
            $language1 = $languageEntity1 ? $languageEntity1->getLibelle() : null;

            $nivlangEntity1 = $doctrine->getRepository(CvNiveauLangue::class)->findOneBy(['id_niveau_langue' => $user_competences->getIdNiveauLangue1()]);
            $nivlang1 = $nivlangEntity1 ? $nivlangEntity1->getLibelle() : null;

            $languageEntity2 = $doctrine->getRepository(CvListeChoixLangue::class)->findOneBy(['id_choix_langue' => $user_competences->getIdChoixLangue2()]);
            $language2 = $languageEntity2 ? $languageEntity2->getLibelle() : null;

            $nivlangEntity2 = $doctrine->getRepository(CvNiveauLangue::class)->findOneBy(['id_niveau_langue' => $user_competences->getIdNiveauLangue2()]);
            $nivlang2 = $nivlangEntity2 ? $nivlangEntity2->getLibelle() : null;
            $competences['language1'] = $language1;
            $competences['nivlang1'] = $nivlang1;
            $competences['language2'] = $language2;
            $competences['nivlang2'] = $nivlang2;
            $competences['comp'] = $user_competences->getListeCompetences();
            if ($user_competences->getPermisB()==1)
            {
                $competences['permis'] = 'Oui';
            }
            else
            {
                $competences['permis'] = 'Non';
            }
        }
        $user_cv = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);
        //$user_adresse = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);

        $users_user = $entity->createQueryBuilder();
        $users_user ->select('ga', 'u')
            ->from('App\Entity\UserUser', 'u')
            ->join('App\Entity\GeoAdresse', 'ga', 'WITH', 'u.geo_adresse_id_adresse  = ga.id_adresse')
            ->where('u.id_user = :id_user')
            ->setParameters(
                new ArrayCollection([
                    new Parameter('id_user', $id_user),
                ])
            );

        $query = $users_user->getQuery();
        $user_user = $query->execute();


        if ($user_veille != null) {
            //get unserialized ID
            $id_contrat = unserialize($user_veille->getTypeContratSerialized());
            $id_experience_serialized = unserialize($user_veille->getExperienceSerialized());
            $id_secteur_activite_serialized = unserialize($user_veille->getSecteurActiviteSerialized());
            $id_region_serialized = unserialize($user_veille->getRegionSerialized());
            //$id_formation_serialized = unserialize($user_veille->getFormationSerialized());
            $id_remuneration_serialized = unserialize($user_veille->getRemunerationSerialized());
            $id_departement_serialized = unserialize($user_veille->getDepartementSerialized());

            //get DATA
            $repository = $doctrine->getRepository(OffListeTypeContrat::class);
            $contrat_serialized = $repository->findOneBy(['id_type_contrat' => $id_contrat]);

            $repository = $doctrine->getRepository(OffListeSecteurActivite::class);
            $secteur_activite_serialized = $repository->findOneBy(['id_secteur_activite' => $id_secteur_activite_serialized]);

            $repository = $doctrine->getRepository(GeoListeRegion::class);
            $region_serialized = $repository->findOneBy(['id_region' => $id_region_serialized]);

            $repository = $doctrine->getRepository(GeoDepartement::class);
            $departement_serialized = $repository->findOneBy(['id_departement' => $id_departement_serialized]);

            $repository = $doctrine->getRepository(UserSituationExperience::class);
            $experience_serialized = $repository->findOneBy(['id_situation_experience' => $id_experience_serialized]);

            $repository = $doctrine->getRepository(OffFourchetteRemuneration::class);
            $remuneration_serialized = $repository->findOneBy(['id_fourchette_remuneration' => $id_remuneration_serialized]);

            //get Libelle
            $contrat = $contrat_serialized ? $contrat_serialized->getLibelle() : null;
            $secteur = $secteur_activite_serialized ? $secteur_activite_serialized->getLibelle() : null;
            $departement = $departement_serialized ? $departement_serialized->getLibelle() : null;
            $experience = $experience_serialized ? $experience_serialized->getLibelle() : null;
            $remuneration = $remuneration_serialized ? $remuneration_serialized->getLibelle() : null;

            // Add values to the $user_user object
            $user_user[2]['contrat'] = $contrat;
            $user_user[2]['secteur'] = $secteur;
            $user_user[2]['departement'] = $departement;
            $user_user[2]['experience'] = $experience;
            $user_user[2]['remuneration'] = $remuneration;
        }

            $pays = $doctrine->getRepository(GeoListePays::class)->findOneBy(['id_pays' => $user_user[1]->getGeoListePaysIdPays()]);
            $user_user[1]->pays = $pays ? $pays->getLibelle() : null;


        return $this->render('carriereRestauration/profil/index.html.twig', [
            'controller_name' => 'ProfilController',
            'user' => $user_user,
            'user_situation' => $situation,
            'user_competences' => $competences,
            'user_cv' => $user_cv
        ]);
    }

    #[Route('/Res/espaceCandidat/profilcv', name: 'app_Res_espaceCandidatprofilcv')]
    public function profilcv(ManagerRegistry $doctrine, EntityManagerInterface $entity,CvCvRepository $cvCvRepository ): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_Res_accueil');
        }

        $Cv = $cvCvRepository->findBy(['user_user_id_user'=>$this->getUser()->getIdUser()]);
        return $this->render('carriereRestauration/profil/profilcv.html.twig', [
            'cvs'=>$Cv,
        ]);
    }

    #[Route('/Res/espaceCandidat/profilmail', name: 'app_Res_espaceCandidatprofilmail')]
    public function profilmail(ManagerRegistry $doctrine, EntityManagerInterface $entity ): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_Res_accueil');
        }
        $id_user = $this->getUser()->getIdUser();
        $user_veille = $doctrine->getRepository(UserOffreVeille::class)->findBy(['user_user_id_user' => $id_user]);

        return $this->render('carriereRestauration/profil/profilmail.html.twig', [
            'alerts'=>$user_veille
        ]);
    }
    #[Route('/Res/espaceCandidat/profiloffre', name: 'app_Res_espaceCandidatprofiloffre')]
    public function profiloffre(ManagerRegistry $doctrine, EntityManagerInterface $entity ): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_Res_accueil');
        }
        return $this->render('carriereRestauration/profil/profiloffre.html.twig', [
        ]);
    }
    #[Route('/Res/espaceCandidat/modifierprofil', name: 'app_Res_modifierprofil')]
    public function modifierprofil(EntityManagerInterface $entityManager, UserPasswordHasherInterface $userPasswordHasher,Request $request,ManagerRegistry $doctrine, EntityManagerInterface $entity ): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_Res_accueil');
        }
        $id_user = $this->getUser()->getIdUser();
        $user_veille = $doctrine->getRepository(UserOffreVeille::class)->findOneBy(['user_user_id_user' => $id_user]);
        $user_cv = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);
        $user = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $id_user]);

        $user_situation = $doctrine->getRepository(UserSituation::class)->findOneBy(['user_user_id_user' => $id_user]);

        $user_competences = $doctrine->getRepository(UserCompetences::class)->findOneBy(['user_user_id_user' => $id_user]);
        $user_adresse = $doctrine->getRepository(GeoAdresse::class)->findOneBy(['id_adresse' => $user->getGeoAdresseIdAdresse()]);

        if ($user_competences != null) {
        $permisBValue = $user_competences->getPermisB();
        $profilvisible=$user_competences->getIsProfilVisible();
        $langue1=$user_competences->getIdChoixLangue1();
        $langue2=$user_competences->getIdChoixLangue2();
        $NiVlangue1=$user_competences->getIdNiveauLangue1();
        $NiVlangue2=$user_competences->getIdNiveauLangue2();
        if ($langue1!=null && $NiVlangue1!=null )
        {
            $langue1Entity = $entityManager->getRepository(CvListeChoixLangue::class)->find($langue1);
            $Niv1Entity = $entityManager->getRepository(CvNiveauLangue::class)->find($NiVlangue1);
        }
            if ($langue2!=null && $NiVlangue2!=null )
            {
                $langue2Entity = $entityManager->getRepository(CvListeChoixLangue::class)->find($langue2);
                $Niv2Entity = $entityManager->getRepository(CvNiveauLangue::class)->find($NiVlangue2);
            }

        }
        if ($user_cv != null)
        {
            $is_visible=$user_cv->getIsVisible();
            $is_anonym=$user_cv->getIsAnonym();
        }

        if ($user_veille != null) {
            $serializedValueContrat = $user_veille->getTypeContratSerialized();
            $deserializedValueContrat = unserialize($serializedValueContrat);
            $contratSouhaiteEntity = $entityManager->getRepository(OffListeTypeContrat::class)->find($deserializedValueContrat);

            $serializedValueSalaire = $user_veille->getRemunerationSerialized();
            $deserializedValueSalaire = unserialize($serializedValueSalaire);
            $SalaireSouhaiteEntity = $entityManager->getRepository(OffFourchetteRemuneration::class)->find($deserializedValueSalaire);
        }
        if ($user_situation != null)
        {
            if ($user_situation->getOffListeSecteurActiviteIdSecteurActivitePoste()!=null)
            {
                $id_secteur_activite_serialized = $user_situation->getOffListeSecteurActiviteIdSecteurActivitePoste();
                $idSecteurdactiviteduposte=$entityManager->getRepository(OffListeSecteurActivite::class)->find($id_secteur_activite_serialized);

            }
            $idsalaireduposte = $entityManager->getRepository(OffFourchetteRemuneration::class)->find($user_situation->getIdOffFourchetteRemuneration());
            $idactivite = $entityManager->getRepository(UserSituationActivite::class)->find($user_situation->getUserSituationActiviteIdSituationActivite());
            $idnbranneexp = $entityManager->getRepository(UserSituationExperience::class)->find($user_situation->getUserSituationExperienceIdSituationExperience());
            $iddisponibilite = $entityManager->getRepository(UserDisponibilite::class)->find($user_situation->getUserDisponibiliteIdDisponibilite());
            $idmobilite = $entityManager->getRepository(GeoListeRegion::class)->find($user_situation->getUserSituationMobiliteIdSituationMobilite());
        }

        $paysobject = $doctrine->getRepository(GeoListePays::class)->findOneBy(['id_pays' => $user_adresse->getGeoListePaysIdPays()]);
        $form = $this->createForm(EditCompteBTPCandidatType::class, [
            'permis' => $permisBValue ?? null,
            'profil_visible' => $profilvisible ?? null,
            'profil_anonyme' => $is_anonym ?? null,
            'cv_visible' => $is_visible ?? null,
            'contrat_souhaite' => $contratSouhaiteEntity ?? null,
            'remuneration_souhaitee' => $SalaireSouhaiteEntity ?? null,
            'langue1' => $langue1Entity ?? null,
            'langue2' => $langue2Entity ?? null,
            'level1' => $Niv1Entity ?? null,
            'level2' => $Niv2Entity ?? null,
            'remuneration' => $idsalaireduposte ?? null,
            'activite' => $idactivite ?? null,
            'experience' => $idnbranneexp ?? null,
            'disponibilite' => $iddisponibilite ?? null,
            'mobilite' => $idmobilite ?? null,
            'domaine_activite_situation' => $idSecteurdactiviteduposte ?? null,
            'pays' => $paysobject ?? null,
        ]);
        $form->handleRequest($request);



        if ($form->isSubmitted())
        {
            $reg = $doctrine->getRepository(GeoDepartement::class)->findRegByDep($form->get('code_postal')->getData());
            $user_adresse->setGeoListeRegionIdRegion($reg ? $reg : 0);
            $user_adresse->setAdresse1($form->get('adresse1')->getData());
            $user_adresse->setAdresse2($form->get('complement_adresse')->getData());
            $user_adresse->setCodePostal($form->get('code_postal')->getData());
            $user_adresse->setNomVille($form->get('ville')->getData());
            $user_adresse->setDateMaj(new \DateTime());
            $user_adresse->setGeoListePaysIdPays($form->get('pays')->getData() ? $form->get('pays')->getData()->getIdPays() : 0);
            $entityManager->persist($user_adresse);
            $entityManager->flush();
            $user->setUserCiviliteIdUserCivilite($form->get('user_civilite_id_user_civilite')->getData());
            $formData = $form->get('ec_ecole')->getData();
            if ($formData instanceof EcEcole) {
                // Code to handle the case where $formData is an instance of EcEcole
                $idEcole = $formData->getIdEcole();
                $user->setEcEcoleIdEcole($idEcole);
                $user->setEcOrganismeFormationIdOrganismeFormation(null);

                // ... other EcEcole specific logic ...
            } elseif ($formData instanceof EcOrganismeFormation) {
                // Code to handle the case where $formData is an instance of EcOrganismeFormation
                $idFormation = $formData->getIdOrganismeFormation();
                $user->setEcOrganismeFormationIdOrganismeFormation($idFormation);
                $user->setEcEcoleIdEcole(null);


            }

            //$user->setEcEcoleIdEcole($form->get('ec_ecole')->getData() ? $form->get('ec_ecole')->getData()->getIdEcole() : 0);
            $user->setUserListeTypeIdType(3); //Type candidat
            $user->setNom($form->get('nom')->getData());
            $user->setPrenom($form->get('prenom')->getData());
            $user->setEmail($form->get('email')->getData());
            $user->setTelephone($form->get('telephone')->getData());
            $user->setDateDerniereModification(new \DateTime());
            $user->setLogin($form->get('login')->getData());
            $user->setPass('');
            if ($form->get('passCrypt')->getData())
                $user->setPassCrypt($userPasswordHasher->hashPassword($user, $form->get('passCrypt')->getData()));
            // $user->setGeoAdresseIdAdresse($user_geo->getIdAdresse());
            $user->setDateDerniereConnexion(new \DateTime());
            $entityManager->persist($user);
            $entityManager->flush();



            $user_situation->setoffListeTypeFormationIdTypeFormation($form->get('formation_level')->getData());
            if($form->get('ec_ecole')->getViewData() != null) {
                if(in_array($form->get('formation_level')->getData(),array(6,7,8))){
                    $user_situation->setEcEcoleIdEcole(null);
                    $user_situation->setEcSessionFormationIdSessionFormation($form->get('ec_ecole')->getViewData());
                } else {
                    $user_situation->setEcEcoleIdEcole($form->get('ec_ecole')->getViewData());
                    $user_situation->setEcSessionFormationIdSessionFormation(0);
                }
            } else {
                $user_situation->setEcEcoleIdEcole(null);
                $user_situation->setEcSessionFormationIdSessionFormation(0);
            }

            $user_situation->setOffListeSecteurActiviteIdSecteurActivite($form->get('domaine_activite')->getData() ? $form->get('domaine_activite')->getData()->getIdSecteurActivite() : null);
            $user_situation->setIdMetier($form->get('metier_metier')->getData() ? $form->get('metier_metier')->getData()->getIdMetier() : null);
            $user_situation->setUserDisponibiliteIdDisponibilite($form->get('disponibilite')->getData() ? $form->get('disponibilite')->getData()->getIdDisponibilite() : 0);
            $user_situation->setUserSituationMobiliteIdSituationMobilite($form->get('mobilite')->getData() ? $form->get('mobilite')->getData()->getIdRegion() : 0);
            $user_situation->setAnneeObtentionDiplome($form->get('anneeObtentionDiplome')->getData() ? $form->get('anneeObtentionDiplome')->getData() : 0);
            //$user_situation->setNomDiplome($form->get('nom_diplome')->getData());
            //$user_situation->setPosteActuel($form->get('poste_actuel')->getData() ? $form->get('poste_actuel')->getData() : 0);
            $user_situation->setOffListeSecteurActiviteIdSecteurActivitePoste($form->get('domaine_activite_situation')->getData() ? $form->get('domaine_activite_situation')->getData()->getIdSecteurActivite() : 0);
            $user_situation->setIdOffFourchetteRemuneration($form->get('remuneration')->getData() ? $form->get('remuneration')->getData()->getIdFourchetteRemuneration() : 0);
            $user_situation->setUserSituationActiviteIdSituationActivite($form->get('activite')->getData() ? $form->get('activite')->getData()->getIdSituationActivite() : 0);
            $user_situation->setUserSituationExperienceIdSituationExperience($form->get('experience')->getData() ? $form->get('experience')->getData()->getIdSituationExperience() : 0);
            $entityManager->persist($user_situation);
            $entityManager->flush();
            if ($user_competences != null)
            {
                $user_competences->setListeCompetences($form->get('competence')->getData());
                $user_competences->setIdChoixLangue1($form->get('langue1')->getData() ? $form->get('langue1')->getData()->getIdChoixLangue() : null);
                $user_competences->setIdNiveauLangue1($form->get('level1')->getData() ? $form->get('level1')->getData()->getIdNiveauLangue() : null);
                $user_competences->setIdChoixLangue2($form->get('langue2')->getData() ? $form->get('langue2')->getData()->getIdChoixLangue() : null);
                $user_competences->setIdNiveauLangue2($form->get('level2')->getData() ? $form->get('level2')->getData()->getIdNiveauLangue() : null);
                $user_competences->setPermisB($form->get('permis')->getData());
                $user_competences->setIsProfilVisible($form->get('profil_visible')->getData());
                $entityManager->persist($user_competences);
                $entityManager->flush();
            }else
            {
                $user_competences = new UserCompetences(); // Replace with your actual entity class
                $user_competences->setListeCompetences($form->get('competence')->getData());
                $user_competences->setIdChoixLangue1($form->get('langue1')->getData() ? $form->get('langue1')->getData()->getIdChoixLangue() : null);
                $user_competences->setIdNiveauLangue1($form->get('level1')->getData() ? $form->get('level1')->getData()->getIdNiveauLangue() : null);
                $user_competences->setIdChoixLangue2($form->get('langue2')->getData() ? $form->get('langue2')->getData()->getIdChoixLangue() : null);
                $user_competences->setIdNiveauLangue2($form->get('level2')->getData() ? $form->get('level2')->getData()->getIdNiveauLangue() : null);
                $user_competences->setPermisB($form->get('permis')->getData());
                $user_competences->setIsProfilVisible($form->get('profil_visible')->getData());
                $user_competences->setUserUserIdUser($user->getIdUser());
                $entityManager->persist($user_competences);
                $entityManager->flush();
            }

            if ($user_veille) {
                $user_veille->setRemunerationSerialized(serialize($form->get('remuneration_souhaitee')->getData() ? $form->get('remuneration_souhaitee')->getData()->getIdFourchetteRemuneration() : null));
                $user_veille->setTypeContratSerialized(serialize($form->get('contrat_souhaite') ? $form->get('contrat_souhaite')->getData()->getIdTypeContrat() : null));
              //  $form->get('poste_souhaite')->getData() ? $user_veille->setMotsCles($form->get('poste_souhaite')->getData()) : 0;
                $entityManager->persist($user_veille);
                $entityManager->flush();
            }
            return $this->redirectToRoute('app_Res_espaceCandidat');
        }
        return $this->render('carriereRestauration/profil/updateprofil.html.twig', [
            'EditCompte' => $form->createView(),
            'situation' => $user_situation,
            'competences' => $user_competences,
            'user' => $user,
            'user_adresse' => $user_adresse,
            'user_cv' => $user_cv,
            'user_veille' => $user_veille,
        ]);


    }
    #[Route('/Res/espaceCandidat/modifiercv/{id}', name: 'app_Res_modifiercv')]
    public function modifiercv($id,Request $request,EntityManagerInterface $entityManager,ManagerRegistry $doctrine,CvCvRepository $cvCvRepository, EntityManagerInterface $entity ): Response
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_Res_accueil');
        }
        $cv = $cvCvRepository->findOneBy([
            'user_user_id_user' => $this->getUser()->getIdUser(),
            'id_cv' => $id,
        ]);
        if ($cv === null)
        {
            return $this->redirectToRoute('app_Res_accueil');
        }

        // Créer un formulaire directement dans le contrôleur
        $form = $this->createFormBuilder($cv)
            ->add('titre', TextType::class, [
                'label' => 'Titre du CV',
                'attr' => [
                    'class' => 'form-control', // Classe appliquée à l'élément input
                    'placeholder' => 'Titre du CV']
            ])
            ->add('isvisible', ChoiceType::class, [
                'label' => 'Rendre mon CV visible',
                'choices' => [
                    'Non' => '0',
                    'Oui' => '1'
                ],
                'expanded' => true,
                'multiple' => false,

            ])
            ->add('isanonym', ChoiceType::class, [
                'label' => 'Rendre mon CV anonyme',
                'choices' => [
                    'Non' => '0',
                    'Oui' => '1'
                ],
                'expanded' => true,
                'multiple' => false,
                'label_attr' => ['class' => 'col-form-label d-inline pt-0'], // Classe appliquée au label

            ])
            ->add('cv_file',FileType::class,
                ['attr'=>['class'=>'form-control'],
                    'mapped'=>false,
                    'required'=>false,
                    'constraints'=>
                        [
                            new File([
                                'mimeTypes'=>[
                                    'image/jpg',
                                    'image/jpeg',
                                    'image/png',
                                    'image/gif',
                                    'application/pdf', // Allow PDF files
                                    'application/msword', // Allow DOCX files
                                ],
                                'mimeTypesMessage'=>"Type de l'image est invalide "
                            ])
                        ]])

            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted()) {
            $cv->setTitre($form->getData()->gettitre());
            $cv->setIsAnonym($form->getData()->getIsAnonym());
            $cv->setIsVisible($form->getData()->getIsVisible());
            if ($form->get('cv_file')->getData()) {
                $brochureFile = $form->get('cv_file')->getData();
                $originalFilename = pathinfo($brochureFile->getClientOriginalName(), PATHINFO_FILENAME);
                //$safeFilename = $slugger->slug($originalFilename);
                //$nom_filename = "cv3343111" . date('YmdHis');
                $nom_filename = $this->getUser()->getIdUser() . date('YmdHis');
                $newFilename = $nom_filename . '-' . uniqid() . '.' . $brochureFile->guessExtension();

                try {
                    $brochureFile->move(
                        $this->getParameter('cv_cand_save'),
                        $newFilename
                    );
                } catch (FileException $e) {
                    // ... handle exception if something happens during file upload
                }

                $cv->setNomFichierCvOriginal($originalFilename);
                $cv->setNomFichierCvStockage($newFilename);
                $cv->setOffFourchetteRemunerationIdFourchetteRemunerationSouhaite(0);
                $cv->setOffFourchetteRemunerationIdFourchetteRemunerationActuel(0);
                $cv->setEtat(0);

            }
            $entityManager->persist($cv);
            $entityManager->flush();
            $this->addFlash('success', 'Mise à jour du CV effectuée avec succès');

        }
        $filename = $cv->getNomFichierCvStockage();
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        return $this->render('carriereRestauration/profil/modifiercv.html.twig', [
            'cv'=>$cv,
            'form' => $form->createView(),
            'extension'=>$extension
        ]);
    }
    /**
     * @Route("/Res/espaceCandidat/cv_onoff", name="cv_onoff")
     */
    public function OnOffcv(Request $request,EntityManagerInterface $entityManager, CvCvRepository $cvCvRepository)
    {

        if (!$this->getUser()) {
            return $this->redirectToRoute('app_Res_accueil');
        }
        $cvId = $request->request->get('cv_id'); // Retrieve cv.id from the form submission

        if ($cvId === null) {
            // Handle the case where cv.id is not provided or invalid
            // You may want to redirect to an error page or handle it differently based on your requirements
            return $this->redirectToRoute('app_Res_accueil');
        }

        $cv = $cvCvRepository->findOneBy([
            'user_user_id_user' => $this->getUser()->getIdUser(),
            'id_cv' => $cvId,
        ]);

        if ($cv === null) {

            return $this->redirectToRoute('app_Res_accueil');
        }
        $cv->setIsVisible($cv->getIsVisible() === 0 ? 1 : 0);
        $entityManager->persist($cv);
        $entityManager->flush();
        return $this->redirectToRoute('app_Res_espaceCandidatprofilcv');


    }
    /**
     * @Route("/Res/espaceCandidat/cv_delete", name="cv_delete", methods={"POST"})
     */
    public function cv_delete(Request $request, EntityManagerInterface $entityManager, CvCvRepository $cvCvRepository)
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('app_Res_accueil');
        }

        $cvId = $request->request->get('cv_id'); // Retrieve cv.id from the form submission

        if ($cvId === null) {
            // Handle the case where cv.id is not provided or invalid
            // You may want to redirect to an error page or handle it differently based on your requirements
            return $this->redirectToRoute('app_Res_accueil');
        }

        $cv = $cvCvRepository->findOneBy([
            'user_user_id_user' => $this->getUser()->getIdUser(),
            'id_cv' => $cvId,
        ]);

        if ($cv === null) {
            // Handle the case where the specified CV is not found
            // You may want to redirect to an error page or handle it differently based on your requirements
            return $this->redirectToRoute('app_Res_accueil');
        }

        $entityManager->remove($cv);
        $entityManager->flush();

        return $this->redirectToRoute('app_Res_espaceCandidatprofilcv');
    }
    #[Route('/Res/espaceCandidat/ajoutercv', name: 'app_Res_ajoutercv')]
    public function ajoutercv(Request $request,EntityManagerInterface $entityManager,ManagerRegistry $doctrine,CvCvRepository $cvCvRepository, EntityManagerInterface $entity ): Response
    {
        $Cv = new CvCv();

        if (!$this->getUser()) {
            return $this->redirectToRoute('app_Res_accueil');
        }


        // Créer un formulaire directement dans le contrôleur
        $form = $this->createFormBuilder()
            ->add('titre', TextType::class, [
                'label' => 'Titre du CV',
                'attr' => [
                    'class' => 'form-control', // Classe appliquée à l'élément input
                    'placeholder' => 'Titre du CV']
            ])
            ->add('isvisible', ChoiceType::class, [
                'label' => 'Rendre mon CV visible',
                'choices' => [
                    'Non' => '0',
                    'Oui' => '1'
                ],
                'expanded' => true,
                'multiple' => false,

            ])
            ->add('isanonym', ChoiceType::class, [
                'label' => 'Rendre mon CV anonyme',
                'choices' => [
                    'Non' => '0',
                    'Oui' => '1'
                ],
                'expanded' => true,
                'multiple' => false,
                'label_attr' => ['class' => 'col-form-label d-inline pt-0'], // Classe appliquée au label

            ])
            ->add('cv_file',FileType::class,
                ['attr'=>['class'=>'form-control'],
                    'mapped'=>false,
                    'required'=>false,
                    'constraints'=>
                        [
                            new File([
                                'mimeTypes'=>[
                                    'image/jpg',
                                    'image/jpeg',
                                    'image/png',
                                    'image/gif',
                                    'application/pdf', // Allow PDF files
                                    'application/msword', // Allow DOCX files
                                ],
                                'mimeTypesMessage'=>"Type de l'image est invalide "
                            ])
                        ]])

            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted()) {
            $formData = $form->getData();
            $Cv->setTitre($formData['titre']);
            $Cv->setIsAnonym($formData['isvisible']);
            $Cv->setIsVisible($formData['isanonym']);
            $Cv->setDateCreation(new \DateTime());
            $Cv->setDateModification(new \DateTime());
            $Cv->setUserUserIdUser($this->getUser()->getIdUser());
            $Cv->setIntitulePoste('');

            if ($form->get('cv_file')->getData()) {
                $brochureFile = $form->get('cv_file')->getData();
                $originalFilename = pathinfo($brochureFile->getClientOriginalName(), PATHINFO_FILENAME);
                //$safeFilename = $slugger->slug($originalFilename);
                //$nom_filename = "cv3343111" . date('YmdHis');
                $nom_filename = $this->getUser()->getIdUser() . date('YmdHis');
                $newFilename = $nom_filename . '-' . uniqid() . '.' . $brochureFile->guessExtension();

                try {
                    $brochureFile->move(
                        $this->getParameter('cv_cand_save'),
                        $newFilename
                    );
                } catch (FileException $e) {
                    // ... handle exception if something happens during file upload
                }

                $Cv->setNomFichierCvOriginal($originalFilename);
                $Cv->setNomFichierCvStockage($newFilename);
                $Cv->setOffFourchetteRemunerationIdFourchetteRemunerationSouhaite(0);
                $Cv->setOffFourchetteRemunerationIdFourchetteRemunerationActuel(0);
                $Cv->setEtat(0);

            }
            $entityManager->persist($Cv);
            $entityManager->flush();
            $this->addFlash('success', 'Mise à jour du CV effectuée avec succès');
            return $this->redirect('/Res/espaceCandidat/profilcv');
        }
        $filename = $Cv->getNomFichierCvStockage();
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        return $this->render('carriereRestauration/profil/ajoutercv.html.twig', [
            'form' => $form->createView(),
            'extension'=>$extension
        ]);
    }
    #[Route('BTP/espaceCandidat/ajouteralertmail', name: 'app_Res_ajouteralertmail')]
    public function ajouteralertmail(Request $request,EntityManagerInterface $entityManager, ManagerRegistry $doctrine): Response
    {
        if ($this->getUser()) {
            $id_user = $this->getUser()->getIdUser();
        } else {

            return $this->redirectToRoute('app_Res_accueil');
        }
        $form = $this->createForm(InscriptionCandidatBTPEtape3Type::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $user_veille = new UserOffreVeille();
            $user_veille->setLibelle($form->get('nom_alerte') ? $form->get('nom_alerte')->getData() : "Mon alerte du " . date("d/m/Y"));
            $user_veille->setExperienceSerialized($form->get('experience')->getData() ? serialize($form->get('experience')->getData()->getIdSituationExperience()) : null);
            $user_veille->setActif("1");
            $user_veille->setTypeContratSerialized($form->get('contrat') ? serialize($form->get('contrat')->getData()->getIdTypeContrat()) : null);
            $user_veille->setSecteurActiviteSerialized($form->get('domaine_activite') ? serialize($form->get('domaine_activite')->getData()->getIdSecteurActivite()) : null);
            $user_veille->setRegionSerialized($form->get('geo_liste_region')->getData() ? serialize($form->get('geo_liste_region')->getData()->getIdRegion()) : null);
            $user_veille->setFormationSerialized(null);
            $user_veille->setRemunerationSerialized(null);
            $user_veille->setMotsCles($form->get('metier_metier') ? $form->get('metier_metier')->getData()->getLibelleMetier() : null);
            $user_veille->setHandicapSerialized($form->get('handi') ? serialize($form->get('handi')->getData()) : serialize(1));
            $user_veille->setIsRss('0');
            $user_veille->setDepartementSerialized($form->get('geo_departement')->getData() ? serialize($form->get('geo_departement')->getData()->getIdDepartement()) : null);
            //$user_veille->setdate_passage_cron;
            $user_veille->setDateDernierEnvoi(new \DateTime("2015-11-01 00:00:00"));
            $user_veille->setUserUserIdUser($id_user);

            $entityManager->persist($user_veille);
            $entityManager->flush();

            return $this->redirectToRoute('app_Res_espaceCandidatprofilmail');
        }

        return $this->render('carriereRestauration/profil/ajouteralertemail.html.twig', [
            'InscriptionEtape3Form' => $form->createView(),

        ]);
    }

    #[Route('BTP/espaceCandidat/modifieralertmail/{id}', name: 'app_Res_modifieralertmail')]
    public function modifieralertmail(Request $request,EntityManagerInterface $entityManager, ManagerRegistry $doctrine,$id): Response
    {
        if ($this->getUser()) {
            $id_user = $this->getUser()->getIdUser();
        } else {

            return $this->redirectToRoute('app_Res_accueil');
        }
        $user_veille = $doctrine->getRepository(UserOffreVeille::class)->findBy(['id_user_offre_veille' => $id]);

        //$idExperienceSerialized=unserialize($user_veille[0]->getExperienceSerialized());
        $idExperienceSerialized=$doctrine->getRepository(UserSituationExperience::class)->findBy(['id_situation_experience'=>unserialize($user_veille[0]->getExperienceSerialized())]);
        $idContratSerialized=$doctrine->getRepository(OffListeTypeContrat::class)->findBy(['id_type_contrat'=>unserialize($user_veille[0]->getTypeContratSerialized())]);
        $iddomaineactiviteSerialized=$doctrine->getRepository(OffListeSecteurActivite::class)->findBy(['id_secteur_activite'=>unserialize($user_veille[0]->getSecteurActiviteSerialized())]);
        $idgeo_liste_regionSerialized=$doctrine->getRepository(GeoListeRegion::class)->findBy(['id_region'=>unserialize($user_veille[0]->getRegionSerialized())]);
        $idgeo_departementSerialized=$doctrine->getRepository(GeoDepartement::class)->findBy(['id_departement'=>unserialize($user_veille[0]->getDepartementSerialized())]);
        $form = $this->createForm(InscriptionCandidatBTPEtape3Type::class, [
            'nom_alerte' => $user_veille[0]->getLibelle() ?? null,
            'experience' => $idExperienceSerialized[0] ?? null,
            'contrat' => $idContratSerialized[0] ?? null,
            'domaine_activite' => $iddomaineactiviteSerialized[0] ?? null,
            'geo_liste_region' => $idgeo_liste_regionSerialized[0]->getIdRegion() ?? null,
            'geo_departement' => $idgeo_departementSerialized[0]->getLibelle() ?? null,
            'metier_metier' => $user_veille[0]->getMotsCles() ?? null,
            'handi' => unserialize($user_veille[0]->getHandicapSerialized()) ?? null,
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() ) {
            $user_veille[0]->setLibelle($form->get('nom_alerte') ? $form->get('nom_alerte')->getData() : "Mon alerte du " . date("d/m/Y"));
            $user_veille[0]->setExperienceSerialized($form->get('experience')->getData() ? serialize($form->get('experience')->getData()->getIdSituationExperience()) : null);
            $user_veille[0]->setActif("1");
            $user_veille[0]->setTypeContratSerialized($form->get('contrat') ? serialize($form->get('contrat')->getData()->getIdTypeContrat()) : null);
            $user_veille[0]->setSecteurActiviteSerialized($form->get('domaine_activite') ? serialize($form->get('domaine_activite')->getData()->getIdSecteurActivite()) : null);

            $user_veille[0]->setRegionSerialized($form->get('geo_liste_region')->getData() ? serialize($form->get('geo_liste_region')->getData()->getIdRegion()) : null);
            $user_veille[0]->setFormationSerialized(null);
            $user_veille[0]->setRemunerationSerialized(null);
            $user_veille[0]->setMotsCles($form->get('metier_metier') ? $form->get('metier_metier')->getData()->getLibelleMetier() : null);
            $user_veille[0]->setHandicapSerialized($form->get('handi') ? serialize($form->get('handi')->getData()) : serialize(1));
            $user_veille[0]->setIsRss('0');
            $user_veille[0]->setDepartementSerialized($form->get('geo_departement')->getData() ? serialize($form->get('geo_departement')->getData()->getIdDepartement()) : null);
            //$user_veille[0]->setdate_passage_cron;
            $user_veille[0]->setDateDernierEnvoi(new \DateTime("2015-11-01 00:00:00"));
            $user_veille[0]->setUserUserIdUser($id_user);
            $entityManager->persist($user_veille[0]);
            $entityManager->flush();

            return $this->redirectToRoute('app_Res_espaceCandidatprofilmail');
        }

        return $this->render('carriereRestauration/profil/modifieralertmail.html.twig', [
            'InscriptionEtape3Form' => $form->createView(),
            'idregion'=>$idgeo_liste_regionSerialized[0]->getIdRegion(),
            'iddepartement'=>$idgeo_departementSerialized[0]->getIdDepartement(),
            'motclemetier'=>$user_veille[0]->getMotsCles(),

        ]);
    }

    /**
     * @Route("/Res/espaceCandidat/alertmail_delete", name="alertmail_delete", methods={"POST"})
     */
    public function alertmail_delete(Request $request,EntityManagerInterface $entityManager, ManagerRegistry $doctrine, CvCvRepository $cvCvRepository)
    {
        if ($this->getUser()) {
            $id_user = $this->getUser()->getIdUser();
        } else {

            return $this->redirectToRoute('app_Res_accueil');
        }
        $idalert = $request->request->get('idalert'); // Retrieve cv.id from the form submission

        $user_veille = $doctrine->getRepository(UserOffreVeille::class)->findBy(['id_user_offre_veille' => $idalert]);

        if ($user_veille === null) {
            // Handle the case where cv.id is not provided or invalid
            // You may want to redirect to an error page or handle it differently based on your requirements
            return $this->redirectToRoute('app_Res_accueil');
        }


        $entityManager->remove($user_veille[0]);
        $entityManager->flush();

        return $this->redirectToRoute('app_Res_espaceCandidatprofilmail');
    }

}
