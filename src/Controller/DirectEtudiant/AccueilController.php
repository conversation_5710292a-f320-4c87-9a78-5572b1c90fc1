<?php

namespace App\Controller\DirectEtudiant;

use App\Dto\ArticlesListDto;
use App\Entity\AdminEditArticle;
use App\Entity\ArticleForAllSiteDirectEmploi;
use App\Entity\Conseil;
use App\Entity\EditArticle;
use App\Entity\EditImageAccueil;
use App\Entity\EditTextAll;
use App\Entity\EntrEntreprise;
use App\Entity\GeoDepartement;
use App\Entity\GeoListePays;
use App\Entity\GeoListeRegion;
use App\Entity\GeoVille;
use App\Entity\GeoVillePrincipale;
use App\Entity\MetierMetier;
use App\Entity\OffDuMomentsSites;
use App\Entity\OffOffre;
use App\Form\carriereBTP\CandidatOffreELKRechercheBTPType;
use App\Repository\ArticleForAllSiteDirectEmploiRepository;
use App\Repository\EntrEntrepriseRepository;
use App\Repository\GeoAdresseRepository;
use App\Repository\OffOffreRepository;
use App\Service\ELKOffOffreService;
use App\Service\EntrEntrepriseService;
use App\Twig\AppExtension;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Elastic\Elasticsearch\ClientBuilder;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Component\HttpFoundation\JsonResponse;

class AccueilController extends AbstractController
{
    private $esClient;
    private $indexName = "geo_directemploi";
    private $indexNameMetier = 'metier_directemploi';
    private $indexOffres = 'direct-emploi-dev2';

    public function __construct()
    {
        $this->esClient = ClientBuilder::create()
            ->setHosts(["https://direct-emploi.es.eu-west-3.aws.elastic-cloud.com:9243"])
            ->setBasicAuthentication("direct-dev", "eiCeila7seesh0Ushait")
            ->build();
    }
    /**
     * @Route("/DE/api/metier-search", name="api_metier_DE_search", methods={"GET"})
     */
    public function metierSearch(Request $request): JsonResponse
    {
        // Get the search term from the request
        $key = trim($request->query->get('key', ''));

        // If the key is empty, return an empty response
        if (empty($key)) {
            return new JsonResponse([]);
        }

        // Normalize the key (accents, tirets, espaces)
        $normalizedKey = $this->normalizeSearchKey($key);

        // Build the Elasticsearch query
        $params = [
            'index' => $this->indexNameMetier,
            'body' => [
                'query' => [
                    'bool' => [
                        'should' => [
                            [
                                'match_phrase_prefix' => [
                                    'libelle_metier' => [
                                        'query' => $key,
                                        'max_expansions' => 50
                                    ]
                                ]
                            ],
                            [
                                'match' => [
                                    'libelle_metier' => [
                                        'query' => $normalizedKey,
                                        'fuzziness' => 'AUTO',
                                        'operator' => 'and'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'size' => 1000
            ]
        ];

        // Execute the Elasticsearch query
        $response = $this->esClient->search($params);

        // Extract the results
        $data = array_filter(array_map(function ($hit) {
            return isset($hit['_source']['libelle_metier']) ? [
                'libelle_metier' => $hit['_source']['libelle_metier']
            ] : null;
        }, $response['hits']['hits']));

        return new JsonResponse($data);
    }
    /**
     * @Route("/DE/api/location-search", name="api_location_DE_search", methods={"GET"})
     */
    public function locationSearch(Request $request): JsonResponse
    {
        $key = trim($request->query->get('key', ''));

        if (empty($key)) {
            return new JsonResponse([['libelle' => 'France', 'type' => 'pays', 'code_postal' => '']]);
        }

        $normalizedKey = $this->normalizeSearchKey($key);

        $params = [
            'index' => $this->indexName,
            'body' => [
                'query' => [
                    'bool' => [
                        'should' => [
                            // Recherche par libellé
                            [
                                'match_phrase_prefix' => [
                                    'libelle' => [
                                        'query' => $key,
                                        'max_expansions' => 50
                                    ]
                                ]
                            ],
                            [
                                'match' => [
                                    'libelle' => [
                                        'query' => $normalizedKey,
                                        'fuzziness' => 'AUTO',
                                        'operator' => 'and'
                                    ]
                                ]
                            ],
                            // Recherche par code_insee pour les départements
                            [
                                'bool' => [
                                    'must' => [
                                        ['term' => ['type' => 'departement']],
                                        ['term' => ['code_insee.keyword' => $key]]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'size' => 1000
            ]
        ];

        $response = $this->esClient->search($params);

        $data = array_filter(array_map(function ($hit) {
            $source = $hit['_source'];

            return isset($source['libelle'], $source['type']) ? [
                'libelle' => $this->removeAccents($source['libelle']), // <- ICI je normalise
                'type' => $source['type'],
                'code_insee' => $source['code_insee'] ?? '',
            ] : null;
        }, $response['hits']['hits']));

        array_unshift($data, ['libelle' => 'France', 'type' => 'pays', 'code_insee' => '']);

        return new JsonResponse($data);
    }

    /**
     * Nettoie une chaîne de caractères en retirant les accents.
     */
    private function removeAccents(string $str): string
    {
        return iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $str);
    }



    /**
     * Normalise un mot-clé pour améliorer les correspondances
     */
    private function normalizeSearchKey(string $key): string
    {
        $key = strtolower($key);
        $key = iconv('UTF-8', 'ASCII//TRANSLIT', $key); // Remove accents
        $key = str_replace(['-', '_'], ' ', $key);      // Replace dashes/underscores by spaces
        $key = preg_replace('/\s+/', ' ', $key);        // Clean multiple spaces
        return trim($key);
    }
    #[Route('/DE/home', name: 'app_DE_accueil')]
    public function index(ArticleForAllSiteDirectEmploiRepository $allSiteDirectEmploiRepository,ManagerRegistry $doctrine, Request $request,Connection $connection, OffOffreRepository $offOffreRepository,EntrEntrepriseRepository $entrEntrepriseRepository, CacheInterface $cache): Response
    {

        $searchForm = $this->createForm(CandidatOffreELKRechercheBTPType::class);
        $formView = $searchForm->createView();

        $carrouselItems = $connection->fetchAllAssociative("SELECT * FROM carrousselForAllSiteDirectEmploi WHERE site LIKE '%2%' ORDER BY RAND() ");

        //$nb_offres = $offOffreRepository->getNbOffresBTP();
        //$nb_offres = $offOffreRepository->getNbOffresStage2();
        $nb_offres = $this->countOffresSecteurs();

        $tabofIdArticles= $allSiteDirectEmploiRepository->findBy(['site' => 2]);

        $editArticleClass = $doctrine->getRepository(EditArticle::class);
        $articles = $editArticleClass->getArticlesTrieSpecialAlterEtud();
        $articles = $editArticleClass->listToEntity($articles);
        $articlesDto = [];

        foreach ($articles as $article) {
            if ($article->getIdArticle() == $tabofIdArticles[0]->getIdArticle() || $article->getIdArticle() == $tabofIdArticles[1]->getIdArticle() || $article->getIdArticle() == $tabofIdArticles[2]->getIdArticle()) {
                $articlesDto[] = new ArticlesListDto(
                    $article->getIdArticle(),
                    $article->getTitre(),
                    $article->getImagePetite(),
                    $article->getChapo()
                );
            }
        }
        /*
                //AutoComplete localization
                $key = $request->query->get('term');
                $dataPays = $doctrine->getRepository(GeoListePays::class)->FindPaysForSearchBTP("France");
                $dataReg = $doctrine->getRepository(GeoListeRegion::class)->FindRegionForSearchBTP($key);
                $dataDep = $doctrine->getRepository(GeoDepartement::class)->FindDepratementForSearchBTP($key);
                $dataVille = $doctrine->getRepository(GeoVille::class)->FindVilleForSearchBTP($key);
                $combinedData = array_merge(
                    $this->transformData($dataPays, 'pays'),
                    $this->transformData($dataReg, 'region'),
                    $this->transformData($dataDep, 'departement'),
                    $this->transformData($dataVille, 'ville')
                );
                $resultArray = array_values(array_unique($combinedData, SORT_REGULAR));


                //AutoComplete Metier
                $key = $request->query->get('term');
                $metier=$doctrine->getRepository(MetierMetier::class)->findMetierAutoCompleteDirectEtudiant($key);
                $metierTransform=$this->transformDataMetierDirectEtudiant($metier,'Metier');
                $resultArrayMetier = array_values(array_unique($metierTransform, SORT_REGULAR));
        */
        // Récupérer les offres du moment pour le site 8 (Direct Étudiant)
        $offDuMoments = $doctrine->getRepository(OffDuMomentsSites::class)->findOneBy(['site' => 2]);
        // Récupérer les données de meta_data
        $metaData = $offDuMoments ? $offDuMoments->getMetaData() : [];

        $entreprisesBTP = $connection->fetchAllAssociative("
            SELECT id_entreprise,image 
            FROM OffreDuMomentsForAllSiteDirectEmploi 
            WHERE site LIKE '%\"2\"%'
        ");
        $idsEntreprisesBTP = array_column($entreprisesBTP, 'id_entreprise');
        $ImagesEntreprisesBTP = array_column($entreprisesBTP, 'image');
        // Filtrer les offres pour chaque entreprise spécifique
        $Company1 = array_filter($metaData, fn($offre) => $offre['id_entreprise'] === $idsEntreprisesBTP[0]);
        $Company2 = array_filter($metaData, fn($offre) => $offre['id_entreprise'] === $idsEntreprisesBTP[1]);
        $Company3 = array_filter($metaData, fn($offre) => $offre['id_entreprise'] === $idsEntreprisesBTP[2]);
        $Company4 = array_filter($metaData, fn($offre) => $offre['id_entreprise'] === $idsEntreprisesBTP[3]);
        $Company5 = array_filter($metaData, fn($offre) => $offre['id_entreprise'] === $idsEntreprisesBTP[4]);
        $Company6 = array_filter($metaData, fn($offre) => $offre['id_entreprise'] === $idsEntreprisesBTP[5]);



        //Offre

        $array_secteurs = array(
            array('1-20', 'Achat / Logistique / Transport'),
            array('30-15', 'Administratif / Secrétariat / Public'),
            array('29', 'Agriculture / Agronomie / Alimentaire'),
            array('38', 'Aide à la personne / Social'),
            array('3', 'Banque / Assurance / Mutuelle'),
            array('4-12', 'BTP / Environnement / Génie civil'),
            array('5', 'Chimie / Matériaux / Plasturgie'),
            array('16-47-48', 'Commerces / Grande distribution / Retail'),
            array('6-13', 'Commercial / Vente / Export'),
            array('8', 'Conseil / Organisation / Stratégie'),
            array('11-27', "Electronique / Electricité / Télécom"),
            array('14-32', "Finance / Audit / Comptabilité"),
            array('17', 'Hôtellerie / Restauration'),
            array('46-18', 'Immobilier / Urbanisme / Architecture'),
            array('2-39-24-23', 'Industrie / Qualité / Sécurité / Auto'),
            array('19-10', "Informatique / Multimédia / Internet"),
            array('21-7-45-44', 'Marketing / Communication / Médias'),
            array('22', 'Production / Mécanique / Maintenance'),
            array('25-9', "RH / Formation / Juridique / Fiscalité"),
            array('26', "Santé / Paramédical / Pharmacie"),
            array('28', 'Tourisme / Loisirs / Sport / Culture'),
        );
        $listeRegions = $doctrine->getRepository(GeoListeRegion::class)->getOffresParRegionAll();

        $listeDepartements = [
            'Ain (01)', 'Aisne (02)', 'Allier (03)', 'Alpes-de-Haute-Provence (04)',
            'Alpes-Maritimes (06)', 'Ardèche (07)', 'Ardennes (08)', 'Ariège (09)',
            'Aube (10)', 'Aude (11)', 'Aveyron (12)', 'Bas-Rhin (67)',
            'Bouches-du-Rhône (13)', 'Calvados (14)', 'Cantal (15)', 'Charente (16)',
            'Charente-Maritime (17)', 'Cher (18)', 'Corrèze (19)', 'Corse (20)',
            'Côte-d\'Or (21)', 'Côtes-d\'Armor (22)', 'Creuse (23)', 'Deux-Sèvres (79)',
            'Dordogne (24)', 'Doubs (25)', 'Drôme (26)', 'Essonne (91)',
            'Eure (27)', 'Eure-et-Loir (28)', 'Finistère (29)', 'Gard (30)',
            'Gers (32)', 'Gironde (33)', 'Guadeloupe (971)', 'Guyane française (973)',
            'Haut-Rhin (68)', 'Haute-Corse (222)', 'Haute-Garonne (31)', 'Haute-Loire (43)',
            'Haute-Marne (52)', 'Haute-Saône (70)', 'Haute-Savoie (74)', 'Haute-Vienne (87)',
            'Hautes-Alpes (05)', 'Hautes-Pyrénées (65)', 'Hauts-de-Seine (92)', 'Herault (34)',
            'Ille-et-Vilaine (35)', 'Indre (36)', 'Indre-et-Loire (37)', 'Isère (38)',
            'Jura (39)', 'Landes (40)', 'Loir-et-Cher (41)', 'Loire (42)',
            'Loire-Atlantique (44)', 'Loiret (45)', 'Lot (46)', 'Lot-et-Garonne (47)',
            'Lozère (48)', 'Maine-et-Loire (49)', 'Manche (50)', 'Marne (51)',
            'Martinique (972)', 'Mayenne (53)', 'Mayotte (976)', 'Meurthe-et-Moselle (54)',
            'Meuse (55)', 'Morbihan (56)', 'Moselle (57)', 'Nièvre (58)',
            'Nord (59)', 'Nouvelle-Calédonie (988)', 'Oise (60)', 'Orne (61)',
            'Paris (75)', 'Pas-de-Calais (62)', 'Polynésie française (987)', 'Puy-de-Dôme (63)',
            'Pyrénées-Atlantiques (64)', 'Pyrénées-Orientales (66)', 'Reunion (974)',
            'Rhône (69)', 'Saône-et-Loire (71)', 'Sarthe (72)', 'Savoie (73)',
            'Seine-et-Marne (77)', 'Seine-Maritime (76)', 'Seine-Saint-Denis (93)',
            'Somme (80)', 'Tarn (81)', 'Tarn-et-Garonne (82)', 'Territoire de Belfort (90)',
            'Val-d\'Oise (95)', 'Val-de-Marne (94)', 'Var (83)', 'Vaucluse (84)',
            'Vendée (85)', 'Vienne (86)', 'Vosges (88)', 'Yonne (89)', 'Yvelines (78)'
        ];
        $listeVilles = $doctrine->getRepository(GeoVillePrincipale::class)->findBy(
            ['homepage' => '1'],
            ['libelle' => 'ASC']
        );
        // $listeMetiers = $doctrine->getRepository(MetierMetier::class)->getMetierAccueilBTP();
        $CP = $doctrine->getRepository(GeoVillePrincipale::class)->getCodePostal();


        // Publicité
        $listentreprisepublicite = [
            "40933",
            "49545",
            "27624",
            "52124",
            "4",
            "55291",
            "36524",
            "16129"
        ];

        $publicites = [];

        for ($i = 0; $i < sizeof($listentreprisepublicite); $i++) {
            $publicite = $entrEntrepriseRepository->getOneEntrepriseByIdPublicite($listentreprisepublicite[$i]);

            // Vérifier si $publicite n'est pas null avant de l'ajouter à $publicites
            if ($publicite !== null) {
                // Ajouter $publicite à la fin du tableau $publicites
                array_push($publicites, $publicite);
            }
        }



        $response = $this->render('DirectEtudiant/accueil/index.html.twig', [
            'searchForm' => $formView,
            'articles' => $articlesDto,
            'nb_offres' => $nb_offres,
            //'combinedData' => $resultArray,
            //'combinedDataMetier' => $resultArrayMetier,
            'Company1' => $Company1,
            'Company2' => $Company2,
            'Company3' => $Company3,
            'Company4' => $Company4,
            'Company5' => $Company5,
            'Company6' => $Company6,
            'ImgCompany1' => $ImagesEntreprisesBTP[0],
            'ImgCompany2' => $ImagesEntreprisesBTP[1],
            'ImgCompany3' => $ImagesEntreprisesBTP[2],
            'ImgCompany4' => $ImagesEntreprisesBTP[3],
            'ImgCompany5' => $ImagesEntreprisesBTP[4],
            'ImgCompany6' => $ImagesEntreprisesBTP[5],
            'boites' => $carrouselItems,
            'listeOffres' => $array_secteurs,
            'listeRegions' => $listeRegions,
            'listeDepartements' => $listeDepartements,
            'listeVilles' => $listeVilles,
            'codePostal' => $CP,
            'publicites' => $publicites,
            'path' => $request->attributes->get('path'),
        ]);
        $response->setSharedMaxAge(3600);
        return $response;
    }
    public function countOffresSecteurs()
    {
        $params = [
            'index' => $this->indexOffres,
            'body'  => [
                'query' => [
                    'bool' => [
                        'must' => [
                            ['term' => ['statut' => 1]],
                            ['terms' => ['id_type_contrat' => [6, 8]]]
                        ]
                    ]
                ]
            ]
        ];

        $response = $this->esClient->count($params);
        return $response['count'];
    }
    private function transformDataMetierDirectEtudiant($entities, $type)
    {
        return array_map(function ($entity) use ($type) {
            return ['libelle_metier' => $entity->getLibelleMetier()];
        }, $entities);
    }
    private function transformData($entities, $type)
    {
        return array_map(function ($entity) use ($type) {
            return ['libelle' => $entity->getLibelle(), 'type' => $type];
        }, $entities);
    }

    #[Route('DE/entreprises', name: 'app_DE_entreprises')]
    public function entreprises(EntrEntrepriseService $entrEntrepriseService,ManagerRegistry $doctrine, Request $request, EntrEntrepriseRepository $entrEntrepriseRepository): Response
    {
        $entreprise = $doctrine->getRepository(EntrEntreprise::class)->entrepriseListeEntreprisesEtudiant($request->query->get('lettre'));

        // Liste d'entreprise
        $nbrEntreprise = $doctrine->getRepository(EntrEntreprise::class)->entrepriseListeEntreprisesEtudiant('');
        $entreprise_pagine = $entrEntrepriseService->entrepriseListeEntreprisesPaginé($entreprise);


        // Liste de lettres
        $ListeLettresAlpha = array('0-9');
        $lettre = 'A';
        for($j=0; $j<26; $j++){
            $ListeLettresAlpha[] = $lettre++;
        }

        $adminArticles = $doctrine->getRepository(AdminEditArticle::class);
        $adminArticlesID = $adminArticles->getAdminArticles(7);
        $articles_id = array (
            $adminArticlesID["first"],
            $adminArticlesID["second"],
            $adminArticlesID["third"]
        );
        //$articles_id = array(3094, 3095, 3096); //    tests en local
        $articles = $doctrine->getRepository(EditArticle::class)->getArticleWithTheme($articles_id);

        $breadcrumb = [
            ["link"=> $this->generateUrl("app_DE_accueil", [], UrlGeneratorInterface::ABSOLUTE_URL), "title" => "Accueil"],
            ["link" => $request->getUri(), "title" => "Entreprises"]
        ];
        $entreprisesParPage = 10; // ou tout autre nombre approprié

// Calculez le nombre total de pages
        $nombreTotalPages = ceil(count($nbrEntreprise) / $entreprisesParPage);
        return $this->render('DirectEtudiant/accueil/new_entreprises.html.twig', [
            'listeLettresAlpha' => $ListeLettresAlpha,
            'entreprises' => $entreprise_pagine,
            'nbrEntreprise' => count($nbrEntreprise),
            'articles' => $articles,
            'breadcrumb' => $breadcrumb,
            'nombre_total_pages' => $nombreTotalPages
        ]);
    }

    #[Route('DE/entreprisesDetail/{id}/{slug}', name: 'app_DE_entreprisesDetails')]
    public function entreprisesDetails(
        ManagerRegistry $doctrine,
        ELKOffOffreService $elkOffOffreService,
        Request $request,
        int $id,
        EntrEntrepriseRepository $entrEntrepriseRepository,
        GeoAdresseRepository $geoAdresseRepository,
        EntrEntrepriseService $entrEntrepriseService

    ): Response {
        $entreprise = $entrEntrepriseRepository->find($id);
        $adresse = $geoAdresseRepository->find($entreprise->getGeoAdresseIdAdresse());

        // Récupérer le numéro de page
        $page = $request->query->getInt('page', 1); // Utilisez $page de la requête

        // Appeler la méthode avec la pagination Knp
        $offresSelonEntreprise = $entrEntrepriseService->entrepriseListeOffres($id);
        // Fetch count of offers
        $count = $entrEntrepriseRepository->getCountOffersByEntreprise($id);
        $count = $count[0]['nb_offres'];
        $breadcrumb = [
            ["link" => $this->generateUrl("app_BTP_accueil", [], UrlGeneratorInterface::ABSOLUTE_URL), "title" => "Accueil"],
            ["link" => $this->generateUrl("app_BTP_entreprises", [], UrlGeneratorInterface::ABSOLUTE_URL), "title" => "Entreprises"],
            ["link" => $request->getUri(), "title" => $entreprise->getRaisonSociale()]
        ];

        return $this->render('DirectEtudiant/accueil/entreprisesDetails.html.twig', [
            'breadcrumb' => $breadcrumb,
            'entreprise' => $entreprise,
            'adresse' => $adresse,
            'offres' => $offresSelonEntreprise,
            'totalCountWithStatusOne' => $count
        ]);
    }

    #[Route('DE/actualites', name: 'app_DE_actualites')]
    public function actualites( ManagerRegistry $doctrine, PaginatorInterface $paginatorInterface, RequestStack $requestStack, Request $request): Response
    {
        $editArticleClass = $doctrine->getRepository(EditArticle::class);

        if ($mot_cle = $request->request->get("mot_cle") or $mot_cle = $request->query->get("mot_cle")) {
            $articles = array();
            $article_mot_cles = $editArticleClass->findArticlesMotCle($mot_cle,2);
            for ($i = 0; $i < count($article_mot_cles); $i++)
                $articles[$i] = $editArticleClass->findArticlesByMotCle($article_mot_cles[$i])[0];
            $articles = $editArticleClass->listToEntity($articles);
        } else {
            $articles = $editArticleClass->getArticlesTrieSpecialDirectEtudiant();
            $articles = $editArticleClass->listToEntity($articles);
        }
        $limit = 24;
        $count = count($articles);
        $request_stack = $requestStack->getMainRequest();
        $page = $request_stack->query->getInt('page', 1);

        $articlesDto = [];
        foreach ($articles as $article) {
            $articlesDto[] = new ArticlesListDto($article->getIdArticle(), $article->getTitre(), $article->getImagePetite(), $article->getChapo());
        }
        $theme = ($mot_cle !== null) ? $mot_cle : "emploi";

        $article_pagine = $paginatorInterface->paginate($articlesDto, $page, $limit);
        return $this->render('DirectEtudiant/accueil/actualites.html.twig', [
            'articlesDTO' => $article_pagine,
            'theme_recherche' => $modified_string = str_replace(array("_", "-", " ", "."), " ", $theme),
            'count' => $count,
        ]);
    }
    #[Route('DE/actualitesdetails/{id_article}/{nom_article}', name: 'app_DE_articles_details')]
    public function actualitesdetails(ArticleForAllSiteDirectEmploiRepository $allSiteDirectEmploiRepository,ManagerRegistry $doctrine, EntityManagerInterface $entityManager, int $id_article, $nom_article): Response
    {
        /**
         * @var EditArticle $article
         */
        $article = $doctrine->getRepository(EditArticle::class)->findBy(
            ['id_article' => $id_article]
        )[0];


        $article_contenu = $article->getContenu();
        $tabofIdArticles = $allSiteDirectEmploiRepository->findBy(['site' => 2]);
        $idsToExclude = array_map(
    fn(ArticleForAllSiteDirectEmploi $a) => $a->getIdArticle(),
    $tabofIdArticles
);

// 2. On y ajoute l’ID de l’article courant
$idsToExclude[] = $id_article;
        $start = new \DateTime('2024-01-01 00:00:00');
        $end   = new \DateTime('2025-12-31 23:59:59');
        $qb = $doctrine
            ->getRepository(EditArticle::class)
            ->createQueryBuilder('a')
            ->where('a.site = :site')
            ->andWhere('a.id_article NOT IN (:ids)')
            ->andWhere('a.date_creation BETWEEN :start AND :end')
            ->setParameters([
                'site'  => 2,
                'ids'   => $idsToExclude ?: [0],
                'start' => $start,
                'end'   => $end,
            ])
            ->orderBy('a.date_creation', 'DESC')
            ->setMaxResults(10);

        $allArticles = $qb->getQuery()->getResult();

        // 3. Mélange et slice pour les récents
        shuffle($allArticles);
        $recents = array_slice($allArticles, 0, 3);
        $baseUrl = AppExtension::forceHTTPS($this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL));

        $jsonLD = [
            "@context" => "https://schema.org",
            "@type" => "Article",
            "headline" => $article->getTitre(),
            "image" => [
                $baseUrl."/uploads/article/". $article->getImagePetite()
            ],
            "author"=> [["@type" => "Organization", "name" => "Direct Emploi", "logo"=> [
                "@type"=> "ImageObject",
                "width"=> "179",
                "height"=> "97",
                "url"=> $baseUrl."images/direct-emploi/new_logo.png"

            ]]],

        ];

        if($article->getDateCreation()){
            $jsonLD["dateCreated"] = $article->getDateCreation()->format('Y-m-d\TH:i:s\Z');
        }
        if($article->getDateModification()){
            $jsonLD["dateModified"] = $article->getDateModification()->format('Y-m-d\TH:i:s\Z');
        }

        //Stats pour Article
        $article->setCountStat($article->getCountStat() + 1);
        $entityManager->flush();

        $breadcrumb = [
            [
                "link" => $baseUrl,
                "title" => "Accueil"
            ],
            [
                "link" =>  $this->generateUrl("app_articles_list",  [], UrlGeneratorInterface::ABSOLUTE_URL),
                "title" => "Actualité"
            ],
            [
                "link" => $this->generateUrl("app_articles_list", [
                    "id_article"=>$id_article,
                    "nom_article"=>$nom_article,
                ], UrlGeneratorInterface::ABSOLUTE_URL),
                "title" => $article->getTitre()
            ]
        ];
        $article_contenu = str_replace("http://", "https://", $article_contenu);
        return $this->render('DirectEtudiant/accueil/actualitesdetail.html.twig', [
            'article_contenu' => $article_contenu,
            'article' => $article,
            'jsonLD' => $jsonLD,
            'recents' => $recents,
            'breadcrumb' => $breadcrumb
        ]);
    }

    #[Route('DE/conseils', name: 'app_DE_conseils')]
    public function conseils(): Response
    {
        return $this->render('DirectEtudiant/conseils/conseils.html.twig', [

        ]);
    }

    #[Route('DE/conseilId/{id}', name: 'app_DE_conseil_id')]
    public function conseilId($id, ManagerRegistry $doctrine, Request $request): Response
    {
        $conseil = $doctrine->getRepository(Conseil::class)->find($id);
        $breadcrumb = $this->getBaseBreadcrumb();
        $breadcrumb[] = $this->getConseilGroupInfo($conseil->getGroupe());
        $breadcrumb[] = [
            "link" => $request->getUri(),
            "title" => $conseil->getTitre()
        ];
        $baseUrl = AppExtension::forceHTTPS($this->generateUrl("directemploi", [], UrlGeneratorInterface::ABSOLUTE_URL));

        $jsonLD = [
            "@context" => "https://schema.org",
            "@type" => "Article",
            "headline" => $conseil->getTitre(),
            "author" => [
                [
                    "@type" => "Organization",
                    "name" => "Direct Emploi",
                    "logo" => [
                        "@type" => "ImageObject",
                        "width" => "179",
                        "height" => "97",
                        "url" => $baseUrl . "/images/direct-emploi/new_logo.png"
                    ]
                ]
            ]
        ];

        if ($conseil->getImage()) {
            $jsonLD["image"] = [
                $baseUrl . "/" . substr($conseil->getImage(), 1)
            ];
        }

        // Retrieve HTML content
        $html = $conseil->getHtml();

        // Replace 'page?id=<id>' with '/<id>'
        $html = preg_replace_callback('/<a href="page\?id=(\d+)" class="lien">/', function ($matches) {
            return '<a href="/DE/conseilId/' . $matches[1] . '" class="lien">';
        }, $html);

        // Extract the description for SEO
        preg_match('/<p class="texte"[^>]*>(.*?)<\/p>/', $html, $matches);
        $texte = $matches[1] ?? '';
        $conseil->setHtml($html);

        return $this->render('DirectEtudiant/conseils/conseildetails.html.twig', [
            'infos' => $conseil,
            'breadcrumb' => $breadcrumb,
            'jsonLD' => $jsonLD,
            'descriptionSEO' => $texte
        ]);
    }

    #[Route('DE/conseilsCV', name: 'app_DE_conseilsCV')]
    public function conseilsCV(ManagerRegistry $doctrine, Request $request): Response
    {
        $infos = $doctrine->getRepository(Conseil::class)->conseilFindByGroup(1);
        $breadcrumb = $this->getBaseBreadcrumb();
        $breadcrumb[] = $this->getConseilGroupInfo(Conseil::GROUP_CV);
        $intro = $doctrine->getRepository(EditTextAll::class)->getIntroWithID(1, 9);
        $h1Title = htmlspecialchars_decode("Nos conseils cv pour trouver un emploi");
        $pageTitle = "Nos conseils recrutement et emploi cv | Carriere DE";

        return $this->render('DirectEtudiant/conseils/conseils_cv.html.twig', [
            'is_etudiant' => false,
            'infos' => $infos,
            'breadcrumb' => $breadcrumb,
            'intro' => $intro,
            'h1Title' => $h1Title,
            'pageTitle' => $pageTitle,
        ]);
    }
    public function getBaseBreadcrumb(){
        return [
            [
                "link" =>   $this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL),
                "title" => "Accueil"
            ],
            [
                "link" =>  $this->generateUrl("app_conseil_index",  [], UrlGeneratorInterface::ABSOLUTE_URL),
                "title" => "Nos conseils emploi"
            ]
        ];
    }

    public function getConseilGroupInfo($group){
        switch ($group){
            case Conseil::GROUP_CV:
                $title =  "CV";
                $link = $this->generateUrl("app_conseil_cv", [], UrlGeneratorInterface::ABSOLUTE_URL);
                break;

            case Conseil::GROUP_LETTRE_MOTIVATION:
                $title =  "Lettre de motivation";
                $link = $this->generateUrl("app_conseil_lm", [], UrlGeneratorInterface::ABSOLUTE_URL);
                break;

            case Conseil::GROUP_ENTRETIEN_EMBAUCHE:
                $title =  "Entretien d'embauche";
                $link = $this->generateUrl("app_conseil_ee", [], UrlGeneratorInterface::ABSOLUTE_URL);
                break;

            case Conseil::GROUP_RECHERCHE_EMPLOI:
                $title =  "Recherche d'emploi";
                $link = $this->generateUrl("app_conseil_re", [], UrlGeneratorInterface::ABSOLUTE_URL);
                break;
            case Conseil::GROUP_RECHERCHE_STAGE:
                $title =  "Recherche de stage";
                $link = $this->generateUrl("app_conseil_rs", [], UrlGeneratorInterface::ABSOLUTE_URL);
                break;
        }

        return [
            "title" => $title,
            "link" => $link
        ];
    }

    #[Route('DE/conseilsembauche', name: 'app_DE_conseilsembauche')]
    public function conseilsembauche(ManagerRegistry $doctrine): Response
    {
        $breadcrumb = $this->getBaseBreadcrumb();
        $breadcrumb[] = $this->getConseilGroupInfo(Conseil::GROUP_ENTRETIEN_EMBAUCHE) ;
        $infos = $doctrine->getRepository(Conseil::class)->conseilFindByGroup(3);
        $intro = $doctrine->getRepository(EditTextAll::class)->getIntroWithID(1, 11);
        $h1Title = "Nos conseils entretien d'embauche pour trouver un emploi";
        $pageTitle = "Nos conseils recrutement et emploi entretien d'embauche | Direct Etudiant";

        return $this->render('DirectEtudiant/conseils/conseils-embauche.html.twig', [
            'is_etudiant' => false,
            'infos' => $infos,
            'intro' => $intro,
            'h1Title' => $h1Title,
            'pageTitle' => $pageTitle,
        ]);
    }
    #[Route('DE/conseilsmotivation', name: 'app_DE_conseilsmotivation')]
    public function conseilsmotivation(ManagerRegistry $doctrine): Response
    {
        $breadcrumb = $this->getBaseBreadcrumb();
        $breadcrumb[] = $this->getConseilGroupInfo(Conseil::GROUP_LETTRE_MOTIVATION) ;
        $infos = $doctrine->getRepository(Conseil::class)->conseilFindByGroup(2);
        $intro = $doctrine->getRepository(EditTextAll::class)->getIntroWithID(1, 10);
        $h1Title = "Nos conseils lettre de motivation pour trouver un emploi";
        $pageTitle = "Nos conseils recrutement et emploi lettre de motivation | Direct Etudiant";

        return $this->render('DirectEtudiant/conseils/conseils-motivation.html.twig', [
            'is_etudiant' => false,
            'infos' => $infos,
            'intro' => $intro,
            'h1Title' => $h1Title,
            'pageTitle' => $pageTitle,
        ]);
    }
    #[Route('DE/conseilsrechercheEmploi', name: 'app_DE_conseilsrechercheEmploi')]
    public function rechercheEmploi(ManagerRegistry $doctrine): Response
    {
        $breadcrumb = $this->getBaseBreadcrumb();
        $breadcrumb[] = $this->getConseilGroupInfo(Conseil::GROUP_RECHERCHE_EMPLOI) ;
        $infos = $doctrine->getRepository(Conseil::class)->conseilFindByGroup(4);
        $intro = $doctrine->getRepository(EditTextAll::class)->getIntroWithID(1, 4);
        $h1Title = "Nos conseils recherche d'emploi pour trouver un emploi";
        $pageTitle = "Nos conseils recrutement et emploi recherche d'emploi | Direct Etudiant";

        return $this->render('DirectEtudiant/conseils/conseils-rechercheemploi.html.twig', [
            'is_etudiant' => false,
            'infos' => $infos,
            'intro' => $intro,
            'h1Title' => $h1Title,
            'pageTitle' => $pageTitle,
        ]);
    }

    #[Route('DE/quisommenous', name: 'app_DE_quisommenous')]
    public function quisommenous(): Response
    {
        return $this->render('DirectEtudiant/footerpages/quisommenous.html.twig', [

        ]);
    }
    #[Route('DE/informationlegal', name: 'app_DE_informationlegal')]
    public function informationlegal(): Response
    {
        return $this->render('DirectEtudiant/footerpages/informationlegal.html.twig', [

        ]);
    }

    #[Route('DE/confidentialite', name: 'app_DE_confidentialite')]
    public function confidentialite(): Response
    {
        return $this->render('DirectEtudiant/footerpages/politique.html.twig', [

        ]);
    }
    #[Route('DE/chartecookies', name: 'app_DE_chartecookies')]
    public function chartecookies(): Response
    {
        return $this->render('DirectEtudiant/footerpages/charte-cookies.html.twig', [

        ]);
    }
    #[Route('DE/plansite', name: 'app_DE_plansite')]
    public function plansite(): Response
    {
        return $this->render('DirectEtudiant/footerpages/plansite.html.twig', [

        ]);
    }
    #[Route('DE/contact', name: 'app_DE_contact')]
    public function contact(Request $request, MailerInterface $mailer): Response
    {
        if ($request->isMethod('POST')) {
            // Récupérer les données du formulaire
            $sujet = $request->request->get('sujet');
            $adresse = $request->request->get('adresse');
            $message = $request->request->get('message');

            // Validation des champs
            if (empty($sujet) || empty($adresse) || empty($message)) {
                return new JsonResponse(['error' => 'Tous les champs sont obligatoires.'], 400);
            }

            // Créer et envoyer l'email
            try {
                $email = (new Email())
                    ->from('<EMAIL>')
                    ->to('<EMAIL>')
                    ->subject('Nouveau message via le formulaire de contact')
                    ->text("Sujet : $sujet\nAdresse : $adresse\n\nMessage :\n$message");

                $mailer->send($email);

                return new JsonResponse(['success' => 'Votre message a été envoyé avec succès.']);
            } catch (\Exception $e) {
                return new JsonResponse(['error' => 'Une erreur est survenue lors de l\'envoi de l\'e-mail.'], 500);
            }
        }

        // Retourner une réponse normale pour afficher la page
        return new Response($this->renderView('DirectEtudiant/footerpages/contact.html.twig'));
    }
    #[Route('DE/societe', name: 'app_DE_societe')]
    public function societe(): Response
    {
        return $this->render('DirectEtudiant/footerpages/Societe.html.twig', [

        ]);
    }
    #[Route('DE/chartedurecrutement', name: 'app_DE_chartedurecrutement')]
    public function chartedurecrutement(): Response
    {
        return $this->render('DirectEtudiant/footerpages/chartedurecrutement.html.twig', [

        ]);
    }
    #[Route('DE/emploi-index-stage', name: 'app_DE_Parcourirlesoffresdemploi_stage')]
    public function ParcourirlesoffresdemploiDirectEtudiant(Request $request): Response
    {
        $metiers = [
            "Offre de stage Acheteur",
            "Offre de stage Administration des ventes",
            "Offre de stage Aide soignant",
            "Offre de stage Assistant de direction",
            "Offre de stage Auxiliaire de vie",
            "Offre de stage Boucher",
            "Offre de stage Boulanger",
            "Offre de stage Caissier",
            "Offre de stage Cariste",
            "Offre de stage Chargé de recrutement",
            "Offre de stage Chauffagiste",
            "Offre de stage Chauffeur",
            "Offre de stage Chef de produit",
            "Offre de stage Chef de rayon",
            "Offre de stage Collaborateur comptable",
            "Offre de stage Commercial",
            "Offre de stage Commercial immobilier",
            "Offre de stage Commercial itinérant",
            "Offre de stage Commercial terrain",
            "Offre de stage Community manager",
            "Offre de stage Comptable",
            "Offre de stage Conducteur d'engins",
            "Offre de stage Conducteur de travaux",
            "Offre de stage Conseiller en patrimoine",
            "Offre de stage Contrôleur de gestion",
            "Offre de stage Dessinateur projeteur",
            "Offre de stage Développeur JS",
            "Offre de stage Développeur web",
            "Offre de stage Éducateur",
            "Offre de stage Electricien",
            "Offre de stage Employé administratif",
            "Offre de stage Frigoriste",
            "Offre de stage Garde d'enfant",
            "Offre de stage Gestionnaire assurance",
            "Offre de stage Gestionnaire copropriété",
            "Offre de stage Gestionnaire de paie",
            "Offre de stage Infirmier",
            "Offre de stage Ingénieur commercial",
            "Offre de stage Ingénieur qualité",
            "Offre de stage Ingénieur travaux",
            "Offre de stage Intégrateur web",
            "Offre de stage Jardinier",
            "Offre de stage Logisticien",
            "Offre de stage Magasinier",
            "Offre de stage Mécanicien",
            "Offre de stage Peintre",
            "Offre de stage Plombier",
            "Offre de stage Préparateur de commandes",
            "Offre de stage Référenceur SEO",
            "Offre de stage Responsable de magasin",
            "Offre de stage Responsable restaurant",
            "Offre de stage Serveur",
            "Offre de stage Technico commercial",
            "Offre de stage Vendeur",
        ];
        $offresEmploi = [
            "Offres de stage Achat",
            "Offres de stage Logistique",
            "Offres de stage Transport",
            "Offres de stage Administratif",
            "Offres de stage Secrétariat",
            "Offres de stage Public",
            "Offres de stage Agriculture",
            "Offres de stage Agronomie",
            "Offres de stage Alimentaire",
            "Offres de stage Aide à la personne",
            "Offres de stage Social",
            "Offres de stage Banque",
            "Offres de stage Assurance",
            "Offres de stage Mutuelle",
            "Offres de stage BTP",
            "Offres de stage Environnement",
            "Offres de stage Génie civil",
            "Offres de stage Chimie",
            "Offres de stage Matériaux",
            "Offres de stage Plasturgie",
            "Offres de stage Commerces",
            "Offres de stage Grande distribution",
            "Offres de stage Retail",
            "Offres de stage Commercial",
            "Offres de stage Vente",
            "Offres de stage Export",
            "Offres de stage Dirigeant",
            "Offres de stage Postes de direction",
            "Offres de stage Electronique",
            "Offres de stage Electricité",
            "Offres de stage Télécom",
            "Offres de stage Finance",
            "Offres de stage Audit",
            "Offres de stage Comptabilité",
            "Offres de stage Hôtellerie",
            "Offres de stage Restauration",
            "Offres de stage Immobilier",
            "Offres de stage Urbanisme",
            "Offres de stage Architecture",
            "Offres de stage Industrie",
            "Offres de stage Qualité",
            "Offres de stage Sécurité",
            "Offres de stage Auto",
            "Offres de stage Informatique",
            "Offres de stage Multimédia",
            "Offres de stage Internet",
            "Offres de stage Marketing",
            "Offres de stage Communication",
            "Offres de stage Médias",
            "Offres de stage Production",
            "Offres de stage Mécanique",
            "Offres de stage Maintenance",
            "Offres de stage RH",
            "Offres de stage Formation",
            "Offres de stage Juridique",
            "Offres de stage Fiscalité",
            "Offres de stage Santé",
            "Offres de stage Paramédical",
            "Offres de stage Pharmacie",
            "Offres de stage Tourisme",
            "Offres de stage Loisirs",
            "Offres de stage Sport",
            "Offres de stage Culture"
        ];
        $breadcrumb= [[
            "link" =>   $this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_DE_Parcourirlesoffresdemploi_stage', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres de stage par métier"
        ]
        ];

        return $this->render('DirectEtudiant/Indexation/Parcourirlesoffresdemploi.html.twig', [
            'metiers' => $metiers,
            'offresEmploi'=>$offresEmploi,
            'breadcrumb'=>$breadcrumb
        ]);
    }

    #[Route('DE/emploi-index-par-ville/', name: 'app_DE_ParcourirlesoffresdemploiVille')]
    public function ParcourirlesoffresdemploiVilleDirectDE(Request $request, ManagerRegistry $doctrine): Response
    {
        $topvilles = [
            "Paris",
            "Lyon",
            "Toulouse",
            "Nantes",
            "Marseille",
            "Bordeaux",
            "Rennes",
            "Lille",
            "Strasbourg",
            "Montpellier",
            "Nice",
            "Aix-en-Provence",
            "Grenoble",
            "Dijon",
            "Annecy",
            "Angers",
            "Reims",
            "Tours",
            "Orléans",
            "Clermont-Ferrand",
            "Boulogne-Billancourt",
            "Metz",
            "Caen",
            "Brest",
            "Rouen",
            "Le Mans",
            "Nanterre",
            "Courbevoie",
            "Saint-Étienne",
            "Nancy",
            "Besançon",
            "La Rochelle"
        ];
        $breadcrumb= [[
            "link" =>   $this->generateUrl("app_DE_accueil",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_DE_ParcourirlesoffresdemploiVille', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres de stage par ville"
        ]
        ];
        $key = $request->query->get('term');
        $dataReg = $doctrine->getRepository(GeoListeRegion::class)->FindRegionForSearchBTP($key);

        $departementsByRegion = [];
        $i=0;
        foreach ($dataReg as $region) {
            $i=$i+1;
            $id_region = $region->getIdRegion();
            $libelleRegion = $region->getLibelle();
            $departements = $doctrine->getRepository(GeoDepartement::class)->FindDepratementFromRegionKey($id_region);
            $departementsByRegion[$i] = [
                'libelle' => $libelleRegion,
                'departements' => $departements
            ];
        }

        return $this->render('DirectEtudiant/Indexation/ParcourirlesoffresdemploiVille.html.twig', [
            'topvilles' => $topvilles,
            'departementsByRegions' => $departementsByRegion,
            'breadcrumb'=>$breadcrumb

        ]);
    }

    #[Route('DE/index-mot-cle/', name: 'app_DE_index_mot_cle')]
    public function index_mot_cleDE(Request $request, ManagerRegistry $doctrine): Response
    {
        $keywordSemrush0_40Avg = [
            "Offres de stage pole emploi",
            "Offres de stage luxembourg",
            "Offres de stage genève",
            "Offres de stage limoges",
            "Offres de stage lyon",
            "Offres de stage saisonnier",
            "Offres de stage toulouse",
            "Offres de stage aide soignante",
            "Offres de stage brest",
            "Offres de stage le bon coin",
            "Offres de stage marseille",
            "Offres de stage montpellier",
            "Offres de stage paris",
            "Offres de stage rennes",
            "Offres de stage strasbourg",
            "Offres de stage angers",
            "Offres de stage bordeaux",
            "Offres de stage chauffeur livreur",
            "Offres de stage clermont ferrand",
            "Offres de stage dole",
            "Offres de stage ile de france",
            "Offres de stage infirmier",
            "Offres de stage monaco",
            "Offres de stage nantes",
            "Offres de stage seniors",
            "Offres de stage sncf",
            "Offres de stage airbus",
            "Offres de stage caen",
            "Offres de stage cambrai",
            "Offres de stage canada",
            "Offres de stage chauffeur",
            "Offres de stage fle",
            "Offres de stage geneve",
            "Offres de stage leclerc",
            "Offres de stage lille",
            "Offres de stage nice",
            "Offres de stage pau",
            "Offres de stage perpignan",
            "Offres de stage petite enfance",
            "Offres de stage poitiers",
            "Offres de stage psychologue",
            "Offres de stage reims",
            "Offres de stage rouen",
            "Offres de stage amiens",
            "Offres de stage berck",
            "Offres de stage cdi",
            "Offres de stage dijon",
            "Offres de stage edf",
            "Offres de stage essonne",
            "Offres de stage fonction publique hospitaliere",
            "Offres de stage grenoble",
            "Offres de stage la rochelle",
            "Offres de stage lannion",
            "Offres de stage le mans",
            "Offres de stage librairie",
            "Offres de stage morbihan",
            "Offres de stage restauration",
            "Offres de stage temps partiel",
            "Offres de stage tours",
            "Offres de stage var",
            "Offres de stage annecy",
            "Offres de stage ash",
            "Offres de stage atsem",
            "Offres de stage bayonne",
            "Offres de stage belgique",
            "Offres de stage carrefour",
            "Offres de stage culture",
            "Offres de stage enseignement",
            "Offres de stage guadeloupe",
            "Offres de stage informatique",
            "Offres de stage la poste",
            "Offres de stage le havre",
            "Offres de stage nancy",
            "Offres de stage nimes",
            "Offres de stage rodez",
            "Offres de stage rqth",
            "Offres de stage senior",
            "Offres de stage tarn",
            "Offres de stage troyes",
            "Offres de stage vosges",
            "Offres de stage yvelines",
            "Offres de stage pole emploi",
            "Offres de stage aix en provence",
            "Offres de stage au luxembourg",
            "Offres de stage auray",
            "Offres de stage banque",
            "Offres de stage caf",
            "Offres de stage calvados",
            "Offres de stage charente maritime",
            "Offres de stage dordogne",
            "Offres de stage dunkerque",
            "Offres de stage environnement",
            "Offres de stage esthéticienne",
            "Offres de stage lausanne",
            "Offres de stage linkedin",
            "Offres de stage lorient",
            "Offres de stage mairie de paris",
            "Offres de stage martinique",
            "Offres de stage metz",
            "Offres de stage niort",
            "Offres de stage thales",
            "Offres de stage toulon",
            "Offres de stage vannes",
            "Offres de stage week end",
            "Offres de stage adecco",
            "Offres de stage air france",
            "Offres de stage ajaccio",
            "Offres de stage albi",
            "Offres de stage anpe",
            "Offres de stage ardennes",
            "Offres de stage assistante dentaire",
            "Offres de stage auxiliaire de vie",
            "Offres de stage aveyron",
            "Offres de stage bourg en bresse",
            "Offres de stage bourges",
            "Offres de stage cadre",
            "Offres de stage cadres",
            "Offres de stage chauffeur spl",
            "Offres de stage commercial",
            "Offres de stage communication",
            "Offres de stage comptable",
            "Offres de stage corse",
            "Offres de stage cpam",
            "Offres de stage education nationale",
            "Offres de stage en alternance",
            "Offres de stage gard",
            "Offres de stage herault",
            "Offres de stage jura",
            "Offres de stage lidl",
            "Offres de stage lvmh",
            "Offres de stage manche",
            "Offres de stage manpower",
            "Offres de stage mi temps",
            "Offres de stage ouest france",
            "Offres de stage pamiers",
            "Offres de stage quimper",
            "Offres de stage ressources humaines",
            "Offres de stage saint brieuc",
            "Offres de stage social",
            "Offres de stage tarbes",
            "Offres de stage pole emploi",
            "Offres de stage agen",
            "Offres de stage agent d accueil",
            "Offres de stage agent d entretien",
            "Offres de stage amazon",
            "Offres de stage assistante de direction",
            "Offres de stage associations",
            "Offres de stage auxerre",
            "Offres de stage bas rhin",
            "Offres de stage cannes",
            "Offres de stage cantal",
            "Offres de stage cap emploi",
            "Offres de stage chauffeur poids lourd",
            "Offres de stage chauffeur routier",
            "Offres de stage cholet",
            "Offres de stage colmar",
            "Offres de stage cuisinier",
            "Offres de stage decathlon",
            "Offres de stage dubai",
            "Offres de stage espagne",
            "Offres de stage fhf",
            "Offres de stage formateur",
            "Offres de stage graphiste",
            "Offres de stage haut rhin",
            "Offres de stage landes",
            "Offres de stage logistique",
            "Offres de stage lot",
            "Offres de stage maison de retraite",
            "Offres de stage maroc",
            "Offres de stage montauban",
            "Offres de stage morlaix",
            "Offres de stage mulhouse",
            "Offres de stage narbonne",
            "Offres de stage nevers",
            "Offres de stage oise",
            "Offres de stage orange",
            "Offres de stage ratp",
            "Offres de stage rh",
            "Offres de stage saint etienne",
            "Offres de stage sarthe",
            "Offres de stage secretaire medicale",
            "Offres de stage valenciennes",
            "Offres de stage vendeuse",
            "Offres de stage vente",
            "Offres de stage vichy",
            "Offres de stage 44",
            "Offres de stage 86",
            "Offres de stage a domicile",
            "Offres de stage administratif",
            "Offres de stage alpes maritimes",
            "Offres de stage angers urgent",
            "Offres de stage angouleme",
            "Offres de stage audiovisuel",
            "Offres de stage auxiliaire de puériculture",
            "Offres de stage bretagne",
            "Offres de stage bruxelles",
            "Offres de stage btp",
            "Offres de stage calais",
            "Offres de stage carcassonne",
            "Offres de stage cherbourg",
            "Offres de stage club med",
            "Offres de stage conseiller en insertion professionnelle",
            "Offres de stage draguignan",
            "Offres de stage drome",
            "Offres de stage gardien d immeuble",
            "Offres de stage gers",
            "Offres de stage gestionnaire de paie",
            "Offres de stage gironde",
            "Offres de stage haute savoie",
            "Offres de stage hotellerie et restauration",
            "Offres de stage journalisme",
            "Offres de stage journaliste",
            "Offres de stage juriste",
            "Offres de stage la roche sur yon",
            "Offres de stage laval",
            "Offres de stage leroy merlin",
            "Offres de stage les sables d olonne",
            "Offres de stage loire atlantique",
            "Offres de stage lons le saunier",
            "Offres de stage lot et garonne",
            "Offres de stage magasinier",
            "Offres de stage mayotte",
            "Offres de stage moselle",
            "Offres de stage nettoyage",
            "Offres de stage onf",
            "Offres de stage orleans",
            "Offres de stage porto vecchio",
            "Offres de stage portugal",
            "Offres de stage pour seniors",
            "Offres de stage pour seniors retraités",
            "Offres de stage roanne",
            "Offres de stage saint malo",
            "Offres de stage sport",
            "Offres de stage tahiti",
            "Offres de stage vaucluse",
            "Offres de stage vendanges",
            "Offres de stage villeneuve sur lot",
            "Offres de stage 47",
            "Offres de stage aisne",
            "Offres de stage arles",
            "Offres de stage aurillac",
            "Offres de stage avignon",
            "Offres de stage blois",
            "Offres de stage cdd",
            "Offres de stage credit agricole",
            "Offres de stage douai",
            "Offres de stage epinal",
            "Offres de stage guingamp",
            "Offres de stage haute loire",
            "Offres de stage hautes alpes",
            "Offres de stage hotesse d accueil",
            "Offres de stage indre et loire",
            "Offres de stage international",
            "Offres de stage maubeuge",
            "Offres de stage menton",
            "Offres de stage notariat",
            "Offres de stage sans diplome",
            "Offres de stage vendeur"
        ];
        $breadcrumb= [[
            "link" =>   $this->generateUrl("app_DE_accueil",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_DE_index_mot_cle', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres de stage par mots clés les plus recherchés"
        ]
        ];
        $key = $request->query->get('term');
        $dataReg = $doctrine->getRepository(GeoListeRegion::class)->FindRegionForSearchBTP($key);

        $departementsByRegion = [];
        $i=0;
        foreach ($dataReg as $region) {
            $i=$i+1;
            $id_region = $region->getIdRegion();
            $libelleRegion = $region->getLibelle(); // Assurez-vous que getLibelle est le bon nom de méthode pour obtenir le libellé de la région

            $departements = $doctrine->getRepository(GeoDepartement::class)->FindDepratementFromRegionKey($id_region);

            // Stocke les informations de la région avec les départements correspondants
            $departementsByRegion[$i] = [
                'libelle' => $libelleRegion,
                'departements' => $departements
            ];
        }

        return $this->render('DirectEtudiant/Indexation/ParcourirlesoffresMotCle.html.twig', [
            'keywords' => $keywordSemrush0_40Avg,
            'breadcrumb'=>$breadcrumb

        ]);
    }

    #[Route('DE/emploi-index/domaine/{domainename}/{idsecteur}', name: 'app_DE_ParcourirlesoffresdemploiMetierParSecteur')]
    public function ParcourirlesoffresdemploiMetierParSecteurDE(ManagerRegistry $doctrine,Request $request,$domainename,$idsecteur): Response
    {
        $listeMetiers = $doctrine->getRepository(MetierMetier::class)->getMetierIndexDirectemploi($idsecteur);
        //dd($listeMetiers);
        $breadcrumb= [[
            "link" =>   $this->generateUrl("app_DE_accueil",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_DE_index_mot_cle', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres de stage par métier"
        ]
        ];
        return $this->render('DirectEtudiant/Indexation/ParcourirlesoffresdemploiMetierParSecteur.html.twig', [
            'domainename'=>$domainename,
            'listeMetiers'=>$listeMetiers,
            'breadcrumb'=>$breadcrumb
        ]);
    }

    #[Route('DE/emploi-index-region-ville/{departementid}', name: 'app_DE_ParcourirlesoffresdemploiVilleParRegion')]
    public function ParcourirlesoffresdemploiVilleParRegionDE(Request $request, ManagerRegistry $doctrine,$departementid): Response
    {
        $dep = $doctrine->getRepository(GeoDepartement::class)->FindDepratementName($departementid);
        $dataVille = $doctrine->getRepository(GeoVille::class)->FindVilleForRegionIDSearchBTP($departementid);
        $breadcrumb= [[
            "link" =>   $this->generateUrl("app_DE_accueil",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_DE_ParcourirlesoffresdemploiVille', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres d'emploi par ville"
        ]
        ];
        return $this->render('DirectEtudiant/Indexation/ParcourirlesoffresdemploiVilleParDepartement.html.twig', [
            'dataVille' => $dataVille,
            'dep' => $dep,
            'breadcrumb'=>$breadcrumb

        ]);
    }




    // Offres en alternance

    #[Route('DE/emploi-index-alternance', name: 'app_DE_Parcourirlesoffresdemploi_alternance')]
    public function ParcourirlesoffresdemploiDirectEtudiantalternance(Request $request): Response
    {
        $metiers = [
            "Offres en alternance Acheteur",
            "Offres en alternance Administration des ventes",
            "Offres en alternance Aide soignant",
            "Offres en alternance Assistant de direction",
            "Offres en alternance Auxiliaire de vie",
            "Offres en alternance Boucher",
            "Offres en alternance Boulanger",
            "Offres en alternance Caissier",
            "Offres en alternance Cariste",
            "Offres en alternance Chargé de recrutement",
            "Offres en alternance Chauffagiste",
            "Offres en alternance Chauffeur",
            "Offres en alternance Chef de produit",
            "Offres en alternance Chef de rayon",
            "Offres en alternance Collaborateur comptable",
            "Offres en alternance Commercial",
            "Offres en alternance Commercial immobilier",
            "Offres en alternance Commercial itinérant",
            "Offres en alternance Commercial terrain",
            "Offres en alternance Community manager",
            "Offres en alternance Comptable",
            "Offres en alternance Conducteur d'engins",
            "Offres en alternance Conducteur de travaux",
            "Offres en alternance Conseiller en patrimoine",
            "Offres en alternance Contrôleur de gestion",
            "Offres en alternance Dessinateur projeteur",
            "Offres en alternance Développeur JS",
            "Offres en alternance Développeur web",
            "Offres en alternance Éducateur",
            "Offres en alternance Electricien",
            "Offres en alternance Employé administratif",
            "Offres en alternance Frigoriste",
            "Offres en alternance Garde d'enfant",
            "Offres en alternance Gestionnaire assurance",
            "Offres en alternance Gestionnaire copropriété",
            "Offres en alternance Gestionnaire de paie",
            "Offres en alternance Infirmier",
            "Offres en alternance Ingénieur commercial",
            "Offres en alternance Ingénieur qualité",
            "Offres en alternance Ingénieur travaux",
            "Offres en alternance Intégrateur web",
            "Offres en alternance Jardinier",
            "Offres en alternance Logisticien",
            "Offres en alternance Magasinier",
            "Offres en alternance Mécanicien",
            "Offres en alternance Peintre",
            "Offres en alternance Plombier",
            "Offres en alternance Préparateur de commandes",
            "Offres en alternance Référenceur SEO",
            "Offres en alternance Responsable de magasin",
            "Offres en alternance Responsable restaurant",
            "Offres en alternance Serveur",
            "Offres en alternance Technico commercial",
            "Offres en alternance Vendeur",
        ];
        $offresEmploi = [
            "Offres en alternance Achat",
            "Offres en alternance Logistique",
            "Offres en alternance Transport",
            "Offres en alternance Administratif",
            "Offres en alternance Secrétariat",
            "Offres en alternance Public",
            "Offres en alternance Agriculture",
            "Offres en alternance Agronomie",
            "Offres en alternance Alimentaire",
            "Offres en alternance Aide à la personne",
            "Offres en alternance Social",
            "Offres en alternance Banque",
            "Offres en alternance Assurance",
            "Offres en alternance Mutuelle",
            "Offres en alternance BTP",
            "Offres en alternance Environnement",
            "Offres en alternance Génie civil",
            "Offres en alternance Chimie",
            "Offres en alternance Matériaux",
            "Offres en alternance Plasturgie",
            "Offres en alternance Commerces",
            "Offres en alternance Grande distribution",
            "Offres en alternance Retail",
            "Offres en alternance Commercial",
            "Offres en alternance Vente",
            "Offres en alternance Export",
            "Offres en alternance Dirigeant",
            "Offres en alternance Postes de direction",
            "Offres en alternance Electronique",
            "Offres en alternance Electricité",
            "Offres en alternance Télécom",
            "Offres en alternance Finance",
            "Offres en alternance Audit",
            "Offres en alternance Comptabilité",
            "Offres en alternance Hôtellerie",
            "Offres en alternance Restauration",
            "Offres en alternance Immobilier",
            "Offres en alternance Urbanisme",
            "Offres en alternance Architecture",
            "Offres en alternance Industrie",
            "Offres en alternance Qualité",
            "Offres en alternance Sécurité",
            "Offres en alternance Auto",
            "Offres en alternance Informatique",
            "Offres en alternance Multimédia",
            "Offres en alternance Internet",
            "Offres en alternance Marketing",
            "Offres en alternance Communication",
            "Offres en alternance Médias",
            "Offres en alternance Production",
            "Offres en alternance Mécanique",
            "Offres en alternance Maintenance",
            "Offres en alternance RH",
            "Offres en alternance Formation",
            "Offres en alternance Juridique",
            "Offres en alternance Fiscalité",
            "Offres en alternance Santé",
            "Offres en alternance Paramédical",
            "Offres en alternance Pharmacie",
            "Offres en alternance Tourisme",
            "Offres en alternance Loisirs",
            "Offres en alternance Sport",
            "Offres en alternance Culture"
        ];
        $breadcrumb= [[
            "link" =>   $this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_DE_Parcourirlesoffresdemploi_stage', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres en alternance par métier"
        ]
        ];

        return $this->render('DirectEtudiant/IndexationAlternance/Parcourirlesoffresdemploi.html.twig', [
            'metiers' => $metiers,
            'offresEmploi'=>$offresEmploi,
            'breadcrumb'=>$breadcrumb
        ]);
    }

    #[Route('DE/emploi-index-par-ville-alternance/', name: 'app_DE_ParcourirlesoffresdemploiVille_alternance')]
    public function ParcourirlesoffresdemploiVilleDirectDEalternance(Request $request, ManagerRegistry $doctrine): Response
    {
        $topvilles = [
            "Paris",
            "Lyon",
            "Toulouse",
            "Nantes",
            "Marseille",
            "Bordeaux",
            "Rennes",
            "Lille",
            "Strasbourg",
            "Montpellier",
            "Nice",
            "Aix-en-Provence",
            "Grenoble",
            "Dijon",
            "Annecy",
            "Angers",
            "Reims",
            "Tours",
            "Orléans",
            "Clermont-Ferrand",
            "Boulogne-Billancourt",
            "Metz",
            "Caen",
            "Brest",
            "Rouen",
            "Le Mans",
            "Nanterre",
            "Courbevoie",
            "Saint-Étienne",
            "Nancy",
            "Besançon",
            "La Rochelle"
        ];
        $breadcrumb= [[
            "link" =>   $this->generateUrl("app_DE_accueil",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_DE_ParcourirlesoffresdemploiVille', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres en alternance par ville"
        ]
        ];
        $key = $request->query->get('term');
        $dataReg = $doctrine->getRepository(GeoListeRegion::class)->FindRegionForSearchBTP($key);

        $departementsByRegion = [];
        $i=0;
        foreach ($dataReg as $region) {
            $i=$i+1;
            $id_region = $region->getIdRegion();
            $libelleRegion = $region->getLibelle();
            $departements = $doctrine->getRepository(GeoDepartement::class)->FindDepratementFromRegionKey($id_region);
            $departementsByRegion[$i] = [
                'libelle' => $libelleRegion,
                'departements' => $departements
            ];
        }

        return $this->render('DirectEtudiant/IndexationAlternance/ParcourirlesoffresdemploiVille.html.twig', [
            'topvilles' => $topvilles,
            'departementsByRegions' => $departementsByRegion,
            'breadcrumb'=>$breadcrumb

        ]);
    }

    #[Route('DE/index-mot-cle-alternance/', name: 'app_DE_index_mot_cle_alternance')]
    public function index_mot_cleDEalternance(Request $request, ManagerRegistry $doctrine): Response
    {
        $keywordSemrush0_40Avg = [
            "Offres en alternance pole emploi",
            "Offres en alternance luxembourg",
            "Offres en alternance genève",
            "Offres en alternance limoges",
            "Offres en alternance lyon",
            "Offres en alternance saisonnier",
            "Offres en alternance toulouse",
            "Offres en alternance aide soignante",
            "Offres en alternance brest",
            "Offres en alternance le bon coin",
            "Offres en alternance marseille",
            "Offres en alternance montpellier",
            "Offres en alternance paris",
            "Offres en alternance rennes",
            "Offres en alternance strasbourg",
            "Offres en alternance angers",
            "Offres en alternance bordeaux",
            "Offres en alternance chauffeur livreur",
            "Offres en alternance clermont ferrand",
            "Offres en alternance dole",
            "Offres en alternance ile de france",
            "Offres en alternance infirmier",
            "Offres en alternance monaco",
            "Offres en alternance nantes",
            "Offres en alternance seniors",
            "Offres en alternance sncf",
            "Offres en alternance airbus",
            "Offres en alternance caen",
            "Offres en alternance cambrai",
            "Offres en alternance canada",
            "Offres en alternance chauffeur",
            "Offres en alternance fle",
            "Offres en alternance geneve",
            "Offres en alternance leclerc",
            "Offres en alternance lille",
            "Offres en alternance nice",
            "Offres en alternance pau",
            "Offres en alternance perpignan",
            "Offres en alternance petite enfance",
            "Offres en alternance poitiers",
            "Offres en alternance psychologue",
            "Offres en alternance reims",
            "Offres en alternance rouen",
            "Offres en alternance amiens",
            "Offres en alternance berck",
            "Offres en alternance cdi",
            "Offres en alternance dijon",
            "Offres en alternance edf",
            "Offres en alternance essonne",
            "Offres en alternance fonction publique hospitaliere",
            "Offres en alternance grenoble",
            "Offres en alternance la rochelle",
            "Offres en alternance lannion",
            "Offres en alternance le mans",
            "Offres en alternance librairie",
            "Offres en alternance morbihan",
            "Offres en alternance restauration",
            "Offres en alternance temps partiel",
            "Offres en alternance tours",
            "Offres en alternance var",
            "Offres en alternance annecy",
            "Offres en alternance ash",
            "Offres en alternance atsem",
            "Offres en alternance bayonne",
            "Offres en alternance belgique",
            "Offres en alternance carrefour",
            "Offres en alternance culture",
            "Offres en alternance enseignement",
            "Offres en alternance guadeloupe",
            "Offres en alternance informatique",
            "Offres en alternance la poste",
            "Offres en alternance le havre",
            "Offres en alternance nancy",
            "Offres en alternance nimes",
            "Offres en alternance rodez",
            "Offres en alternance rqth",
            "Offres en alternance senior",
            "Offres en alternance tarn",
            "Offres en alternance troyes",
            "Offres en alternance vosges",
            "Offres en alternance yvelines",
            "Offres en alternance pole emploi",
            "Offres en alternance aix en provence",
            "Offres en alternance au luxembourg",
            "Offres en alternance auray",
            "Offres en alternance banque",
            "Offres en alternance caf",
            "Offres en alternance calvados",
            "Offres en alternance charente maritime",
            "Offres en alternance dordogne",
            "Offres en alternance dunkerque",
            "Offres en alternance environnement",
            "Offres en alternance esthéticienne",
            "Offres en alternance lausanne",
            "Offres en alternance linkedin",
            "Offres en alternance lorient",
            "Offres en alternance mairie de paris",
            "Offres en alternance martinique",
            "Offres en alternance metz",
            "Offres en alternance niort",
            "Offres en alternance thales",
            "Offres en alternance toulon",
            "Offres en alternance vannes",
            "Offres en alternance week end",
            "Offres en alternance adecco",
            "Offres en alternance air france",
            "Offres en alternance ajaccio",
            "Offres en alternance albi",
            "Offres en alternance anpe",
            "Offres en alternance ardennes",
            "Offres en alternance assistante dentaire",
            "Offres en alternance auxiliaire de vie",
            "Offres en alternance aveyron",
            "Offres en alternance bourg en bresse",
            "Offres en alternance bourges",
            "Offres en alternance cadre",
            "Offres en alternance cadres",
            "Offres en alternance chauffeur spl",
            "Offres en alternance commercial",
            "Offres en alternance communication",
            "Offres en alternance comptable",
            "Offres en alternance corse",
            "Offres en alternance cpam",
            "Offres en alternance education nationale",
            "Offres en alternance en alternance",
            "Offres en alternance gard",
            "Offres en alternance herault",
            "Offres en alternance jura",
            "Offres en alternance lidl",
            "Offres en alternance lvmh",
            "Offres en alternance manche",
            "Offres en alternance manpower",
            "Offres en alternance mi temps",
            "Offres en alternance ouest france",
            "Offres en alternance pamiers",
            "Offres en alternance quimper",
            "Offres en alternance ressources humaines",
            "Offres en alternance saint brieuc",
            "Offres en alternance social",
            "Offres en alternance tarbes",
            "Offres en alternance pole emploi",
            "Offres en alternance agen",
            "Offres en alternance agent d accueil",
            "Offres en alternance agent d entretien",
            "Offres en alternance amazon",
            "Offres en alternance assistante de direction",
            "Offres en alternance associations",
            "Offres en alternance auxerre",
            "Offres en alternance bas rhin",
            "Offres en alternance cannes",
            "Offres en alternance cantal",
            "Offres en alternance cap emploi",
            "Offres en alternance chauffeur poids lourd",
            "Offres en alternance chauffeur routier",
            "Offres en alternance cholet",
            "Offres en alternance colmar",
            "Offres en alternance cuisinier",
            "Offres en alternance decathlon",
            "Offres en alternance dubai",
            "Offres en alternance espagne",
            "Offres en alternance fhf",
            "Offres en alternance formateur",
            "Offres en alternance graphiste",
            "Offres en alternance haut rhin",
            "Offres en alternance landes",
            "Offres en alternance logistique",
            "Offres en alternance lot",
            "Offres en alternance maison de retraite",
            "Offres en alternance maroc",
            "Offres en alternance montauban",
            "Offres en alternance morlaix",
            "Offres en alternance mulhouse",
            "Offres en alternance narbonne",
            "Offres en alternance nevers",
            "Offres en alternance oise",
            "Offres en alternance orange",
            "Offres en alternance ratp",
            "Offres en alternance rh",
            "Offres en alternance saint etienne",
            "Offres en alternance sarthe",
            "Offres en alternance secretaire medicale",
            "Offres en alternance valenciennes",
            "Offres en alternance vendeuse",
            "Offres en alternance vente",
            "Offres en alternance vichy",
            "Offres en alternance 44",
            "Offres en alternance 86",
            "Offres en alternance a domicile",
            "Offres en alternance administratif",
            "Offres en alternance alpes maritimes",
            "Offres en alternance angers urgent",
            "Offres en alternance angouleme",
            "Offres en alternance audiovisuel",
            "Offres en alternance auxiliaire de puériculture",
            "Offres en alternance bretagne",
            "Offres en alternance bruxelles",
            "Offres en alternance btp",
            "Offres en alternance calais",
            "Offres en alternance carcassonne",
            "Offres en alternance cherbourg",
            "Offres en alternance club med",
            "Offres en alternance conseiller en insertion professionnelle",
            "Offres en alternance draguignan",
            "Offres en alternance drome",
            "Offres en alternance gardien d immeuble",
            "Offres en alternance gers",
            "Offres en alternance gestionnaire de paie",
            "Offres en alternance gironde",
            "Offres en alternance haute savoie",
            "Offres en alternance hotellerie et restauration",
            "Offres en alternance journalisme",
            "Offres en alternance journaliste",
            "Offres en alternance juriste",
            "Offres en alternance la roche sur yon",
            "Offres en alternance laval",
            "Offres en alternance leroy merlin",
            "Offres en alternance les sables d olonne",
            "Offres en alternance loire atlantique",
            "Offres en alternance lons le saunier",
            "Offres en alternance lot et garonne",
            "Offres en alternance magasinier",
            "Offres en alternance mayotte",
            "Offres en alternance moselle",
            "Offres en alternance nettoyage",
            "Offres en alternance onf",
            "Offres en alternance orleans",
            "Offres en alternance porto vecchio",
            "Offres en alternance portugal",
            "Offres en alternance pour seniors",
            "Offres en alternance pour seniors retraités",
            "Offres en alternance roanne",
            "Offres en alternance saint malo",
            "Offres en alternance sport",
            "Offres en alternance tahiti",
            "Offres en alternance vaucluse",
            "Offres en alternance vendanges",
            "Offres en alternance villeneuve sur lot",
            "Offres en alternance 47",
            "Offres en alternance aisne",
            "Offres en alternance arles",
            "Offres en alternance aurillac",
            "Offres en alternance avignon",
            "Offres en alternance blois",
            "Offres en alternance cdd",
            "Offres en alternance credit agricole",
            "Offres en alternance douai",
            "Offres en alternance epinal",
            "Offres en alternance guingamp",
            "Offres en alternance haute loire",
            "Offres en alternance hautes alpes",
            "Offres en alternance hotesse d accueil",
            "Offres en alternance indre et loire",
            "Offres en alternance international",
            "Offres en alternance maubeuge",
            "Offres en alternance menton",
            "Offres en alternance notariat",
            "Offres en alternance sans diplome",
            "Offres en alternance vendeur"
        ];
        $breadcrumb= [[
            "link" =>   $this->generateUrl("app_DE_accueil",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_DE_index_mot_cle', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres en alternance par mots clés les plus recherchés"
        ]
        ];
        $key = $request->query->get('term');
        $dataReg = $doctrine->getRepository(GeoListeRegion::class)->FindRegionForSearchBTP($key);

        $departementsByRegion = [];
        $i=0;
        foreach ($dataReg as $region) {
            $i=$i+1;
            $id_region = $region->getIdRegion();
            $libelleRegion = $region->getLibelle(); // Assurez-vous que getLibelle est le bon nom de méthode pour obtenir le libellé de la région

            $departements = $doctrine->getRepository(GeoDepartement::class)->FindDepratementFromRegionKey($id_region);

            // Stocke les informations de la région avec les départements correspondants
            $departementsByRegion[$i] = [
                'libelle' => $libelleRegion,
                'departements' => $departements
            ];
        }

        return $this->render('DirectEtudiant/IndexationAlternance/ParcourirlesoffresMotCle.html.twig', [
            'keywords' => $keywordSemrush0_40Avg,
            'breadcrumb'=>$breadcrumb

        ]);
    }

    #[Route('DE/emploi-index-alternance/domaine/{domainename}/{idsecteur}', name: 'app_DE_ParcourirlesoffresdemploiMetierParSecteur_alternance')]
    public function ParcourirlesoffresdemploiMetierParSecteurDEalternance(ManagerRegistry $doctrine,Request $request,$domainename,$idsecteur): Response
    {
        $listeMetiers = $doctrine->getRepository(MetierMetier::class)->getMetierIndexDirectemploi($idsecteur);
        //dd($listeMetiers);
        $breadcrumb= [[
            "link" =>   $this->generateUrl("app_DE_accueil",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_DE_index_mot_cle', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres en alternance par métier"
        ]
        ];
        return $this->render('DirectEtudiant/IndexationAlternance/ParcourirlesoffresdemploiMetierParSecteur.html.twig', [
            'domainename'=>$domainename,
            'listeMetiers'=>$listeMetiers,
            'breadcrumb'=>$breadcrumb
        ]);
    }

    #[Route('DE/emploi-index-region-ville-alternance/{departementid}', name: 'app_DE_ParcourirlesoffresdemploiVilleParRegion_alternance')]
    public function ParcourirlesoffresdemploiVilleParRegionDEalternance(Request $request, ManagerRegistry $doctrine,$departementid): Response
    {
        $dep = $doctrine->getRepository(GeoDepartement::class)->FindDepratementName($departementid);
        $dataVille = $doctrine->getRepository(GeoVille::class)->FindVilleForRegionIDSearchBTP($departementid);
        $breadcrumb= [[
            "link" =>   $this->generateUrl("app_DE_accueil",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_DE_ParcourirlesoffresdemploiVille', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres d'emploi par ville"
        ]
        ];
        return $this->render('DirectEtudiant/IndexationAlternance/ParcourirlesoffresdemploiVilleParDepartement.html.twig', [
            'dataVille' => $dataVille,
            'dep' => $dep,
            'breadcrumb'=>$breadcrumb
        ]);
    }

}