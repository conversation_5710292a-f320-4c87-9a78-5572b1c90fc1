<?php

namespace App\Controller\carriereMedicale;

use App\Entity\Conseil;
use App\Entity\EditTextAll;
use App\Twig\AppExtension;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class ConseilController extends AbstractController
{
    #[Route('Medicale/conseils', name: 'app_Medicale_conseils')]
    public function conseils(): Response
    {
        return $this->render('carriereMedicale/conseils/conseils.html.twig', [

        ]);
    }
    #[Route('Medicale/conseilsCV', name: 'app_Medicale_conseilsCV')]
    public function conseilsCV(ManagerRegistry $doctrine, Request $request): Response
    {
        $infos = $doctrine->getRepository(Conseil::class)->conseilFindByGroup(1);
        $breadcrumb = $this->getBaseBreadcrumb();
        $breadcrumb[] = $this->getConseilGroupInfo(Conseil::GROUP_CV);
        $intro = $doctrine->getRepository(EditTextAll::class)->getIntroWithID(1, 9);
        $h1Title = htmlspecialchars_decode("Nos conseils cv pour trouver un emploi");
        $pageTitle = "Nos conseils recrutement et emploi cv | Carriere Medicale";

        return $this->render('carriereMedicale/conseils/conseils_cv.html.twig', [
            'is_etudiant' => false,
            'infos' => $infos,
            'breadcrumb' => $breadcrumb,
            'intro' => $intro,
            'h1Title' => $h1Title,
            'pageTitle' => $pageTitle,
        ]);
    }
    #[Route('Medicale/conseilsembauche', name: 'app_Medicale_conseilsembauche')]
    public function conseilsembauche(ManagerRegistry $doctrine): Response
    {
        $breadcrumb = $this->getBaseBreadcrumb();
        $breadcrumb[] = $this->getConseilGroupInfo(Conseil::GROUP_ENTRETIEN_EMBAUCHE) ;
        $infos = $doctrine->getRepository(Conseil::class)->conseilFindByGroup(3);
        $intro = $doctrine->getRepository(EditTextAll::class)->getIntroWithID(1, 11);
        $h1Title = "Nos conseils entretien d'embauche pour trouver un emploi";
        $pageTitle = "Nos conseils recrutement et emploi entretien d'embauche | Carriere Medicale";

        return $this->render('carriereMedicale/conseils/conseils-embauche.html.twig', [
            'is_etudiant' => false,
            'infos' => $infos,
            'intro' => $intro,
            'h1Title' => $h1Title,
            'pageTitle' => $pageTitle,
        ]);
    }
    #[Route('Medicale/conseilsmotivation', name: 'app_Medicale_conseilsmotivation')]
    public function conseilsmotivation(ManagerRegistry $doctrine): Response
    {
        $breadcrumb = $this->getBaseBreadcrumb();
        $breadcrumb[] = $this->getConseilGroupInfo(Conseil::GROUP_LETTRE_MOTIVATION) ;
        $infos = $doctrine->getRepository(Conseil::class)->conseilFindByGroup(2);
        $intro = $doctrine->getRepository(EditTextAll::class)->getIntroWithID(1, 10);
        $h1Title = "Nos conseils lettre de motivation pour trouver un emploi";
        $pageTitle = "Nos conseils recrutement et emploi lettre de motivation | Carriere Medicale";

        return $this->render('carriereMedicale/conseils/conseils-motivation.html.twig', [
            'is_etudiant' => false,
            'infos' => $infos,
            'intro' => $intro,
            'h1Title' => $h1Title,
            'pageTitle' => $pageTitle,
        ]);
    }
    #[Route('Medicale/conseilsrechercheEmploi', name: 'app_Medicale_conseilsrechercheEmploi')]
    public function rechercheEmploi(ManagerRegistry $doctrine): Response
    {
        $breadcrumb = $this->getBaseBreadcrumb();
        $breadcrumb[] = $this->getConseilGroupInfo(Conseil::GROUP_RECHERCHE_EMPLOI) ;
        $infos = $doctrine->getRepository(Conseil::class)->conseilFindByGroup(4);
        $intro = $doctrine->getRepository(EditTextAll::class)->getIntroWithID(1, 4);
        $h1Title = "Nos conseils recherche d'emploi pour trouver un emploi";
        $pageTitle = "Nos conseils recrutement et emploi recherche d'emploi | Carriere Medicale";

        return $this->render('carriereMedicale/conseils/conseils-rechercheemploi.html.twig', [
            'is_etudiant' => false,
            'infos' => $infos,
            'intro' => $intro,
            'h1Title' => $h1Title,
            'pageTitle' => $pageTitle,
        ]);
    }
    #[Route('Medicale/conseilId/{id}', name: 'app_Medicale_conseil_id')]
    public function conseilId($id,ManagerRegistry $doctrine, Request $request): Response
    {
        $conseil = $doctrine->getRepository(Conseil::class)->find($id);
        $breadcrumb = $this->getBaseBreadcrumb();
        $breadcrumb[] = $this->getConseilGroupInfo($conseil->getGroupe()) ;
        $breadcrumb[] = [
            "link" => $request->getUri(),
            "title" => $conseil->getTitre()
        ];
        $baseUrl = AppExtension::forceHTTPS($this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL));

        $jsonLD = [
            "@context" => "https://schema.org",
            "@type" => "Article",
            "headline" => $conseil->getTitre(),

            "author"=> [["@type" => "Organization", "name" => "Direct Emploi", "logo"=> [
                "@type"=> "ImageObject",
                "width"=> "179",
                "height"=> "97",
                "url"=> $baseUrl."/images/direct-emploi/new_logo.png"

            ]]]

        ];
        if($conseil->getImage()){
            $jsonLD["image"] = [
                $baseUrl."/".substr($conseil->getImage(),1)
            ];
        }
        $html = $conseil->getHtml();
        preg_match('/<p class="texte"[^>]*>(.*?)<\/p>/', $html, $matches);
        $texte = $matches[1] ?? '';
        return $this->render('carriereMedicale/conseils/conseildetails.html.twig', [
            'infos' => $conseil,
            'breadcrumb' => $breadcrumb,
            'jsonLD' => $jsonLD,
            'descriptionSEO'=>$texte
        ]);
    }
    public function getBaseBreadcrumb(){
        return [
            [
                "link" =>   $this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL),
                "title" => "Accueil"
            ],
            [
                "link" =>  $this->generateUrl("app_conseil_index",  [], UrlGeneratorInterface::ABSOLUTE_URL),
                "title" => "Nos conseils emploi"
            ]
        ];
    }

    public function getConseilGroupInfo($group){
        switch ($group){
            case Conseil::GROUP_CV:
                $title =  "CV";
                $link = $this->generateUrl("app_conseil_cv", [], UrlGeneratorInterface::ABSOLUTE_URL);
                break;

            case Conseil::GROUP_LETTRE_MOTIVATION:
                $title =  "Lettre de motivation";
                $link = $this->generateUrl("app_conseil_lm", [], UrlGeneratorInterface::ABSOLUTE_URL);
                break;

            case Conseil::GROUP_ENTRETIEN_EMBAUCHE:
                $title =  "Entretien d'embauche";
                $link = $this->generateUrl("app_conseil_ee", [], UrlGeneratorInterface::ABSOLUTE_URL);
                break;

            case Conseil::GROUP_RECHERCHE_EMPLOI:
                $title =  "Recherche d'emploi";
                $link = $this->generateUrl("app_conseil_re", [], UrlGeneratorInterface::ABSOLUTE_URL);
                break;
            case Conseil::GROUP_RECHERCHE_STAGE:
                $title =  "Recherche de stage";
                $link = $this->generateUrl("app_conseil_rs", [], UrlGeneratorInterface::ABSOLUTE_URL);
                break;
        }

        return [
            "title" => $title,
            "link" => $link
        ];
    }
}