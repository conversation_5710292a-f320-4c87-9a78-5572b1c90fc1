<?php

namespace App\Controller;

use App\Entity\UserSituation;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\User\UserInterface;
use App\Form\EditCompteCandidatType;
use App\Entity\UserUser;
use App\Entity\CvCv;
use App\Entity\EntrEntreprise;
use App\Entity\OffCandidature;
use App\Entity\OffOffre;
use App\Entity\CvListeChoixLangue;
use App\Entity\CvNiveauLangue;
use App\Entity\EcEcole;
use App\Entity\UserOffreVeille;
use App\Entity\GeoAdresse;
use App\Entity\GeoListeRegion;
use App\Entity\MetierMetier;
use App\Entity\OffFourchetteRemuneration;
use App\Entity\OffListeSecteurActivite;
use App\Entity\OffListeTypeFormation;
use App\Entity\UserCompetences;
use App\Entity\UserDisponibilite;
use App\Entity\UserLienSituationMobiliteGeoListeRegion;
use App\Entity\UserSituationActivite;
use App\Entity\UserSituationExperience;
use App\Entity\UserSituationMobilite;
use App\Entity\GeoDepartement;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Query\Parameter;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\Security\Core\Security;
use App\Entity\OffAlertemail;
use App\Repository\OffAlertemailRepository;
use DateTime;

class EspaceCandidatController extends AbstractController
{
    private Security $security;

    public function __construct(Security $security)
	{
        $this->security = $security;
	}

    #[Route('/espaceCandidat', name: 'app_espace_candidat')]
    public function index(ManagerRegistry $doctrine, EntityManagerInterface $entity, UserInterface $user=null): Response
    {
        if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        $id_user = $this->getUser()->getIdUser();

        $user_situation = $doctrine->getRepository(UserSituation::class)->findOneBy(['user_user_id_user' => $id_user]);
        $situation = array();
        /*$niveau_formation = $doctrine->getRepository(OffListeTypeFormation::class)->findOneBy(['id_type_formation' => $user_situation->getoffListeTypeFormationIdTypeFormation()]);
        $niveau_formation ? $situation["niveau_formation"] = $niveau_formation->getLibelle() : 0;
        $etablissement = $doctrine->getRepository(EcEcole::class)->findOneBy(['id_ecole' => $user_situation->getEcEcoleIdEcole()]);
        $etablissement ? $situation["etablissement"] = $etablissement->getNomEcole() : 0;
        $user_situation->getAnneeObtentionDiplome() ? $situation["annee_obtention"] = $user_situation->getAnneeObtentionDiplome() : 0;
        $user_situation->getNomDiplome() ? $situation["nom_diplome"] = $user_situation->getNomDiplome() : 0;
        $nom_metier = $doctrine->getRepository(MetierMetier::class)->findOneBy(['id_metier' => $user_situation->getIdMetier()]);
        $nom_metier ? $situation["nom_metier"] = $nom_metier->getLibelleMetier() : 0;
        $secteur = $doctrine->getRepository(OffListeSecteurActivite::class)->findOneBy(['id_secteur_activite' => $user_situation->getOffListeSecteurActiviteIdSecteurActivite()]);
        $secteur ? $situation["secteur"] = $secteur->getLibelle() : 0;
        $user_situation->getPosteActuel() ? $situation["poste"] = $user_situation->getPosteActuel() : 0;
        $remu = $doctrine->getRepository(OffFourchetteRemuneration::class)->findOneBy(['id_fourchette_remuneration' => $user_situation->getIdOffFourchetteRemuneration()]);
        $remu ? $situation["remu"] = $remu->getLibelle() : 0;
        $activite = $doctrine->getRepository(UserSituationActivite::class)->findOneBy(['id_situation_activite' => $user_situation->getUserSituationActiviteIdSituationActivite()]);
        $activite ? $situation["activite"] = $activite->getLibelle() : 0;
        $experience = $doctrine->getRepository(UserSituationExperience::class)->findOneBy(['id_situation_experience' => $user_situation->getUserSituationExperienceIdSituationExperience()]);
        $experience ? $situation["experience"] = $experience->getLibelle() : 0;
        $mobilite = $doctrine->getRepository(GeoListeRegion::class)->findOneBy(['id_region' => $user_situation->getUserSituationMobiliteIdSituationMobilite()]);
        $mobilite ? $situation["mobilite"] = $mobilite->getLibelle() : 0;
        $disponibilite = $doctrine->getRepository(UserDisponibilite::class)->findOneBy(['id_disponibilite' => $user_situation->getUserDisponibiliteIdDisponibilite()]);
        $disponibilite ? $situation["dispo"] = $disponibilite->getLibelle() : 0;
        */
        $user_competences = $doctrine->getRepository(UserCompetences::class)->findOneBy(['user_user_id_user' => $id_user]);
        $competences = array();
        if ($user_competences) {
            $user_competences->getIdChoixLangue1() ? $competences["langue1"] = $doctrine->getRepository(CvListeChoixLangue::class)->findOneBy(['id_choix_langue' => $user_competences->getIdChoixLangue1()])->getLibelle() : 0;
            $user_competences->getIdChoixLangue2() ? $competences["langue2"] = $doctrine->getRepository(CvListeChoixLangue::class)->findOneBy(['id_choix_langue' => $user_competences->getIdChoixLangue2()])->getLibelle() : 0;
            $user_competences->getIdNiveauLangue1() ? $competences["niveau_langue1"] = $doctrine->getRepository(CvNiveauLangue::class)->findOneBy(['id_niveau_langue' => $user_competences->getIdNiveauLangue1()])->getLibelle() : 0;
            $user_competences->getIdNiveauLangue2() ? $competences["niveau_langue2"] = $doctrine->getRepository(CvNiveauLangue::class)->findOneBy(['id_niveau_langue' => $user_competences->getIdNiveauLangue2()])->getLibelle() : 0;
            $competences["permis_b"] = ($user_competences->getPermisB() == 0 ? "Non" : "Oui");
        }

        $user_cv = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);
        //$user_adresse = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);

        $users_user = $entity->createQueryBuilder();
        $users_user ->select('ga', 'u')
                ->from('App\Entity\UserUser', 'u')
                ->join('App\Entity\GeoAdresse', 'ga', 'WITH', 'u.geo_adresse_id_adresse  = ga.id_adresse')
                ->where('u.id_user = :id_user')
                ->setParameters(
                    new ArrayCollection([
                        new Parameter('id_user', $id_user),
                    ])
                    );

                $query = $users_user->getQuery();
                $user_user = $query->execute();
        

        //$user_user = $doctrine->getRepository(UserUser::class)->find($id_user);



        // $qb = $this->getEntityManager()->createQueryBuilder();
        // $qb->select('a', 'b.libelle AS contrat', 'b.id_type_contrat', 'e.logo', 'e.raison_sociale', 'f.libelle AS departement', 'g.libelle AS region', 'i.libelle AS secteur', 'i.libelle_alternatif1 AS secteur_alternatif', 'i.id_secteur_activite')
        //             ->from('App\Entity\OffOffre', 'a')
        //             ->join('App\Entity\GeoVille', 'c', 'WITH', 'a.geo_ville  = c.libelle')
        //             ->join('App\Entity\OffListeTypeContrat', 'b', 'WITH', 'b.id_type_contrat = a.off_liste_type_contrat_id_type_contrat')
        //             ->join('App\Entity\UserUser', 'd', 'WITH', 'a.user_user_id_user = d.id_user')
        //             ->join('App\Entity\EntrEntreprise', 'e', 'WITH', 'e.id_entreprise = d.entr_entreprise_id_entreprise')
        //             ->join('App\Entity\GeoDepartement', 'f', 'WITH', 'c.geo_departement_id_departement = f.id_departement')
        //             ->join('App\Entity\GeoListeRegion', 'g', 'WITH', 'c.geo_liste_region_id_region = g.id_region')
        //             ->join('App\Entity\OffLienOffreListeSecteurActivite', 'h', 'WITH', 'h.off_offre_id_offre = a.id_offre')
        //             ->join('App\Entity\OffListeSecteurActivite', 'i', 'WITH', 'h.off_liste_secteur_activite_id_secteur_activite = i.id_secteur_activite')
        //             ->where('a.intitule LIKE :mot_cle')
        //             ->setParameters(
        //                 new ArrayCollection([
        //                     new Parameter('mot_cle', '%' . $mot_cle . '%'),
        //                 ])
        //             )
        //             ->andWhere('a.statut = 1')
        //             ->orderBy('a.date_creation', 'DESC');
                    
        return $this->render('directemploi/espace_candidat/index.html.twig', [
            'controller_name' => 'EspaceCandidatController', 
            'user' => $user_user, 
            'user_situation' => $situation,
            'user_competences' => $competences,
            'user_cv' => $user_cv]);
    }

    #[Route('/espaceCandidat/profil', name: 'app_espace_candidat_profil')]
    public function ongletProfil(ManagerRegistry $doctrine, EntityManagerInterface $entity, UserInterface $user=null): Response
    {

        if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        $id_user = $this->getUser()->getIdUser();

        $user_situation = $doctrine->getRepository(UserSituation::class)->findOneBy(['user_user_id_user' => $id_user]);
        $situation = array();
        /*$niveau_formation = $doctrine->getRepository(OffListeTypeFormation::class)->findOneBy(['id_type_formation' => $user_situation->getoffListeTypeFormationIdTypeFormation()]);
        $niveau_formation ? $situation["niveau_formation"] = $niveau_formation->getLibelle() : 0;
        $etablissement = $doctrine->getRepository(EcEcole::class)->findOneBy(['id_ecole' => $user_situation->getEcEcoleIdEcole()]);
        $etablissement ? $situation["etablissement"] = $etablissement->getNomEcole() : 0;
        $user_situation->getAnneeObtentionDiplome() ? $situation["annee_obtention"] = $user_situation->getAnneeObtentionDiplome() : 0;
        $user_situation->getNomDiplome() ? $situation["nom_diplome"] = $user_situation->getNomDiplome() : 0;
        $nom_metier = $doctrine->getRepository(MetierMetier::class)->findOneBy(['id_metier' => $user_situation->getIdMetier()]);
        $nom_metier ? $situation["nom_metier"] = $nom_metier->getLibelleMetier() : 0;
        $secteur = $doctrine->getRepository(OffListeSecteurActivite::class)->findOneBy(['id_secteur_activite' => $user_situation->getOffListeSecteurActiviteIdSecteurActivite()]);
        $secteur ? $situation["secteur"] = $secteur->getLibelle() : 0;
        $user_situation->getPosteActuel() ? $situation["poste"] = $user_situation->getPosteActuel() : 0;
        $remu = $doctrine->getRepository(OffFourchetteRemuneration::class)->findOneBy(['id_fourchette_remuneration' => $user_situation->getIdOffFourchetteRemuneration()]);
        $remu ? $situation["remu"] = $remu->getLibelle() : 0;
        $activite = $doctrine->getRepository(UserSituationActivite::class)->findOneBy(['id_situation_activite' => $user_situation->getUserSituationActiviteIdSituationActivite()]);
        $activite ? $situation["activite"] = $activite->getLibelle() : 0;
        $experience = $doctrine->getRepository(UserSituationExperience::class)->findOneBy(['id_situation_experience' => $user_situation->getUserSituationExperienceIdSituationExperience()]);
        $experience ? $situation["experience"] = $experience->getLibelle() : 0;
        $mobilite = $doctrine->getRepository(GeoListeRegion::class)->findOneBy(['id_region' => $user_situation->getUserSituationMobiliteIdSituationMobilite()]);
        $mobilite ? $situation["mobilite"] = $mobilite->getLibelle() : 0;
        $disponibilite = $doctrine->getRepository(UserDisponibilite::class)->findOneBy(['id_disponibilite' => $user_situation->getUserDisponibiliteIdDisponibilite()]);
        $disponibilite ? $situation["dispo"] = $disponibilite->getLibelle() : 0;
*/
        $user_competences = $doctrine->getRepository(UserCompetences::class)->findOneBy(['user_user_id_user' => $id_user]);
        $competences = array();
        if ($user_competences) {
            $user_competences->getIdChoixLangue1() ? $competences["langue1"] = $doctrine->getRepository(CvListeChoixLangue::class)->findOneBy(['id_choix_langue' => $user_competences->getIdChoixLangue1()])->getLibelle() : 0;
            $user_competences->getIdChoixLangue2() ? $competences["langue2"] = $doctrine->getRepository(CvListeChoixLangue::class)->findOneBy(['id_choix_langue' => $user_competences->getIdChoixLangue2()])->getLibelle() : 0;
            $user_competences->getIdNiveauLangue1() ? $competences["niveau_langue1"] = $doctrine->getRepository(CvNiveauLangue::class)->findOneBy(['id_niveau_langue' => $user_competences->getIdNiveauLangue1()])->getLibelle() : 0;
            $user_competences->getIdNiveauLangue2() ? $competences["niveau_langue2"] = $doctrine->getRepository(CvNiveauLangue::class)->findOneBy(['id_niveau_langue' => $user_competences->getIdNiveauLangue2()])->getLibelle() : 0;
            $competences["permis_b"] = ($user_competences->getPermisB() == 0 ? "Non" : "Oui");
        }

        $user_cv = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);
        //$user_adresse = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);

        $users_user = $entity->createQueryBuilder();
        $users_user ->select('ga', 'u')
                ->from('App\Entity\UserUser', 'u')
                ->join('App\Entity\GeoAdresse', 'ga', 'WITH', 'u.geo_adresse_id_adresse  = ga.id_adresse')
                ->where('u.id_user = :id_user')
                ->setParameters(
                    new ArrayCollection([
                        new Parameter('id_user', $id_user),
                    ])
                    );

                $query = $users_user->getQuery();
                $user_user = $query->execute();
        
        // exemple de fonction avec l'ajax


        // if ($request->request->get('onclick')) {

        //     if ($this->getUser()) {
        //         $id_user = $this->getUser()->getIdUser();
        //     } else {
        //         $id_user = 1208544;
        //     }

        //     $user_situation = $doctrine->getRepository(UserSituation::class)->findOneBy(['user_user_id_user' => $id_user]);
        //     $user_cv = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);
            
        //     $users_user = $entity->createQueryBuilder();
        //     $users_user ->select('ga', 'u')
        //             ->from('App\Entity\UserUser', 'u')
        //             ->join('App\Entity\GeoAdresse', 'ga', 'WITH', 'u.geo_adresse_id_adresse  = ga.id_adresse')
        //             ->where('u.id_user = :id_user')
        //             ->setParameters(
        //                 new ArrayCollection([
        //                     new Parameter('id_user', $id_user),
        //                 ])
        //                 );

        //             $query = $users_user->getQuery();
        //             $user_user = $query->execute();
            
        //     $response = ["output" => $this->render('directemploi/espace_candidat/_profil.html.twig', 
        //                 ['user' => $user_user, 'user_situation' => $user_situation, 'user_cv' => $user_cv])->getContent()];

        //     return new JsonResponse($response);
        // }
        return $this->render('directemploi/espace_candidat/_profil.html.twig', [
            'controller_name' => 'EspaceCandidatController', 
            'user' => $user_user, 
            'user_situation' => $situation,
            'user_competences' => $competences,
            'user_cv' => $user_cv]);
     }

    #[Route('/espaceCandidat/cv', name: 'app_espace_candidat_cv')]
    public function ongletCv(ManagerRegistry $doctrine, UserInterface $user=null): Response
    {
        if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        $id_user = $this->getUser()->getIdUser();

        $user_cv = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);

        return $this->render('directemploi/espace_candidat/_cv.html.twig', [
            'controller_name' => 'EspaceCandidatController', 
            'user_id' => $id_user,
            'user_cv' => $user_cv]);
    }

    #[Route('/espaceCandidat/offre', name: 'app_espace_candidat_offre')]
    public function ongletOffre(ManagerRegistry $doctrine, UserInterface $user=null): Response
    {
        if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        $id_user = $this->getUser()->getIdUser();
        
        $my_offers = $doctrine->getRepository(OffCandidature::class)->findBy(['user_user_id_user' => $id_user]);
        $entreprises_offres = $doctrine->getRepository(OffOffre::class);
        $users = $doctrine->getRepository(UserUser::class);
        $entreprise = $doctrine->getRepository(EntrEntreprise::class);
        $geoLocalisation = $doctrine->getRepository(GeoAdresse::class);

        $all_contracts = [
            "" => "Contrat",
            "2" => "CDI",
            "1" => "CDD",
            "4" => "Interim",
            "3" => "FreeLance / Indépendant",
            "6" => "Alternance",
            "8" => "Stage"
        ];

        $my_unique_offers = [];
        $offres_duplique_skip = [];
        foreach ($my_offers as $offer) {
            $current_candid_offre_id = $offer->getOffOffreIdOffre();
        
            if (in_array($current_candid_offre_id, $offres_duplique_skip))
                continue;
            $offres_duplique_skip[] = $current_candid_offre_id;
            $my_unique_offers[] = $offer;
        }

        $offers_info_array_basic = [];
        $candidat_info_array_hide = [];
        $entreprise_info_array_hide = [];

        foreach ($my_unique_offers as $offer) {
            $current_candid_offre_id = $offer->getOffOffreIdOffre();
            /*  Entrprise   DISPLAY*/
            $current_offre_owner_id = $entreprises_offres->findOneBy(["id_offre" => $current_candid_offre_id])->getUserUserIdUser();
            $current_entrprise_from_user_user = $users->findOneBy(['id_user' => $current_offre_owner_id])->getEntrEntrepriseIdEntreprise();
            $entreprise_target_info = $entreprise->findOneBy(["id_entreprise" => $current_entrprise_from_user_user]);
            
            /*  Candidat   HIDE */
            $current_candid_user_user = $users->findOneBy(["id_user" => $id_user]);
            $current_candid_geo_adress = $geoLocalisation->findOneBy(["id_adresse" => $current_candid_user_user->getGeoAdresseIdAdresse()]);
            $current_candid_off_candidature = $offer;
            /*  Entreprise  HIDE */
            $current_owner_off_offre = $entreprises_offres->findOneBy(["id_offre" => $current_candid_offre_id]);

            
            if (isset($offers_info_array_basic[$current_candid_offre_id])) {
                continue;
            }

            $entr_offre = $entreprises_offres->findOneBy(['id_offre' => $current_candid_offre_id]);

            /*  Informations visibles   */
            if ($entr_offre) {
                $offers_info_array_basic[$current_candid_offre_id] = [
                    'title' => $entreprise_target_info->getRaisonSociale(),
                    'intitule' => $entr_offre->getIntitule(),
                    'ville' => $entr_offre->getGeoVille(),
                    'societe' => $entr_offre->getNomSociete(),
                    'contrat'=> $all_contracts[$entr_offre->getOffListeTypeContratIdTypeContrat()],
                ];
            }
            else {
                continue;
            }

            /*  Informations candidats cachées    */
            if ($current_candid_user_user) {
                $candidat_info_array_hide[$current_candid_offre_id] = [
                    'nom' => $current_candid_user_user->getNom(),
                    'prenom' => $current_candid_user_user->getPrenom(),
                    'ville' => $current_candid_geo_adress->getNomVille(),
                    'cp' => $current_candid_geo_adress->getCodePostal(),
                    'mail' => $current_candid_user_user->getEmail(),
                    'numero' => $current_candid_user_user->getTelephone(),
                    'date_soumission' => $current_candid_off_candidature->getDateCandidature(),
                ];
            } else {
                continue;
            }
                
            /*  Informations entreprise cachées    */
            if ($current_owner_off_offre) {
                $entreprise_info_array_hide[$current_candid_offre_id] = [
                    'nom' => $current_candid_user_user->getNom(),
                    'prenom' => $current_candid_user_user->getPrenom(),
                    'ville' => $current_candid_geo_adress->getNomVille(),
                    'cp' => $current_candid_geo_adress->getCodePostal(),

                    'mail' => $current_owner_off_offre->getRepMail(),
                    'url' => $current_owner_off_offre->getRepUrl(),
                    'date_creation' => $current_owner_off_offre->getDateCreation(),
                    'entreprise_mission' => $entr_offre->getMission(),
                    'entreprise_profile' => $entr_offre->getProfil()
                ];
            } else {
                continue;
            }
        }
        return $this->render('directemploi/espace_candidat/_offre.html.twig', [
            'user_id' => $id_user,
            'user_offers' => $my_unique_offers,
            'offre_info_visible' => $offers_info_array_basic,
            'candid_info_hide' => $candidat_info_array_hide,
            'entreprise_info_hide' => $entreprise_info_array_hide,
        ]);
    }

    #[Route('/espaceCandidat/alertemail', name: 'app_espace_candidat_alertemail')]
    public function ongletAlertemail(ManagerRegistry $doctrine, UserInterface $user=null, Request $request): Response
    {
        if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        $id_user = $this->getUser()->getIdUser();
        $alertRepository = $doctrine->getRepository(OffAlertemail::class)->findBy(["user_user_id_user" => $id_user]);
        $contracts = [
			"" => "Contrat",
			"2" => "CDI",
			"1" => "CDD",
			"4" => "Interim",
			"3" => "FreeLance / Indépendant",
			"6" => "Alternance",
			"8" => "Stage"
		];
        return $this->render("directemploi/espace_candidat/_alertemail.html.twig", [
            'id_user' => $id_user,
            'allContracts' => $contracts,
            'user_favorites' => $alertRepository,
        ]);
    }

    #[Route('/app_espace_candidat_favorite_delete', name: 'app_espace_candidat_favorite_delete')]
	public function lookForFavorite(ManagerRegistry $doctrine, 
	EntityManagerInterface $entityManager,
	UserInterface $user=null,
	Request $request)
	{
		if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        $id_user = $this->getUser()->getIdUser();
        $myIdAlert = $_POST["myIdAlertMail"] ? $_POST["myIdAlertMail"] : "";
		$message = ["id" => "res-delete",
                    "bgc" => "#f8d7da",
                    "bdc" => "#f5c6cb",
                    "color" => "#721c24",//red
                    "msg" => "Votre sauvegarde a été supprimée"];
		if ($myIdAlert == null) {
			return new JsonResponse(['error' => 'All variables are null'], 400);
		}

		$alertRepository = $doctrine->getRepository(OffAlertemail::class)->findOneBy(["id_alertemail" => (int) $myIdAlert]);
		
		if ($alertRepository != null) {
			$entityManager->remove($alertRepository);
			$entityManager->flush();
		}
        $this->addFlash('danger', "Votre recherche a été supprimée");
		return $this->redirectToRoute('app_espace_candidat');
	}

    #[Route('/espaceCandidat/desinscription', name: 'app_espace_candidat_delete')]
    public function delete(Request $request,
                           ManagerRegistry $doctrine,
                           EntityManagerInterface $entity,
                           UserInterface $user=null,
                           Session $session): Response
    {
        if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        if ($data = $request->request->get("valid")) {
            $id_user = $this->getUser()->getIdUser();
                       
            $user_user = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $id_user]);
            $geo_adresse = $doctrine->getRepository(GeoAdresse::class)->findOneBy(['id_adresse' => $user_user->getGeoAdresseIdAdresse()]);
            $user_cv = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);
            $user_situation = $doctrine->getRepository(UserSituation::class)->findOneBy(['user_user_id_user' => $id_user]);
            $user_competences = $doctrine->getRepository(UserCompetences::class)->findOneBy(['user_user_id_user' => $id_user]);
                       
            $alerte = $doctrine->getRepository(UserOffreVeille::class)->findOneBy(['user_user_id_user' => $id_user]);
            $alerte ? $entity->remove($alerte) : 0;
            $alerte ? $entity->flush() : 0;
            $geo_adresse ? $entity->remove($geo_adresse) : 0;
            $geo_adresse ? $entity->flush() : 0;
            $user_cv ? $entity->remove($user_cv) : 0;
            $user_cv ? $entity->flush() : 0;
            $user_competences ? $entity->remove($user_competences) : 0;
            $user_competences ? $entity->flush() : 0;
            $user_situation ? $entity->remove($user_situation) : 0;
            $user_situation ? $entity->flush() : 0;
            
            $session = $this->getSubscribedServices('session');
            $session = new Session();
            $session->invalidate();
                       
            $user_user ? $entity->remove($user_user) : 0;
            $user_user ? $entity->flush() : 0;
                       
            return $this->redirectToRoute('directemploi');
        }
                       
        return $this->render('directemploi/espace_candidat/delete.html.twig', [
                             'controller_name' => 'EspaceCandidatController',
        ]);
    }

    #[Route('/espaceCandidat/gestionCompte', name: 'app_espace_candidat_gestion')]
    public function edit(Request $request,
                           ManagerRegistry $doctrine,
                           EntityManagerInterface $entityManager,
                           UserInterface $user=null,
                           Session $session,
                           UserPasswordHasherInterface $userPasswordHasher,
                           ): Response
    {
        if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        $id_user = $this->getUser()->getIdUser();

        $user_veille = $doctrine->getRepository(UserOffreVeille::class)->findOneBy(['user_user_id_user' => $id_user]);
        $user_cv = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);
        $user = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $id_user]);
        $user_situation = $doctrine->getRepository(UserSituation::class)->findOneBy(['user_user_id_user' => $id_user]);
        $user_competences = $doctrine->getRepository(UserCompetences::class)->findOneBy(['user_user_id_user' => $id_user]);
        $user_adresse = $doctrine->getRepository(GeoAdresse::class)->findOneBy(['id_adresse' => $user->getGeoAdresseIdAdresse()]);

        $form = $this->createForm(EditCompteCandidatType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $reg = $doctrine->getRepository(GeoDepartement::class)->findRegByDep($form->get('code_postal')->getData());
            $user_adresse->setGeoListeRegionIdRegion($reg ? $reg : 0);
            $user_adresse->setAdresse1($form->get('adresse1')->getData());
            $user_adresse->setAdresse2($form->get('complement_adresse')->getData());
            $user_adresse->setCodePostal($form->get('code_postal')->getData());
            $user_adresse->setNomVille($form->get('ville')->getData());
            $user_adresse->setDateMaj(new \DateTime());
            $user_adresse->setGeoListePaysIdPays($form->get('pays')->getData() ? $form->get('pays')->getData()->getIdPays() : 0);
            $entityManager->persist($user_adresse);
            $entityManager->flush();

            $user->setUserCiviliteIdUserCivilite($form->get('user_civilite_id_user_civilite')->getData());
            $user->setEcEcoleIdEcole($form->get('ec_ecole')->getData() ? $form->get('ec_ecole')->getData()->getIdEcole() : 0);
            $user->setUserListeTypeIdType(3); //Type candidat
            $user->setNom($form->get('nom')->getData());
            $user->setPrenom($form->get('prenom')->getData());
            $user->setEmail($form->get('email')->getData());
            $user->setTelephone($form->get('telephone')->getData());
            $user->setDateDerniereModification(new \DateTime());
            $user->setLogin($form->get('login')->getData());
            $user->setPass('');
            if ($form->get('passCrypt')->getData())
                $user->setPassCrypt($userPasswordHasher->hashPassword($user, $form->get('passCrypt')->getData()));
            // $user->setGeoAdresseIdAdresse($user_geo->getIdAdresse());
            $user->setDateDerniereConnexion(new \DateTime());
            $entityManager->persist($user);
            $entityManager->flush();

            $user_situation->setoffListeTypeFormationIdTypeFormation($form->get('formation_level')->getData() ? $form->get('formation_level')->getData() : null);
            
            if($form->get('ec_ecole')->getData()) {
                if($form->get('formation_level')){
                    if(in_array($form->get('formation_level')->getData(),array(6,7,8))){
                        $user_situation->setEcEcoleIdEcole(null);
                        $user_situation->setEcSessionFormationIdSessionFormation($form->get('ec_ecole')->getData()->getIdSessionFormation());
                    } else {
                        $user_situation->setEcEcoleIdEcole($form->get('ec_ecole')->getData()->getIdEcole());
                        $user_situation->setEcSessionFormationIdSessionFormation(0);
                    }
                }
            } else {
                $user_situation->setEcEcoleIdEcole(null);
                $user_situation->setEcSessionFormationIdSessionFormation(0);
            }
            //$user_situation->setEcEcoleIdEcole($form->get('ec_ecole')->getData() ? $form->get('ec_ecole')->getData()->getIdEcole() : null);
            
            
            $user_situation->setOffListeSecteurActiviteIdSecteurActivite($form->get('domaine_activite')->getData() ? $form->get('domaine_activite')->getData()->getIdSecteurActivite() : null);
            $user_situation->setIdMetier($form->get('metier_metier')->getData() ? $form->get('metier_metier')->getData()->getIdMetier() : null);
            $user_situation->setUserDisponibiliteIdDisponibilite($form->get('disponibilite')->getData() ? $form->get('disponibilite')->getData()->getIdDisponibilite() : 0);
            $user_situation->setUserSituationMobiliteIdSituationMobilite($form->get('mobilite')->getData() ? $form->get('mobilite')->getData()->getIdRegion() : 0);
            $user_situation->setAnneeObtentionDiplome($form->get('annee_obtention')->getData() ? $form->get('annee_obtention')->getData() : 0);
            $user_situation->setNomDiplome($form->get('nom_diplome')->getData());
            $user_situation->setPosteActuel($form->get('poste_actuel')->getData() ? $form->get('poste_actuel')->getData() : 0);
            $user_situation->setOffListeSecteurActiviteIdSecteurActivitePoste($form->get('domaine_activite_situation')->getData() ? $form->get('domaine_activite_situation')->getData()->getIdSecteurActivite() : 0);
            $user_situation->setIdOffFourchetteRemuneration($form->get('remuneration')->getData() ? $form->get('remuneration')->getData()->getIdFourchetteRemuneration() : 0);
            $user_situation->setUserSituationActiviteIdSituationActivite($form->get('activite')->getData() ? $form->get('activite')->getData()->getIdSituationActivite() : 0);
            $user_situation->setUserSituationExperienceIdSituationExperience($form->get('experience')->getData() ? $form->get('experience')->getData()->getIdSituationExperience() : 0);
            $entityManager->persist($user_situation);
            $entityManager->flush();
 
            if ($user_cv) {
                $user_cv->setIsVisible($form->get('cv_visible')->getData());
                $user_cv->setIsAnonym($form->get('profil_anonyme')->getData());
                $user_cv->setDateModification(new \DateTime());
                $user_cv->setOffFourchetteRemunerationIdFourchetteRemunerationSouhaite($form->get('remuneration_souhaitee')->getData() ? $form->get('remuneration_souhaitee')->getData()->getIdFourchetteRemuneration() : 0);
                $user_cv->setOffFourchetteRemunerationIdFourchetteRemunerationActuel($form->get('remuneration')->getData() ? $form->get('remuneration')->getData()->getIdFourchetteRemuneration() : 0);
                $entityManager->persist($user_cv);
                $entityManager->flush();
            }

            $user_competences->setListeCompetences((string)$form->get('competence')->getData());
            $user_competences->setIdChoixLangue1($form->get('langue1')->getData() ? $form->get('langue1')->getData()->getIdChoixLangue() : null);
            $user_competences->setIdNiveauLangue1($form->get('level1')->getData() ? $form->get('level1')->getData()->getIdNiveauLangue() : null);
            $user_competences->setIdChoixLangue2($form->get('langue2')->getData() ? $form->get('langue2')->getData()->getIdChoixLangue() : null);
            $user_competences->setIdNiveauLangue2($form->get('level2')->getData() ? $form->get('level2')->getData()->getIdNiveauLangue() : null);
            $user_competences->setPermisB($form->get('permis')->getData());
            $user_competences->setIsProfilVisible($form->get('profil_visible')->getData());
            $entityManager->persist($user_competences);
            $entityManager->flush();

            if ($user_veille) {
                $user_veille->setRemunerationSerialized($form->get('remuneration_souhaitee')->getData() ? $form->get('remuneration_souhaitee')->getData()->getIdFourchetteRemuneration() : null);
                $user_veille->setTypeContratSerialized($form->get('contrat_souhaite') ? $form->get('contrat_souhaite')->getData()->getIdTypeContrat() : null);
                $form->get('poste_souhaite')->getData() ? $user_veille->setMotsCles($form->get('poste_souhaite')->getData()) : 0;
                $entityManager->persist($user_veille);
                $entityManager->flush();
            }

            return $this->redirectToRoute('app_espace_candidat');
        }

        return $this->render('directemploi/espace_candidat/edit_account.html.twig', [
            'controller_name' => 'EspaceCandidatController',
            'EditCompte' => $form->createView(),
            'situation' => $user_situation,
            'competences' => $user_competences,
            'user' => $user,
            'user_adresse' => $user_adresse,
            'user_cv' => $user_cv,
            'user_veille' => $user_veille,
        ]);
    }

    public function allowed_extensions($find) {
        $extensions = ["pdf", "png", "jpg", "jpeg"];

        if ($find === null)
            return false;
        for ($i = 0; $i < count($extensions); $i++)
            if ($find == $extensions[$i])
                return true;
        return false;
    }

    #[Route('/espaceCandidat/gestionCV', name: 'app_espace_candidat_gestion_cv')]
    public function edit_cv(Request $request,
                           ManagerRegistry $doctrine,
                           EntityManagerInterface $entityManager,
                           UserInterface $user=null,
                           Security $security,
                           ): Response
    {
        if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        $today = new DateTime();
        $today->format('Y-m-d H:i:s');
        $id_user = $this->getUser()->getIdUser();
        $user_cv = $doctrine->getRepository(CvCv::class)->findOneBy(['user_user_id_user' => $id_user]);
        $user_situation = $doctrine->getRepository(UserSituation::class)->findOneBy(['user_user_id_user' => $id_user]);

        $form = $this->createForm(EditCompteCandidatType::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted()) {

            $tmp = $form->get("file")->getData();
            $ext_ok = self::allowed_extensions($tmp->guessExtension());
            if ($ext_ok == false) {
                $this->addFlash('danger', 'Votre CV n\'est pas valide. (pdf, png, jpg, jpeg)');
                return $this->redirectToRoute('app_espace_candidat');
            }
            
            if ($user_cv) {

                $docFile = $form->get("file")->getData();
                $is_visible = $form->get("cv_visible")->getData();
                $is_anonyme = $form->get("cv_anonyme")->getData();
                /*$remu_actu = $form->get("remuneration")->getViewData();
                $remu_souh = $form->get("remuneration_souhaitee")->getViewData();
                $cont_souh = $form->get("contrat_souhaite")->getViewData();*/
                
                if ($docFile !== null) {
                    if  ($docFile->getClientOriginalName() !== null)
                        $originalFilename = pathinfo($docFile->getClientOriginalName(), PATHINFO_FILENAME);
                    else
                        $originalFilename = 'unknown';
                    
                    $oldFileStock = $user_cv->getNomFichierCvStockage();
                    $newStockFile = $id_user . date('YmdHis') . '-' . uniqid() . '.' . $docFile->guessExtension();

                    try {
                        $oldFileStockDir = $this->getParameter('cv_cand_save').'/'. $oldFileStock;
                        if (file_exists($oldFileStockDir))
                            unlink($oldFileStockDir);
                        $docFile->move($this->getParameter('cv_cand_save'), $newStockFile);
                    } catch (FileException $e) {
                        // ... handle exception if something happens during file upload
                    }
                
                    $user_cv->setNomFichierCvOriginal($originalFilename);
                    $user_cv->setNomFichierCvStockage($newStockFile);
                    $user_cv->setDateModification($today);
                    $entityManager->flush();
                }
                
                $user_cv->setIsVisible($is_visible);
                $user_cv->setIsAnonym($is_anonyme);
                $user_cv->setOffFourchetteRemunerationIdFourchetteRemunerationActuel($user_situation->getIdOffFourchetteRemuneration());
                /*$user_cv->setOffFourchetteRemunerationIdFourchetteRemunerationActuel(($remu_actu == "") ? 0 : ((int) $remu_actu));
                $user_cv->setOffFourchetteRemunerationIdFourchetteRemunerationSouhaite(($remu_souh == "") ? 0 : ((int) $remu_souh));
                $user_cv->setOffTypeContratIdTypeContratSouhaite(($cont_souh == "") ? 0 : ((int) $cont_souh)); */
                $user_cv->setDateModification($today);
                $entityManager->flush();

                $this->addFlash('success', 'Votre CV a été mis à jour');
                return $this->redirectToRoute('app_espace_candidat');
            
            } else {

                
                $CvCv = new CvCv();
                $docFile = $form->get("file")->getData();
                $originalFilename = pathinfo($docFile->getClientOriginalName(), PATHINFO_FILENAME);
                $newStockFile = $id_user . date('YmdHis') . '-' . uniqid() . '.' . $docFile->guessExtension();

                $is_visible = $form->get("cv_visible")->getData();
                $is_anonyme = $form->get("cv_anonyme")->getData();
                /*$remu_actu = $form->get("remuneration")->getViewData();
                $remu_souh = $form->get("remuneration_souhaitee")->getViewData();
                $cont_souh = $form->get("contrat_souhaite")->getViewData();*/

                try {
                    $docFile->move($this->getParameter('cv_cand_save'), $newStockFile);
                } catch (FileException $e) {
                    // ... handle exception if something happens during file upload
                }

                $CvCv->setTitre(null);
                $CvCv->setIntitulePoste(null);
                $CvCv->setUserUserIdUser($id_user);
                $CvCv->setNomFichierCvOriginal($originalFilename);
                $CvCv->setNomFichierCvStockage($newStockFile);
                $CvCv->setEtat(0); // A modifier
                $CvCv->setIsVisible($is_visible);
                $CvCv->setIsAnonym($is_anonyme);
                $CvCv->setDateCreation($today);
                $CvCv->setDateModification($today);
                $CvCv->setOffFourchetteRemunerationIdFourchetteRemunerationActuel($user_situation->getIdOffFourchetteRemuneration());
                $CvCv->setOffFourchetteRemunerationIdFourchetteRemunerationSouhaite(0);
                $CvCv->setOffTypeContratIdTypeContratSouhaite(0);
                /*$CvCv->setOffFourchetteRemunerationIdFourchetteRemunerationSouhaite(($remu_souh == "") ? 0 : ((int) $remu_souh));
                $CvCv->setOffTypeContratIdTypeContratSouhaite(($cont_souh == "") ? 0 : ((int) $cont_souh)); */
                $entityManager->persist($CvCv);
                $entityManager->flush();

                $this->addFlash('success', 'Votre CV a été déposé');
                return $this->redirectToRoute('app_espace_candidat');
            }
        }

        return $this->render('directemploi/espace_candidat/edit_cv.html.twig', [
            'controller_name' => 'EspaceCandidatController',
            'UpdateCV' => $form->createView(),
            'user_id' => $id_user,
            'user_cv' => $user_cv]
        );
    }
}
