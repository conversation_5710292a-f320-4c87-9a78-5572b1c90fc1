<?php

namespace App\Controller;

use App\Entity\GeoDepartement;
use App\Entity\GeoVille;
use App\Entity\OffLienOffreGeoDepartement;
use App\Entity\OffLienOffreGeoListeRegion;
use App\Entity\OffLienOffreListeSecteurActivite;
use App\Entity\OffListeSecteurActivite;
use App\Entity\GeoListeRegion;
use App\Entity\GeoVillePrincipale;
use App\Entity\MetierMetier;
use App\Entity\OffListeTypeContrat;
use App\Entity\EditArticle;
use App\Entity\AdminEditArticle;
use App\Entity\EditPubPave;
use App\Entity\EntrEntreprise;
use App\Entity\OffOffre;
use App\Entity\OffStatistique;
use App\Entity\UserUser;
use App\Helpers\Utils;
use App\Repository\ArticleForAllSiteDirectEmploiRepository;
use App\Repository\OffOffreRepository;
use App\Service\ELKOffOffreService;
use App\Service\MailerService;
use App\Service\myToolsService;
use App\Service\ToolsService;
use App\Twig\AppExtension;
use DateTime;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Elastic\Elasticsearch\ClientBuilder;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Cache\CacheInterface;
use Psr\Log\LoggerInterface;


class MainController extends AbstractController
{
    private $esClient;
    private $indexName = "geo_directemploi";
    private $indexNameMetier = 'metier_directemploi';


    public function __construct(ManagerRegistry $doctrine)
    {
        $this->esClient = ClientBuilder::create()
            ->setHosts(["https://direct-emploi.es.eu-west-3.aws.elastic-cloud.com:9243"])
            ->setBasicAuthentication("direct-dev", "eiCeila7seesh0Ushait")
            ->build();
        $this->doctrine = $doctrine;

    }
    #[Route('/', name: 'directemploi')]
    public function index(ArticleForAllSiteDirectEmploiRepository $allSiteDirectEmploiRepository,Request $request,ManagerRegistry $doctrine): Response
    {

        $adminArticles = $doctrine->getRepository(AdminEditArticle::class);
        $adminArticlesID = $adminArticles->getAdminArticles(0);
        $tabofIdArticles= $allSiteDirectEmploiRepository->findBy(['site' => 1]);


        $articles_id = array(   $tabofIdArticles[0]->getIdArticle(),
            $tabofIdArticles[1]->getIdArticle(),
            $tabofIdArticles[2]->getIdArticle());
        //$articles_id = array(3094, 3095, 3096); //    tests en local
        $articles = $doctrine->getRepository(EditArticle::class)->getArticleWithTheme($articles_id);

        $nb_offres = $doctrine->getRepository(OffOffre::class)->getNbOffres();
        $nb_offres_stage = $doctrine->getRepository(OffOffre::class)->getNbOffresStage();
        $offre = $doctrine->getRepository(OffOffre::class);
        $offre_orange =  $offre->findByOffreMomentDirectOrange();
        $offre_adzuna =  $offre->findByOffreMomentDirectAdzuna();
        $offre_michael =  $offre->findByOffreMomentDirectMichaelPage();


        $entreprises_id = $doctrine->getRepository(EditPubPave::class)->getEntreprisesPubById(1);
        $entreprises = $doctrine->getRepository(EntrEntreprise::class)->getEntreprisesById($entreprises_id);

        // entr_entreprise_id_entreprise
        // si l'utilisateur est connecté, on récupère son entreprise
        if ($this->getUser() != null && $this->getUser()->getEntrEntrepriseIdEntreprise() != null) {
        $userIdCompany = $this->getUser()->getEntrEntrepriseIdEntreprise();
        $userEnt = $doctrine->getRepository(EntrEntreprise::class)->findBy(['id_entreprise' => $userIdCompany]);
        $companyName = $userEnt[0]->getRaisonSociale();
        } else {
            $companyName = "";
        }

        $nb_entr = count($entreprises);

        for ($i = 0; $i < $nb_entr; $i++) {
            $numbers_img[$i] = rand(1, 12);
        }

        $images = array();
        for ($i = 0; $i < $nb_entr; $i++)
            $images[$i] = "/uploads/logos_fond/imagesfond" . $numbers_img[$i] . ".jpg";


        $response = $this->render('directemploi/new_landing.html.twig', [
            'articles' => $articles,
            'entreprises' => $entreprises,
            'images' => $images,
            'nb_offres' => $nb_offres,
            'nb_offres_stage' => $nb_offres_stage,
            'nb_entr' => $nb_entr - 1,
            'offresOrange'=>$offre_orange,
            'offresAdzuna'=>$offre_adzuna,
            'offresMichael'=>$offre_michael,
            'companyName' => $companyName,


        ]);
        $response->setSharedMaxAge(3600);

        return $response;
    }
    private function transformData($entities, $type)
    {
        return array_map(function ($entity) use ($type) {
            return ['libelle' => $entity->getLibelle(), 'type' => $type];
        }, $entities);
    }
    public function recherche(ManagerRegistry $doctrine, Request $request, ELKOffOffreService $offreService) {

        $path = $request->attributes->get('path');

        $allParams = $request->query->all();
        $limit = 10;
        list($listOffres, $elkResults) = $offreService->searchOffreForPage($allParams, $request->query->getInt("page", 1), $limit);

        switch($path){
            case "/theme/offres-stage" :
                $nb_offres = $doctrine->getRepository(OffOffre::class)->getNbOffresStage();
                $intitule = "STAGE";
                $intitule_long = "OFFRES DE STAGE";
                break;
            case "/theme/offres-alternance" :
                $nb_offres = $doctrine->getRepository(OffOffre::class)->getNbOffresAlternance();
                $intitule = "ALTERNANCE";
                $intitule_long = "OFFRES D'ALTERNANCE";
                break;
            case "/theme/offres-emploi-interim" :
                $nb_offres = $doctrine->getRepository(OffOffre::class)->getNbOffresInterim();
                $intitule = "EMPLOI";
                $intitule_long = "OFFRES EN INTERIM";
                break;
            case "/theme/offres-emploi-cabinet-de-recrutement" :
                $nb_offres = $doctrine->getRepository(OffOffre::class)->getNbOffresCabinetRecrut();
                $intitule = "EMPLOI";
                $intitule_long = "OFFRES EN CABINET DE RECRUTEMENT";
                break;                
            default:
                $nb_offres = $doctrine->getRepository(OffOffre::class)->getNbOffres();
                $intitule = "EMPLOI";
                $intitule_long = "OFFRES";
        }
        /*
        $key = $request->query->get('term');
        $dataPays = $doctrine->getRepository(GeoListePays::class)->FindPaysForSearchBTP("France");
        $dataReg = $doctrine->getRepository(GeoListeRegion::class)->FindRegionForSearchBTP($key);
        $dataDep = $doctrine->getRepository(GeoDepartement::class)->FindDepratementForSearchBTP($key);
        $dataVille = $doctrine->getRepository(GeoVille::class)->FindVilleForSearchBTP($key);

        $combinedData = array_merge(
            $this->transformData($dataPays, 'pays'),
            $this->transformData($dataReg, 'region'),
            $this->transformData($dataDep, 'departement'),
            $this->transformData($dataVille, 'ville')
        );

        $resultArray = array_values(array_unique($combinedData, SORT_REGULAR));
*/
        return $this->render('directemploi/composants/new_recherche.html.twig', [
            'nb_offres' => $nb_offres,
            'path' => $path,
            'intitule' => $intitule,
            'intitule_long' => $intitule_long,
            'listOffres' => $listOffres,
            //'combinedData' => $resultArray,

        ]);
    }
    private function transformDataMetier($entities, $type)
    {
        return array_map(function ($entity) use ($type) {
            return ['libelle_metier' => $entity->getLibelleMetier()];
        }, $entities);
    }
    public function carousel(Connection $connection, CacheInterface $cache): Response
    {
        $carrouselItems = $connection->fetchAllAssociative("SELECT * FROM carrousselForAllSiteDirectEmploi WHERE site LIKE '%1%' ORDER BY RAND() ");
        return $this->render('directemploi/composants/new_carrousel.html.twig', [
            'boites' => $carrouselItems,
        ]);
    }


    public function offres(ManagerRegistry $doctrine, Request $request, OffOffreRepository $offOffreRepository): Response
    {
        // Définir les secteurs
        $array_secteurs = [
            ['1-20', 'Achat / Logistique / Transport'],
            ['30-15', 'Administratif / Secrétariat / Public'],
            ['29', 'Agriculture / Agronomie / Alimentaire'],
            ['38', 'Aide à la personne / Social'],
            ['3', 'Banque / Assurance / Mutuelle'],
            ['4-12', 'BTP / Environnement / Génie civil'],
            ['5', 'Chimie / Matériaux / Plasturgie'],
            ['16-47-48', 'Commerces / Grande distribution / Retail'],
            ['6-13', 'Commercial / Vente / Export'],
            ['8', 'Dirigeant / Postes de direction'],
            ['11-27', "Electronique / Electricité / Télécom"],
            ['14-32', "Finance / Audit / Comptabilité"],
            ['17', 'Hôtellerie / Restauration'],
            ['46-18', 'Immobilier / Urbanisme / Architecture'],
            ['2-39-24-23', 'Industrie / Qualité / Sécurité / Auto'],
            ['19-10', "Informatique / Multimédia / Internet"],
            ['21-7-45-44', 'Marketing / Communication / Médias'],
            ['22', 'Production / Mécanique / Maintenance'],
            ['25-9', "RH / Formation / Juridique / Fiscalité"],
            ['26', "Santé / Paramédical / Pharmacie"],
            ['28', 'Tourisme / Loisirs / Sport / Culture'],
        ];

        // Client ES
        $client = ClientBuilder::create()
            ->setHosts([$_ENV['ELK_URL']])
            ->setBasicAuthentication($_ENV['ELK_BASIC_AUTH_USER'], $_ENV['ELK_BASIC_AUTH_PASSWORD'])
            ->build();

        // Fonction normalisation
        $normalizeVille = function (string $libelle): string {
            return strtoupper(str_replace(['’', '\'', '’', ' '], ['-', '-', '-', '-'], $libelle));
        };

        // Comptage offres par secteur
        foreach ($array_secteurs as &$sector) {
            $codes = explode('-', $sector[0]);
            $totalOffres = 0;
            foreach ($codes as $code) {
                $params = [
                    'index' => $_ENV['ELK_INDEX'],
                    'body' => [
                        'query' => [
                            'bool' => [
                                'must' => [
                                    ['term' => ['secteur_activite.id_secteur_activite' => $code]],
                                    ['term' => ['statut' => 1]],
                                    ['range' => ['date_soumission' => ['gte' => 'now-7d/d', 'lte' => 'now/d']]]
                                ]
                            ]
                        ]
                    ]
                ];
                $response = $client->count($params);
                $totalOffres += $response['count'];
            }
            $sector[] = $totalOffres;
        }

        // Regions
        $listeRegions = [
            'Auvergne-Rhône-Alpes',
            'Bourgogne-Franche-Comté',
            'Bretagne',
            'Centre-Val-de-Loire',
            'Corse',
            'Grand-Est',
            'Guadeloupe',
            'Guyane française',
            'Hauts-de-France',
            'Ile-de-France',
            'La Réunion',
            'Martinique',
            'Mayotte',
            'Normandie',
            'Nouvelle-Aquitaine',
            'Occitanie',
            'Pays-de-Loire',
            'Provence-Alpes-Côte d\'Azur',
        ];


        $regionCounts = [];
        foreach ($listeRegions as $region) {
            $params = [
                'index' => $_ENV['ELK_INDEX'],
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                ['term' => ['lieu.region.keyword' => $region]],
                                ['term' => ['statut' => 1]]
                            ]
                        ]
                    ]
                ]
            ];
            $response = $client->count($params);
            $regionCounts[$region] = $response['count'];
        }

        // Départements et villes
        $listeDepartements = $doctrine->getRepository(GeoDepartement::class)->findBy([], ['libelle' => 'ASC']);
        $listeVilles = $doctrine->getRepository(GeoVillePrincipale::class)->getCodePostal();

        // Fonction de comptage générique
        $fetchCounts = function ($field, $values) use ($client) {
            $counts = [];
            foreach ($values as $value) {
                $params = [
                    'index' => $_ENV['ELK_INDEX'],
                    'body' => [
                        'query' => [
                            'bool' => [
                                'must' => [
                                    ['term' => [$field => $value]],
                                    ['term' => ['statut' => 1]]
                                ]
                            ]
                        ]
                    ]
                ];
                $response = $client->count($params);
                $counts[$value] = $response['count'] ?? 0;
            }
            return $counts;
        };

        // Liste de correspondance des départements
        $departementCorrections = [
            'Reunion' => 'Reunion',
            'Mayotte' => 'Mayotte',
            'Guadeloupe' => 'Guadeloupe',
            'Martinique' => 'Martinique',
            'Guyane française' => 'Guyane française',
            'Haute-Corse' => 'Haute-Corse',
            'Nouvelle-Calédonie' => 'Nouvelle-Calédonie',
            'Polynésie française' => 'Polynésie française',
            'Corse' => 'Corse',
            // Tu peux ajouter d’autres corrections ici si besoin
        ];

// Comptage des départements avec corrections et suppression des 0 offres
        $departementsFiltres = [];

        foreach ($listeDepartements as $departement) {
            $departementName = $departement->getLibelle();

            // Appliquer la correspondance si elle existe
            $searchKey = $departementCorrections[$departementName] ?? $departementName;

            $count = $fetchCounts('lieu.departement.keyword', [$searchKey])[$searchKey] ?? 0;

            if ($count > 0) {
                $departement->count = $count;
                $departementsFiltres[] = $departement;
            }
        }

// Remplace la liste par la version filtrée
        $listeDepartements = $departementsFiltres;


        // Table de correspondance des villes spécifiques
        $villeCorrections = [
            "Alençon" => "ALENCON",
            "Angoulême" => "ANGOULEME",
            "Besançon" => "BESANCON",
            "Béziers" => "BEZIERS",
            "Compiègne" => "COMPIEGNE",
            "Créteil" => "CRETEIL",
            "Guéret" => "GUERET",
            "La Roche-sur-Yon" => "LA ROCHE-SUR-YON",
            "La Rochelle" => "LA ROCHELLE",
            "Le Havre" => "LE HAVRE",
            "Le Mans" => "LE MANS",
            "Lons-le-Saulnier" => "LONS-LE-SAUNIER",
            "Mâcon" => "MACON",
            "Nîmes" => "NIMES",
            "Orléans" => "ORLEANS",
            "Périgueux" => "PERIGUEUX",
            "Saint-Lô" => "SAINT-LO",
        ];

// Appliquer normalisation avec correction personnalisée
        $villeToNormalized = [];
        $normalizedVilles = [];
        foreach ($listeVilles as $ville) {
            $libelle = $ville->getLibelle();
            if (array_key_exists($libelle, $villeCorrections)) {
                $norm = $villeCorrections[$libelle];
            } else {
                $norm = $normalizeVille($libelle);
            }
            $villeToNormalized[$libelle] = $norm;
            $normalizedVilles[] = $norm;
        }


        // Obtenir les counts groupés
        $villeCounts = $fetchCounts('lieu.ville.keyword', $normalizedVilles);

        // Injection des counts dans les objets villes
        foreach ($listeVilles as &$ville) {
            $libelle = $ville->getLibelle();
            $norm = $villeToNormalized[$libelle];
            $ville->count = $villeCounts[$norm] ?? 0;
        }

        $listeMetiers = $doctrine->getRepository(MetierMetier::class)->getMetierAccueil();
        $CP = $doctrine->getRepository(GeoVillePrincipale::class)->getCodePostal();

        return $this->render('directemploi/composants/new_offresDuMoment.html.twig', [
            'listeOffres' => $array_secteurs,
            'listeRegions' => $listeRegions,
            'regionCounts' => $regionCounts,
            'listeDepartements' => $listeDepartements,
            'listeVilles' => $listeVilles,
            'listeMetiers' => $listeMetiers,
            'codePostal' => $CP,
            'path' => $request->attributes->get('path'),
        ]);
    }

    public function offresStage(ManagerRegistry $doctrine, Request $request, OffOffreRepository $offOffreRepository): Response
    {
        // Définir les secteurs
        $array_secteurs = [
            ['1-20', 'Achat / Logistique / Transport'],
            ['30-15', 'Administratif / Secrétariat / Public'],
            ['29', 'Agriculture / Agronomie / Alimentaire'],
            ['38', 'Aide à la personne / Social'],
            ['3', 'Banque / Assurance / Mutuelle'],
            ['4-12', 'BTP / Environnement / Génie civil'],
            ['5', 'Chimie / Matériaux / Plasturgie'],
            ['16-47-48', 'Commerces / Grande distribution / Retail'],
            ['6-13', 'Commercial / Vente / Export'],
            ['8', 'Dirigeant / Postes de direction'],
            ['11-27', "Electronique / Electricité / Télécom"],
            ['14-32', "Finance / Audit / Comptabilité"],
            ['17', 'Hôtellerie / Restauration'],
            ['46-18', 'Immobilier / Urbanisme / Architecture'],
            ['2-39-24-23', 'Industrie / Qualité / Sécurité / Auto'],
            ['19-10', "Informatique / Multimédia / Internet"],
            ['21-7-45-44', 'Marketing / Communication / Médias'],
            ['22', 'Production / Mécanique / Maintenance'],
            ['25-9', "RH / Formation / Juridique / Fiscalité"],
            ['26', "Santé / Paramédical / Pharmacie"],
            ['28', 'Tourisme / Loisirs / Sport / Culture'],
        ];

        // Client ES
        $client = ClientBuilder::create()
            ->setHosts([$_ENV['ELK_URL']])
            ->setBasicAuthentication($_ENV['ELK_BASIC_AUTH_USER'], $_ENV['ELK_BASIC_AUTH_PASSWORD'])
            ->build();

        // Fonction normalisation
        $normalizeVille = function (string $libelle): string {
            return strtoupper(str_replace(['’', '\'', '’', ' '], ['-', '-', '-', '-'], $libelle));
        };

        // Comptage offres par secteur
        foreach ($array_secteurs as &$sector) {
            $codes = explode('-', $sector[0]);
            $totalOffres = 0;
            foreach ($codes as $code) {
                $params = [
                    'index' => $_ENV['ELK_INDEX'],
                    'body' => [
                        'query' => [
                            'bool' => [
                                'must' => [
                                    ['term' => ['secteur_activite.id_secteur_activite' => $code]],
                                    ['term' => ['statut' => 1]],
                                    ['terms' => ['id_type_contrat' => [5,7,8,9]]]
                                ]
                            ]
                        ]
                    ]
                ];
                $response = $client->count($params);
                $totalOffres += $response['count'];
            }
            $sector[] = $totalOffres;
        }

        // Regions
        $listeRegions = [
            'Auvergne-Rhône-Alpes',
            'Bourgogne-Franche-Comté',
            'Bretagne',
            'Centre-Val-de-Loire',
            'Corse',
            'Grand-Est',
            'Guadeloupe',
            'Guyane française',
            'Hauts-de-France',
            'Ile-de-France',
            'La Réunion',
            'Martinique',
            'Mayotte',
            'Normandie',
            'Nouvelle-Aquitaine',
            'Occitanie',
            'Pays-de-Loire',
            'Provence-Alpes-Côte d\'Azur',
        ];


        $regionCounts = [];
        foreach ($listeRegions as $region) {
            $params = [
                'index' => $_ENV['ELK_INDEX'],
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                ['term' => ['lieu.region.keyword' => $region]],
                                ['terms' => ['id_type_contrat' => [5,7,8,9]]],
                                ['term' => ['statut' => 1]]
                            ]
                        ]
                    ]
                ]
            ];
            $response = $client->count($params);
            $regionCounts[$region] = $response['count'];
        }

        // Départements et villes
        $listeDepartements = $doctrine->getRepository(GeoDepartement::class)->findBy([], ['libelle' => 'ASC']);
        $listeVilles = $doctrine->getRepository(GeoVillePrincipale::class)->getCodePostal();

        // Fonction de comptage générique
        $fetchCounts = function ($field, $values) use ($client) {
            $counts = [];
            foreach ($values as $value) {
                $params = [
                    'index' => $_ENV['ELK_INDEX'],
                    'body' => [
                        'query' => [
                            'bool' => [
                                'must' => [
                                    ['term' => [$field => $value]],
                                    ['terms' => ['id_type_contrat' => [5,7,8,9]]],
                                    ['term' => ['statut' => 1]]
                                ]
                            ]
                        ]
                    ]
                ];
                $response = $client->count($params);
                $counts[$value] = $response['count'] ?? 0;
            }
            return $counts;
        };

        // Liste de correspondance des départements
        $departementCorrections = [
            'Reunion' => 'Reunion',
            'Mayotte' => 'Mayotte',
            'Guadeloupe' => 'Guadeloupe',
            'Martinique' => 'Martinique',
            'Guyane française' => 'Guyane française',
            'Haute-Corse' => 'Haute-Corse',
            'Nouvelle-Calédonie' => 'Nouvelle-Calédonie',
            'Polynésie française' => 'Polynésie française',
            'Corse' => 'Corse',
            // Tu peux ajouter d’autres corrections ici si besoin
        ];

// Comptage des départements avec corrections et suppression des 0 offres
        $departementsFiltres = [];

        foreach ($listeDepartements as $departement) {
            $departementName = $departement->getLibelle();

            // Appliquer la correspondance si elle existe
            $searchKey = $departementCorrections[$departementName] ?? $departementName;

            $count = $fetchCounts('lieu.departement.keyword', [$searchKey])[$searchKey] ?? 0;

            if ($count > 0) {
                $departement->count = $count;
                $departementsFiltres[] = $departement;
            }
        }

// Remplace la liste par la version filtrée
        $listeDepartements = $departementsFiltres;


        // Table de correspondance des villes spécifiques
        $villeCorrections = [
            "Alençon" => "ALENCON",
            "Angoulême" => "ANGOULEME",
            "Besançon" => "BESANCON",
            "Béziers" => "BEZIERS",
            "Compiègne" => "COMPIEGNE",
            "Créteil" => "CRETEIL",
            "Guéret" => "GUERET",
            "La Roche-sur-Yon" => "LA ROCHE-SUR-YON",
            "La Rochelle" => "LA ROCHELLE",
            "Le Havre" => "LE HAVRE",
            "Le Mans" => "LE MANS",
            "Lons-le-Saulnier" => "LONS-LE-SAUNIER",
            "Mâcon" => "MACON",
            "Nîmes" => "NIMES",
            "Orléans" => "ORLEANS",
            "Périgueux" => "PERIGUEUX",
            "Saint-Lô" => "SAINT-LO",
        ];

// Appliquer normalisation avec correction personnalisée
        $villeToNormalized = [];
        $normalizedVilles = [];
        foreach ($listeVilles as $ville) {
            $libelle = $ville->getLibelle();
            if (array_key_exists($libelle, $villeCorrections)) {
                $norm = $villeCorrections[$libelle];
            } else {
                $norm = $normalizeVille($libelle);
            }
            $villeToNormalized[$libelle] = $norm;
            $normalizedVilles[] = $norm;
        }


        // Obtenir les counts groupés
        $villeCounts = $fetchCounts('lieu.ville.keyword', $normalizedVilles);

        // Injection des counts dans les objets villes
        foreach ($listeVilles as &$ville) {
            $libelle = $ville->getLibelle();
            $norm = $villeToNormalized[$libelle];
            $ville->count = $villeCounts[$norm] ?? 0;
        }

        $listeMetiers = $doctrine->getRepository(MetierMetier::class)->getMetierAccueil();
        $CP = $doctrine->getRepository(GeoVillePrincipale::class)->getCodePostal();

        return $this->render('directemploi/composants/new_offresDuMoment.html.twig', [
            'listeOffres' => $array_secteurs,
            'listeRegions' => $listeRegions,
            'regionCounts' => $regionCounts,
            'listeDepartements' => $listeDepartements,
            'listeVilles' => $listeVilles,
            'listeMetiers' => $listeMetiers,
            'codePostal' => $CP,
            'path' => $request->attributes->get('path'),
        ]);
    }

    public function offresInterim(ManagerRegistry $doctrine, Request $request, OffOffreRepository $offOffreRepository): Response
    {
        // Définir les secteurs
        $array_secteurs = [
            ['1-20', 'Achat / Logistique / Transport'],
            ['30-15', 'Administratif / Secrétariat / Public'],
            ['29', 'Agriculture / Agronomie / Alimentaire'],
            ['38', 'Aide à la personne / Social'],
            ['3', 'Banque / Assurance / Mutuelle'],
            ['4-12', 'BTP / Environnement / Génie civil'],
            ['5', 'Chimie / Matériaux / Plasturgie'],
            ['16-47-48', 'Commerces / Grande distribution / Retail'],
            ['6-13', 'Commercial / Vente / Export'],
            ['8', 'Dirigeant / Postes de direction'],
            ['11-27', "Electronique / Electricité / Télécom"],
            ['14-32', "Finance / Audit / Comptabilité"],
            ['17', 'Hôtellerie / Restauration'],
            ['46-18', 'Immobilier / Urbanisme / Architecture'],
            ['2-39-24-23', 'Industrie / Qualité / Sécurité / Auto'],
            ['19-10', "Informatique / Multimédia / Internet"],
            ['21-7-45-44', 'Marketing / Communication / Médias'],
            ['22', 'Production / Mécanique / Maintenance'],
            ['25-9', "RH / Formation / Juridique / Fiscalité"],
            ['26', "Santé / Paramédical / Pharmacie"],
            ['28', 'Tourisme / Loisirs / Sport / Culture'],
        ];

        // Client ES
        $client = ClientBuilder::create()
            ->setHosts([$_ENV['ELK_URL']])
            ->setBasicAuthentication($_ENV['ELK_BASIC_AUTH_USER'], $_ENV['ELK_BASIC_AUTH_PASSWORD'])
            ->build();

        // Fonction normalisation
        $normalizeVille = function (string $libelle): string {
            return strtoupper(str_replace(['’', '\'', '’', ' '], ['-', '-', '-', '-'], $libelle));
        };

        // Comptage offres par secteur
        foreach ($array_secteurs as &$sector) {
            $codes = explode('-', $sector[0]);
            $totalOffres = 0;
            foreach ($codes as $code) {
                $params = [
                    'index' => $_ENV['ELK_INDEX'],
                    'body' => [
                        'query' => [
                            'bool' => [
                                'must' => [
                                    ['term' => ['secteur_activite.id_secteur_activite' => $code]],
                                    ['term' => ['statut' => 1]],
                                    ['terms' => ['id_type_contrat' => [4]]]
                                ]
                            ]
                        ]
                    ]
                ];
                $response = $client->count($params);
                $totalOffres += $response['count'];
            }
            $sector[] = $totalOffres;
        }

        // Regions
        $listeRegions = [
            'Auvergne-Rhône-Alpes',
            'Bourgogne-Franche-Comté',
            'Bretagne',
            'Centre-Val-de-Loire',
            'Corse',
            'Grand-Est',
            'Guadeloupe',
            'Guyane française',
            'Hauts-de-France',
            'Ile-de-France',
            'La Réunion',
            'Martinique',
            'Mayotte',
            'Normandie',
            'Nouvelle-Aquitaine',
            'Occitanie',
            'Pays-de-Loire',
            'Provence-Alpes-Côte d\'Azur',
        ];


        $regionCounts = [];
        foreach ($listeRegions as $region) {
            $params = [
                'index' => $_ENV['ELK_INDEX'],
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                ['term' => ['lieu.region.keyword' => $region]],
                                ['terms' => ['id_type_contrat' => [4]]],
                                ['term' => ['statut' => 1]]
                            ]
                        ]
                    ]
                ]
            ];
            $response = $client->count($params);
            $regionCounts[$region] = $response['count'];
        }

        // Départements et villes
        $listeDepartements = $doctrine->getRepository(GeoDepartement::class)->findBy([], ['libelle' => 'ASC']);
        $listeVilles = $doctrine->getRepository(GeoVillePrincipale::class)->getCodePostal();

        // Fonction de comptage générique
        $fetchCounts = function ($field, $values) use ($client) {
            $counts = [];
            foreach ($values as $value) {
                $params = [
                    'index' => $_ENV['ELK_INDEX'],
                    'body' => [
                        'query' => [
                            'bool' => [
                                'must' => [
                                    ['term' => [$field => $value]],
                                    ['terms' => ['id_type_contrat' => [4]]],
                                    ['term' => ['statut' => 1]]
                                ]
                            ]
                        ]
                    ]
                ];
                $response = $client->count($params);
                $counts[$value] = $response['count'] ?? 0;
            }
            return $counts;
        };

        // Liste de correspondance des départements
        $departementCorrections = [
            'Reunion' => 'Reunion',
            'Mayotte' => 'Mayotte',
            'Guadeloupe' => 'Guadeloupe',
            'Martinique' => 'Martinique',
            'Guyane française' => 'Guyane française',
            'Haute-Corse' => 'Haute-Corse',
            'Nouvelle-Calédonie' => 'Nouvelle-Calédonie',
            'Polynésie française' => 'Polynésie française',
            'Corse' => 'Corse',
            // Tu peux ajouter d’autres corrections ici si besoin
        ];

// Comptage des départements avec corrections et suppression des 0 offres
        $departementsFiltres = [];

        foreach ($listeDepartements as $departement) {
            $departementName = $departement->getLibelle();

            // Appliquer la correspondance si elle existe
            $searchKey = $departementCorrections[$departementName] ?? $departementName;

            $count = $fetchCounts('lieu.departement.keyword', [$searchKey])[$searchKey] ?? 0;

            if ($count > 0) {
                $departement->count = $count;
                $departementsFiltres[] = $departement;
            }
        }

// Remplace la liste par la version filtrée
        $listeDepartements = $departementsFiltres;


        // Table de correspondance des villes spécifiques
        $villeCorrections = [
            "Alençon" => "ALENCON",
            "Angoulême" => "ANGOULEME",
            "Besançon" => "BESANCON",
            "Béziers" => "BEZIERS",
            "Compiègne" => "COMPIEGNE",
            "Créteil" => "CRETEIL",
            "Guéret" => "GUERET",
            "La Roche-sur-Yon" => "LA ROCHE-SUR-YON",
            "La Rochelle" => "LA ROCHELLE",
            "Le Havre" => "LE HAVRE",
            "Le Mans" => "LE MANS",
            "Lons-le-Saulnier" => "LONS-LE-SAUNIER",
            "Mâcon" => "MACON",
            "Nîmes" => "NIMES",
            "Orléans" => "ORLEANS",
            "Périgueux" => "PERIGUEUX",
            "Saint-Lô" => "SAINT-LO",
        ];

// Appliquer normalisation avec correction personnalisée
        $villeToNormalized = [];
        $normalizedVilles = [];
        foreach ($listeVilles as $ville) {
            $libelle = $ville->getLibelle();
            if (array_key_exists($libelle, $villeCorrections)) {
                $norm = $villeCorrections[$libelle];
            } else {
                $norm = $normalizeVille($libelle);
            }
            $villeToNormalized[$libelle] = $norm;
            $normalizedVilles[] = $norm;
        }


        // Obtenir les counts groupés
        $villeCounts = $fetchCounts('lieu.ville.keyword', $normalizedVilles);

        // Injection des counts dans les objets villes
        foreach ($listeVilles as &$ville) {
            $libelle = $ville->getLibelle();
            $norm = $villeToNormalized[$libelle];
            $ville->count = $villeCounts[$norm] ?? 0;
        }

        $listeMetiers = $doctrine->getRepository(MetierMetier::class)->getMetierAccueil();
        $CP = $doctrine->getRepository(GeoVillePrincipale::class)->getCodePostal();

        return $this->render('directemploi/composants/new_offresDuMoment.html.twig', [
            'listeOffres' => $array_secteurs,
            'listeRegions' => $listeRegions,
            'regionCounts' => $regionCounts,
            'listeDepartements' => $listeDepartements,
            'listeVilles' => $listeVilles,
            'listeMetiers' => $listeMetiers,
            'codePostal' => $CP,
            'path' => $request->attributes->get('path'),
        ]);
    }


    public function offresAlternance(ManagerRegistry $doctrine, Request $request, OffOffreRepository $offOffreRepository): Response
    {
        // Définir les secteurs
        $array_secteurs = [
            ['1-20', 'Achat / Logistique / Transport'],
            ['30-15', 'Administratif / Secrétariat / Public'],
            ['29', 'Agriculture / Agronomie / Alimentaire'],
            ['38', 'Aide à la personne / Social'],
            ['3', 'Banque / Assurance / Mutuelle'],
            ['4-12', 'BTP / Environnement / Génie civil'],
            ['5', 'Chimie / Matériaux / Plasturgie'],
            ['16-47-48', 'Commerces / Grande distribution / Retail'],
            ['6-13', 'Commercial / Vente / Export'],
            ['8', 'Dirigeant / Postes de direction'],
            ['11-27', "Electronique / Electricité / Télécom"],
            ['14-32', "Finance / Audit / Comptabilité"],
            ['17', 'Hôtellerie / Restauration'],
            ['46-18', 'Immobilier / Urbanisme / Architecture'],
            ['2-39-24-23', 'Industrie / Qualité / Sécurité / Auto'],
            ['19-10', "Informatique / Multimédia / Internet"],
            ['21-7-45-44', 'Marketing / Communication / Médias'],
            ['22', 'Production / Mécanique / Maintenance'],
            ['25-9', "RH / Formation / Juridique / Fiscalité"],
            ['26', "Santé / Paramédical / Pharmacie"],
            ['28', 'Tourisme / Loisirs / Sport / Culture'],
        ];

        // Client ES
        $client = ClientBuilder::create()
            ->setHosts([$_ENV['ELK_URL']])
            ->setBasicAuthentication($_ENV['ELK_BASIC_AUTH_USER'], $_ENV['ELK_BASIC_AUTH_PASSWORD'])
            ->build();

        // Fonction normalisation
        $normalizeVille = function (string $libelle): string {
            return strtoupper(str_replace(['’', '\'', '’', ' '], ['-', '-', '-', '-'], $libelle));
        };

        // Comptage offres par secteur
        foreach ($array_secteurs as &$sector) {
            $codes = explode('-', $sector[0]);
            $totalOffres = 0;
            foreach ($codes as $code) {
                $params = [
                    'index' => $_ENV['ELK_INDEX'],
                    'body' => [
                        'query' => [
                            'bool' => [
                                'must' => [
                                    ['term' => ['secteur_activite.id_secteur_activite' => $code]],
                                    ['term' => ['statut' => 1]],
                                    ['term' => ['id_type_contrat' => 8]],
                                    ['range' => ['date_soumission' => ['gte' => 'now-7d/d', 'lte' => 'now/d']]]
                                ]
                            ]
                        ]
                    ]
                ];
                $response = $client->count($params);
                $totalOffres += $response['count'];
            }
            $sector[] = $totalOffres;
        }

        // Regions
        $listeRegions = [
            'Auvergne-Rhône-Alpes',
            'Bourgogne-Franche-Comté',
            'Bretagne',
            'Centre-Val-de-Loire',
            'Corse',
            'Grand-Est',
            'Guadeloupe',
            'Guyane française',
            'Hauts-de-France',
            'Ile-de-France',
            'La Réunion',
            'Martinique',
            'Mayotte',
            'Normandie',
            'Nouvelle-Aquitaine',
            'Occitanie',
            'Pays-de-Loire',
            'Provence-Alpes-Côte d\'Azur',
        ];


        $regionCounts = [];
        foreach ($listeRegions as $region) {
            $params = [
                'index' => $_ENV['ELK_INDEX'],
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                ['term' => ['lieu.region.keyword' => $region]],
                                ['term' => ['id_type_contrat' => 8]],
                                ['range' => ['date_soumission' => ['gte' => 'now-7d/d', 'lte' => 'now/d']]],
                                ['term' => ['statut' => 1]]
                            ]
                        ]
                    ]
                ]
            ];
            $response = $client->count($params);
            $regionCounts[$region] = $response['count'];
        }

        // Départements et villes
        $listeDepartements = $doctrine->getRepository(GeoDepartement::class)->findBy([], ['libelle' => 'ASC']);
        $listeVilles = $doctrine->getRepository(GeoVillePrincipale::class)->getCodePostal();

        // Fonction de comptage générique
        $fetchCounts = function ($field, $values) use ($client) {
            $counts = [];
            foreach ($values as $value) {
                $params = [
                    'index' => $_ENV['ELK_INDEX'],
                    'body' => [
                        'query' => [
                            'bool' => [
                                'must' => [
                                    ['term' => [$field => $value]],
                                    ['term' => ['id_type_contrat' => 8]],
                                    ['range' => ['date_soumission' => ['gte' => 'now-7d/d', 'lte' => 'now/d']]],
                                    ['term' => ['statut' => 1]]
                                ]
                            ]
                        ]
                    ]
                ];
                $response = $client->count($params);
                $counts[$value] = $response['count'] ?? 0;
            }
            return $counts;
        };

        // Liste de correspondance des départements
        $departementCorrections = [
            'Reunion' => 'Reunion',
            'Mayotte' => 'Mayotte',
            'Guadeloupe' => 'Guadeloupe',
            'Martinique' => 'Martinique',
            'Guyane française' => 'Guyane française',
            'Haute-Corse' => 'Haute-Corse',
            'Nouvelle-Calédonie' => 'Nouvelle-Calédonie',
            'Polynésie française' => 'Polynésie française',
            'Corse' => 'Corse',
            // Tu peux ajouter d’autres corrections ici si besoin
        ];

// Comptage des départements avec corrections et suppression des 0 offres
        $departementsFiltres = [];

        foreach ($listeDepartements as $departement) {
            $departementName = $departement->getLibelle();

            // Appliquer la correspondance si elle existe
            $searchKey = $departementCorrections[$departementName] ?? $departementName;

            $count = $fetchCounts('lieu.departement.keyword', [$searchKey])[$searchKey] ?? 0;

            if ($count > 0) {
                $departement->count = $count;
                $departementsFiltres[] = $departement;
            }
        }

// Remplace la liste par la version filtrée
        $listeDepartements = $departementsFiltres;


        // Table de correspondance des villes spécifiques
        $villeCorrections = [
            "Alençon" => "ALENCON",
            "Angoulême" => "ANGOULEME",
            "Besançon" => "BESANCON",
            "Béziers" => "BEZIERS",
            "Compiègne" => "COMPIEGNE",
            "Créteil" => "CRETEIL",
            "Guéret" => "GUERET",
            "La Roche-sur-Yon" => "LA ROCHE-SUR-YON",
            "La Rochelle" => "LA ROCHELLE",
            "Le Havre" => "LE HAVRE",
            "Le Mans" => "LE MANS",
            "Lons-le-Saulnier" => "LONS-LE-SAUNIER",
            "Mâcon" => "MACON",
            "Nîmes" => "NIMES",
            "Orléans" => "ORLEANS",
            "Périgueux" => "PERIGUEUX",
            "Saint-Lô" => "SAINT-LO",
        ];

// Appliquer normalisation avec correction personnalisée
        $villeToNormalized = [];
        $normalizedVilles = [];
        foreach ($listeVilles as $ville) {
            $libelle = $ville->getLibelle();
            if (array_key_exists($libelle, $villeCorrections)) {
                $norm = $villeCorrections[$libelle];
            } else {
                $norm = $normalizeVille($libelle);
            }
            $villeToNormalized[$libelle] = $norm;
            $normalizedVilles[] = $norm;
        }


        // Obtenir les counts groupés
        $villeCounts = $fetchCounts('lieu.ville.keyword', $normalizedVilles);

        // Injection des counts dans les objets villes
        foreach ($listeVilles as &$ville) {
            $libelle = $ville->getLibelle();
            $norm = $villeToNormalized[$libelle];
            $ville->count = $villeCounts[$norm] ?? 0;
        }

        $listeMetiers = $doctrine->getRepository(MetierMetier::class)->getMetierAccueil();
        $CP = $doctrine->getRepository(GeoVillePrincipale::class)->getCodePostal();

        return $this->render('directemploi/composants/new_offresDuMoment.html.twig', [
            'listeOffres' => $array_secteurs,
            'listeRegions' => $listeRegions,
            'regionCounts' => $regionCounts,
            'listeDepartements' => $listeDepartements,
            'listeVilles' => $listeVilles,
            'listeMetiers' => $listeMetiers,
            'codePostal' => $CP,
            'path' => $request->attributes->get('path'),
        ]);
    }

    /**
     * @Route("/api/location-search", name="api_location_search", methods={"GET"})
     */
    public function locationSearch(Request $request): JsonResponse
    {
        $key = trim($request->query->get('key', ''));

        if (empty($key)) {
            return new JsonResponse([['libelle' => 'France', 'type' => 'pays', 'code_insee' => '']]);
        }

        // Remplace les espaces par des tirets pour matcher les noms comme "Saint-Lo"
        $keyWithHyphen = str_replace(' ', '-', $key);
        $normalizedKey = $this->normalizeSearchKey($keyWithHyphen);

        $params = [
            'index' => $this->indexName,
            'body' => [
                'query' => [
                    'bool' => [
                        'should' => [
                            // Recherche par libellé
                            [
                                'match_phrase_prefix' => [
                                    'libelle' => [
                                        'query' => $keyWithHyphen,
                                        'max_expansions' => 50
                                    ]
                                ]
                            ],
                            [
                                'match' => [
                                    'libelle' => [
                                        'query' => $normalizedKey,
                                        'fuzziness' => 'AUTO',
                                        'operator' => 'and'
                                    ]
                                ]
                            ],
                            // Recherche par code_insee pour les départements
                            [
                                'bool' => [
                                    'must' => [
                                        ['term' => ['type' => 'departement']],
                                        ['term' => ['code_insee.keyword' => $key]]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'size' => 1000
            ]
        ];

        $response = $this->esClient->search($params);

        $data = array_filter(array_map(function ($hit) {
            $source = $hit['_source'];

            return isset($source['libelle'], $source['type']) ? [
                'libelle' => str_replace('-', ' ', $this->removeAccents($source['libelle'])),
                'type' => $source['type'],
                'code_insee' => $source['code_insee'] ?? '',
            ] : null;
        }, $response['hits']['hits']));

        array_unshift($data, ['libelle' => 'France', 'type' => 'pays', 'code_insee' => '']);

        return new JsonResponse($data);
    }


    /**
     * Nettoie une chaîne de caractères en retirant les accents.
     */
    private function removeAccents(string $str): string
    {
        return iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $str);
    }


    /**
     * Normalise un mot-clé pour améliorer les correspondances
     */
    private function normalizeSearchKey(string $key): string
    {
        $key = strtolower($key);
        $key = iconv('UTF-8', 'ASCII//TRANSLIT', $key); // Remove accents
        $key = str_replace(['-', '_'], ' ', $key);      // Replace dashes/underscores by spaces
        $key = preg_replace('/\s+/', ' ', $key);        // Clean multiple spaces
        return trim($key);
    }

    /**
     * @Route("/api/metier-search", name="api_metier_search", methods={"GET"})
     */
    public function metierSearch(Request $request): JsonResponse
    {
        // Get the search term from the request
        $key = trim($request->query->get('key', ''));

        // If the key is empty, return an empty response
        if (empty($key)) {
            return new JsonResponse([]);
        }

        // Normalize the key (accents, tirets, espaces)
        $normalizedKey = $this->normalizeSearchKey($key);

        // Build the Elasticsearch query
        $params = [
            'index' => $this->indexNameMetier,
            'body' => [
                'query' => [
                    'bool' => [
                        'should' => [
                            [
                                'match_phrase_prefix' => [
                                    'libelle_metier' => [
                                        'query' => $key,
                                        'max_expansions' => 50
                                    ]
                                ]
                            ],
                            [
                                'match' => [
                                    'libelle_metier' => [
                                        'query' => $normalizedKey,
                                        'fuzziness' => 'AUTO',
                                        'operator' => 'and'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'size' => 1000
            ]
        ];

        // Execute the Elasticsearch query
        $response = $this->esClient->search($params);

        // Extract the results
        $data = array_filter(array_map(function ($hit) {
            return isset($hit['_source']['libelle_metier']) ? [
                'libelle_metier' => $hit['_source']['libelle_metier']
            ] : null;
        }, $response['hits']['hits']));

        return new JsonResponse($data);
    }



    private function transformEntityArray($entities, $type)
    {
        return array_map(function ($entity) use ($type) {
            return ['libelle' => $entity->getLibelle(), 'type' => $type];
        }, $entities);
    }
    public function cartefrance(ManagerRegistry $doctrine, Request $request): Response
    {
        return $this->render('directemploi/cartefranceDirect.html.twig', [
        ]);
    }

    #[Route('emploi-index', name: 'app_Parcourirlesoffresdemploi')]
    public function ParcourirlesoffresdemploiDirect(Request $request): Response
    {
        $metiers = [
            "Offre d'emploi Acheteur",
            "Offre d'emploi Administration des ventes",
            "Offre d'emploi Aide soignant",
            "Offre d'emploi Assistant de direction",
            "Offre d'emploi Auxiliaire de vie",
            "Offre d'emploi Boucher",
            "Offre d'emploi Boulanger",
            "Offre d'emploi Caissier",
            "Offre d'emploi Cariste",
            "Offre d'emploi Chargé de recrutement",
            "Offre d'emploi Chauffagiste",
            "Offre d'emploi Chauffeur",
            "Offre d'emploi Chef de produit",
            "Offre d'emploi Chef de rayon",
            "Offre d'emploi Collaborateur comptable",
            "Offre d'emploi Commercial",
            "Offre d'emploi Commercial immobilier",
            "Offre d'emploi Commercial itinérant",
            "Offre d'emploi Commercial terrain",
            "Offre d'emploi Community manager",
            "Offre d'emploi Comptable",
            "Offre d'emploi Conducteur d'engins",
            "Offre d'emploi Conducteur de travaux",
            "Offre d'emploi Conseiller en patrimoine",
            "Offre d'emploi Contrôleur de gestion",
            "Offre d'emploi Dessinateur projeteur",
            "Offre d'emploi Développeur JS",
            "Offre d'emploi Développeur web",
            "Offre d'emploi Éducateur",
            "Offre d'emploi Electricien",
            "Offre d'emploi Employé administratif",
            "Offre d'emploi Frigoriste",
            "Offre d'emploi Garde d'enfant",
            "Offre d'emploi Gestionnaire assurance",
            "Offre d'emploi Gestionnaire copropriété",
            "Offre d'emploi Gestionnaire de paie",
            "Offre d'emploi Infirmier",
            "Offre d'emploi Ingénieur commercial",
            "Offre d'emploi Ingénieur qualité",
            "Offre d'emploi Ingénieur travaux",
            "Offre d'emploi Intégrateur web",
            "Offre d'emploi Jardinier",
            "Offre d'emploi Logisticien",
            "Offre d'emploi Magasinier",
            "Offre d'emploi Mécanicien",
            "Offre d'emploi Peintre",
            "Offre d'emploi Plombier",
            "Offre d'emploi Préparateur de commandes",
            "Offre d'emploi Référenceur SEO",
            "Offre d'emploi Responsable de magasin",
            "Offre d'emploi Responsable restaurant",
            "Offre d'emploi Serveur",
            //"Offre d'emploi Technicien bureau d'études",
            //"Offre d'emploi Technicien de maintenance",
            "Offre d'emploi Technico commercial",
            "Offre d'emploi Vendeur",
        ];
        $offresEmploi = [
            "Offres d'emploi Achat",
            "Offres d'emploi Logistique",
            "Offres d'emploi Transport",
            "Offres d'emploi Administratif",
            "Offres d'emploi Secrétariat",
            "Offres d'emploi Public",
            "Offres d'emploi Agriculture",
            "Offres d'emploi Agronomie",
            "Offres d'emploi Alimentaire",
            "Offres d'emploi Aide à la personne",
            "Offres d'emploi Social",
            "Offres d'emploi Banque",
            "Offres d'emploi Assurance",
            "Offres d'emploi Mutuelle",
            "Offres d'emploi BTP",
            "Offres d'emploi Environnement",
            "Offres d'emploi Génie civil",
            "Offres d'emploi Chimie",
            "Offres d'emploi Matériaux",
            "Offres d'emploi Plasturgie",
            "Offres d'emploi Commerces",
            "Offres d'emploi Grande distribution",
            "Offres d'emploi Retail",
            "Offres d'emploi Commercial",
            "Offres d'emploi Vente",
            "Offres d'emploi Export",
            "Offres d'emploi Dirigeant",
            "Offres d'emploi Postes de direction",
            "Offres d'emploi Electronique",
            "Offres d'emploi Electricité",
            "Offres d'emploi Télécom",
            "Offres d'emploi Finance",
            "Offres d'emploi Audit",
            "Offres d'emploi Comptabilité",
            "Offres d'emploi Hôtellerie",
            "Offres d'emploi Restauration",
            "Offres d'emploi Immobilier",
            "Offres d'emploi Urbanisme",
            "Offres d'emploi Architecture",
            "Offres d'emploi Industrie",
            "Offres d'emploi Qualité",
            "Offres d'emploi Sécurité",
            "Offres d'emploi Auto",
            "Offres d'emploi Informatique",
            "Offres d'emploi Multimédia",
            "Offres d'emploi Internet",
            "Offres d'emploi Marketing",
            "Offres d'emploi Communication",
            "Offres d'emploi Médias",
            "Offres d'emploi Production",
            "Offres d'emploi Mécanique",
            "Offres d'emploi Maintenance",
            "Offres d'emploi RH",
            "Offres d'emploi Formation",
            "Offres d'emploi Juridique",
            "Offres d'emploi Fiscalité",
            "Offres d'emploi Santé",
            "Offres d'emploi Paramédical",
            "Offres d'emploi Pharmacie",
            "Offres d'emploi Tourisme",
            "Offres d'emploi Loisirs",
            "Offres d'emploi Sport",
            "Offres d'emploi Culture"
        ];
        $breadcrumb= [[
            "link" =>   $this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_Parcourirlesoffresdemploi', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres d'emploi par métier"
        ]
        ];

        return $this->render('Indexation/Parcourirlesoffresdemploi.html.twig', [
            'metiers' => $metiers,
            'offresEmploi'=>$offresEmploi,
            'breadcrumb'=>$breadcrumb
        ]);
    }

    #[Route('emploi-index/domaine/{domainename}/{idsecteur}', name: 'app_ParcourirlesoffresdemploiMetierParSecteur')]
    public function ParcourirlesoffresdemploiMetierParSecteur(ManagerRegistry $doctrine,Request $request,$domainename,$idsecteur): Response
    {
        $listeMetiers = $doctrine->getRepository(MetierMetier::class)->getMetierIndexDirectemploi($idsecteur);
        //dd($listeMetiers);
        $breadcrumb= [[
            "link" =>   $this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_Parcourirlesoffresdemploi', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres d'emploi par métier"
        ]
        ];
        return $this->render('Indexation/ParcourirlesoffresdemploiMetierParSecteur.html.twig', [
        'domainename'=>$domainename,
            'listeMetiers'=>$listeMetiers,
            'breadcrumb'=>$breadcrumb
        ]);
    }
/*
    #[Route('emploi-index/metier-ville/{metier}', name: 'ParcourirlesoffresdemploiMetierParVilleSector')]
    public function ParcourirlesoffresdemploiMetierParVilleSector(ManagerRegistry $doctrine,Request $request,$metier): Response
    {
        $breadcrumb= [[
            "link" =>   $this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_ParcourirlesoffresdemploiVille', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres d'emploi par ville"
        ]
        ];
        $metierDecoded = urldecode($metier);

        $listVille = $doctrine->getRepository(OffOffre::class)->GetListVilleFromMetier($metierDecoded);
        // Tableau statique des 20 principales villes en France
        $villesStatiques = [
            'Paris', 'Marseille', 'Lyon', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg', 'Montpellier',
            'Bordeaux', 'Lille', 'Rennes', 'Reims', 'Le Havre', 'Saint-Étienne', 'Toulon',
            'Grenoble', 'Dijon', 'Angers', 'Nîmes', 'Villeurbanne'
        ];

        if (empty($listVille)) {
            $listVille = array_map(function($ville) {
                return ['geo_ville' => $ville];
            }, $villesStatiques);
        }

        sort($listVille);
        $offresEmploi = [];
        foreach ($listVille as $villeArray) {
            $nomVille = $villeArray['geo_ville']; // Accéder au nom de la ville avec la clé 'geo_ville'
            $offresEmploi[] = [
                'metier' => $metierDecoded,
                'ville' => $nomVille
            ];
        }
        return $this->render('Indexation/ParcourirlesoffresdemploiMetierParVilleSector.html.twig', [
            'offresEmploi' => $offresEmploi,
            'metier'=>$metierDecoded,
            'breadcrumb'=>$breadcrumb
        ]);
    }
*/
    #[Route('emploi-index-par-ville/', name: 'app_ParcourirlesoffresdemploiVille')]
    public function ParcourirlesoffresdemploiVilleDirect(Request $request, ManagerRegistry $doctrine): Response
    {
        $topvilles = [
            "Paris",
            "Lyon",
            "Toulouse",
            "Nantes",
            "Marseille",
            "Bordeaux",
            "Rennes",
            "Lille",
            "Strasbourg",
            "Montpellier",
            "Nice",
            "Aix-en-Provence",
            "Grenoble",
            "Dijon",
            "Annecy",
            "Angers",
            "Reims",
            "Tours",
            "Orléans",
            "Clermont-Ferrand",
            "Boulogne-Billancourt",
            "Metz",
            "Caen",
            "Brest",
            "Rouen",
            "Le Mans",
            "Nanterre",
            "Courbevoie",
            "Saint-Étienne",
            "Nancy",
            "Besançon",
            "La Rochelle"
        ];
        $breadcrumb= [[
            "link" =>   $this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_ParcourirlesoffresdemploiVille', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres d'emploi par ville"
        ]
        ];
        $key = $request->query->get('term');
        $dataReg = $doctrine->getRepository(GeoListeRegion::class)->FindRegionForSearchBTP($key);

        $departementsByRegion = [];
        $i=0;
        foreach ($dataReg as $region) {
            $i=$i+1;
            $id_region = $region->getIdRegion();
            $libelleRegion = $region->getLibelle(); // Assurez-vous que getLibelle est le bon nom de méthode pour obtenir le libellé de la région

            $departements = $doctrine->getRepository(GeoDepartement::class)->FindDepratementFromRegionKey($id_region);

            // Stocke les informations de la région avec les départements correspondants
            $departementsByRegion[$i] = [
                'libelle' => $libelleRegion,
                'departements' => $departements
            ];
        }

        return $this->render('Indexation/ParcourirlesoffresdemploiVille.html.twig', [
            'topvilles' => $topvilles,
            'departementsByRegions' => $departementsByRegion,
            'breadcrumb'=>$breadcrumb

        ]);
    }
    #[Route('emploi-index-region-ville/{departementid}', name: 'app_ParcourirlesoffresdemploiVilleParRegion')]
    public function ParcourirlesoffresdemploiVilleParRegionDirect(Request $request, ManagerRegistry $doctrine,$departementid): Response
    {
        $dep = $doctrine->getRepository(GeoDepartement::class)->FindDepratementName($departementid);
        $dataVille = $doctrine->getRepository(GeoVille::class)->FindVilleForRegionIDSearchBTP($departementid);
        $breadcrumb= [[
            "link" =>   $this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_ParcourirlesoffresdemploiVille', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres d'emploi par ville"
        ]
        ];
        return $this->render('Indexation/ParcourirlesoffresdemploiVilleParDepartement.html.twig', [
            'dataVille' => $dataVille,
            'dep' => $dep,
            'breadcrumb'=>$breadcrumb

        ]);
    }
    #[Route('/enregistrer-clic-bouton', name: 'enregistrer_clic_bouton', methods: ['POST'])]
    public function enregistrerClicBouton(Request $request, EntityManagerInterface $em): Response
    {
        $data = json_decode($request->getContent(), true);

        $offreId = $data['offreId'];
        $entrepriseId = $data['entrepriseId'];

        $stats = new OffStatistique();
        $stats->setOffOffreIdOffre($offreId);
        $stats->setDateLog(new DateTime());
        $stats->setTypeLog('3');
        $stats->setEntrEntrepriseIdEntreprise($entrepriseId);
        $em->persist($stats);
        $em->flush();

        return new JsonResponse(['message' => 'Clic enregistré']);
    }
    #[Route('index-mot-cle/', name: 'app_index_mot_cle')]
    public function index_mot_cle(Request $request, ManagerRegistry $doctrine): Response
    {
        $keywordSemrush0_40Avg = [
            "Offres d'emploi pole emploi",
            "Offres d'emploi luxembourg",
            "Offres d'emploi genève",
            "Offres d'emploi limoges",
            "Offres d'emploi lyon",
            "Offres d'emploi saisonnier",
            "Offres d'emploi toulouse",
            "Offres d'emploi aide soignante",
            "Offres d'emploi brest",
            "Offres d'emploi le bon coin",
            "Offres d'emploi marseille",
            "Offres d'emploi montpellier",
            "Offres d'emploi paris",
            "Offres d'emploi rennes",
            "Offres d'emploi strasbourg",
            "Offres d'emploi angers",
            "Offres d'emploi bordeaux",
            "Offres d'emploi chauffeur livreur",
            "Offres d'emploi clermont ferrand",
            "Offres d'emploi dole",
            "Offres d'emploi ile de france",
            "Offres d'emploi infirmier",
            "Offres d'emploi monaco",
            "Offres d'emploi nantes",
            "Offres d'emploi seniors",
            "Offres d'emploi sncf",
            "Offres d'emploi airbus",
            "Offres d'emploi caen",
            "Offres d'emploi cambrai",
            "Offres d'emploi canada",
            "Offres d'emploi chauffeur",
            "Offres d'emploi fle",
            "Offres d'emploi geneve",
            "Offres d'emploi leclerc",
            "Offres d'emploi lille",
            "Offres d'emploi nice",
            "Offres d'emploi pau",
            "Offres d'emploi perpignan",
            "Offres d'emploi petite enfance",
            "Offres d'emploi poitiers",
            "Offres d'emploi psychologue",
            "Offres d'emploi reims",
            "Offres d'emploi rouen",
            "Offres d'emploi amiens",
            "Offres d'emploi berck",
            "Offres d'emploi cdi",
            "Offres d'emploi dijon",
            "Offres d'emploi edf",
            "Offres d'emploi essonne",
            "Offres d'emploi fonction publique hospitaliere",
            "Offres d'emploi grenoble",
            "Offres d'emploi la rochelle",
            "Offres d'emploi lannion",
            "Offres d'emploi le mans",
            "Offres d'emploi librairie",
            "Offres d'emploi morbihan",
            "Offres d'emploi restauration",
            "Offres d'emploi temps partiel",
            "Offres d'emploi tours",
            "Offres d'emploi var",
            "Offres d'emploi annecy",
            "Offres d'emploi ash",
            "Offres d'emploi atsem",
            "Offres d'emploi bayonne",
            "Offres d'emploi belgique",
            "Offres d'emploi carrefour",
            "Offres d'emploi culture",
            "Offres d'emploi enseignement",
            "Offres d'emploi guadeloupe",
            "Offres d'emploi informatique",
            "Offres d'emploi la poste",
            "Offres d'emploi le havre",
            "Offres d'emploi nancy",
            "Offres d'emploi nimes",
            "Offres d'emploi rodez",
            "Offres d'emploi rqth",
            "Offres d'emploi senior",
            "Offres d'emploi tarn",
            "Offres d'emploi troyes",
            "Offres d'emploi vosges",
            "Offres d'emploi yvelines",
            "Offres d'emploi pole emploi",
            "Offres d'emploi aix en provence",
            "Offres d'emploi au luxembourg",
            "Offres d'emploi auray",
            "Offres d'emploi banque",
            "Offres d'emploi caf",
            "Offres d'emploi calvados",
            "Offres d'emploi charente maritime",
            "Offres d'emploi dordogne",
            "Offres d'emploi dunkerque",
            "Offres d'emploi environnement",
            "Offres d'emploi esthéticienne",
            "Offres d'emploi lausanne",
            "Offres d'emploi linkedin",
            "Offres d'emploi lorient",
            "Offres d'emploi mairie de paris",
            "Offres d'emploi martinique",
            "Offres d'emploi metz",
            "Offres d'emploi niort",
            "Offres d'emploi thales",
            "Offres d'emploi toulon",
            "Offres d'emploi vannes",
            "Offres d'emploi week end",
            "Offres d'emploi adecco",
            "Offres d'emploi air france",
            "Offres d'emploi ajaccio",
            "Offres d'emploi albi",
            "Offres d'emploi anpe",
            "Offres d'emploi ardennes",
            "Offres d'emploi assistante dentaire",
            "Offres d'emploi auxiliaire de vie",
            "Offres d'emploi aveyron",
            "Offres d'emploi bourg en bresse",
            "Offres d'emploi bourges",
            "Offres d'emploi cadre",
            "Offres d'emploi cadres",
            "Offres d'emploi chauffeur spl",
            "Offres d'emploi commercial",
            "Offres d'emploi communication",
            "Offres d'emploi comptable",
            "Offres d'emploi corse",
            "Offres d'emploi cpam",
            "Offres d'emploi education nationale",
            "Offres d'emploi en alternance",
            "Offres d'emploi gard",
            "Offres d'emploi herault",
            "Offres d'emploi jura",
            "Offres d'emploi lidl",
            "Offres d'emploi lvmh",
            "Offres d'emploi manche",
            "Offres d'emploi manpower",
            "Offres d'emploi mi temps",
            "Offres d'emploi ouest france",
            "Offres d'emploi pamiers",
            "Offres d'emploi quimper",
            "Offres d'emploi ressources humaines",
            "Offres d'emploi saint brieuc",
            "Offres d'emploi social",
            "Offres d'emploi tarbes",
            "Offres d'emploi pole emploi",
            "Offres d'emploi agen",
            "Offres d'emploi agent d accueil",
            "Offres d'emploi agent d entretien",
            "Offres d'emploi amazon",
            "Offres d'emploi assistante de direction",
            "Offres d'emploi associations",
            "Offres d'emploi auxerre",
            "Offres d'emploi bas rhin",
            "Offres d'emploi cannes",
            "Offres d'emploi cantal",
            "Offres d'emploi cap emploi",
            "Offres d'emploi chauffeur poids lourd",
            "Offres d'emploi chauffeur routier",
            "Offres d'emploi cholet",
            "Offres d'emploi colmar",
            "Offres d'emploi cuisinier",
            "Offres d'emploi decathlon",
            "Offres d'emploi dubai",
            "Offres d'emploi espagne",
            "Offres d'emploi fhf",
            "Offres d'emploi formateur",
            "Offres d'emploi graphiste",
            "Offres d'emploi haut rhin",
            "Offres d'emploi landes",
            "Offres d'emploi logistique",
            "Offres d'emploi lot",
            "Offres d'emploi maison de retraite",
            "Offres d'emploi maroc",
            "Offres d'emploi montauban",
            "Offres d'emploi morlaix",
            "Offres d'emploi mulhouse",
            "Offres d'emploi narbonne",
            "Offres d'emploi nevers",
            "Offres d'emploi oise",
            "Offres d'emploi orange",
            "Offres d'emploi ratp",
            "Offres d'emploi rh",
            "Offres d'emploi saint etienne",
            "Offres d'emploi sarthe",
            "Offres d'emploi secretaire medicale",
            "Offres d'emploi valenciennes",
            "Offres d'emploi vendeuse",
            "Offres d'emploi vente",
            "Offres d'emploi vichy",
            "Offres d'emploi 44",
            "Offres d'emploi 86",
            "Offres d'emploi a domicile",
            "Offres d'emploi administratif",
            "Offres d'emploi alpes maritimes",
            "Offres d'emploi angers urgent",
            "Offres d'emploi angouleme",
            "Offres d'emploi audiovisuel",
            "Offres d'emploi auxiliaire de puériculture",
            "Offres d'emploi bretagne",
            "Offres d'emploi bruxelles",
            "Offres d'emploi btp",
            "Offres d'emploi calais",
            "Offres d'emploi carcassonne",
            "Offres d'emploi cherbourg",
            "Offres d'emploi club med",
            "Offres d'emploi conseiller en insertion professionnelle",
            "Offres d'emploi draguignan",
            "Offres d'emploi drome",
            "Offres d'emploi gardien d immeuble",
            "Offres d'emploi gers",
            "Offres d'emploi gestionnaire de paie",
            "Offres d'emploi gironde",
            "Offres d'emploi haute savoie",
            "Offres d'emploi hotellerie et restauration",
            "Offres d'emploi journalisme",
            "Offres d'emploi journaliste",
            "Offres d'emploi juriste",
            "Offres d'emploi la roche sur yon",
            "Offres d'emploi laval",
            "Offres d'emploi leroy merlin",
            "Offres d'emploi les sables d olonne",
            "Offres d'emploi loire atlantique",
            "Offres d'emploi lons le saunier",
            "Offres d'emploi lot et garonne",
            "Offres d'emploi magasinier",
            "Offres d'emploi mayotte",
            "Offres d'emploi moselle",
            "Offres d'emploi nettoyage",
            "Offres d'emploi onf",
            "Offres d'emploi orleans",
            "Offres d'emploi porto vecchio",
            "Offres d'emploi portugal",
            "Offres d'emploi pour seniors",
            "Offres d'emploi pour seniors retraités",
            "Offres d'emploi roanne",
            "Offres d'emploi saint malo",
            "Offres d'emploi sport",
            "Offres d'emploi tahiti",
            "Offres d'emploi vaucluse",
            "Offres d'emploi vendanges",
            "Offres d'emploi villeneuve sur lot",
            "Offres d'emploi 47",
            "Offres d'emploi aisne",
            "Offres d'emploi arles",
            "Offres d'emploi aurillac",
            "Offres d'emploi avignon",
            "Offres d'emploi blois",
            "Offres d'emploi cdd",
            "Offres d'emploi credit agricole",
            "Offres d'emploi douai",
            "Offres d'emploi epinal",
            "Offres d'emploi guingamp",
            "Offres d'emploi haute loire",
            "Offres d'emploi hautes alpes",
            "Offres d'emploi hotesse d accueil",
            "Offres d'emploi indre et loire",
            "Offres d'emploi international",
            "Offres d'emploi maubeuge",
            "Offres d'emploi menton",
            "Offres d'emploi notariat",
            "Offres d'emploi sans diplome",
            "Offres d'emploi vendeur"
        ];
        $breadcrumb= [[
            "link" =>   $this->generateUrl("directemploi",  [], UrlGeneratorInterface::ABSOLUTE_URL),
            "title" => "Accueil"
        ], [
            'link' => $this->generateUrl('app_ParcourirlesoffresdemploiVille', [], UrlGeneratorInterface::ABSOLUTE_URL),
            'title' => "Offres d'emploi par mots clés les plus recherchés"
        ]
        ];
        $key = $request->query->get('term');
        $dataReg = $doctrine->getRepository(GeoListeRegion::class)->FindRegionForSearchBTP($key);

        $departementsByRegion = [];
        $i=0;
        foreach ($dataReg as $region) {
            $i=$i+1;
            $id_region = $region->getIdRegion();
            $libelleRegion = $region->getLibelle(); // Assurez-vous que getLibelle est le bon nom de méthode pour obtenir le libellé de la région

            $departements = $doctrine->getRepository(GeoDepartement::class)->FindDepratementFromRegionKey($id_region);

            // Stocke les informations de la région avec les départements correspondants
            $departementsByRegion[$i] = [
                'libelle' => $libelleRegion,
                'departements' => $departements
            ];
        }

        return $this->render('Indexation/ParcourirlesoffresMotCle.html.twig', [
            'keywords' => $keywordSemrush0_40Avg,
            'breadcrumb'=>$breadcrumb

        ]);
    }


    #[Route('DetailsOffre/view/{id}', name: '_view_search_details')]
    public function viewOffreDetails(
        int $id,
        ManagerRegistry $doctrine,
        Request $request,
        Session $session,
        myToolsService $myToolsService,
        MailerService $mailerService,
        UrlGeneratorInterface $urlGenerator
    ): Response {
        // Vérifier l'origine de la requête
        $allowedOrigins = [
            'https://www.carriere-industrie.com',
            'https://www.carriere-medicale.com',
            'https://www.carriere-restauration.com',
            'https://www.directetudiant.com',
            'https://www.directalternance.com',
            'https://directetudiant.com',
            'https://directalternance.com'
        ];
        $origin = $request->headers->get('Origin');

        if ($request->query->get('intitule') != null)
        {
            $intitule = $request->query->get('intitule');
        }else
        {
            $intitule=null;
        }

        $baseURL = AppExtension::forceHTTPS($this->generateUrl('directemploi', [], UrlGeneratorInterface::ABSOLUTE_URL));
        $offre = $doctrine->getRepository(OffOffre::class)->candidatOffreDetails($id);


        /**
         * @greg: offre[0] is $OffOffre why not use it ?
         */
        /**
         * @var OffOffre $OffOffre
         */
        $OffOffre = $doctrine->getRepository(OffOffre::class)->findOneBy(['id_offre' => $id]);
        if (!$OffOffre) {
            return $this->redirectToRoute('directemploi');
        }
        $UserUser = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $OffOffre->getUserUserIdUser()]);
        $secteurLien = $doctrine->getRepository(OffLienOffreListeSecteurActivite::class)->findOneBy(['off_offre_id_offre' => $id]);
        if (!$secteurLien) {
            $secteurLien = new OffLienOffreListeSecteurActivite();
            $secteurLien->setOffOffreIdOffre($id);
            $secteurLien->setOffListeSecteurActiviteIdSecteurActivite(31);
            $entityManager = $doctrine->getManager();
            $entityManager->persist($secteurLien);
            $entityManager->flush();
        }
        $secteur = $doctrine->getRepository(OffListeSecteurActivite::class)->findOneBy(['id_secteur_activite' => $secteurLien->getOffListeSecteurActiviteIdSecteurActivite()]);

        $jsonLD = [
            "@context" => "https://schema.org/",
            "@type" => "JobPosting",
            "title" => $OffOffre->getIntitule(),
            "description" => Utils::purgeHTMLForJsonLD($OffOffre->getMission()."  ".$OffOffre->getProfil()),
            "mission"=>$OffOffre->getMission(),
            "profil"=>$OffOffre->getProfil(),
            "datePosted"=> $OffOffre->getDateSoumission() ? $OffOffre->getDateSoumission()->format('Y-m-d\TH:i:s\Z') : null,
            "identifier"=>[
                "@type"=> "PropertyValue",
                "name"=> "id_offre",
                "value"=> $OffOffre->getIdOffre()
            ],
            "jobLocation"=> [
                "@type"=> "Place",
                "address"=> [
                    "@type"=> "PostalAddress",
                    "addressCountry"=> "FR",
                    "addressLocality"=> $OffOffre->getGeoVille()
                ]
            ]
        ];

        if($typeContrat = $OffOffre->getOffListeTypeContratIdTypeContrat()){
            if($typeContrat == OffListeTypeContrat::TYPE_CDD){
                $jsonLD["employmentType"] = "TEMPORARY";
            } elseif ($typeContrat == OffListeTypeContrat::TYPE_FREELANCE) {
                $jsonLD["employmentType"] = "CONTRACTOR" ;
            }elseif ($typeContrat == OffListeTypeContrat::TYPE_STAGE || $typeContrat == OffListeTypeContrat::TYPE_STAGE_FIN_ETUDE) {
                $jsonLD["employmentType"] = "INTERN";
            }

        }
        if($offre['libelleSecteur'] ?? false){
            // hesitate with "industry" and "occupationalCategory"
            $jsonLD["occupationalCategory"] = $offre['libelleSecteur'];
        }

        if($offre["raison_sociale"] ?? false){
            $jsonLD["hiringOrganization"] = [
                "@type" => "Organization",
                "name" => $offre["raison_sociale"],
                "logo"=> $baseURL.'/uploads/'.$offre["logo"],
                "description"=> Utils::purgeHTMLForJsonLD($offre["presentation_societe"])

            ];
        }

        if (!$myToolsService->isBot() && $OffOffre->getStatut() == 1) {
            self::statPrint($OffOffre, $UserUser);
            // return $this->redirectToRoute('directemploi');
        }

        $id_region = $doctrine->getRepository(OffLienOffreGeoListeRegion::class)->findOneBy(
            ['off_offre_id_offre' => $id]
        );

        if ($id_region) {
            $id_region = $id_region->getGeoListeRegionIdRegion();
        }

        //$id_region = 17;

        if ($id_region) {
            $region = $doctrine->getRepository(GeoListeRegion::class)->findOneBy(
                ['id_region' => $id_region]
            );
            if($region){
                $jsonLD["jobLocation"]["address"]["addressRegion"] = $region->getLibelle();
            }

        }

        $departement = $doctrine->getRepository(GeoDepartement::class)->findOneBy(
            ['id_departement' => $doctrine->getRepository(OffLienOffreGeoDepartement::class)->findOneBy(
                ['off_offre_id_offre' => $id]
            )]
        );
        if ($departement) {
            $departement = $departement->getGeoDepartementIdDepartement();
        }
        /**
         * @greg: $departement seems to be null
         */
        if ($OffOffre->getDateDebut() != null) {
            if (is_string($OffOffre->getDateDebut())) {
                $date_debut = $OffOffre->getDateDebut();
            } elseif (preg_match("/[0-9]{4}-[0-9]{2}-[0-9]{2}/", $OffOffre->getDateDebut())) {
                $date_debut = date_format($OffOffre->getDateDebut(), "d/m/Y");
            } else {
                $date_debut = $OffOffre->getDateDebut();
            }
        } else {
            $date_debut = null;
        }

        $id_recruteur = $OffOffre->getUserUserIdUser();
        $user_offre = $doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $id_recruteur]);

        // $red_direct = $user_offre->getRedirectionAuto();

        if ($OffOffre->getRepUrl() && $user_offre->getTypeDiffusion() != 5) {
            // TO REMOVE
            if(strpos($OffOffre->getRepUrl(), 'http') === false){
                $url_response = 'https://'.$OffOffre->getRepUrl();
            } else {
                $url_response = $OffOffre->getRepUrl();
            }
        }

        if (!isset($region)) {
            $region = null;
        }
        if ($request->isXmlHttpRequest()) {
            $id = (int) $request->request->get('offId');
            $select = (int) $request->request->get('select');

            $target = $doctrine->getRepository(OffOffre::class)->findOneBy(['id_offre' => $id]);
            $select == 1 ? $target->setStatut(1) : $target->setStatut(0);
            $entityManager = $doctrine->getManager();
            $entityManager->persist($target);
            $entityManager->flush();
            $data = ["select" => $select];
            return new JsonResponse($data);
        }
        $idoffre = $id;
        // return $this->render('candidat_offre/offreDetails.html.twig', [
        //         'url_direct' => $url_direct,
        //         'offreDetails' => $offre,
        //         "jsonLD"=> $jsonLD,
        //         'dateDebut' => $date_debut,
        //         'region' => $region,
        //         'departement' => $departement,
        //         'secteur' => $secteur->getLibelle(),
        //         'formulaireCandidature' => $form->createView(),
        //         'intitule'=>$intitule,
        //         'idoffre' => $idoffre,
        //     ]);
        $responseData = [
            'url_direct' => $url_response ?? null,
            'offreDetails' => $offre,
            'jsonLD' => $jsonLD,
            'dateDebut' => $date_debut,
            'region' => $region,
            'departement' => $departement,
            'secteur' => $secteur ? $secteur->getLibelle() : 'Secteur inconnu',
            'id_user' => $id_recruteur,
        ];
        if (in_array($origin, $allowedOrigins)) {
            $response = new JsonResponse([
                'id' => $id,
                'url_direct' => $url_response ?? null,
                'offreDetails' => $offre,
                'jsonLD' => $jsonLD,
                'dateDebut' => $date_debut,
                'region' => $region,
                'departement' => $departement,
                'secteur' => $secteur ? $secteur->getLibelle() : 'Secteur inconnu',
                'id_user' => $id_recruteur,
            ]);

            // Ajouter les en-têtes CORS
            $response->headers->set('Access-Control-Allow-Origin', $origin);
            $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
            $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

            return $response;
        }

        return new JsonResponse($responseData);
    }
    public function statPrint($OffOffre, $UserUser)
    {
        $stat_print = new OffStatistique();
        $stat_print->setOffOffreIdOffre($OffOffre->getIdOffre());
        $stat_print->setEntrEntrepriseIdEntreprise($UserUser->getEntrEntrepriseIdEntreprise());
        $stat_print->setDateLog(new DateTime());
        $stat_print->setTypeLog('1');
        $entityManager = $this->doctrine->getManager();
        $entityManager->persist($stat_print);
        $entityManager->flush();
    }
    #[Route('/api/import-offres', name: 'api_import_offres', methods: ['POST'])]
    public function import(Request $request, ToolsService $toolsService, LoggerInterface $logger): JsonResponse
    {
        $token = $request->headers->get('X-API-KEY');
        $expectedToken = $_ENV['IMPORT_SECRET_KEY'];

        if ($token !== $expectedToken) {
            $logger->warning('Clé API invalide reçue pour import-offres.');
            return new JsonResponse(['error' => 'Clé API invalide'], 401);
        }

        $content = json_decode($request->getContent(), true);
        $datas = $content['datas'] ?? null;
        $idUser = $content['id_user'] ?? null;
        $company = $content['company'] ?? null;

        if (!$datas || !$idUser || !$company) {
            $logger->warning('Paramètres manquants lors de l\'import.', [
                'datas' => $datas,
                'id_user' => $idUser,
                'company' => $company
            ]);
            return new JsonResponse(['error' => 'Paramètres manquants'], 400);
        }

        try {
            $logger->info('Début de l\'import d\'offres.', [
                'user_id' => $idUser,
                'company' => $company,
                'nb_offres' => count($datas)
            ]);

            ob_start();
            [$log, $offres] = $toolsService->saveInDatabase($datas, $idUser, $company);
            $output = ob_get_clean();

            if (str_contains($output, 'Erreur : l utilisateur n est pas reconnu')) {
                $logger->error('Utilisateur non reconnu.', ['output' => $output]);
                return new JsonResponse(['error' => 'Utilisateur non reconnu', 'output' => $output], 422);
            }

            if (str_contains($output, "Data n'est pas un tableau")) {
                $logger->error('Format d\'offre invalide.', ['output' => $output]);
                return new JsonResponse(['error' => 'Format d\'offre invalide', 'output' => $output], 422);
            }

            if (str_contains($output, '❌ Création impossible : pas de crédits')) {
                $logger->error('Utilisateur sans crédit.', ['output' => $output]);
                return new JsonResponse(['error' => 'Utilisateur sans crédit', 'output' => $output], 422);
            }

            $logger->info('Import terminé avec succès.', [
                'nb_offres_importees' => count($offres)
            ]);

            return new JsonResponse([
                'message' => 'Import terminé',
                'log' => $log,
                'offres_conservees' => $offres,
                'debug_output' => trim($output)
            ]);

        } catch (\Throwable $e) {
            $logger->critical('Erreur inattendue lors de l\'import.', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return new JsonResponse([
                'error' => 'Erreur inattendue',
                'message' => $e->getMessage()
            ], 500);
        }
    }

}
