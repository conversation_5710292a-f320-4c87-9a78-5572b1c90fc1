<?php

namespace App\Controller\carriereBTP;

use App\Entity\MetierMetier;
use App\Entity\Newfichemetier;
use App\Entity\SeoEditReferencement;
use App\Repository\ConseilRepository;
use App\Repository\MetierMetierRepository;
use App\Repository\NewfichemetierRepository;
use App\Repository\SeoEditReferencementRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\String\Slugger\SluggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class AdminController extends AbstractController
{
    private $httpClient;

    public function __construct(HttpClientInterface $httpClient)
    {
        $this->httpClient = $httpClient;
    }
    //Check if the user has permission to access
    public function isAdmin()
    {
        if (!$this->getUser()) {
            return false;
        }

        $roles = $this->getUser()->getRoles();

        return in_array("ROLE_ADMIN", $roles);
    }


    //Carriere BTP administrator
    #[Route('/BTP/admin', name: 'app_BTP_admin')]
    public function index(): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }

        return $this->render('carriereBTP/Admin/index.html.twig', [
        ]);
    }
    #[Route('/BTP/admin/EditMetier', name: 'app_BTP_admin_EditMetier')]
    public function EditMetier(ConseilRepository $conseilRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $Metier = $conseilRepository->getAllMetierBTP();
        $NbrMetier = count($Metier);
        return $this->render('carriereBTP/Admin/EditListeMetier.html.twig', [
        'Metiers'=>$Metier,
        'NbrMetier'=>$NbrMetier,
        ]);
    }
    #[Route('/BTP/admin/AjouterMetier', name: 'app_BTP_admin_AjouterMetier')]
    public function AjouterMetier(ConseilRepository $conseilRepository,Request $request): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        return $this->render('carriereBTP/Admin/AjouterMetierAvecSeo.html.twig', [
        ]);
    }
    /**
     * @Route("/BTP/admin/pushMetiertobase", name="app_BTP_admin_pushMetiertobase", methods={"POST"})
     */
    public function pushMetiertobase(Request $request, EntityManagerInterface $entityManager,SluggerInterface $slugger,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $formData = $request->request->all();
        $fileData = $request->files->get('imageMetier');

        $Metier= new MetierMetier();
        $nomMetier= $formData['nomMetier'];
        $numeroSecteur= $formData['numeroSecteur'];
        $Metier->setLibelleMetier($nomMetier);
        $Metier->setDomaineIdDomaine($numeroSecteur);
        $Metier->setFormCand(1);
        $Metier->setFormCvtheque(1);
        $Metier->setPageAcceuil(null);
        $Metier->setZoneMc(null);
        $Metier->setZoneMcDomaine(0);
        $Metier->setZoneCadre(null);
        $Metier->setMcRechercheAvancee(null);



        $SeoReferencement= new SeoEditReferencement();
        $nomMetierUrl = str_replace(' ', '-', $nomMetier);
        $nomMetierUrl = "metier-" . $nomMetierUrl;
        $nomMetierUrl = strtolower($nomMetierUrl);
        $preposition= $formData['preposition'];
        $motsCles= $formData['motsCles'];
        $paragraphe= $formData['paragraphe'];

        $SeoReferencement->setUrl($nomMetierUrl);
        $SeoReferencement->setNom($nomMetier);
        $SeoReferencement->setEditAppIdApp(4);
        $SeoReferencement->setNom($nomMetier);
        $SeoReferencement->setPreposition($preposition);
        $SeoReferencement->setKeywords($motsCles);
        $SeoReferencement->setParagrapheTitre(null);
        $SeoReferencement->setParagrapheContenu($paragraphe);
    if ($fileData)
    {
        $photo = $fileData;
        $originalFilename = pathinfo($photo->getClientOriginalName(), PATHINFO_FILENAME);
        // this is needed to safely include the file name as part of the URL
        $safeFilename = $slugger->slug($originalFilename);
        $newFilename = $safeFilename.'-'.uniqid().'.'.$photo->guessExtension();
        // Move the file to the directory where brochures are stored
        try {
            $photo->move(
                $this->getParameter('metierSeoPhoto'),
                $newFilename
            );
        } catch (FileException $e) {
            // ... handle exception if something happens during file upload
        }
        $SeoReferencement->setPhotoPath($newFilename);
    }else
    {
        $SeoReferencement->setPhotoPath(null);
    }

        $CheckMetierURL = $seoEditReferencementRepository->findOneBy(['url'=>$nomMetierUrl]);

    if ($CheckMetierURL != null)
    {
        $domaines = [4, 11, 12, 18, 46];
        $criteria = ['libelle_metier' => $nomMetier, 'domaine_id_domaine' => $domaines];
        $Checkmetier = $metierMetierRepository->findOneBy($criteria);
        $this->addFlash('msg1', 'Metier deja existe');
        $this->addFlash('idmetier', $Checkmetier->getIdMetier());
        $this->addFlash('idseo', $CheckMetierURL->getIdSeoEditReferencement());
        return $this->redirectToRoute('app_BTP_admin_AjouterMetier');

    }

        $entityManager->persist($Metier);
        $entityManager->flush();
        $entityManager->persist($SeoReferencement);
        $entityManager->flush();

        $this->addFlash('msg', 'Metier Ajouté avec succès.');
        return $this->redirectToRoute('app_BTP_admin_EditMetier');
    }

    #[Route('/BTP/admin/DeleteMetier/{idmetier}/{idseo}', name: 'app_BTP_admin_DeleteMetier')]
    public function DeleteMetier(EntityManagerInterface $entityManager,$idmetier,$idseo,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $MetierToDelete = $metierMetierRepository->findOneBy(['id_metier'=>$idmetier]);
        $SeoToDelete = $seoEditReferencementRepository->findOneBy(['id_seo_edit_referencement'=>$idseo]);
        $entityManager->remove($MetierToDelete);
        $entityManager->flush();
        $entityManager->remove($SeoToDelete);
        $entityManager->flush();
        $this->addFlash('msgdelete', 'Métier supprimé avec succès.');

        return $this->redirectToRoute('app_BTP_admin_EditMetier');

    }
    #[Route('/BTP/admin/UpdateMetier/{idmetier}/{idseo}', name: 'app_BTP_admin_UpdateMetier')]
    public function UpdateMetier(EntityManagerInterface $entityManager,$idmetier,$idseo,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $Metier = $metierMetierRepository->findOneBy(['id_metier'=>$idmetier]);
        $Seo = $seoEditReferencementRepository->findOneBy(['id_seo_edit_referencement'=>$idseo]);
        return $this->render('carriereBTP/Admin/EditMetier.html.twig', [
            'metier'=>$Metier,
            'Seo'=>$Seo
        ]);
    }

    /**
     * @Route("BTP/api/chat", name="api_chat", methods={"POST"})
     */
    public function chat(Request $request): JsonResponse
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $data = json_decode($request->getContent(), true);
        $metier = $data['metier'];

        $prompt = "Le nom du métier est : $metier. 
        Fournir:
        - Petit paragraphe descriptif du métier (3 lignes)
        - Descriptif et missions du métier (une paragraphe de 10 lignes)
        - Quelles sont les qualités requises pour devenir $metier (une paragraphe de 5 lignes)
        - Quelles sont les formations demandées pour devenir $metier (une paragraphe de 5 lignes)
        - Quelles sont les évolutions possibles après le métier de $metier (une paragraphe de 5 lignes)
        - Quelles sont les types d'entreprises qui recrutent dans le domaine du BTP (une paragraphe de 5 lignes)";

        try {
            $response = $this->httpClient->request('POST', 'https://api.openai.com/v1/completions', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ***************************************************',
                ],
                'json' => [
                    'model' => 'gpt-4',
                    'prompt' => $prompt,
                    'max_tokens' => 500,
                ],
            ]);

            $content = $response->getContent();
            $result = json_decode($content, true);

            return new JsonResponse($result['choices'][0]['text']);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Something went wrong'], 500);
        }
    }

    #[Route('/BTP/admin/AddFicheMetier/{idmetier}', name: 'app_BTP_admin_AddFicheMetier')]
    public function AddFicheMetier(EntityManagerInterface $entityManager,$idmetier,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $Metier = $metierMetierRepository->findOneBy(['id_metier'=>$idmetier]);
        return $this->render('carriereBTP/Admin/Fichemetier/AddFicheMetier.html.twig', [
            'metier'=>$Metier
        ]);
    }

    #[Route('/BTP/admin/UpdateFicheMetier/{idmetier}', name: 'app_BTP_admin_UpdateFicheMetier')]
    public function UpdateFicheMetier(EntityManagerInterface $entityManager,NewfichemetierRepository $newfichemetierRepository,$idmetier,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $Metier = $metierMetierRepository->findOneBy(['id_metier'=>$idmetier]);
        $fichemetier=$newfichemetierRepository->findOneBy(['idmetier'=>$idmetier]);
        return $this->render('carriereBTP/Admin/Fichemetier/UpdateFicheMetier.html.twig', [
            'metier'=>$Metier,
            'fichemetier'=>$fichemetier
        ]);
    }
    /**
     * @Route("/BTP/admin/pushUpdatedMetiertobase", name="app_BTP_admin_pushUpdatedMetiertobase", methods={"POST"})
     */
    public function pushUpdatedMetiertobase(Request $request, EntityManagerInterface $entityManager,SluggerInterface $slugger,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $formData = $request->request->all();
        $fileData = $request->files->get('imageMetier');
        $idMetier= $formData['idmetier'];
        $idseo= $formData['idseo'];
        $nomMetier= $formData['nomMetier'];
        $numeroSecteur= $formData['numeroSecteur'];
        $Metier = $metierMetierRepository->findOneBy(['id_metier'=>$idMetier]);
        $Metier->setLibelleMetier($nomMetier);
        $Metier->setDomaineIdDomaine($numeroSecteur);
        $entityManager->persist($Metier);
        $entityManager->flush();

        $SeoReferencement = $seoEditReferencementRepository->findOneBy(['id_seo_edit_referencement'=>$idseo]);
        $nomMetierUrl = str_replace(' ', '-', $nomMetier);
        $nomMetierUrl = "metier-" . $nomMetierUrl;
        $nomMetierUrl = strtolower($nomMetierUrl);
        $preposition= $formData['preposition'];
        $motsCles= $formData['motsCles'];
        $paragraphe= $formData['paragraphe'];

        $SeoReferencement->setUrl($nomMetierUrl);
        $SeoReferencement->setNom($nomMetier);
        $SeoReferencement->setNom($nomMetier);
        $SeoReferencement->setPreposition($preposition);
        $SeoReferencement->setKeywords($motsCles);
        $SeoReferencement->setParagrapheContenu($paragraphe);
        if ($fileData)
        {
            $photo = $fileData;
            $originalFilename = pathinfo($photo->getClientOriginalName(), PATHINFO_FILENAME);
            // this is needed to safely include the file name as part of the URL
            $safeFilename = $slugger->slug($originalFilename);
            $newFilename = $safeFilename.'-'.uniqid().'.'.$photo->guessExtension();
            // Move the file to the directory where brochures are stored
            try {
                $photo->move(
                    $this->getParameter('metierSeoPhoto'),
                    $newFilename
                );
            } catch (FileException $e) {
                // ... handle exception if something happens during file upload
            }
            $SeoReferencement->setPhotoPath($newFilename);
        }
        $entityManager->persist($SeoReferencement);
        $entityManager->flush();


        $this->addFlash('msgupdate', 'Metier Modifié avec succès.');
        return $this->redirectToRoute('app_BTP_admin_EditMetier');
    }
    #[Route('/BTP/admin/updatedb', name: 'app_BTP_admin_updatedb')]
    public function updatedb(Request $request, EntityManagerInterface $entityManager,SluggerInterface $slugger,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository)
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
       $metiers = [
            "Adjoint chargé d'affaires serrurerie",
            "Adjoint responsable d'affaires serrurerie ",
            "Agent autoroutier",
            "Agent de voirie",
            "Agent routier",
            "Agent technique second uvre",
            "Aide maçon",
            "AMOA BTP",
            "AMOE BTP",
            "Animateur d'équipe bardage",
            "Approvisionnement travaux",
            "Approvisionneur BTP",
            "Ascensoriste",
            "Assistant chef de chantier",
            "Assistant conducteur de travaux",
            "Atrier",
            "Auxiliaire de chantier",
            "Bancheur",
            "Bardeur",
            "Bétonneur",
            "Bobinier",
            "Boiseur",
            "Bolinier ",
            "Borneur",
            "Brasqueur",
            "Briqueteur",
            "Cadre de maintenance BTP",
            "Calicoteur",
            "Calorifugeur",
            "Canalisateur",
            "Cantonnier",
            "Cariste",
            "Cariste entrepôt",
            "Carreleur",
            "Carreleur mosaiste ",
            "Carrier",
            "Cartographe",
            "Centraliste béton",
            "Centraliste de boue",
            "Chapiste",
            "Chargé activité TCE",
            "Chargé d'affaires",
            "Chargé d'affaires agencement",
            "Chargé d'affaires amiante",
            "chargé d'affaires bardage",
            "Chargé d'affaires bâtiment",
            "Chargé d'affaires BTP",
            "Chargé d'affaires chantier",
            "Chargé d'affaires construction",
            "Chargé d'affaires constructions métalliques",
            "Chargé d'affaires couverture",
            "Chargé d'affaires CVC",
            "chargé d'affaires CVCD",
            "Chargé d'affaires désenfumage",
            "Chargé d'affaires efficacité énergétique",
            "Chargé d'affaires en robineterie ",
            "Chargé d'affaires foncières",
            "Chargé d'affaires génie civil",
            "Chargé d'affaires génie climatique",
            "Chargé d'affaires gros uvre ",
            "Chargé d'affaires maîtrise d'uvre",
            "chargé d'affaires menuiserie",
            "Chargé d'affaires peinture",
            "Chargé d'affaires plomberie",
            "Chargé d'affaires ravalement",
            "Chargé d'affaires rénovation",
            "Chargé d'affaires réseaux secs",
            "Chargé d'affaires sanitaire plomberie",
            "chargé d'affaires second uvre ",
            "Chargé d'affaires TCE",
            "Chargé d'affaires travaux publics",
            "chargé d'affaires VRD",
            "Chargé d'afffaires BGC",
            "Chargé de projet CFO-cfa",
            "Chargé de travaux",
            "Charpentier ",
            "Charpentier bois",
            "Charpentier métallique",
            "Chauffagiste",
            "Chauffeur de toupie",
            "Chef bardeur",
            "Chef canalisateur",
            "Chef cariste",
            "Chef d'atelier",
            "Chef de bois",
            "Chef de bureau d'études en BTP",
            "Chef de carrière ",
            "Chef de chantier",
            "Chef de chantier démolition",
            "chef de chantier forage",
            "Chef de chantier gros uvre",
            "Chef de chantier menuiserie",
            "chef de chantier menuiserie aluminium",
            "Chef de chantier travaux agencement",
            "Chef de chantier travaux publics",
            "Chef de chantier tuyauterie",
            "Chef de chantier voirie et réseaux de distribution",
            "Chef de département équipe mobile travaux",
            "Chef de groupe affaires en plomberie",
            "Chef de groupe bâtiment",
            "Chef de groupe BGC",
            "Chef de groupe BTP",
            "chef de groupe construction",
            "Chef de groupe second uvre",
            "Chef de groupe TCE",
            "Chef de groupe TP",
            "chef de groupe VRD",
            "Chef de projet bâtiment",
            "Chef de projet BIM",
            "Chef de projet BTP",
            "Chef de projet building information modeling",
            "Chef de projet conception construction",
            "Chef de projet éclairage",
            "Chef de projet industriel",
            "chef de projet TCE",
            "Chef de projet Travaux",
            "Chef de secteur travaux publics",
            "chef de service contrôle technique construction",
            "Chef de service VRD",
            "Chef de travaux",
            "Chef de travaux bâtiment",
            "Chef de travaux TCE",
            "Chef d'entretien de la voie ferrée",
            "Chef d'équipe Banche",
            "Chef d'équipe bâtiment ",
            "chef d'équipe BTP",
            "Chef d'équipe charpente",
            "Chef d'équipe coffrage",
            "Chef d'équipe couverture",
            "Chef d'équipe de démolition",
            "Chef d'équipe des travaux de voirie",
            "Chef d'équipe échafaudage",
            "Chef d'équipe électricien",
            "Chef d'équipe étanchéité",
            "Chef d'équipe ferrailleur",
            "Chef d'équipe génie civil",
            "Chef d'équipe gros uvre",
            "Chef d'équipe isolation thermique",
            "Chef d'équipe maçon",
            "Chef d'équipe mineur",
            "Chef d'équipe peinture",
            "Chef d'équipe photovoltaiques",
            "Chef d'équipe placo",
            "Chef d'équipe plombier",
            "Chef d'équipe ravalement",
            "chef d'équipe réseaux secs",
            "Chef d'équipe second uvre",
            "Chef d'équipe terrassement ",
            "Chef d'équipe travaux publics",
            "Chef d'équipe vitrage",
            "Chef d'équipe VRD",
            "Chef frigoriste",
            "Cimentier",
            "Coffreur",
            "Coffreur boiseur",
            "Commis de chantier",
            "Conducteur de benne TP",
            "Conducteur de brokk",
            "Conducteur de buldozer",
            "Conducteur de compacteur",
            "Conducteur de dumper",
            "Conducteur de grue mobile",
            "Conducteur de mécalac",
            "Conducteur de mini pelle",
            "Conducteur de nacelle",
            "Conducteur de pelle",
            "Conducteur de répandeuse",
            "Conducteur de tombereau",
            "Conducteur de travaux",
            "Conducteur de travaux agencement",
            "Conducteur de travaux bâtiment",
            "Conducteur de travaux BTP",
            "Conducteur de travaux charpente métallique",
            "Conducteur de travaux cloisons",
            "Conducteur de travaux démolition",
            "Conducteur de travaux gros uvre",
            "Conducteur de travaux maison individuelle",
            "Conducteur de travaux menuiserie",
            "Conducteur de travaux publics",
            "Conducteur de travaux second uvre",
            "Conducteur de travaux TCE",
            "Conducteur de travaux tuyauterie",
            "Conducteur d'engins",
            "Conseiller menuisier",
            "Conseiller technique",
            "Conseiller technique BTP",
            "Constructeur bois",
            "Constructeur en béton armée",
            "Constructeur en maçonnerie et béton armé",
            "Contremaître",
            "Contrôleur technique BTP",
            "Coordinateur de travaux",
            "Coordinateur lots tuyauterie",
            "Cordiste",
            "Courtier travaux",
            "Couvreur",
            "Cuisiniste",
            "Démolisseur",
            "Dénissateur BTP",
            "Dépanneur chauffage",
            "Dessinateur projet électricité",
            "Déssinateur projeteur",
            "Directeur de chantier",
            "Directeur de projet génie civil",
            "Directeur de travaux",
            "Ebéniste",
            "Echafaudeur",
            "Economiste de la construction",
            "Electricien",
            "Electricien basse tension",
            "Electricien bâtiment",
            "Electricien câbleur",
            "Electricien courant faible",
            "Electricien de bâtiment",
            "Electricien de chantier",
            "Electricien industriel",
            "Electricien nucléaire",
            "Electromécanicien ",
            "Electromécanicien maintenance",
            "Enduiseur",
            "Etancheiste",
            "Etancheur",
            "Etudes de prix",
            "Façadier",
            "Factorum",
            "Fendeur",
            "Ferraileur",
            "Fomateur en bâtiment",
            "Foreur",
            "Forgeron",
            "Frigoriste",
            "Géomètre",
            "Gravatier",
            "Gravillonneur",
            "Grutier",
            "Grutier tour",
            "Ingénieur acoustique",
            "Ingénieur bâtiment génie civil",
            "ingénieur BIM",
            "ingénieur BTP",
            "Ingénieur bureau d'études BTP",
            "Ingénieur contrôleur technique BTP",
            "ingénieur d'affaires",
            "Ingénieur d'affaires étanchéité",
            "Ingénieur d'affaires gros uvre ",
            "Ingénieur d'affaires réseaux secs",
            "Ingénieur d'affaires terrassement ",
            "ingénieur de planification en BTP",
            "Ingénieur démantèlement ",
            "Ingénieur d'études béton armé",
            "Ingénieur d'études génie électrique",
            "Ingénieur électricité",
            "Ingénieur étude de prix",
            "Ingénieur génie civil",
            "Ingénieur génie électrique",
            "Ingénieur géométre",
            "Ingénieur jenue diplômé travaux publics",
            "Ingénieur jeune diplômé  BTP",
            "Ingénieur jeune diplômé bâtiment",
            "Ingénieur jeune diplômé génie civil",
            "Ingénieur méthodes BTP",
            "Ingénieur plateforme",
            "Ingénieur structure",
            "Ingénieur travaux",
            "Ingénieur travaux confirmé",
            "Ingénieur travaux junior",
            "Ingénieur travaux neuf",
            "Ingénieur VRD",
            "Installateur en froid et conditionnement d'air",
            "Installateur panneaux photovoltaîques ",
            "Installateurphotovoltaîque ",
            "Installeur sanitaire",
            "Jointeur",
            "Maçon",
            "Maçon bâtiment",
            "Maçon coffreur",
            "Maçon couvreur",
            "Maçon finisseur",
            "Maçon fumiste",
            "Maçon VRD",
            "Maître d'uvre",
            "Maître d'uvre bâtiment",
            "Maïtre d'uvre OPC",
            "Manager affaires conception maîtrise d'uvre",
            "Manager d'affaire robineterie",
            "Manager d'affaires en plomberie",
            "Manager d'affaires génie climatique",
            "Manager d'affaires peinture",
            "Manuvre",
            "Manuvre BTP",
            "Manuvre de chantier ",
            "Manuvre travaux publics",
            "Marbrier",
            "Menuisier",
            "Menuisier agenceur",
            "Menuisier aluminium",
            "Menuisier atelier",
            "Menuisier ébéniste",
            "Menuisier installateur",
            "Menuisier poseur",
            "Menuisier poseur aluminium",
            "Métallier",
            "Métallier serrurier",
            "Métreur",
            "Miroitier",
            "Moellonneur",
            "Monteur assembleur BTP",
            "Monteur cableur",
            "Monteur charpente métallique",
            "Monteur de grue",
            "Monteur de réseaux électiques",
            "Monteur de stands",
            "Monteur de structures bois",
            "Monteur dépanneur frigoriste",
            "Monteur échafaudeur",
            "Monteur en climatisation",
            "Monteur en isolation thermique ",
            "Monteur levageur",
            "Monteur poseur en agencement",
            "Monteur structures métalliques",
            "Moquettiste ",
            "Mosaite",
            "Nacelliste",
            "Opérateur de démolition",
            "Opérateur de maintenance et travaux",
            "Ouvrier de signalisation",
            "Ouvrier du bâtiment",
            "Ouvrier polyvalent de bâtiment ",
            "Ouvrier poseur",
            "Ouvrier spécialisé rénovation",
            "Ouvrier travaux publics",
            "Ouvrier travaux publics",
            "Ouvrier voiries",
            "Ouvrier VRD",
            "Parqueteur",
            "Paveur",
            "Paysagiste",
            "Peintre",
            "Peintre au pistolet",
            "Peintre décorateur",
            "Peintre en bâtiment",
            "Peintre industriel",
            "pilote OPC",
            "Piscinier",
            "Pisciniste ",
            "Plaquiste",
            "Plaquiste confirmé",
            "Plaquiste enduiseur",
            "Plâtrier",
            "Plombier",
            "Polisseur ",
            "Poseur",
            "Poseur canalisateur",
            "Poseur de bardeaux",
            "Poseur de cheminées et poeles ",
            "Poseur de clôture",
            "Poseur de compteurs",
            "Poseur de cuisine",
            "Poseur de fenêtre",
            "Poseur de menuiserie",
            "Poseur de vérandas",
            "Poseur de voies ferrées",
            "Poseur parqueteur ",
            "Préparateur assembleur",
            "Préparateur de chantier",
            "projeteur",
            "Projeteur béton armée",
            "projeteur charpente métallique",
            "Projeteur électricité",
            "Projeteur VRD",
            "Raboteur",
            "Ramoneur",
            "Ravaleur",
            "Régaleur sur voiries",
            "Régaleur voies férrées",
            "Régleur d'enrobés",
            "Releveur de compteur",
            "Relieur",
            "Repsonsable affaires structures métalliques",
            "Repsonsable d'affaires CVCD",
            "Responsable affaires bardage",
            "Responsable affaires chantier",
            "Responsable affaires charpente",
            "Responsable affaires CVC",
            "Responsable bardeur",
            "Responsable calorifugeur",
            "Responsable construction",
            "Responsable d'affaires agencement",
            "Responsable d'affaires coffrage",
            "Responsable d'affaires désenfumage",
            "Responsable d'affaires efficacité énergétique",
            "Responsable d'affaires en génie électrique",
            "Responsable d'affaires étanchéité",
            "Responsable d'affaires gros uvre",
            "Responsable d'affaires menuiserie",
            "Responsable d'affaires ravalement",
            "Responsable d'affaires rénovation",
            "Responsable d'affaires réseaux secs",
            "Responsable de travaux BTP",
            "Responsable électromécanicien",
            "Responsable foncier",
            "Responsable génie civil",
            "Responsable parc matériel",
            "Responsable technique amiante",
            "Scieur carotteur",
            "Serrurier",
            "Staffeur",
            "Tailleur de pierre",
            "Technicie chauffagiste",
            "Technicien BTP",
            "Technicien bureau d'études",
            "Technicien chauffagiste",
            "Technicien construction",
            "Technicien d'affaires BTP",
            "Technicien des méthodes BTP",
            "Technicien d'études BTP",
            "Technicien électromécanicien",
            "Technicien électrotechnicien",
            "Technicien en électricité",
            "Technicien expert VRD",
            "Technicien génie civil",
            "Technicien gros uvre",
            "Technicien maintenance électrique",
            "Technicien mécatronique",
            "Technicien monteur réseaux électrique",
            "Technicien piscine",
            "Terrassier",
            "Tonnelier",
            "Topographe",
            "Tourneur",
            "Travaux neufs",
            "Vitrier",
            "Zingueur"
        ];


        for ($i = 0; $i < count($metiers); $i++)
            {
                $SeoToFind = $seoEditReferencementRepository
                    ->createQueryBuilder('s')
                    ->where('s.nom = :nom')
                    ->setParameter('nom', $metiers[$i])
                    ->andWhere('s.code IS NULL')
                    ->andWhere('s.edit_app_id_app = :appId')
                    ->setParameter('appId', 4)
                    ->andWhere('s.paragraphe_titre IS NULL')
                    ->andWhere('s.paragraphe_contenu IS NOT NULL')
                    ->getQuery()
                    ->getOneOrNullResult();
                $MetierToFind= $metierMetierRepository->createQueryBuilder('m')
                    ->where('m.libelle_metier = :nom')
                    ->setParameter('nom', $metiers[$i])
                    ->andWhere('m.domaine_id_domaine IN (:domaineIds)')
                    ->andWhere('m.zone_mc_domaine = 0')
                    ->setParameter('domaineIds', 4)
                    ->getQuery()
                    ->getOneOrNullResult();

                if (!$SeoToFind and !$MetierToFind)
                {
                    $Metier= new MetierMetier();
                    $nomMetier= $metiers[$i];
                    $numeroSecteur= 4;
                    $Metier->setLibelleMetier($nomMetier);
                    $Metier->setDomaineIdDomaine($numeroSecteur);
                    $Metier->setFormCand(1);
                    $Metier->setFormCvtheque(1);
                    $Metier->setPageAcceuil(null);
                    $Metier->setZoneMc(null);
                    $Metier->setZoneMcDomaine(0);
                    $Metier->setZoneCadre(null);
                    $Metier->setMcRechercheAvancee(null);

                    $entityManager->persist($Metier);
                    $entityManager->flush();


                    $SeoReferencement= new SeoEditReferencement();
                    $nomMetierUrl = str_replace(' ', '-', $nomMetier);
                    $nomMetierUrl = "metier-" . $nomMetierUrl;
                    $nomMetierUrl = strtolower($nomMetierUrl);
                    $preposition= "";
                    $motsCles= "";
                    $paragraphe= "";

                    $SeoReferencement->setUrl($nomMetierUrl);
                    $SeoReferencement->setNom($nomMetier);
                    $SeoReferencement->setEditAppIdApp(4);
                    $SeoReferencement->setPreposition($preposition);
                    $SeoReferencement->setKeywords($motsCles);
                    $SeoReferencement->setParagrapheTitre(null);
                    $SeoReferencement->setParagrapheContenu($paragraphe);
                    $SeoReferencement->setPhotoPath(null);
                    $entityManager->persist($SeoReferencement);
                    $entityManager->flush();
                }
                elseif($SeoToFind and !$MetierToFind)
                    {
                                $Metier= new MetierMetier();
                                $nomMetier= $metiers[$i];
                                $numeroSecteur= 4;
                                $Metier->setLibelleMetier($nomMetier);
                                $Metier->setDomaineIdDomaine($numeroSecteur);
                                $Metier->setFormCand(1);
                                $Metier->setFormCvtheque(1);
                                $Metier->setPageAcceuil(null);
                                $Metier->setZoneMc(null);
                                $Metier->setZoneMcDomaine(0);
                                $Metier->setZoneCadre(null);
                                $Metier->setMcRechercheAvancee(null);
                                $entityManager->persist($Metier);
                                $entityManager->flush();

                    }
                elseif(!$SeoToFind and $MetierToFind)
                {
                    $SeoReferencement= new SeoEditReferencement();
                    $nomMetier= $metiers[$i];
                    $nomMetierUrl = str_replace(' ', '-', $nomMetier);
                    $nomMetierUrl = "metier-" . $nomMetierUrl;
                    $nomMetierUrl = strtolower($nomMetierUrl);
                    $preposition= "";
                    $motsCles= "";
                    $paragraphe= "";

                    $SeoReferencement->setUrl($nomMetierUrl);
                    $SeoReferencement->setNom($nomMetier);
                    $SeoReferencement->setEditAppIdApp(4);
                    $SeoReferencement->setPreposition($preposition);
                    $SeoReferencement->setKeywords($motsCles);
                    $SeoReferencement->setParagrapheTitre(null);
                    $SeoReferencement->setParagrapheContenu($paragraphe);
                    $SeoReferencement->setPhotoPath(null);
                    $entityManager->persist($SeoReferencement);
                    $entityManager->flush();
                }
            }
        $this->addFlash('script', 'script done');
        return $this->redirectToRoute('app_BTP_admin_EditMetier');
    }


    //Carriere Medicale administrator

    #[Route('/Medicale/admin', name: 'app_Medicale_admin')]
    public function indexMedicale(): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_Medicale_accueil');
        }

        return $this->render('carriereMedicale/Admin/index.html.twig', [
        ]);
    }

    #[Route('Medicale/EditMetier', name: 'app_Medicale_admin_EditMetier')]
    public function EditMetierMedicale(ConseilRepository $conseilRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $Metier = $conseilRepository->getAllMetierMedicale();
        $NbrMetier = count($Metier);
        return $this->render('carriereMedicale/Admin/EditListeMetier.html.twig', [
            'Metiers'=>$Metier,
            'NbrMetier'=>$NbrMetier,
        ]);
    }

    #[Route('/Medicale/AjouterMetier', name: 'app_Medicale_admin_AjouterMetier')]
    public function AjouterMetierMedicale(ConseilRepository $conseilRepository,Request $request): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        return $this->render('carriereMedicale/Admin/AjouterMetierAvecSeo.html.twig', [
        ]);
    }

    #[Route('/Medicale/admin/UpdateMetier/{idmetier}/{idseo}', name: 'app_Medicale_admin_UpdateMetier')]
    public function UpdateMetierMedicale(EntityManagerInterface $entityManager,$idmetier,$idseo,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $Metier = $metierMetierRepository->findOneBy(['id_metier'=>$idmetier]);
        $Seo = $seoEditReferencementRepository->findOneBy(['id_seo_edit_referencement'=>$idseo]);
        return $this->render('carriereMedicale/Admin/EditMetier.html.twig', [
            'metier'=>$Metier,
            'Seo'=>$Seo
        ]);
    }
    #[Route('/Medicale/DeleteMetier/{idmetier}/{idseo}', name: 'app_Medicale_admin_DeleteMetier')]
    public function DeleteMetierMedicale(EntityManagerInterface $entityManager,$idmetier,$idseo,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $MetierToDelete = $metierMetierRepository->findOneBy(['id_metier'=>$idmetier]);
        $SeoToDelete = $seoEditReferencementRepository->findOneBy(['id_seo_edit_referencement'=>$idseo]);
        $entityManager->remove($MetierToDelete);
        $entityManager->flush();
        $entityManager->remove($SeoToDelete);
        $entityManager->flush();
        $this->addFlash('msgdelete', 'Métier supprimé avec succès.');

        return $this->redirectToRoute('app_Medicale_admin_EditMetier');
    }

    /**
     * @Route("/Medicale/admin/pushMetiertobase", name="app_Medicale_admin_pushMetiertobase", methods={"POST"})
     */
    public function pushMetiertobaseMedicale(Request $request, EntityManagerInterface $entityManager,SluggerInterface $slugger,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $formData = $request->request->all();
        $fileData = $request->files->get('imageMetier');

        $Metier= new MetierMetier();
        $nomMetier= $formData['nomMetier'];
        $numeroSecteur= $formData['numeroSecteur'];
        $Metier->setLibelleMetier($nomMetier);
        $Metier->setDomaineIdDomaine($numeroSecteur);
        $Metier->setFormCand(1);
        $Metier->setFormCvtheque(1);
        $Metier->setPageAcceuil(null);
        $Metier->setZoneMc(null);
        $Metier->setZoneMcDomaine(0);
        $Metier->setZoneCadre(null);
        $Metier->setMcRechercheAvancee(null);



        $SeoReferencement= new SeoEditReferencement();
        $nomMetierUrl = str_replace(' ', '-', $nomMetier);
        $nomMetierUrl = "metier-" . $nomMetierUrl;
        $nomMetierUrl = strtolower($nomMetierUrl);
        $preposition= $formData['preposition'];
        $motsCles= $formData['motsCles'];
        $paragraphe= $formData['paragraphe'];

        $SeoReferencement->setUrl($nomMetierUrl);
        $SeoReferencement->setNom($nomMetier);
        $SeoReferencement->setEditAppIdApp(8);
        $SeoReferencement->setNom($nomMetier);
        $SeoReferencement->setPreposition($preposition);
        $SeoReferencement->setKeywords($motsCles);
        $SeoReferencement->setParagrapheTitre(null);
        $SeoReferencement->setParagrapheContenu($paragraphe);
        if ($fileData)
        {
            $photo = $fileData;
            $originalFilename = pathinfo($photo->getClientOriginalName(), PATHINFO_FILENAME);
            // this is needed to safely include the file name as part of the URL
            $safeFilename = $slugger->slug($originalFilename);
            $newFilename = $safeFilename.'-'.uniqid().'.'.$photo->guessExtension();
            // Move the file to the directory where brochures are stored
            try {
                $photo->move(
                    $this->getParameter('metierSeoPhoto'),
                    $newFilename
                );
            } catch (FileException $e) {
                // ... handle exception if something happens during file upload
            }
            $SeoReferencement->setPhotoPath($newFilename);
        }else
        {
            $SeoReferencement->setPhotoPath(null);
        }

        $CheckMetierURL = $seoEditReferencementRepository->findOneBy(['url'=>$nomMetierUrl]);

        if ($CheckMetierURL != null)
        {
            $domaines = [26,38];
            $criteria = ['libelle_metier' => $nomMetier, 'domaine_id_domaine' => $domaines];
            $Checkmetier = $metierMetierRepository->findOneBy($criteria);
            $this->addFlash('msg1', 'Metier deja existe');
            $this->addFlash('idmetier', $Checkmetier->getIdMetier());
            $this->addFlash('idseo', $CheckMetierURL->getIdSeoEditReferencement());
            return $this->redirectToRoute('app_Medicale_admin_AjouterMetier');

        }

        $entityManager->persist($Metier);
        $entityManager->flush();
        $entityManager->persist($SeoReferencement);
        $entityManager->flush();

        $this->addFlash('msg', 'Metier Ajouté avec succès.');
        return $this->redirectToRoute('app_Medicale_admin_EditMetier');
    }


    /**
     * @Route("/Medicale/admin/pushUpdatedMetiertobase", name="app_Medicale_admin_pushUpdatedMetiertobase", methods={"POST"})
     */
    public function pushUpdatedMetiertobaseMedicale(Request $request, EntityManagerInterface $entityManager,SluggerInterface $slugger,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $formData = $request->request->all();
        $fileData = $request->files->get('imageMetier');
        $idMetier= $formData['idmetier'];
        $idseo= $formData['idseo'];
        $nomMetier= $formData['nomMetier'];
        $numeroSecteur= $formData['numeroSecteur'];
        $Metier = $metierMetierRepository->findOneBy(['id_metier'=>$idMetier]);
        $Metier->setLibelleMetier($nomMetier);
        $Metier->setDomaineIdDomaine($numeroSecteur);
        $entityManager->persist($Metier);
        $entityManager->flush();

        $SeoReferencement = $seoEditReferencementRepository->findOneBy(['id_seo_edit_referencement'=>$idseo]);
        $nomMetierUrl = str_replace(' ', '-', $nomMetier);
        $nomMetierUrl = "metier-" . $nomMetierUrl;
        $nomMetierUrl = strtolower($nomMetierUrl);
        $preposition= $formData['preposition'];
        $motsCles= $formData['motsCles'];
        $paragraphe= $formData['paragraphe'];

        $SeoReferencement->setUrl($nomMetierUrl);
        $SeoReferencement->setNom($nomMetier);
        $SeoReferencement->setNom($nomMetier);
        $SeoReferencement->setPreposition($preposition);
        $SeoReferencement->setKeywords($motsCles);
        $SeoReferencement->setParagrapheContenu($paragraphe);
        if ($fileData)
        {
            $photo = $fileData;
            $originalFilename = pathinfo($photo->getClientOriginalName(), PATHINFO_FILENAME);
            // this is needed to safely include the file name as part of the URL
            $safeFilename = $slugger->slug($originalFilename);
            $newFilename = $safeFilename.'-'.uniqid().'.'.$photo->guessExtension();
            // Move the file to the directory where brochures are stored
            try {
                $photo->move(
                    $this->getParameter('metierSeoPhoto'),
                    $newFilename
                );
            } catch (FileException $e) {
                // ... handle exception if something happens during file upload
            }
            $SeoReferencement->setPhotoPath($newFilename);
        }
        $entityManager->persist($SeoReferencement);
        $entityManager->flush();


        $this->addFlash('msgupdate', 'Metier Modifié avec succès.');
        return $this->redirectToRoute('app_Medicale_admin_EditMetier');
    }
    #[Route('/BTP/admin/Fichemetier', name: 'app_BTP_admin_Fichemetier')]
    public function indexFicheMetier(ConseilRepository $conseilRepository, NewfichemetierRepository $newfichemetierRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $Metier = $conseilRepository->getAllMetierBTP();

        $uniqueMetiers = [];
        foreach ($Metier as $metier) {
            $uniqueMetiers[$metier['nom']] = $metier;
        }
        $Metier = array_values($uniqueMetiers);
        for ($i = 0; $i < sizeof($Metier); $i++) {
            $fichemetier = $newfichemetierRepository->findOneBy(['idmetier' => $Metier[$i]['id_metier']]);
            if ($fichemetier) {
                $Metier[$i]['hasFichemetier'] = true;
            } else {
                $Metier[$i]['hasFichemetier'] = false;
            }
        }
        $NbrMetier = count($Metier);
        return $this->render('carriereBTP/Admin/Fichemetier/index.html.twig', [
            'Metiers' => $Metier,
            'NbrMetier' => $NbrMetier,
        ]);
    }




    /**
     * @Route("/BTP/admin/pushUpdatedFicheMetiertobase", name="app_BTP_admin_pushUpdatedFicheMetiertobase", methods={"POST"})
     */
    public function pushUpdatedFicheMetiertobase(Request $request,NewfichemetierRepository $newfichemetierRepository, EntityManagerInterface $entityManager,SluggerInterface $slugger,MetierMetierRepository $metierMetierRepository,SeoEditReferencementRepository $seoEditReferencementRepository): Response
    {
        if (!$this->isAdmin())
        {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        $formData = $request->request->all();

        $idMetier= $formData['idmetier'];
        $smalldesc= $formData['smalldesc'];
        $titre1= $formData['titre1'];
        $titre2= $formData['titre2'];
        $titre3= $formData['titre3'];
        $titre4= $formData['titre4'];
        $titre5= $formData['titre5'];


        $paragraphe1= $formData['paragraphe1'];
        $paragraphe2= $formData['paragraphe2'];
        $paragraphe3= $formData['paragraphe3'];
        $paragraphe4= $formData['paragraphe4'];
        $paragraphe5= $formData['paragraphe5'];

        $NewFicheMetier=$newfichemetierRepository->findOneBy(['idmetier'=>$idMetier]);
        if (!$NewFicheMetier)
        {
            $NewFicheMetier= new Newfichemetier();
        }
       $NewFicheMetier->setQ1($titre1);
       $NewFicheMetier->setQ2($titre2);
       $NewFicheMetier->setQ3($titre3);
       $NewFicheMetier->setQ4($titre4);
       $NewFicheMetier->setQ5($titre5);

       $NewFicheMetier->setD1($paragraphe1);
       $NewFicheMetier->setD2($paragraphe2);
       $NewFicheMetier->setD3($paragraphe3);
       $NewFicheMetier->setD4($paragraphe4);
       $NewFicheMetier->setD5($paragraphe5);

       $NewFicheMetier->setIdmetier($idMetier);
       $NewFicheMetier->setDomainesiteid(4);
       $NewFicheMetier->setPetitDescription($smalldesc);
        $entityManager->persist($NewFicheMetier);
        $entityManager->flush();

        if (!$NewFicheMetier) {
            $this->addFlash('msginsertfichemetier', 'Fiche metier ajouté avec succès.');
        }else
            $this->addFlash('msgupdatefichemetier', 'Fiche metier modifié avec succès.');
        return $this->redirectToRoute('app_BTP_admin_Fichemetier');
    }

}
