<?php

namespace App\Controller\carriereBTP;

use App\Entity\CvCv;
use App\Entity\DomaineMetier;
use App\Entity\EcEcole;
use App\Entity\EcOrganismeFormation;
use App\Entity\EcSessionFormation;
use App\Entity\GeoAdresse;
use App\Entity\GeoDepartement;
use App\Entity\GeoListePays;
use App\Entity\GeoListeRegion;
use App\Entity\GeoVille;
use App\Entity\GeoVillePrincipale;
use App\Entity\MetierMetier;
use App\Entity\OffOffre;
use App\Entity\UserCompetences;
use App\Entity\UserOffreVeille;
use App\Entity\UserSituation;
use App\Entity\UserUser;
use App\Form\carriereBTP\InscriptionCandidatBTPEtape2Type;
use App\Form\carriereBTP\InscriptionCandidatBTPEtape3Type;
use App\Form\carriereBTP\RegistrationFormBTPType;
use App\Repository\GeoListePaysRepository;
use App\Security\UsersAuthenticator;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mime\Address;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Authentication\UserAuthenticatorInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class RegistrationBTPController extends AbstractController
{

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    #[Route('/BTP/inscriptionCandEtape1', name: 'app_BTP_register_candidat_e1')]
    public function register(Request $request , UserPasswordHasherInterface $userPasswordHasher, ManagerRegistry $doctrine,UserAuthenticatorInterface $userAuthenticator, UsersAuthenticator $authenticator, EntityManagerInterface $entityManager): Response
    {
        if ($this->getUser()) {
            return $this->redirectToRoute('app_BTP_accueil');
        }
        //$user = new User();
        $form = $this->createForm(RegistrationFormBTPType::class);
        $form->handleRequest($request);
        if ($form->isSubmitted()) {
            //**User registration For GeoAddress**//
            $user_geo = new GeoAdresse();
            $user_geo->setGeoListeRegionIdRegion($doctrine->getRepository(GeoDepartement::class)->findRegByDep($form->get('code_postal')->getData()));
            $user_geo->setAdresse1("Vide");
            $user_geo->setAdresse2("Vide");
            $user_geo->setCodePostal($form->get('code_postal')->getData());
            $user_geo->setNomVille($form->get('ville')->getData());
            $user_geo->setDateMaj(new \DateTime());
            // Récupérer l'objet GeoListePays à partir du formulaire
            $pays = $form->get('pays')->getData();
            $paysobject = $doctrine->getRepository(GeoListePays::class)->findOneBy(['id_pays' => $pays->getIdpays()]);

// Assigner l'objet GeoListePays à l'entité GeoAdresse
            $user_geo->setGeoListePays($paysobject);

// Persistez et sauvegardez l'entité
            $entityManager->persist($user_geo);
            $entityManager->flush();



            //**User registration For UserUser**//
            $user_recrut = new UserUser();
            $user_recrut->setRoles(array("ROLE_CANDID"));
            $user_recrut->setUserCiviliteIdUserCivilite($form->get('user_civilite_id_user_civilite')->getData());
            $user_recrut->setEntrEntrepriseIdEntreprise(null);
            $user_recrut->setEcEcoleIdEcole(null);
            $user_recrut->setEcOrganismeFormationIdOrganismeFormation(null);
            $user_recrut->setUserListeTypeIdType(3); //Type candidat
            $user_recrut->setNom($form->get('nom')->getData());
            $user_recrut->setPrenom($form->get('prenom')->getData());
            $user_recrut->setEmail($form->get('email')->getData());
            $user_recrut->setTelephone($form->get('telephone')->getData());
            $user_recrut->setFax(null);
            $user_recrut->setMobile(null);
            $user_recrut->setDateCreation(new \DateTime());
            $user_recrut->setDateDerniereModification(new \DateTime());
            $user_recrut->setLogin($form->get('login')->getData());
            $user_recrut->setPass('');
            $user_recrut->setPassCrypt($userPasswordHasher->hashPassword($user_recrut, $form->get('passCrypt')->getData()));
            $user_recrut->setGeoAdresseIdAdresse($user_geo->getIdAdresse());
            $user_recrut->setDateNaissance(null);
            $user_recrut->setIsHtmlFormat(0);
            $user_recrut->setDateDerniereConnexion(new \DateTime());
            $user_recrut->setDateDerniereRelance(null);
            $user_recrut->setPassForm(1);
            $user_recrut->setFormSimplified(0);
            $user_recrut->setAlertFreq(0);
            $user_recrut->setAboNews($form->getData('newsletter') ? 1 : 0);
            $user_recrut->setRedirectionAuto(null);
            $user_recrut->setSponsor(0);
            $user_recrut->setCleRecuperation(null);
            $user_recrut->setTypeDiffusion(null);
            $user_recrut->setUserEcole(null);
            $entityManager->persist($user_recrut);
            $entityManager->flush();


            //**User registration For UserSituation**//
            $user_situation = new UserSituation();
            $user_situation->setUserUserIdUser($user_recrut->getIdUser());
            $user_situation->setoffListeTypeFormationIdTypeFormation($form->get('formation_level')->getData());
            if($form->get('ec_ecole')->getViewData() != null) {

                if(in_array($form->get('formation_level')->getData(),array(6,7,8))){
                    $user_situation->setEcEcoleIdEcole(null);
                    $user_situation->setEcSessionFormationIdSessionFormation($form->get('ec_ecole')->getViewData());
                } else {
                    $user_situation->setEcEcoleIdEcole($form->get('ec_ecole')->getViewData());
                    $user_situation->setEcSessionFormationIdSessionFormation(0);
                }
            } else {
                $user_situation->setEcEcoleIdEcole(null);
                $user_situation->setEcSessionFormationIdSessionFormation(0);
            }
            $user_situation->setOffListeSecteurActiviteIdSecteurActivite($form->get('domaine_activite')->getData()->getIdSecteurActivite());
            $user_situation->setIdMetier($form->get('metier_metier')->getViewData());
            $user_situation->setNationalite(0);
            $user_situation->setUserDisponibiliteIdDisponibilite(0);
            $user_situation->setUserSituationMobiliteIdSituationMobilite(0);
            $user_situation->setEtablissementAutre(0);
            $user_situation->setAnneeObtentionDiplome($form->get('anneeObtentionDiplome')->getData());
            $user_situation->setNomDiplome(0);
            $user_situation->setPosteActuel(0);
            $user_situation->setIdOffFourchetteRemuneration(0);
            $user_situation->setOffListeSecteurActiviteIdSecteurActivitePoste(0);
            $user_situation->setUserSituationStatutIdSituationStatut(0);
            $user_situation->setUserSituationExperienceIdSituationExperience(0);
            $user_situation->setUserSituationActiviteIdSituationActivite(0);

            $entityManager->persist($user_situation);
            $entityManager->flush();

            //**User registration For cv**//
            if ($form->get('cv_file')->getData()) {
                $CvCv = new CvCv();
                $CvCv->setDateCreation(new \DateTime());
                $CvCv->setIsVisible(1);
                $CvCv->setIsAnonym(0);
                $CvCv->setDateModification(new \DateTime());
                $CvCv->setUserUserIdUser($user_recrut->getIdUser());
                $CvCv->setTitre('Cv de ' . $user_recrut->getNom() . ' ' . $user_recrut->getPrenom());
                $CvCv->setIntitulePoste('');

                $brochureFile = $form->get('cv_file')->getData();
                $originalFilename = pathinfo($brochureFile->getClientOriginalName(), PATHINFO_FILENAME);
                //$safeFilename = $slugger->slug($originalFilename);
                //$nom_filename = "cv3343111" . date('YmdHis');
                $nom_filename = $user_recrut->getIdUser() . date('YmdHis');
                $newFilename = $nom_filename . '-' . uniqid() . '.' . $brochureFile->guessExtension();

                try {
                    $brochureFile->move(
                        $this->getParameter('cv_cand_save'),
                        $newFilename
                    );
                } catch (FileException $e) {
                    // ... handle exception if something happens during file upload
                }

                $CvCv->setNomFichierCvOriginal($originalFilename);
                $CvCv->setNomFichierCvStockage($newFilename);
                $CvCv->setOffFourchetteRemunerationIdFourchetteRemunerationSouhaite(0);
                $CvCv->setOffFourchetteRemunerationIdFourchetteRemunerationActuel(0);
                $CvCv->setEtat(0);
                $entityManager->persist($CvCv);
                $entityManager->flush();
            }
            // encode the plain password




            $userAuthenticator->authenticateUser(
                $user_recrut,
                $authenticator,
                $request
            );
            return $this->redirectToRoute('app_BTP_register_candidat_e2');
            //$baseURL = $this->generateUrl("directemploi", [], UrlGeneratorInterface::ABSOLUTE_URL);

        }

        return $this->render('carriereBTP/registration/register.html.twig', [
            'registrationForm' => $form->createView(),
        ]);
    }

    #[Route('/BTP/inscriptionCandEtape2', name: 'app_BTP_register_candidat_e2')]
    public function inscriptionCandidatEtape2(
        Request $request,
        EntityManagerInterface $entityManager,
        ManagerRegistry $doctrine,
    ): Response {

        if ($this->getUser()) {
            $id_user = $this->getUser()->getIdUser();
        } else {
            $id_user = 66666666;
            return $this->redirectToRoute('app_BTP_register_candidat_e1');
        }

        $form = $this->createForm(InscriptionCandidatBTPEtape2Type::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $user_situation = $doctrine->getRepository(UserSituation::class)->findOneBy(["user_user_id_user" => $id_user]);
            $user_situation->setUserUserIdUser($id_user);
            $user_situation->setUserSituationExperienceIdSituationExperience($form->get('experience')->getData() ? $form->get('experience')->getData()->getIdSituationExperience() : 0);
            //$user_situation->setOffListeSecteurActiviteIdSecteurActivite($form->get('domaine_activite')->getData()->getIdSecteurActivite());
            $user_situation->setIdOffFourchetteRemuneration($form->get('remuneration')->getData() ? $form->get('remuneration')->getData()->getIdFourchetteRemuneration() : 0);
            $user_situation->setUserDisponibiliteIdDisponibilite($form->get('disponibilite')->getData() ? $form->get('disponibilite')->getData()->getIdDisponibilite() : 0);
            $user_situation->setUserSituationMobiliteIdSituationMobilite($form->get('mobilite')->getData() ? $form->get('mobilite')->getData()->getIdRegion() : 0);
            $user_situation->setUserSituationActiviteIdSituationActivite($form->get('activite')->getData() ? $form->get('activite')->getData()->getIdSituationActivite() : 0);
            $entityManager->persist($user_situation);
            $entityManager->flush();
            $user_competences = new UserCompetences();
            $user_competences->setUserUserIdUser($id_user);
            $user_competences->setListeCompetences((string)$form->get('competence')->getData());

            if ($form->get('langue1')->getData()) {
                $user_competences->setIdChoixLangue1($form->get('langue1')->getData() ? $form->get('langue1')->getData()->getIdChoixLangue() : null);
                $user_competences->setIdNiveauLangue1($form->get('level1')->getData() ? $form->get('level1')->getData()->getIdNiveauLangue() : null);
            } else {
                $user_competences->setIdChoixLangue1(null);
                $user_competences->setIdNiveauLangue1(null);
            }

            if ($form->get('langue2')->getData()) {
                $user_competences->setIdChoixLangue2($form->get('langue2')->getData() ? $form->get('langue2')->getData()->getIdChoixLangue() : null);
                $user_competences->setIdNiveauLangue2($form->get('level1')->getData() ? $form->get('level2')->getData()->getIdNiveauLangue() : null);
            } else {
                $user_competences->setIdChoixLangue2(null);
                $user_competences->setIdNiveauLangue2(null);
            }

            $user_competences->setPermisB($form->get('permis')->getData());
            $user_competences->setIsProfilVisible($form->get('profil_anonyme')->getData());

            $entityManager->persist($user_competences);
            $entityManager->flush();

            return $this->redirectToRoute('app_BTP_register_candidat_e3');
        }


        return $this->render('carriereBTP/registration/registerEtape2.html.twig', [
            'InscriptionEtape2Form' => $form->createView(),

        ]);
    }
    #[Route('/BTP/inscriptionCandEtape3', name: 'app_BTP_register_candidat_e3')]
    public function inscriptionCandidatEtape3(
        Request $request,
        EntityManagerInterface $entityManager,
        ManagerRegistry $doctrine,
    ): Response {

        if ($this->getUser()) {
            $id_user = $this->getUser()->getIdUser();
        } else {
            $id_user = 66666666;
            //return $this->redirectToRoute('app_register_candidat_e1');
        }

        $form = $this->createForm(InscriptionCandidatBTPEtape3Type::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $user_veille = new UserOffreVeille();
            $user_veille->setLibelle($form->get('nom_alerte') ? $form->get('nom_alerte')->getData() : "Mon alerte du " . date("d/m/Y"));
            $user_veille->setExperienceSerialized($form->get('experience')->getData() ? serialize($form->get('experience')->getData()->getIdSituationExperience()) : null);
            $user_veille->setActif("1");
            $user_veille->setTypeContratSerialized($form->get('contrat') ? serialize($form->get('contrat')->getData()->getIdTypeContrat()) : null);
            $user_veille->setSecteurActiviteSerialized($form->get('domaine_activite') ? serialize($form->get('domaine_activite')->getData()->getIdSecteurActivite()) : null);
            $user_veille->setRegionSerialized($form->get('geo_liste_region')->getData() ? serialize($form->get('geo_liste_region')->getData()->getIdRegion()) : null);
            $user_veille->setFormationSerialized(null);
            $user_veille->setRemunerationSerialized(null);
            $user_veille->setMotsCles($form->get('metier_metier') ? $form->get('metier_metier')->getData()->getLibelleMetier() : null);
            $user_veille->setHandicapSerialized($form->get('handi') ? serialize($form->get('handi')->getData()) : serialize(1));
            $user_veille->setIsRss('0');
            $user_veille->setDepartementSerialized($form->get('geo_departement')->getData() ? serialize($form->get('geo_departement')->getData()->getIdDepartement()) : null);
            //$user_veille->setdate_passage_cron;
            $user_veille->setDateDernierEnvoi(new \DateTime("2015-11-01 00:00:00"));
            $user_veille->setUserUserIdUser($id_user);
            $entityManager->persist($user_veille);
            $entityManager->flush();

            return $this->redirectToRoute('app_BTP_espaceCandidat');
        }

        return $this->render('carriereBTP/registration/registerEtape3.html.twig', [
            'InscriptionEtape3Form' => $form->createView(),

        ]);
    }


    #[Route('/ajax/get-metiers/{domaineActiviteId}', name: 'ajax_get_metiers', methods: ['GET'])]
    public function getMetiers($domaineActiviteId): JsonResponse
    {
        $metiers = $this->entityManager
            ->getRepository(MetierMetier::class)
            ->findBy(['domaine_id_domaine' => $domaineActiviteId]);

        $metiersArray = [];
        foreach ($metiers as $metier) {
            $metiersArray[] = [
                'id' => $metier->getIdMetier(),
                'libelle_metier' => $metier->getLibelleMetier(),
            ];
        }

        return new JsonResponse($metiersArray);
    }
    #[Route('BTP/ajax/get-offre', name: 'ajax_get_offres', methods: ['GET'])]
    public function getOffre(): JsonResponse
    {
        $AlloffresperWeek=$this->entityManager->getRepository(OffOffre::class)->getOffresPerWeek();

        return new JsonResponse($AlloffresperWeek);
    }
    #[Route('BTP/ajax/get-offre-ville', name: 'ajax_get_offres_ville', methods: ['GET'])]
    public function getvilleOffre(): JsonResponse
    {
        $AlloffresperWeek=$this->entityManager->getRepository(OffOffre::class)->getVilleOffresPerWeek();
        // Initialiser un tableau pour stocker les villes uniques
        $villes = array_unique(array_column($AlloffresperWeek, 'geo_ville'));

        // Initialiser le tableau final
        $villesWithPostalCodes = [];

        // Parcourir chaque ville pour récupérer son code postal
        foreach ($villes as $ville) {
            $postalCode = $this->entityManager->getRepository(GeoVille::class)->FindPostalCodeVille($ville);

            // Vérifier que le code postal est trouvé et qu'il est un tableau
            if (is_array($postalCode) && isset($postalCode['code_postal'])) {
                $villesWithPostalCodes[] = [
                    'ville' => $ville,
                    'code_postal' => $postalCode['code_postal']
                ];
            }
        }
dd($villesWithPostalCodes);
        return new JsonResponse($villesWithPostalCodes);
    }
    #[Route('BTP/ajax/get-metiers/{domaineActiviteId}', name: 'ajax_BTP_get_metiers', methods: ['GET'])]
    public function getMetiersBTP($domaineActiviteId): JsonResponse
    {
        $metiers = $this->entityManager
            ->getRepository(DomaineMetier::class)
            ->findBy(['domaine_id_domaine' => $domaineActiviteId]);

        $metiersArray = [];
        foreach ($metiers as $metier) {
            $metiersArray[] = [
                'id' => $metier->getIdMetier(),
                'libelle_metier' => $metier->getLibelle(),
            ];
        }

        usort($metiersArray, function ($a, $b) {
            return strcmp($a['libelle_metier'], $b['libelle_metier']);
        });

        return new JsonResponse($metiersArray);
    }

    #[Route('BTP/ajax/get-ecoles/{formationLevelId}', name: 'ajax_get_ecoles_BTP', methods: ['GET'])]
    public function getEcoles($formationLevelId): JsonResponse
    {
        $ecolesArray = [];

        if ($formationLevelId == 22) {
            $ecoles = $this->entityManager
                ->getRepository(EcOrganismeFormation::class)
                ->findBy(['ec_liste_type_organisme_formation_id_type_organisme_formation' => 1]);

            foreach ($ecoles as $ecole) {
                $ecolesArray[] = [
                    'id' => $ecole->getIdOrganismeFormation(),
                    'nom_ecole' => $ecole->getNomOrganisme(),
                ];
            }
        } elseif (in_array($formationLevelId, [6, 7, 8])) {
            // Exemple avec EcSessionFormation
            $ecoles = $this->entityManager
                ->getRepository(EcSessionFormation::class)
                ->createQueryBuilder('p')
                //->join('App\Entity\EcSessionFormation', 'b', 'WITH', 'b.ec_ecole_id_ecole = p.id_ecole')
                ->where('p.off_liste_type_formation_id_type_formation = :id')
                ->distinct('p.nom_formation')
                ->setParameter('id', $formationLevelId)
                ->orderBy('p.nom_formation')
                ->getQuery()
                ->getResult();

            foreach ($ecoles as $ecole) {
                $ecolesArray[] = [
                    'id' => $ecole->getEcEcoleIdEcole(),
                    'nom_ecole' => $ecole->getNomFormation(),
                ];
            }
        } else {
            // Exemple avec EcEcole
            $ecoles = $this->entityManager
                ->getRepository(EcEcole::class)
                ->createQueryBuilder('p')
                ->join('App\Entity\EcSessionFormation', 'b', 'WITH', 'b.ec_ecole_id_ecole = p.id_ecole')
                ->where('b.off_liste_type_formation_id_type_formation = :id')
                ->distinct('p.nom_ecole')
                ->setParameter('id', $formationLevelId)
                ->getQuery()
                ->getResult();

            foreach ($ecoles as $ecole) {
                $ecolesArray[] = [
                    'id' => $ecole->getIdEcole(),
                    'nom_ecole' => $ecole->getNomEcole(),
                ];
            }
        }
        usort($ecolesArray, function ($a, $b) {
            return strcmp($a['nom_ecole'], $b['nom_ecole']);
        });
        return new JsonResponse($ecolesArray);
    }
    #[Route('/ajax/get-metiers-with-mot-cle/{motcle}', name: 'ajax_get_Metierswithmotcle', methods: ['GET'])]
    public function getMetierswithmotcle($motcle): JsonResponse
    {
        $metiers = $this->entityManager
            ->getRepository(MetierMetier::class)
            ->findBy(['libelle_metier' => $motcle]);

        $metiersArray = [];
        foreach ($metiers as $metier) {
            $metiersArray[] = [
                'id' => $metier->getIdMetier(),
                'libelle_metier' => $metier->getLibelleMetier(),
            ];
        }

        return new JsonResponse($metiersArray);
    }
    #[Route('/ajax/VerifyEmail/{email}', name: 'ajax_VerifyEmail', methods: ['GET'])]
    public function VerifyEmail($email): JsonResponse
    {
        $user = $this->entityManager->getRepository(UserUser::class)->findOneBy(['email' => $email]);

        if ($user) {
            // User with the given email exists
            return new JsonResponse(['exists' => true]);
        } else {
            // User with the given email does not exist
            return new JsonResponse(['exists' => false]);
        }
    }


    #[Route('/ajax/get-departements/{regionId}', name: 'ajax_get_departements', methods: ['GET'])]
    public function getDepartements($regionId): JsonResponse
    {
        $departements = $this->entityManager
            ->getRepository(GeoDepartement::class)
            ->findBy(['geo_liste_region_id_region' => $regionId]);

        $departementsArray = [];
        foreach ($departements as $departement) {
            $departementsArray[] = [
                'id' => $departement->getIdDepartement(),
                'libelle' => $departement->getLibelle(),
            ];
        }

        return new JsonResponse($departementsArray);
    }
    #[Route('/ajax/get-region/{regionId}', name: 'ajax_get_region', methods: ['GET'])]
    public function getregion($regionId): JsonResponse
    {
        $regions = $this->entityManager
            ->getRepository(GeoListeRegion::class)
            ->findBy(['id_region' => $regionId]);

        $regionArray = [];
        foreach ($regions as $region) {
            $regionArray[] = [
                'id' => $region->getIdRegion(),
                'libelle' => $region->getLibelle(),
            ];
        }

        return new JsonResponse($regionArray);
    }
    #[Route('/ajax/get-region/', name: 'ajax_get_all_region', methods: ['GET'])]
    public function getAllregion(): JsonResponse
    {
        $regions = $this->entityManager
            ->getRepository(GeoListeRegion::class)
            ->findAll();

        $regionArray = [];
        foreach ($regions as $region) {
            $regionArray[] = [
                'id' => $region->getIdRegion(),
                'libelle' => $region->getLibelle(),
            ];
        }

        return new JsonResponse($regionArray);
    }

}
