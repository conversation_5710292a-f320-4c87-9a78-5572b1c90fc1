<?php

namespace App\Controller\carriereBTP;

use App\Entity\Conseil;
use App\Entity\CvCv;
use App\Entity\EditReferencement;
use App\Entity\GeoDepartement;
use App\Entity\GeoListePays;
use App\Entity\GeoListeRegion;
use App\Entity\GeoVille;
use App\Entity\MetierMetier;
use App\Entity\OffListeSecteurActivite;
use App\Entity\RechercheAvancee;
use App\Entity\SeoEditReferencement;
use App\Entity\OffAlertemail;
use App\Form\carriereBTP\CandidatOffreELKRechercheBTPType;
use App\Repository\ConseilRepository;
use App\Repository\OffAlertemailRepository;

use App\Form\CandidatOffreELKRechercheType;

use App\Service\ContratTypeCacheService;
use App\Service\ELKOffOffreService;
use App\Service\GeoCacheService;

use App\Service\OffOffreService;

use App\Service\RechercheAvanceeCacheService;
use App\Service\SecteurActivityCacheService;

use App\Service\SimpleCacheService;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\ORM\EntityManagerInterface;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Security\Core\User\UserInterface;

use Symfony\Component\Cache\CacheItem;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Cache\CacheInterface;

use Symfony\Component\HttpFoundation\Session\Session;


#[Route('/BTP/lists-offre', name: 'app_BTP_candidat')]
class ListOffreBTPController extends AbstractController
{
	private $cache;
	private $requestStack;
	private $offreService ;
	private $managerRegistry;
	private $contratTypeCacheService;
	private $geoCacheService;
	private $secteurActivityCacheService;
	private $rechercheCacheService;
	private $editAppId;

	const SLUG_ALL_METIERS_SECTEURS_SUFFIXE = "metiers-secteurs";
	const SLUG_ALL_SECTEURS_SUFFIXE = "secteurs";
	const SLUG_METIER_SEPARATOR = "metier";
	const SLUG_METIERS_SEPARATOR = "metiers";
	const BASE_LIST_URL = "/BTP/lists-offre/";

	const LOCALISATION_PARAM = "localisation";
	const LOCALISATION_FIELD_PARAM = "localisation_field";

	const QUERY_PARAM = "q";
	const QUERY_FIELD_PARAM = "q_field";


    function __construct(int $editAppId, ELKOffOffreService $offOffreService,RechercheAvanceeCacheService $rechercheCacheService, ManagerRegistry $managerRegistry,
	                     CacheInterface $cache,ConseilRepository $conseilRepository, RequestStack $requestStack, GeoCacheService $geoCacheService, ContratTypeCacheService $contratTypeCacheService, SecteurActivityCacheService $secteurActivityCacheService)
	{
		$this->offreService = $offOffreService;
		$this->managerRegistry = $managerRegistry;
		$this->contratTypeCacheService = $contratTypeCacheService;
		$this->geoCacheService = $geoCacheService;
		$this->secteurActivityCacheService = $secteurActivityCacheService;
		$this->cache = $cache;
		$this->requestStack = $requestStack;
		$this->rechercheCacheService = $rechercheCacheService;
		$this->editAppId = $editAppId;
		$this->conseilRepository = $conseilRepository;
	}



	#[Route('/', name: '_search')]
	public function searchElk(	
		int $id=null,
		Request $request, 
		Session $session=null,
		UserInterface $user=null, 
		ELKOffOffreService $offOffreService): Response
    {

		$sentParams =  [
			"id" => $id,
			"session" => $session,
			"user" => $user,
		];
		$searchParams = $request->query->all();
		return $this->displaySearchList($searchParams, $sentParams);
    }

	#[Route('/save_search_elk', name: '_save_search_elk')]
	public function saveResearch(ManagerRegistry $doctrine, 
	EntityManagerInterface $entityManager,
	UserInterface $user=null,
	Request $request)
	{
		if (!$user) {
            return $this->redirectToRoute('app_login');
        }
        $id_user = $this->getUser()->getIdUser();
		$q = $request->request->get('q') ? $request->request->get('q') : "";
		$localisation = $request->request->get('localisation') ? $request->request->get('localisation') : "";
		$localisationType = $request->request->get('localisationType') ? $request->request->get('localisationType') : "";
		$contrat = $request->request->get('contrat') ? $request->request->get('contrat') : "";
		$url = $request->request->get('url') ? $request->request->get('url') : "";
		
		$message = ["id" => "error-fav",
					"bgc" => "",
					"bdc" => "",
					"color" => "red",
					"msg" => "Votre sauvegarde a rencontré une erreur"];
					
		if ($q == null && $localisation == null &&
		$localisationType == null && $contrat == null) {
			return new JsonResponse(['error' => 'All variables are null'], 400);
		}
		$alertRepository = $doctrine->getRepository(OffAlertemail::class);
			
		/*	Si aucune sauvegarde */
		if ($alertRepository->findSavedAlertEmailsForUser($id_user) == null) {
			$alertRepository = new OffAlertemail();
			$alertRepository->setUserUserIdUser($id_user);
			$alertRepository->setTypeRegion($localisationType);
			$alertRepository->setRegion($localisation);
			$alertRepository->setMetier($q);
			$alertRepository->setContrat($contrat);
			$alertRepository->setUrl($url);
			$alertRepository->setIsSaved(1);
			$entityManager->persist($alertRepository);
			$entityManager->flush();
			$message = ["id" => "found-fav",
						"bgc" => "#d4edda",
						"bdc" => "#c3e6cb",
						"color" => "#155724", // green
						"msg" => "Votre recherche a été sauvegardée"];
		
			} else { /*	si au moins une sauvegarde */
			$alertRepository = $alertRepository->findAlertEmailsWithCriteria($id_user, $q, $localisation, $localisationType, $contrat);
			$message = ["id" => "exist-fav",
					"bgc" => "#fff3cd",
					"bdc" => "#ffeeba",
					"color" => "#856404",//orange
					"msg" => "Votre recherche est déjà sauvegardée"];
				
			/*	Eviter les duplications	*/
			if ($alertRepository == null) { 
				$alertRepository = new OffAlertemail();
				$alertRepository->setUserUserIdUser($id_user);
				$alertRepository->setTypeRegion($localisationType);
				$alertRepository->setRegion($localisation);
				$alertRepository->setMetier($q);
				$alertRepository->setContrat($contrat);
				$alertRepository->setUrl($url);
				$alertRepository->setIsSaved(1);
				$entityManager->persist($alertRepository);
				$entityManager->flush();
				$message = ["id" => "found-fav",
							"bgc" => "#d4edda",
							"bdc" => "#c3e6cb",
							"color" => "#155724",//green
							"msg" => "Votre recherche a été sauvegardée"];
			}
		}
		return (new JsonResponse($message));
	}
	
	#[Route('/delete_favorite_elk', name: '_delete_favorite_elk')]
	public function lookForFavorite(ManagerRegistry $doctrine, 
	EntityManagerInterface $entityManager,
	UserInterface $user=null,
	Request $request)
	{
		if (!$user) {
            return $this->redirectToRoute('app_login');
        }

        $id_user = $this->getUser()->getIdUser();
		$q = $request->request->get('q') ? $request->request->get('q') : "";
		$localisation = $request->request->get('localisation') ? $request->request->get('localisation') : "";
		$localisationType = $request->request->get('localisationType') ? $request->request->get('localisationType') : "";
		$contrat = $request->request->get('contrat') ? $request->request->get('contrat') : "";
		$url = $request->request->get('url') ? $request->request->get('url') : "";
		$message = ["id" => "found-fav",
					"bgc" => "#d4edda",
					"bdc" => "#c3e6cb",
					"color" => "#155724", // green
					"msg" => "Votre recherche a été sauvegardée"];

		if ($q == null && $localisation == null &&
		$localisationType == null && $contrat == null) {
			return new JsonResponse(['error' => 'All variables are null'], 400);
		}

		$alertRepository = $doctrine->getRepository(OffAlertemail::class)->findSavedAlertEmailWithUrl($id_user, $url);
		
		if ($alertRepository != null) {
			for ($i = 0; $i < count($alertRepository); $i++) {
				$entityManager->remove($alertRepository[$i]);
			}
			$entityManager->flush();
			$message = ["id" => "res-delete",
						"bgc" => "#f8d7da",
						"bdc" => "#f5c6cb",
						"color" => "#721c24",//red
						"msg" => "Votre sauvegarde a été supprimée"];
		}
	
		return (new JsonResponse($message));
	}
	

	public function displaySearchList($searchParams, $sentParams = [], $fixedParams=[], $options = [])
	{

		$found = !empty($sentParams) ? 1 : 0;
		if ($found == 0 || ($sentParams["id"] == null)) {
			$allParams = $searchParams + $fixedParams;
		}
        else
        {
			$allParams = $searchParams["search_params"] + $fixedParams;
		}
		$array_domtom = array("Mayotte", "la réunion", "réunion", "martinique", "guyane", "guyane française", "guadeloupe");
		if(array_key_exists('departement', $allParams)){
			if(in_array(trim(strtolower($allParams['departement'])),$array_domtom)){
				$allParams['localisation'] = $allParams['departement'];
				unset($allParams['departement']);
			}
		};

		if(array_key_exists('region', $allParams)){
			if(in_array(trim(strtolower($allParams['region'])),$array_domtom)){
				$allParams['localisation'] = $allParams['region'];
				unset($allParams['region']);
			}
		};
        if (isset($searchParams['q']) && $searchParams['q'] !== "") {
            $metiersQuery = $this->conseilRepository->ExtractParaContenuMetierBTP($searchParams['q']);
        } elseif (isset($allParams['q']) && $allParams['q'] !== "") {
            $metiersQuery = $this->conseilRepository->ExtractParaContenuMetierBTP($allParams['q']);
        } else {
            $metiersQuery = null;
        }


        $array_old_region = array(
			"alsace" => "Grand-Est",
			"aquitaine" => "Nouvelle-Aquitaine",
			"auvergne" => "Auvergne-Rhône-Alpes",
			"bourgogne" => "Bourgogne-Franche-Comté",
			"champagne-ardenne" => "Grand-Est",
			"champagne ardenne" => "Grand-Est",
			"franche-comté" => "Bourgogne-Franche-Comté",
			"franche comté" => "Bourgogne-Franche-Comté",
			"languedoc-roussillon" => "Occitanie",
			"languedoc roussillon" => "Occitanie",
			"limousin" => "Nouvelle-Aquitaine",
			"lorraine" => "Grand-Est",
			"midi-pyrénées" => "Occitanie",
			"midi pyrénées" => "Occitanie",
			"nord-pas-de-calais" => "Hauts-de-France",
			"nord pas de calais" => "Hauts-de-France",
			"picardie" => "Hauts-de-France",
			"poitou-charentes" => "Nouvelle-Aquitaine",
			"poitou charentes" => "Nouvelle-Aquitaine",
			"rhone-alpes" => "Auvergne-Rhône-Alpes",
			"rhone alpes" => "Auvergne-Rhône-Alpes",
			"paca" => "Provence-Alpes-Côte d'Azur",
		);


        $array_secteurs = array(
            array('4-12', 'BTP / Environnement / Génie civil'),
            array('11-27', 'Electronique / Electricite / Telecom'),
        );


        $array_secteurs_lib = array(
            'BTP / Construction / Génie Civil, Énergie / Environnement' => 'BTP / Environnement / Génie civil',
            'Electronique / Electricite / Telecom' => 'Electronique / Electricite / Telecom',
        );

		if(array_key_exists('localisation',$allParams)) {
			if(array_key_exists(trim(strtolower($allParams['localisation'])), $array_old_region)) {
				$allParams['localisation'] = $array_old_region[trim(strtolower($allParams['localisation']))];
				$allParams['localisation_field'] = "region";
			}
		}

		$array_voyelle = array("A","E","I","O","Y","U");

		$lib_comp = array();
		$lib_comp['ville'] = array_key_exists("ville",$allParams) ? " à ". (in_array($allParams['ville'][0],$array_voyelle) ? "d'": "") . ucfirst(strtolower($allParams['ville'])) : "";
		$lib_comp["departement"] = array_key_exists("departement",$allParams) ? ", " . ucfirst(strtolower($allParams['departement'])) : "";
		$lib_comp["region"] = array_key_exists("region",$allParams) ? ", " . ucfirst(strtolower($allParams['region'])) : "";
		// $lib_comp["secteur"] = array_key_exists("secteur",$allParams) ? " du domaine " . str_replace(array(" - "," , ", ", ", " ,"), " / " ,ucfirst(strtolower($allParams['secteur']))) : "";
		$lib_comp["mc"] = array_key_exists("q",$allParams) ? "  " . ucfirst(strtolower($allParams['q'])) : "";
		if(isset($options["slugParts"]['secteur'])){
			$id_secteur = explode("-",$options['slugParts']['secteur'])[0];
			foreach($array_secteurs as $secteur) {

				$key_a = "'".$secteur[0]."-";
				$key_b = "-".$secteur[0]."'";
				$key_c = "-".$secteur[0]."-";

				if(strpos($key_a, $id_secteur)) {
					$lib_comp["secteur"] = " du domaine " . $secteur[1];
					break;
				} elseif(strpos($key_b,$id_secteur)) {
					$lib_comp["secteur"] = " du domaine " . $secteur[1];
					break;
				} elseif(strpos($key_c,$id_secteur)) {
					$lib_comp["secteur"] = " du domaine " . $secteur[1];
					break;
				} else {
					$lib_comp["secteur"] = "";
				}
			}

		} else {
			$lib_comp["secteur"] = "";
		}

		foreach ($allParams as $key => $value){
			if(!$value){
				unset($allParams[$key]);
			}
		}

        if (!isset($allParams["q"])) {
            if (isset($allParams["secteur"])) {
                $secteurId = $allParams["secteur"];
                $foundSecteur = false;

                // Cherche dans $array_secteurs le libellé correspondant
                foreach ($array_secteurs as [$id, $libelle]) {
                    if ($id === $secteurId) {
                        $allParams["q"] = $libelle;
                        $foundSecteur = true;
                        break;
                    }
                }

                // Fallback si aucun libellé trouvé
                if (!$foundSecteur) {
                    $allParams["q"] = $secteurId;
                }

                $allParams["q_field"] = "secteur";
                unset($allParams["secteur"]);
                // ne surtout pas unset q_field ici, sinon il disparaît
            }
        }

        if(!isset($allParams["localisation"])){
			foreach (["region","departement","ville"] as $field){
				if(!isset($allParams["localisation"]) && isset($allParams[$field] )){
					$allParams["localisation"] = $allParams[$field];
					$allParams["localisation_field"] = $field;
				}
				unset($allParams[$field]);
			}
		}else if(isset($allParams["localisation_field"])){
			$localisationField = $allParams["localisation_field"];
			$posLocalisation = array_search($localisationField , ["region", "departement", "ville"]);			
			foreach (["region", "departement", "ville"] as $pos => $field){
				if(isset($allParams[$field])){
					if($pos < $posLocalisation){
						unset($allParams[$field]);
					}else{
						$allParams["localisation"] = $allParams[$field];
						$allParams["localisation_field"] = $field;
						unset($allParams[$field]);
					}
				}
			}
		}

		$request = $this->requestStack->getMainRequest();
		$uri = $request->getRequestUri();

		//$allParams['q'] = $array_secteurs_lib[$allParams['q']];

		$searchForm =  $this->createForm(CandidatOffreELKRechercheBTPType::class, $allParams );
		$metiers = null;
		if($rechercheAvancee = $this->getRechecheIfMatchSecteurs($allParams)){
			if($this->rechercheCacheService->data["metiers_by_id"][$rechercheAvancee->getIdRechercheAvancee()] ?? false){
				$metiers = $this->rechercheCacheService->data["metiers_by_id"][$rechercheAvancee->getIdRechercheAvancee()];
			}

		}
		unset($allParams["_token"]);
		$editRef = null;
		if($uri){
			$repoRef = $this->managerRegistry->getRepository(EditReferencement::class);
			$editRef = $this->cache->get("edit_referencement_".str_replace(["/", "(", ")","@"],"|",$uri),function() use ($repoRef, $uri){
				return $repoRef->findOneBy(["url"=>$uri]);
			});
		}

		$limit = 15;
		if ($found == 1 && $sentParams["id"])
			$limit = 5;
		list($listOffres, $elkResults) = $this->offreService->searchOffreForPageBTP($allParams, $request->query->getInt("page", 1), $limit);

        $pageCount = $listOffres->getPageCount();

        $isLastPage = ($pageCount <= 1) || ($pageCount == $listOffres->getPage());
        $breacrumb = $this->getBreadcrumpSearch($allParams, $request);

		$myDomaine = "";
		$myLocalisationType = "";
		$myLocalisation = "";
		$myContrat = "";

		if (isset($allParams["q"]))
			$myDomaine = $allParams["q"];
		if (isset($allParams["localisation"])) {
			$myLocalisation = $allParams["localisation"];

			if(isset($localisationField)){
				$myLocalisationType = $localisationField;
			}
		}
		// if(in_array(strtolower($allParams['localisation']), $array_domtom)) {
		// 	unset($allParams['localisation_field']);
		// }
    	if (isset($allParams["contrat"])) {
        	$selectedValue = $allParams["contrat"];
			$options = [
				"" => "Contrat",
				"2" => "CDI",
				"1" => "CDD",
				"4" => "Interim",
				"3" => "FreeLance / Indépendant",
				"6" => "Alternance",
				"8" => "Stage"
			];
			$myContrat = isset($options[$selectedValue]) ? $options[$selectedValue] : "Unknown option";
		}
        $title_seo = "offres d'emploi BTP";
        $title_seo .= $myDomaine ? " " . $myDomaine : "";
        $key = $request->query->get('term');

// Vérifie si la localisation est fournie
        if(strlen($myLocalisation) == 0) {
            // Si aucune localisation spécifique n'est fournie, utiliser "en France"
            $title_seo .= " en France";
        } else {
            // Si une localisation est spécifiée, procéder à la vérification
            $dataReg = $this->managerRegistry->getRepository(GeoListeRegion::class)->FindRegionForSearchBTP($key);
            $dataDep = $this->managerRegistry->getRepository(GeoDepartement::class)->FindDepratementForSearchBTP($key);
            $foundInRegOrDep = false;

            // Vérifier si $myLocalisation existe dans $dataReg
            foreach ($dataReg as $reg) {
                if (strtoupper($reg->getLibelle()) === strtoupper($myLocalisation)) {
                    $foundInRegOrDep = true;
                    break; // Quitte la boucle si trouvé
                }
            }

            // Si non trouvé dans $dataReg, vérifier dans $dataDep
            if (!$foundInRegOrDep) {
                foreach ($dataDep as $dep) {
                    if (strtoupper($dep->getLibelle()) === strtoupper($myLocalisation)) {
                        $foundInRegOrDep = true;
                        break; // Quitte la boucle si trouvé
                    }
                }
            }

            // Ajuster $title_seo en fonction de la localisation trouvée
            if (strtoupper($myLocalisation)=="PARIS")
            {
                $title_seo .= " à " . $myLocalisation;
            }
            elseif ($foundInRegOrDep) {
                // Si trouvé dans région ou département, utiliser "en"
                $title_seo .= " en " . $myLocalisation;
            } else {
                // Si non trouvé dans les régions ou départements, supposer que c'est une ville et utiliser "à"
                $title_seo .= " à " . $myLocalisation;
            }
        }

        //$title_seo .= $myContrat ? " en " . $myContrat : ", en CDI/CDD/Interim/FreeLance/indépendent/Alternance/Stage";
		if ($found == 1) {
			if ($sentParams["id"]) {
				$locs = ["departement", "region"]; // ce limiter a ces 3 type d'emplacement ("ville" par defaut)
				// $locs = ["departement", "region", true]; // rechers offres avec meme caracteristiques partout en France
				for ($i = 0; $i < count($locs) && $pageCount == 0; $i++) {
					$allParams['localisation_field'] = $locs[$i];
					$searchParams['search_params'] = $allParams;
					list($listOffres, $elkResults) = $this->offreService->searchOffreForPageBTP($allParams, $request->query->getInt("page", 1), $limit);
					$pageCount = $listOffres->getPageCount();
				}
				if ($pageCount == 0)
					return $this->redirectToRoute('app_candidat_search_details', ["id" => $sentParams["id"]]);
			}
		}
		if ($found == 1 && $sentParams["id"] != null) {
			$twig =  [
				'listeOffres' => $listOffres,
				'breadcrumb' => $breacrumb,
				//'moreResults' => $isLastPage ? $this->extendsSearchLocation($searchParams):null,
			];
		} else {
			$twig =  [
				'myDomaine' => $myDomaine,
				'myLocalisationType' => $myLocalisationType,
				'myLocalisation' => $myLocalisation,
				'myContrat' => $myContrat,
				'listeOffres' => $listOffres,
				'editRef' => $editRef,
				'canonical' => $breacrumb[count($breacrumb)-1]["link"],
				'breadcrumb' => $breacrumb,
				'moreResults' => $isLastPage ? $this->extendsSearchLocation($searchParams):null,
				"rechercheAvancee" => $rechercheAvancee,
                'seoEditReferencement' => isset($options["slugParts"]) ? $this->getSeoEditReferencement($options["slugParts"]):null,
                'sideMenus'=> $this->generateSideMenu(true, $allParams, $metiers, $elkResults["aggs"]),
				'searchForm' => $searchForm->createView(),
				'connected' => ($found == 1 && $sentParams["user"]) ? 1 : 0,
				'title_seo' => $title_seo,
			];
		}
        if($twig['seoEditReferencement'] ==null && $metiersQuery != null)
        {
            $seo = new SeoEditReferencement();
            $seo->setNom($metiersQuery[0]['nom']);
            $seo->setUrl($metiersQuery[0]['url']);
            $seo->setParagrapheContenu($metiersQuery[0]['paragraphe_contenu']);
            $seo->setPhotoPath(null);
            $twig['seoEditReferencement']=$seo;
        }
		if($options["template_options"] ?? false){
			$twig =  $twig + $options["template_options"];
		}
		$twig['lib_comp'] = $lib_comp;
		
		if ($found == 1) {
			if ($sentParams["id"]) {
				
				$newOffers = [];
				foreach ($listOffres as $offer) {
					foreach ($offer as $data) {
						$newOffers[] = $data; 
					}
				}

				$sentParams["session"]->set("offers", $newOffers);
				$sentParams["session"]->set("user_datas", $searchParams["global"]["user_data"]);

				return $this->redirectToRoute('app_candidat_offres_similaires', 
					[
						"id" => $sentParams["id"],
					]
				);
			}
		}
        return $this->render('carriereBTP/offres/resultats_elk_recherche.html.twig', $twig);

	}

    public function getBreadcrumpSearch($params, $request){
        $baseURL = $this->generateUrl("app_BTP_accueil", [], UrlGeneratorInterface::ABSOLUTE_URL);
        $breadcrumb = [
            ["link"=> $this->generateUrl("app_BTP_accueil", [], UrlGeneratorInterface::ABSOLUTE_URL), "title" => "Accueil"],
            ["link"=> $this->generateUrl("app_BTP_candidat_search", [], UrlGeneratorInterface::ABSOLUTE_URL), "title" => " Offres d'emploi"],

        ];
        $titles = [];
        $slugField = [];
        if (isset($params["localisation_field"]) && isset($params["localisation"])) {
			$params[$params["localisation_field"]] = $params["localisation"];
		}		
		
        if($params["q_field"] ?? false){
            $params[$params["q_field"]] = $params["q"];
        }



        if($params["metier"] ?? false){
            if(count($titles)){
                $titles [] = self::SLUG_METIER_SEPARATOR;
            }
            $titles [] = SimpleCacheService::getLegacySlug($params["metier"]);
            $slugField [] = "metier";
        }else if($params["recherche_avance_id"] ?? false ){
            /**
             * @var $rechercheAvance RechercheAvancee
             */
            $rechercheAvance = $this->rechercheCacheService->getById($params["recherche_avance_id"]);
            if($rechercheAvance){

                $slugField [] = "secteur";
                $titles[]= $rechercheAvance->getTitre();
            }

        }else if($params["secteur"] ?? false ){

            $titles[ ] = $params["secteur"];
            $slugField [] = "secteur";

        }else if($params["q"] ?? false){

            $titles [] = $params["q"];
            $slugField [] = "secteur";
        }

        if($params["localisation_field"] ?? false){
            foreach(["ville","departement","region", "pays"] as $field){
                if($params[$field] ?? false){
                    $titles[] = $params[$field];
                    $slugField [] = $field;
                    break;
                }
            }
        }else if($params["localisation"] ?? false){
            $titles[] = $params["localisation"];
            $slugField [] = "localisation";
        }

        if(isset($params["contrat"])){
			if(is_numeric($params["contrat"])){
                $contrat = $this->contratTypeCacheService->getById($params["contrat"]);
                if($contrat){
                    $titles [] = $contrat["label"];
                    $slugField [] = "contrat";
                }
            }else{
                $titles [] = $params["contrat"];
                $slugField [] = "contrat";
            }
		}
		if(isset($params["id_type_contrat"])){
            $slug [] = $this->contratTypeCacheService->data["by_id"][$params["id_type_contrat"]]["slug"] ;
            $slugField [] = "contrat";
        }




        $title = "Toutes les Offres";
        if(count($titles)>0){
            $title = implode(" - ", $titles);
        }
        $page = ($request->get("page") && $request->get("page") != 1) ? "?page=". $request->get("page"): "";

        $breadcrumb [] = ["link"=>$baseURL.substr($this->searchParamsToLegacyUrl($params).$page, 1), "title" => $title];
        return $breadcrumb;

    }

    public function extendsSearchLocation($searchParams)
    {
        if (isset($searchParams["localisation_field"])) {
            if ($searchParams["localisation_field"] === "ville") {
                $department = $this->geoCacheService->getDepartementForVilleLabel($searchParams["localisation"]);
                if ($department) {
                    $newParams = array_merge($searchParams, [
                        "localisation" => $department["label"],
                        "localisation_field" => "departement",
                        "page" => 1
                    ]);
                    return [
                        "location" => $department["label"],
                        "url" => $this->searchParamsToURL($newParams)
                    ];
                } else {
                    return [
                        "error" => "Aucun département trouvé pour la ville spécifiée."
                    ];
                }
            } elseif ($searchParams["localisation_field"] === "departement") {
                $region = $this->geoCacheService->getRegionForDepartementLabel($searchParams["localisation"]);
                if ($region) {
                    $newParams = array_merge($searchParams, [
                        "localisation" => $region["label"],
                        "localisation_field" => "region",
                        "page" => 1
                    ]);
                    return [
                        "location" => $region["label"],
                        "url" => $this->searchParamsToURL($newParams)
                    ];
                } else {
                    return [
                        "error" => "Aucune région trouvée pour le département spécifié."
                    ];
                }
            } elseif ($searchParams["localisation_field"] === "region") {
                $country = $this->geoCacheService->getCountryForRegionLabel($searchParams["localisation"]);
                if ($country) {
                    $newParams = array_merge($searchParams, [
                        "localisation" => $country["label"],
                        "localisation_field" => "pays",
                        "page" => 1
                    ]);
                    return [
                        "location" => $country["label"],
                        "url" => $this->searchParamsToURL($newParams)
                    ];
                } else {
                    return [
                        "error" => "Aucun pays trouvé pour la région spécifiée."
                    ];
                }
            }
        }
        return null;
    }

	public function getSeoEditReferencement($slugParts)
	{
		$seoRepo = $this->managerRegistry->getRepository(SeoEditReferencement::class);
		foreach(["secteur", "metier", "ville", "localisation"] as $field){
			if($slugParts [$field] ?? FALSE){
				$slug = $slugParts [$field];

				if($field == "secteur"){
					$slug = "metiers-".$slug;
				} elseif($field == "metier"){
					$slug = "metier-".str_replace(" ", "-", $slug);
				}

				$cacheKey = str_replace(["(",")"],"|", $slug);
				$seo = $this->cache->get("seo_edit_referencement_".$cacheKey, function (CacheItem $item ) use ($seoRepo, $slug){
					return $seoRepo->findOneBy(["edit_app_id_app"=>$this->editAppId, "url"=>$slug]);
				});

				if($seo){
					return $seo;
				}
			}
		}
		return null;
	}

	public function generateSideMenu($fromElk, $params, $metiers=[], $listItems = [], $newURLFormat = true){
		$menu = [];
		$hasContratSelected  = isset($params["contrat"]) || isset($params["id_contrat_type"]);
		$menu [] = ["label"=>"Toutes les offres d'emploi par Contrat", "items"=>$this->generateMenuLinksContrat($fromElk && !$hasContratSelected,  $params, $listItems["contrat"] ?? false, $newURLFormat)];

		if(isset($params["localisation_field"]) && isset($params["localisation"]) && $params["localisation"] && $params["localisation_field"]){
			$field = $params["localisation_field"];
			$params[$field] = $params["localisation"] ;
		}
        // Juste avant d'utiliser $listItems["secteur"], vous pouvez filtrer les éléments
        if (isset($listItems["secteur"])) {
            $secteursCibles = [
                "BTP / Construction / Génie Civil",
                "Immobilier / Urbanisme / Aménagement",
                "Électronique / Électricité",
                "Architecture / Art / Design",
                "Énergie / Environnement",
            ];

            // Normalisation pour éviter les erreurs d'accents ou de casse
            $normalize = function ($text) {
                return strtolower(trim(iconv('UTF-8', 'ASCII//TRANSLIT', $text)));
            };

            $normalizedCibles = array_map($normalize, $secteursCibles);

            $listItems["secteur"] = array_values(array_filter($listItems["secteur"], function($item) use ($normalizedCibles, $normalize) {
                return in_array($normalize($item['label']), $normalizedCibles);
            }));
        }


        if($params["ville"] ?? false ){
		}elseif($params["departement"] ?? false){
			$menu[] =  ["label"=> "Toutes les offres d'emploi dans votre Ville", "items"=>$this->generateMenuLinksVille($fromElk , $params, $listItems["ville"] ?? false, $newURLFormat)];
		}elseif($params["region"] ?? false){
			$menu[] =  ["label"=> "Toutes les offres d'emploi dans votre Département", "items"=>$this->generateMenuLinksDepartement($fromElk, $params, $listItems["departement"] ?? false,$newURLFormat)];
		}else{
			$menu[] =  ["label"=> "Toutes les offres d'emploi dans votre Région", "items"=>$this->generateMenuLinksRegion($fromElk, $params, $listItems["region"] ?? false, $newURLFormat)];
		}
		if($metiers){
			$menu [] = ["label"=>"Toutes les offres d'emploi par Metiers", "items"=>$this->generateMenuLinksMetiers($params,$metiers,  $newURLFormat)];
		}else{
			$request = $this->requestStack->getMainRequest();
			if(str_contains($request->getRequestUri(),"-metiers-secteurs") || !str_contains($request->getRequestUri(), "-".self::SLUG_METIER_SEPARATOR."-")
				&& !str_contains($request->getRequestUri(), "-".self::SLUG_METIERS_SEPARATOR."-") && (!isset($params["q"]) || ! $params["q"]) ){

				$menu [] = ["label"=>"Toutes les offres d'emploi par Domaine", "items"=>$this->generateMenuLinksSecteurs($fromElk, $params,$listItems["secteur"] ?? false, $newURLFormat)];
			}
		}

        return $menu;

	}

	public function generateMenuLinksMetiers($params, $metiers,  $newURLFormat = true){
		$contrats = $this->contratTypeCacheService->getContratsWithOrdre();
		$links = [];
		foreach($metiers  as $metier){
			$url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["metier"=>$metier])
				: $this->searchParamsToURL(array_merge($params , ["metier"=>$metier]));

			$links[] = ["label"=>$metier, "url"=>$url];
		}
		return $links;
	}
	public function generateMenuLinksContrat($fromElk, $params, $items, $newURLFormat = true){
		$links = [];
		//contrat and id_type_contrat are supported => confusion
		$contratField = isset($params["contrat"]) ? "contrat" : (isset($params["id_type_contrat"]) ? "id_type_contrat" : "contrat") ;

		if($fromElk){

            foreach ($items as $item ){
				$contrat = $this->contratTypeCacheService->getByLabel($item["label"]);
				$url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl(array_merge($params , [$contratField=>$contrat["label"]]))
					: $this->searchParamsToURL(array_merge($params , [$contratField=>$contrat["id"]]));

				$links[] = ["label"=>$contrat["label"] , "url"=>$url, "count"=>$item["count"]];
			}
		}else{
			$contrats = $this->contratTypeCacheService->getContratsWithOrdre();

			foreach($contrats  as $contrat){
				$url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl(array_merge($params , [$contratField=>$contrat["id"]]))
					: $this->searchParamsToURL(array_merge($params , [$contratField=>$contrat["id"]]));

				$links[] = ["label"=>$contrat["label"], "url"=>$url];
			}
		}

		return $links;
	}

	public function generateMenuLinksDepartement($fromElk, $params, $items , $newURLFormat = true){
		$region = $params["region"];
		unset($params["region"]);

		$links = [];

		if($fromElk){
			foreach ($items as $departement){
				$url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["departement"=>$departement["label"]])
					: $this->searchParamsToURL(array_merge($params , ["localisation"=>$departement["label"], "localisation_field"=>"departement"]));

				$links[] = ["label"=>$departement["label"], "url"=>$url, "count"=>$departement["count"]];
			}
		}else{
			$departements = $this->geoCacheService->getDepartementListForRegionLabel($region);
			foreach($departements  as $departement){
				$url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["departement"=>$departement["label"]])
					: $this->searchParamsToURL(array_merge($params , ["localisation"=>$departement["label"], "localisation_field"=>"departement"]));

				$links[] = ["label"=>$departement["label"], "url"=>$url];
			}
		}

		return $links;
	}

    public function generateMenuLinksSecteurs($fromElk, $params, $items, $newURLFormat = true){

        $links = [];
        if($fromElk) {

            /**
            @note: Try to regroup secteurs with secteur group defined in recherche_avancee
             */
            $secteurGroups = [];
            $secteurs = [];
            foreach ($items as $k=> $item){
                /**
                 * @var $secteur array
                 */
                $secteur = $this->secteurActivityCacheService->getByLabel($item["label"]);
                if($secteur){
                    $items[$k]["id"] = $secteur["id"];
                    $group = $this->rechercheCacheService->getBySecteur($items[$k]["id"]);
                    if($group){
                        $groupId = $group->getIdRechercheAvancee();
                        if(!isset($secteurGroups[$groupId])){
                            $secteurGroups[$groupId] = ["count"=>$item["count"], "label"=>$group->getTitre(), "type"=>"recherche_avance_id", "id"=>$groupId];
                        }else{
                            $secteurGroups[$groupId]["count"] = $secteurGroups[$groupId]["count"] + $item["count"];
                        }
                    }else{
                        $item["type"] = "secteur";
                        $secteurs[] = $item;
                    }
                }
            }
            $secteurs = array_merge($secteurs, array_values($secteurGroups));
            usort($secteurs, function ($a, $b){
                return  $b["count"] - $a["count"] ;
            });

            foreach($secteurs as $secteur){
                $secteurParams =($secteur["type"] == "recherche_avance_id") ?  ["recherche_avance_id" => $secteur["id"]] : ["secteur"=> $secteur["label"]];
                $url = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + $secteurParams)
                    : $this->searchParamsToURL(array_merge($params, ["q" => $secteur["label"], "q_field" => "secteur"]));

                $links[] = ["label" => $secteur["label"], "url" => $url, "count"=>$secteur["count"]];
            }
        }else{
            $secteurs = $this->secteurActivityCacheService->data["by_id"];
            foreach ($secteurs as $secteur) {
                $url = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["secteur" => $secteur["label"]])
                    : $this->searchParamsToURL(array_merge($params, ["q" => $secteur["label"], "q_field" => "secteur"]));

                $links[] = ["label" => $secteur["label"], "url" => $url];
            }
        }

        return $links;
    }

	public function generateMenuLinksVille($fromELk, $params, $items, $newURLFormat = true){
		$links = [];
		if($fromELk){
            //$departement = $this->geoCacheService->getDepartementByLabel($params["departement"];

			unset($params["departement"]);

			foreach($items  as $ville){
				$url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["ville"=>$ville["label"]])
					: $this->searchParamsToURL(array_merge($params, ["localisation"=>$ville["label"], "localisation_field"=>"ville"]));

				$links[] = ["label"=>$ville["pretty"] ?? $ville["label"], "url"=>$url, "count"=>$ville["count"]];
			}
		}else{
			$villes = $this->geoCacheService->getMainVillesForDepartementLabel($params["departement"]);
			unset($params["departement"]);

			foreach($villes  as $ville){
				$url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["ville"=>$ville["label"]])
					: $this->searchParamsToURL(array_merge($params, ["localisation"=>$ville["label"], "localisation_field"=>"ville"]));

				$links[] = ["label"=>$ville["pretty"] ?? $ville["label"], "url"=>$url];
			}
		}

		return $links;
	}

	public function generateMenuLinksRegion($fromElk, $params, $items , $newURLFormat = true){
		$links = [];
		if($fromElk){
			foreach($items as $region){
				$url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["region"=>$region["label"]])
					: $this->searchParamsToURL(array_merge($params + ["localisation"=>$region["label"], "localisation_field"=>"region"]));

				$links[] = ["label"=>$region["label"], "url"=>$url, "count"=>$region["count"]];
			}
		}else{
			$regions = $this->geoCacheService->getRegions();

			foreach($regions as $region){
				$url  = ($newURLFormat) ? $this->searchParamsToLegacyUrl($params + ["region"=>$region["label"]])
					: $this->searchParamsToURL(array_merge($params + ["localisation"=>$region["label"], "localisation_field"=>"region"]));

				$links[] = ["label"=>$region["label"], "url"=>$url];
			}
		}

		return $links;
	}


	public function searchParamsToLegacyUrl($params)
	{
		$slug = [];
		$slugField = [];
		if (($params["localisation_field"] ?? false) && ($params["localisation"] ?? false)) {
			$params[$params["localisation_field"]] = $params["localisation"];
		}		
		if($params["q_field"] ?? false){
			$params[$params["q_field"]] = $params["q"];
		}
		if(isset($params["contrat"])){
			if(is_numeric($params["contrat"])){
				$contrat = $this->contratTypeCacheService->getById($params["contrat"]);
				if($contrat){
					$slug [] = $contrat["slug"];
					$slugField [] = "contrat";
				}
			}else{
				$contratSlug =  $this->contratTypeCacheService->getSlugByLabel($params["contrat"]);
				if($contratSlug){
					$slug [] = $contratSlug;
					$slugField [] = "contrat";
				}
			}


		}
		if(isset($params["id_type_contrat"])){
			$slug [] = $this->contratTypeCacheService->data["by_id"][$params["id_type_contrat"]]["slug"] ;
			$slugField [] = "contrat";
		}

		foreach(["ville","departement","region", "pays"] as $field){
			if($params[$field] ?? false){
                if($field == "ville"){
                    $villeSlug = SimpleCacheService::getLegacySlug($params[$field]);
                    $ville = $this->geoCacheService->getFirstVilleBySlug($villeSlug);
                    if($ville){
                        $id_departement = $ville["id_departement"];
                        $departement = $this->geoCacheService->getDepartementById($id_departement);
                        $slug[] = $villeSlug;
                        $slug[] = "(". $departement["code_postal"].")";
                    }
                    //$this->geoCacheService->get
                }else{
                    $slug[] = SimpleCacheService::getLegacySlug($params[$field]);
                }

				$slugField [] = $field;
				break;
			}
		}
		if($params["metier"] ?? false){
			if(count($slug)){
				$slug [] = self::SLUG_METIER_SEPARATOR;
			}
			$slug [] = SimpleCacheService::getLegacySlug($params["metier"]);
			$slugField [] = "metier";
		}else if($params["recherche_avance_id"] ?? false ){
			/**
			 * @var $rechercheAvance RechercheAvancee
			 */
			$rechercheAvance = $this->rechercheCacheService->getById($params["recherche_avance_id"]);
			if($rechercheAvance){
				if(count($slug)){
					$slug [] = self::SLUG_METIERS_SEPARATOR;
				}
				$slugField [] = "secteur";
				$slug[]=implode("-", $rechercheAvance->getIdSecteurActivites()). "-".SimpleCacheService::getLegacySlug($rechercheAvance->getTitre());
			}

		}else if($params["secteur"] ?? false ){
			if(count($slug)){
				$slug [] = self::SLUG_METIERS_SEPARATOR;
			}
			//can be multiple
			if(strpos($params["secteur"], ",") !== false){
				$secteurLabels = explode( ",",$params["secteur"]);
				$secteurSlugs = [];
				$secteurIds = [];
				foreach ($secteurLabels as $label){
					$label = trim($label);
					$secteur = $this->secteurActivityCacheService->getBySlug(SimpleCacheService::getLegacySlug($label));
					if($secteur){
						$secteurIds[] = $secteur["id"];
						$secteurSlugs[] = str_replace("--","-",$secteur["slug"]);
					}
				}
				sort($secteurIds, SORT_NUMERIC);
				$slug[] = implode("-",$secteurIds)."-".implode("-", $secteurSlugs);
			}else{
				$slug [] = SimpleCacheService::getLegacySlug($params["secteur"]);



			}
			$slugField [] = "secteur";

		}/*elseif(isset($params["ville"]) || isset($params["departement"]) || isset($params["region"]) || isset($params["pays"])) {
			$slug [] = self::SLUG_ALL_METIERS_SECTEURS_SUFFIXE;
		}*/ else if(isset($params["q"])  && $params["q"]){
			if(count($slug)){
				$slug [] = self::SLUG_METIER_SEPARATOR;
			}
			$slug [] = SimpleCacheService::getLegacySlug($params["q"]);
            $slug [] = self::SLUG_ALL_SECTEURS_SUFFIXE;
			$slugField [] = "secteur";
		}
        if(!in_array("secteur",$slugField) && !in_array("metier",$slugField)){
            $slug [] = self::SLUG_ALL_METIERS_SECTEURS_SUFFIXE;
        }
        if(count($slugField) == 0){
           return self::BASE_LIST_URL;
        }
        return self::BASE_LIST_URL .implode("-", $slug);
		/*if(count($slugField) > 1 ){

		}else{
			return self::BASE_LIST_URL .$slugField[0]."/".implode("-",$slug);
		}*/

	}

	public function searchParamsToURL ($params){
		return self::BASE_LIST_URL."?".http_build_query($params);
	}




    // Recherche avancée
    #[Route('/search/avance', name: '_search_avancee')]
    public function offreRechercheAvance(ManagerRegistry $doctrine): Response
    {
        $liste_domaines = $doctrine->getRepository(RechercheAvancee::class)->findAll();
        $liste_departements = $doctrine->getRepository(GeoDepartement::class)->findBy(
            [],
            ['libelle' => 'ASC'],
        );

        return $this->render('carriereBTP/candidat_offre/rechercheAvancee.html.twig', [
            'listeDomaines' => $liste_domaines,
            'listeDepartements' => $liste_departements,
        ]);
    }

    // Recherche avancée résultat
    #[Route('/emploi', name: '_search_avancee_result')]
    public function offreRechercheAvanceResultat(Request $request, ManagerRegistry $doctrine, OffOffreService $offOffreService): Response
    {
        $array_of_id_secteur = explode(',', $request->query->get('id_secteur'));
        $url = explode('/', $request->query->get('lien'));

		$domaine_activite = $doctrine->getRepository(SeoEditReferencement::class)->findOneBy([
            'url' => substr($url[2], 7)
        ]);

        $offres_par_secteur = $offOffreService->candidatOffresParSecteurPaginé($array_of_id_secteur);

        $listeRegions = $doctrine->getRepository(GeoListeRegion::class)->candidatOffreRegionsRechercheAvancee();

        $listeMetiersDemandes = $doctrine->getRepository(MetierMetier::class)->findBy(
            ['domaine_id_domaine' => $request->query->get('id_secteur'), 'page_acceuil' => 1],
            [],
            20
        );

        return $this->render('carriereBTP/candidat_offre/resultatRechercheAvancee.html.twig', [
            'domaineActivite' => $domaine_activite,
            'offresParSecteur' => $offres_par_secteur,
            'listeRegions' => $listeRegions,
            'listeMetiersDemandes' => $listeMetiersDemandes,
        ]);
    }

    #[Route('/emploi/metier/{metier}', name: '_emploi_metier')]
    public function offresMetier(Request $request, $metier): Response
    {

        return $this->displaySearchList($request->query->all(), [], ["q"=>$metier]);
    }

    #[Route('/emploi/secteur/{secteur}', name: '_emploi_secteur')]
    public function offresSecteur(Request $request, $secteur): Response
    {
        $secteurs  = $this->parseSlugMetier($secteur);
        if(count($secteurs)){
            return $this->displaySearchList($request->query->all(), [], ["secteur"=>implode(", ",$secteurs)]);
        }

	    return $this->displaySearchList($request->query->all());
    }


	#[Route('/emploi/secteur/{secteur}/{id}', name: '_emploi_secteur_id')]
	public function offresForSecteurId(Request $request, $secteur, $id): Response
	{
		$secteur = $this->secteurActivityCacheService->getById($id);
		if($secteur){
			return $this->displaySearchList($request->query->all(), [], ["secteur"=>$secteur["label"]]);
		}
		return $this->displaySearchList($request->query->all());
	}

	#[Route('/emploi/ville/{ville}', name: '_emploi_ville')]
	public function offresVille(Request $request, $ville): Response
	{
		$slugVille = $ville;
		if(preg_match('/(.*)-\((\d+)\)/', $ville, $matches)) {
			$codePostal = $matches[2];
			$ville = $matches[1];
		}
		$ville = $this->geoCacheService->getBySlug("ville",$ville);
		if($ville){
			return $this->displaySearchList($request->query->all(), [], ["ville"=>$ville["label"]], ["slugParts"=>["localisation"=>$slugVille]]);
		}
		return $this->displaySearchList($request->query->all(), []);

	}

	#[Route('/emploi/region/{region}', name: '_emploi_region')]
	public function offresRegion(Request $request, $region): Response
	{
		$slugRegion = $region;
		$region = $this->geoCacheService->getBySlug("region",$region);
		if($region){
			return $this->displaySearchList($request->query->all(), [], ["region"=>$region["label"]],["slugParts"=>["localisation"=>$slugRegion]]);
		}
		return $this->displaySearchList($request->query->all());

	}

	#[Route('/emploi/region/{region}/{secteur}', name: '_emploi_region_secteur')]
	public function offresForRegionAndSecteur(Request $request, $region, $secteur): Response
	{
		$slugParts = [];

		$region = $this->geoCacheService->getBySlug("region",$region);
		$secteur = $this->secteurActivityCacheService->getById($secteur);
		if($region){
			$params = ["region"=>$region["label"]];
			if($secteur) {
				$params["secteur"] = $secteur["label"];
				$slugParts["secteur"] = $secteur["slug"];
			}
			return $this->displaySearchList($request->query->all(), [], $params, ['slugPars'=>$slugParts]);
		}
		return $this->displaySearchList($request->query->all());

	}

    #[Route('/emploi/departement/{departement}', name: '_emploi_departement')]
    public function offresDepartement(Request $request, $departement): Response
    {
	    $departement = $this->geoCacheService->getBySlug("departement",$departement);
	    if($departement){
		    return $this->displaySearchList($request->query->all(), [], ["departement"=>$departement["label"]]);
	    }

	    return  $this->displaySearchList($request->query->all());
    }



    #[Route('/emploi/contrat/{contrat}', name: '_emploi_contrat')]
    public function offreRechercheAvanceContrat(Request $request, OffOffreService $offOffreService, GeoCacheService $geoCache,  $contrat): Response
    {
		$q = $request->getRequestUri();
		if(!is_numeric($contrat)){
			$contrat = $this->contratTypeCacheService->getBySlug($contrat);
			if($contrat){
				$contrat = $contrat["id"];

			}else{
				return  $this->displaySearchList($request->query->all());
			}
		}
        return  $this->displaySearchList($request->query->all(), [], ["contrat"=>$contrat], $q);
    }


    public function parseLegacySlug($slug)
    {
        $params = [];
        $slugParts = [];

        // Contrat (au début du slug)
        foreach ($this->contratTypeCacheService->data["by_slug"] as $slugContrat => $id) {
            if (strpos($slug, $slugContrat . "-") === 0) {
                $params["contrat"] = $id;
                $slugParts["contrat"] = $slugContrat;
                $slug = substr($slug, strlen($slugContrat) + 1);
                break;
            }
        }

        // Nettoyage des suffixes inutiles
        foreach (["-" . self::SLUG_ALL_METIERS_SECTEURS_SUFFIXE, "-" . self::SLUG_ALL_SECTEURS_SUFFIXE] as $suffix) {
            if (($pos = strpos($slug, $suffix)) !== false) {
                $slug = substr($slug, 0, $pos);
            }
        }

        // Détection secteur avec IDs + texte (ex: metiers-4-5-btp)
        if (preg_match('#(?:^|-)metiers?-([0-9\-]+)-(.+)$#i', $slug, $matches)) {
            $ids = explode("-", $matches[1]);
            $params["secteur"] = implode(", ", $ids);
            $params["secteur_ids"] = $ids;
            $params["q"] = str_replace(["-", "--"], [" ", " "], $matches[2]);
            $params["q_field"] = "secteur";
            $slugParts["secteur"] = $matches[1] . "-" . $matches[2];
        }

        // Fallback : slug brut avec ID(s) (ex: 4-5-btp)
        elseif (preg_match('#^(\d+(?:-\d+)*?)-(.+)$#', $slug, $matches)) {
            $ids = explode("-", $matches[1]);
            $params["secteur"] = implode(", ", $ids);
            $params["secteur_ids"] = $ids;
            $params["q"] = str_replace(["-", "--"], [" ", " "], $matches[2]);
            $params["q_field"] = "secteur";
            $slugParts["secteur"] = $slug;
        }

        // Fallback secteur par SLUG exact (sans ID)
        elseif (isset($this->secteurActivityCacheService->data["by_slug"][$slug])) {
            $id = $this->secteurActivityCacheService->data["by_slug"][$slug];
            $secteur = $this->secteurActivityCacheService->data["by_id"][$id];
            $params["secteur"] = $secteur["label"];
            $params["secteur_ids"] = [$id];
            $params["q"] = $secteur["label"];
            $params["q_field"] = "secteur";
            $slugParts["secteur"] = $slug;
        }

        // Fallback libre : slug non reconnu comme secteur → on considère que c'est une recherche libre
        else {
            $params["q"] = str_replace(["-", "--"], [" ", " "], $slug);
            $params["q_field"] = "";
            $slugParts["metier"] = $params["q"];
        }

        // Localisation avec code postal (ex: aix-en-provence-(13))
        if (preg_match('/(.*)-\((\d+)\)/', $slug, $matches)) {
            $slugParts["ville"] = $slug;
            $slugVille = $matches[1];
            $ville = $this->geoCacheService->getFirstVilleBySlug($slugVille);
            if ($ville) {
                $params["ville"] = $ville["label"];
                $slugParts["localisation"] = $slugVille;
                return [$params, $slugParts];
            }
        }

        // Recherche exacte dans geoCache (ville, département, région, pays)
        if (!isset($params["ville"]) && !isset($params["departement"]) && !isset($params["region"])) {
            $result = $this->geoCacheService->searchExactSlug($slug, ["pays", "departement", "region", "ville"]);
            if ($result) {
                $params[$result["type"]] = $result["label"];
                $slugParts["localisation"] = $slug;
            }
        }

        return [$params, $slugParts];
    }


    #[Route('/{slug}', name: 'BTP_emploi_slug')]
	public function offreSearchHome( Request $request, $slug, RechercheAvanceeCacheService $avanceeCacheService): Response{

		$options = [];
		list($params, $slugParts) = $this->parseLegacySlug($slug);
		$options["slugParts"] = $slugParts;
        return  $this->displaySearchList($request->query->all(), [], $params, $options);

	}

	public function getRechecheIfMatchSecteurs($params){
		if(isset($params["q"]) && isset($params["q_field"]) && $params["q_field"] == "secteur"){
			$secteurs = $this->secteurActivityCacheService->secteurListToSecteurs($params["q"]);
			$ids = [];
			foreach ($secteurs as $secteur){
				$ids[] = $secteur["id"];

			}
			if(count($ids)){
				return $this->rechercheCacheService->getRechercheForSecteurIds($ids);
			}
		}
	}

    public function parseSlugMetier($slugMetier){
        $secteurs = [];
        if(preg_match('/(\d+)-(.*)/', $slugMetier)){
            while($slugMetier && preg_match('/(\d+)-(.*)/', $slugMetier, $matches)){
                $id = $matches[1];
                $slugMetier = $matches[2];
                if(isset($this->secteurActivityCacheService->data["by_id"][$id])){
                    $secteur = $this->secteurActivityCacheService->data["by_id"][$id];
                    $secteurs[] = $secteur["label"];
                }
            }
        }else{
            $secteur = $this->secteurActivityCacheService->getBySlug($slugMetier);
            if($secteur){
                $secteurs[] = $secteur["label"];
            }

        }

        return $secteurs;
    }

    public function struct_home_search($string)
    {
        $from = array("'", 'É', 'È', 'é', 'ê', 'è', 'à', 'ô', ' - ', ' ', '-/');
        $to = array('-', 'e', 'e', 'e', 'e', 'e', 'a', 'o', '-', '-', '');
        $result = str_replace(array("--", "---"), array("-", "-"), str_ireplace($from, $to, strtolower($string)));
        return $result;
    }


}