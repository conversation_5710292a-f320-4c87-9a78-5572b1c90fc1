<?php

namespace App\Controller;

use App\Entity\OffOffre;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class AdminToolsController extends AbstractController
{
    #[Route('/admin/tools', name: 'app_admin_tools')]
    public function index(): Response
    {
        return $this->render('admin_tools/index.html.twig', [
            'controller_name' => 'AdminToolsController',
        ]);
    }

    #[Route('/admin/tools', name: 'app_admin_tools')]
    public function countHomePage(ManagerRegistry $doctrine): Response
    {

        $count_nbr_offre = $doctrine->getRepository(OffOffre::class)->findBy(["statut" => "1"]);
        dump($count_nbr_offre);
        dd();

        return $this->render('admin_tools/count_home_page.html.twig', [
            'controller_name' => 'AdminToolsController',
        ]);
    }
}
