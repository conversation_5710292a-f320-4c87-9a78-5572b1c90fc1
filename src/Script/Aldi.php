<?php

namespace App\Script;

use App\Entity\GeoDepartement;
use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use XMLReader;
use SimpleXMLElement;
use Exception;
use Pharse;

class Aldi extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }
    private function logScriptStart($scriptName)
    {
        $connection = $this->doctrine->getConnection();

        $queryCheck = "
            SELECT id
            FROM script_logs
            WHERE script_name = :script_name
            LIMIT 1
        ";
        $existingLog = $connection->fetchOne($queryCheck, [
            'script_name' => $scriptName,
        ]);

        if ($existingLog) {
            $queryUpdate = "
                UPDATE script_logs
                SET start_time = NOW(),
                    end_time = NULL,
                    status = FALSE,
                    offers_processed = 0,
                    error_message = NULL,
                    created_at = NOW()
                WHERE id = :id
            ";
            $connection->executeStatement($queryUpdate, [
                'id' => $existingLog,
            ]);

            return $existingLog;
        } else {
            $queryInsert = "
                INSERT INTO script_logs (script_name, start_time, status)
                VALUES (:script_name, NOW(), FALSE)
            ";
            $connection->executeStatement($queryInsert, [
                'script_name' => $scriptName,
            ]);

            return $connection->lastInsertId();
        }
    }

    private function logScriptEnd($logId, $status, $offersProcessed, $errorMessage = null)
    {
        $connection = $this->doctrine->getConnection();
        $query = "
            UPDATE script_logs
            SET end_time = NOW(),
                status = :status,
                offers_processed = :offers_processed,
                error_message = :error_message
            WHERE id = :id
        ";
        $connection->executeStatement($query, [
            'status' => $status,
            'offers_processed' => $offersProcessed,
            'error_message' => $errorMessage,
            'id' => $logId,
        ]);
    }

    public function AldiScript()
    {
        $logId = $this->logScriptStart('Aldi'); // Démarre un log
        $offersProcessed = 0;
        $errorMessage = null;
        try {


            $offersProcessed++;
        set_time_limit(0);
        error_reporting(E_ALL);

        $t_initial = time();
        $tab = array();
        $a=0;
        $datas = array();

        $lieu_manquant = array();
        $remuneration_manquante = array();
        $exp_manquante = array();

        $datas = [];
        $out = '';
        $subject = '';
        $OffreMaj = 0;
        $OffreCrees = 0;
        $OffreSupprimees = 0;


        $corr_lieu = array(
            "Provence-Cote d'Azur" => "Alpes-Maritimes",
            'Région Bruxelles-Capital' => 'Paris',
            'France' => 'Paris',
            "allée de L'arche 2 Courbevoie" => 'Courbevoie',
            'ST DIE DES VOSGES' => 'Saint-Dié-des-Vosges',
            'Semécourt' => 'Metz',
            'ST OUEN' => 'Saint-Ouen',
            'rue Blanqui Saint-Ouen' => 'Saint-Ouen',
            
            
        );

        $corr_type_contrat = array(
            'CDD' => 1,
            'CDI' => 2,
            'Expérimenté' => 2,
            'Graduate program' => 2,
            'Alternance : DUT/BTS' => 6,
            'Contrat à Durée Déterminée' => 1,
            'Interim' => 4,
            'Autre' => 4,
            'Contrat Pro' => 6,
            'Job étudiant' => 2,
            'Débutant' => 2,
            'Alternance / Apprentissage' => 6,
            'Alternance' => 6,
            'Stage' => 8,
            'Stage (6 mois)' => 8,
        );

        $urls = [
            'url' => 'https://www.aldi.fr/mds/hr/api/V2/jobSearch/2/public',
            'id_user' => '1255997',
            'name' => 'Aldi',
        ];

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $urls['url'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => array(
                "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36",
                "Cookie: 603ae3bca210bee6241540d7076e7253=3pl3ot6e9g3ge9j3ho2d11aj76",
                "Content-Type: application/json",
            ),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_COOKIEJAR => '/tmp/cookie.txt',
            CURLOPT_COOKIEFILE => '/tmp/cookie.txt'
        ]);

        $response = curl_exec($curl);
        curl_close($curl);

        $json_data = json_decode($response, true);

        foreach ($json_data as $offre) {
            $data = [];

            // dd($offre);

            if ($offre->internal_apply_url ?? false) {
                continue;
            }

            $data['intitule'] = $offre['title'];
            $data['reference'] = "ALDI-" . $offre['postingId'];
            // $data['id_type_contrat'] = $corr_type_contrat[$offre['contract']];

            $data['id_type_contrat'] = $corr_type_contrat[$offre['careerLevel']];

            $profil_paragraphs = $offre['summary'];
            $profil_text = substr($profil_paragraphs, strpos($profil_paragraphs, "A propos de nous"));

            $data['mission'] = $profil_text;

            $rep_url = $offre['applicationUrl'];

            // $data['rep_mail'] = $offre->email;
            $data['rep_url'] = $rep_url;

            $lieux = $offre['locations'][0]['city'];
            $ville_find = $this->localisation($lieux, null, null, null, $out);

            if ($ville_find) {
                $data['id_departements'] = $ville_find["id_departements"];
                $data['postale_code'] = $ville_find["postale_code"];
                $data['id_regions'] = $ville_find["id_regions"];
                $data['geo_ville'] = $ville_find["geo_ville"];
            } else {
                echo "ville non trouvée = " . $lieux;
            }

            $data['id_secteurs'] = 58; // Grande Distribution

            $datas[] = $data;
        }

        // Uncomment the following lines to perform further actions
        $info = $this->saveInDatabase($datas, $urls['id_user'], $urls['name']);
        $out .= $info[0];
        $idsOffresToConserve = $info[1];
            $entityManager = $this->doctrine->getManager();
            $out = $this->supprimerOffresV2($out, $offreMaj = 0, $offreCrees = 0, $idsOffresToConserve, $urls['id_user'], $entityManager);
        $this->saveInfoScript("Aldi", $out, $subject, $offreSupprimees = 0, $offreMaj, $offreCrees);
            $this->logScriptEnd($logId, true, $offersProcessed, null); // Enregistre la fin avec succès
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $this->logScriptEnd($logId, false, $offersProcessed, $errorMessage); // Enregistre la fin avec erreur
        }
        $OffreSupprimees += $offreSupprimees;
        $OffreMaj += $offreMaj;
        $OffreCrees += $offreCrees;

        $this->envoyerMail($out, "Aldi", $offreMaj, $offreCrees);

        $this->outStatscrawl('Aldi');
    }

    function time_elapsed_A($secs)
    {
        $bit = array(
            'y' => $secs / 31556926 % 12,
            'w' => $secs / 604800 % 52,
            'd' => $secs / 86400 % 7,
            'h' => $secs / 3600 % 24,
            'm' => $secs / 60 % 60,
            's' => $secs % 60
        );

        $ret = [];
        foreach ($bit as $k => $v) {
            if ($v > 0) $ret[] = $v . $k;
        }

        return join(' ', $ret);
    }

    private function envoyerMail($out, $name_crawl, $nb_offres_maj, $nb_offres_crees)
    {
        global $subject;
        if ($nb_offres_maj > 1 || $nb_offres_crees > 1) {
            $to = '<EMAIL>';
            $subject = 'Crawl import ' . $name_crawl;
            global $t_initial;
            $subject .= " offre :";
            $subject .= $nb_offres_maj + $nb_offres_crees;
            if (isset($t_initial)) {
                $t_mail = time();
                $subject .= "  temps total :" . $this->time_elapsed_A($t_mail - $t_initial);
            }
            $headers = 'From: <EMAIL>' . "\r\n";
            $headers .= 'Reply-To: <EMAIL>' . "\r\n";
            $headers .= 'MIME-Version: 1.0' . "\r\n";
            $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
            mail($to, $subject, $out, $headers);
        } else {
            $to = '<EMAIL>';
            $subject = '!!!!!!!!Crawl vide!!!!!!! ' . $name_crawl;
            $headers = 'From: <EMAIL>' . "\r\n";
            $headers .= 'Reply-To: <EMAIL>' . "\r\n";
            $headers .= 'MIME-Version: 1.0' . "\r\n";
            $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
            mail($to, $subject, $out, $headers);
        }
    }
}
