<?php

namespace App\Script;

use App\Entity\CvthequeSaveSearch;
use Doctrine\Persistence\ManagerRegistry;
use App\Service\ELKCVService;
use App\Entity\UserUser;
use App\Service\ToolsService;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\GeoDepartement;
use App\Entity\OffOffre;
use App\Entity\OffLienOffreGeoDepartement;
use App\Entity\OffLienOffreGeoListeRegion;
use App\Entity\OffLienOffreListeExperienceRequise;
use App\Entity\OffLienOffreListeSecteurActivite;
use App\Entity\OffLienOffreListeTypeFormation;
use App\Entity\OffLienOffreListeSousSecteurActivite;
use App\Entity\GeoListeRegion;
use App\Entity\OffDiffuTmp;
use Doctrine\ORM\EntityManagerInterface;
use DateTime;
use Doctrine\ORM\Mapping\Entity;
use App\Service\MailerService;

class AlerteMailCvtSearch extends ToolsService
{

    //private ManagerRegistry $doctrine;
    private EntityManagerInterface $entityManager;
    private ELKCVService $ELKCVService;
    private MailerService $mailer;

    public function __construct(MailerService $mailer, ManagerRegistry $doctrine, EntityManagerInterface $entityManager, ELKCVService $ELKCVService)
    {
        parent::__construct($doctrine);
        //$this->doctrine = $doctrine;
        $this->entityManager = $entityManager;
        $this->ELKCVService = $ELKCVService;
        $this->mailer = $mailer;



    }

    public function sendCvtMailSearchSave()
    {

        $all_save_search = $this->doctrine->getRepository(CvthequeSaveSearch::class)->findBy(
            ["mail" => "1"]
        );

        $mail_user = array();

        if($all_save_search){
            foreach($all_save_search as $one_save_search){

                $params = array();
                $name = $one_save_search->getName();
                $user_user = $one_save_search->getUserUserIdUser();
                $params["mot_cle"] = $one_save_search->getMotCle();
                $params["id_secteur_activite"] = unserialize($one_save_search->getIdSecteurActiviteSerialize());
                $params["id_region"] = unserialize($one_save_search->getIdRegionSerialize());
                $params['id_experience_requise'] = unserialize($one_save_search->getIdExperienceRequiseSerialize());
                $params["id_departement"] = unserialize($one_save_search->getIdDepartementSerialize());
                $params["id_metier"] = unserialize($one_save_search->getIdMetierSerialize());
                $params["id_type_contrat"] = unserialize($one_save_search->getIdTypeContratSerialize());
                $params["id_type_formation"] = unserialize($one_save_search->getIdTypeFormationSerialize());

                $params = array_filter($params);
                $array_result = $this->ELKCVService->mutltipleSearch($params);

		 	    $total = 0;                
                for($i = 1; $i <= 9; $i++) {
                    $total += count($array_result['selection'.$i][0]);
                }

		 	    $all_total =  $total;

                if($total > 0) {
                    $email_txt = "<strong>" . $total . "</strong> Cvs correspondent actuellement à votre recherche : ". $name . "<br>";
                    $mail_user[$user_user][] = $email_txt;
                }


            }

            foreach($mail_user as $key => $mail_one_user ) {
                //$email_text = $key;
                $email_text = 
                "Bonjour,
                
                De nouveaux Cvs sont arrivés chez Direct emploi.</br>";
                $email_text .= implode(' ',$mail_one_user);
                $email_text .= 
                "
                Retrouver tous vos Cvs dans votre espace recruteur sur notre site www.directemploi.com

                Bien cordialement
                
                L'équipe de Direct Emploi";

                $user = $this->doctrine->getRepository(UserUser::class)->findOneBy(["id_user" => $user_user]);    
                //$email = $user->getEmail();
                $bodyTxt = strip_tags($email_text);
                $bodyHtml = nl2br($email_text);
		        $email = "<EMAIL>";
                $subject = "Votre recherche de Cv chez Direct Emploi";
                $this->mailer->sendMailCvt($email, $bodyTxt, $bodyHtml, $subject);

            }
                   


            /*dump($email_text);
            dump($subject);
            dump($email);
            dd('la');*/
        }
    }

}
