<?php

namespace App\Script;

use App\Entity\GeoDepartement;
use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use XMLReader;
use SimpleXMLElement;
use Exception;

class Lareleve extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }

    public function LareleveScript()
    {
        $logId = $this->logScriptStart('Lareleve'); // Démarre un log
        $offersProcessed = 0;
        $errorMessage = null;
        try {
        set_time_limit(0);
        error_reporting(E_ALL);

        $t_initial = time();

        $timestamp_debut = microtime(true);
        $timeArray = array();

        $datas = array();
        $array = array(
            //LA RELEVE (1065808) :
            //'url' => 'https://feeds.la-releve.com/rss/direct_emploi',
            'url' => 'https://app.recruitcrm.io/services/jobfeed/indeed.php?account=La_Relve',
            'id_user' => '1065808',
            'name' => 'LA RELEVE',
        );
        

        $this->inStatscrawl('Lareleve');

        $corr_type_contrat = array(
            'STAGE' => 8,
            'ALTERNANCE' => 6,
            'CDI' => 2,
            'CDD' => 1,
            'JOB_ETUDIANT' => 9,
            'JOB_ETUDIANT ' => 9,
        );

        $out = '';
        $info = '';

        $list_secteur = array();


        $info .= "START Client : " . $array["id_user"] . '<br/><br/>';
        $source = $array['url'];


        $XMLReader = new XMLReader;
        $XMLReader->open($source);

        $out .= "<br/>";

        $idsOffresToConserve = array();
        $count = 0;

        while ($XMLReader->read() && $XMLReader->name !== "job");
        while ($XMLReader->name === "job") {

            $data = array();
            $item = new SimpleXMLElement($XMLReader->readOuterXML());

            $bloc_principal = trim(str_replace(array("?", "??", "???", "????", "?????", "??????"), "", utf8_decode((string)$item->description)));

            $full_info_supp = str_ireplace("Informations complémentaires", "", strstr($bloc_principal, "Informations complémentaires"));
            $full_profil = str_ireplace(array("Profil recherché", $full_info_supp, "Informations complémentaires"), "", strstr($bloc_principal, "Profil recherché"));
            $full_mission = str_ireplace(array("Description du poste et des missions", $full_profil, $full_info_supp, "Profil recherché"), "", strstr($bloc_principal, "Description du poste et des missions"));
            $full_type_contrat = strstr($full_info_supp, "Statut", true);

            $profil = strstr($bloc_principal, "Profil recherché");


            if (stripos((string)$item->title, "CDI") !== false) {
                $data['id_type_contrat'] = $corr_type_contrat["CDI"];
            } elseif (stripos((string)$item->title, "CDD") !== false) {
                $data['id_type_contrat'] = $corr_type_contrat["CDD"];
            } elseif (stripos((string)$item->title, "STAGE") !== false) {
                $data['id_type_contrat'] = $corr_type_contrat["STAGE"];
            } elseif (stripos((string)$item->title, "INTERIM") !== false) {
                $data['id_type_contrat'] = $corr_type_contrat["INTERIM"];
            } elseif (stripos((string)$item->title, "ALTERNANCE") !== false) {
                $data['id_type_contrat'] = $corr_type_contrat["ALTERNANCE"];
            }


            $data['reference'] = "LaR-" . (string)$item->referencenumber;
            $data['intitule'] = (string)$item->title;
            $data['id_secteurs'] = array();
            $data['id_sous_secteurs'] = array();
            $data['mission'] = trim($full_mission);
            $data['searched_profile'] =  trim($full_profil) . "</br>" . trim($full_info_supp);
            $data['rep_url'] = (string)$item->url;
            $data['rep_mail'] = null;
            $data['duree_contrat'] = null;
            $data['id_fourchette_remuneration'] = null;
            $data['id_experience_requise'] = null;

            //LIEUX
            $localisation = (string)$item->city;
            //localisation($lieux, $id_region, $cp_two, $id_dep, $out)
            $ville_find = $this->localisation($localisation, $out, null, null, null);
            if ($ville_find) {
                $data['id_departements'] = $ville_find["id_departements"];
                $data['postale_code'] = $ville_find["postale_code"];
                $data['id_regions'] = $ville_find["id_regions"];
                $data['geo_ville'] = $ville_find["geo_ville"];
            }

            if ($data['geo_ville'] == "France") {

                $localisation = (string)$item->state;
                $ville_find = $this->localisation($localisation, $out, null, null, null);

                if ($ville_find) {
                    $data['id_departements'] = $ville_find["id_departements"];
                    $data['postale_code'] = $ville_find["postale_code"];
                    $data['id_regions'] = $ville_find["id_regions"];
                    $data['geo_ville'] = $ville_find["geo_ville"];
                }
            }

            $secteur_array = $this->remplirTableScriptSecteursAll((string)$item->title, $array['id_user'], $data['intitule']);
            $data['id_secteurs'] = $secteur_array[0];
            $data['id_sous_secteurs'] = $secteur_array[1];

            if ($data['reference'] != "LaR-160283") {
                $offersProcessed++;
                $datas[] = $data;

            }

            $XMLReader->next("job");
        }


        $infos = $this->saveInDatabase($datas, $array['id_user'], $array['name']);
        $info .= $infos[0];
        $idsOffresToConserve = $infos[1];
        $entityManager = $this->doctrine->getManager();
        $info = $this->supprimerOffresV2($info, $offreMaj = 0, $offreCrees = 0, $idsOffresToConserve, $array['id_user'], $entityManager);
        // $info = $this->supprimerOffres($info, $offreMaj = 0, $offreCrees = 0, $infos[1], $array['id_user']);



        $info .= '<br/><br/>Secteurs manquants :' . implode(',', $list_secteur);

        $this->saveInfoScript("Lareleve", $info, $offreSupprimees = 0, $offreMaj, $offreCrees, $array_id_users = null);
            $this->logScriptEnd($logId, true, $offersProcessed, null); // Enregistre la fin avec succès
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $this->logScriptEnd($logId, false, $offersProcessed, $errorMessage); // Enregistre la fin avec erreur
        }
        $this->outStatscrawl('Lareleve');
    }

    private function logScriptStart($scriptName)
    {
        $connection = $this->doctrine->getConnection();

        $queryCheck = "
            SELECT id 
            FROM script_logs 
            WHERE script_name = :script_name
            LIMIT 1
        ";
        $existingLog = $connection->fetchOne($queryCheck, [
            'script_name' => $scriptName,
        ]);

        if ($existingLog) {
            $queryUpdate = "
                UPDATE script_logs
                SET start_time = NOW(),
                    end_time = NULL,
                    status = FALSE,
                    offers_processed = 0,
                    error_message = NULL,
                    created_at = NOW()
                WHERE id = :id
            ";
            $connection->executeStatement($queryUpdate, [
                'id' => $existingLog,
            ]);

            return $existingLog;
        } else {
            $queryInsert = "
                INSERT INTO script_logs (script_name, start_time, status)
                VALUES (:script_name, NOW(), FALSE)
            ";
            $connection->executeStatement($queryInsert, [
                'script_name' => $scriptName,
            ]);

            return $connection->lastInsertId();
        }
    }

    private function logScriptEnd($logId, $status, $offersProcessed, $errorMessage = null)
    {
        $connection = $this->doctrine->getConnection();
        $query = "
            UPDATE script_logs
            SET end_time = NOW(),
                status = :status,
                offers_processed = :offers_processed,
                error_message = :error_message
            WHERE id = :id
        ";
        $connection->executeStatement($query, [
            'status' => $status,
            'offers_processed' => $offersProcessed,
            'error_message' => $errorMessage,
            'id' => $logId,
        ]);
    }
}