<?php

namespace App\Script;

use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use XMLReader;
use SimpleXMLElement;
use Exception;
use Pharse;

class Alterego extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }

    public function AlteregoScript()
    {
        set_time_limit(0);
        error_reporting(E_ALL);
        $t_initial = time();

        $urls = array(
            array(
                // Alterego (1103980) :
                'url' => 'https://www.alterego-interim.fr/offres-en-cours',
                'id_user' => '1103980',
                'name' => 'Alterego',
            ),
        );


        $this->inStatscrawl('Alterego');

        $corr_type_contrat = array(
            'CDD' => 1,
            'CDI' => 2,
            'Intérim' => 4,
            'Autre' => 4,
        );

        $out = '';
        $subject = '';
        $error_code = '';

        foreach ($urls as $array) {

            $datas = array();

            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $array['url'],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTPHEADER => array(
                    "Cookie: 603ae3bca210bee6241540d7076e7253=davgh2chdk7k26i9bv8bkqs293"
                ),
                CURLOPT_SSL_VERIFYPEER => false,
            ));

            $response = curl_exec($curl);
            curl_close($curl);

            $html = Pharse::str_get_dom($response);

            foreach($html('ul[id="listeoffresror"]>li>a') as $li_offre){

                $data = array();

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => (string)$li_offre->getAttribute('href'),
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => "",
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => "GET",
                    CURLOPT_HTTPHEADER => array(
                        "Cookie: 603ae3bca210bee6241540d7076e7253=3pl3ot6e9g3ge9j3ho2d11aj76"
                    ),
                    CURLOPT_SSL_VERIFYPEER => false,
                ));

                $response = curl_exec($curl);
                curl_close($curl);

                $html = Pharse::str_get_dom($response);

                $data['intitule'] = (string)$html('h2[class="title"]', 0)->getPlainText();
                $contrat = $html('div[id="wuc_offre_description_detailContainer"]', 0)->getPlainText();
                if (preg_match('/CDD/', $contrat)) {
                    $data['id_type_contrat'] = 1;
                } elseif (preg_match('/CDI/', $contrat)) {
                    $data['id_type_contrat'] = 2;
                } elseif (preg_match('/Intérim/', $contrat)) {
                    $data['id_type_contrat'] = 4;
                } else {
                    $data['id_type_contrat'] = 4;
                }
                // $data['id_type_contrat'] = $corr_type_contrat[(string)$html('span[class="wuc_offre_description_job_contract"]', 0)->getPlainText()];
                $data['reference'] = 'ALT-'.substr(md5($data['intitule']),0,5);
                // $rep_url = str_ireplace("prov=alterego-interim.fr","prov=directemploi", (string)$html('div[class="of_repo"] > a', 0)->getAttribute('href'));
                // $data['rep_url'] = $rep_url;
                $data['rep_url'] = (string)$li_offre->getAttribute('href');
                // $data['mission'] = (string)trim($html('div[class="of_mis"]',0)->getPlainText());
                // $data['mission'] = (string)trim($html('div[class="job-content"]',0)->getPlainText());
                // $data['mission'] = (string)trim($html('div[class="job-content"]',0)->getPlainText()); je voudrais récuperer le premier p mais je n'y arrive pas
                $data['mission'] = (string)trim($html('div[class="job-content"] > p',0)->getPlainText());
                $data['searched_profile'] = (string)trim($html('div[class="job-content"] > p',1)->getPlainText());

                //Localisation

                // $lieu = (string)$html('h4[class="of_lieu"]', 0)->getPlainText();
                $lieu = (string)$html('div[class="detail-container"] > span', 2)->getPlainText();
                // Recherche de la première occurrence de l'espace suivi du tiret "-"
                $pos = strpos($lieu, ' - ');

                if ($pos !== false) {
                    // Extraire le nom de la ville après la première occurrence de l'espace et du tiret
                    $ville = trim(substr($lieu, $pos + 3));

                    // Recherche de la première occurrence de la parenthèse ouvrante
                    $parenthesePos = strpos($ville, '(');

                    if ($parenthesePos !== false) {
                        // Supprimer la partie entre parenthèses (le code postal)
                        $ville = trim(substr($ville, 0, $parenthesePos));
                    }
                } else {
                    $ville = '';
                }

                $data['geo_ville'] = $ville;

                $ville_find = $this->localisation($ville, null, null, null, $out);

                if($ville_find){
                    $data['id_departements'] = $ville_find["id_departements"];
                    $data['postale_code'] = $ville_find["postale_code"];
                    $data['id_regions'] = $ville_find["id_regions"];
                    $data['geo_ville'] = $ville_find["geo_ville"];
                } else {
                    echo "ville non trouvee = ".$ville;
                }

                //Secteur
                $secteur_array = $this->remplirTableScriptSecteursAll($data['intitule'], $array['id_user'], $data['intitule']);
                $data['id_secteurs'] = $secteur_array[0];
                $data['id_sous_secteurs'] = $secteur_array[1];

                //INFORMATIONS NON FOURNIES
                $data['date_debut'] = null;
                $data['duree_contrat'] = null;
                $exp = (string)$html('div[class="detail-container"] > span', 4)->getPlainText();
                $data['id_experience_requise'] = [$exp];
                // $data['id_experience_requise'] = array(null);
                $data['id_fourchette_remuneration'] = null;
                $data['company'] = "Alterego Intérim";

                $datas[] = $data;

            }

            $info =  $this->saveInDatabase($datas, $array['id_user'], $array['name']);
            $out.=$info[0];
            $idsOffresToConserve = $info[1];
            $entityManager = $this->doctrine->getManager();
            $out = $this->supprimerOffresV2($out, $offreMaj = 0, $offreCrees = 0, $idsOffresToConserve, $array['id_user'], $entityManager);

        }

        $t_final = time();
        $out .= "<br>temps total = " .  $this->time_elapsed_A($t_final - $t_initial);

        $this->saveInfoScript("Alterego", $out, $offreSupprimees = 0, $offreMaj, $offreCrees, $subject);

        $this->outStatscrawl('Alterego');
            }

}