<?php

namespace App\Script;

use App\Entity\GeoDepartement;
use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use XMLReader;
use SimpleXMLElement;
use Exception;

class NextgenRH extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }

    public function NextgenRHScript()
    {

        set_time_limit(0);
        error_reporting(E_ALL);

        $array_id_users=array();
        $OffreMaj=0;
        $OffreCrees=0;
        $OffreSupprimees=0;
        $t_initial=time();

        $timestamp_debut = microtime(true);
        $timeArray = array();

        $limit_nombre_integration = 1000000;
        //$limit_nombre_integration = 1;
        $nb_offres_importees=0;
        $nb_offres_importees_flux=0;

        // PARAMETERS
        // desactivations des offres :
        // UPDATE off_offre SET statut=0 WHERE user_user_id_user IN (963737, 963740, 963743, 963743);
        $urls = array(
            array(
                'url' => 'https://bench.talents-in.com/jobboards/direct_emploi/XML_directemploi.xml',
                'id_user' => '1192188',  
                 'name' => 'NextgenRh',
            ),   
        );

        $this->inStatscrawl('NextgenRh');

        $corr_type_contrat = array(
            'CDD' => 1,
            'Indépendant' => 12,
            'CDD Temps partiel' => 1,
            'CDI Temps partiel' => 2,
            'CDI' => 2,
            'Intérim' => 4,
            'interim' => 4,
            'Vacation' => 4,
            'Contrat professionnalisation' => 6,
            "Contrat d'apprentissage" => 6,
            'Stage' => 8,
            "Alternance" => 6,
        );

        $corr_salary = array( 
            '25000(€) 30000(€) Annuel' => 3,
            '39000(€) 40000(€) Annuel' => 4,
            '37000(€) 38000(€) Annuel' => 3,
            '39000(€) 42000(€) Annuel' => array(3,4),
            '42000(€) 52000(€) Annuel' => array(4,5),
            '30000(€) 37000(€) Annuel' => 3,
            '35000(€) 40000(€) Annuel' => 3,
            '45000(€) 55000(€) Annuel' => array(4,5),
            '23000(€) 24000(€) Annuel' => 2,
            '45000(€) 48000(€) Annuel' => 4,
            '24000(€) 26000(€) Annuel' => 2,
            '1850(€) 2000(€) Mensuel' => 2,
            '1700(€) 2200(€) Mensuel' => 2,
            '1700(€) 1900(€) Mensuel' => 2,

        );

        $corr_formation = array(
            "Bac /Bac + 2"=> array(2,8),
            "LICENCE"=>array(9),
            "Bac +3 (Licence)" => array(3),
            "BTS/DUT/LICENCE PRO/CAP"=>array(8,9,6),
            "Bac" =>array(2),
            "BAC + 2"=>array(8),
            "Bac + 2"=>array(8),
            "Bac +3/4"=>array(9,10),
            "Bac +4"=>array(10),
            "BTS/DUT"=>array(8),
            "Bac +2"=>array(8),
            "Bac +5 (Master)" => array(10,11),
        );  

        $corr_exp = array(         //tableau réalisé à partir des données de la table off_liste_experience_requise
            "0 à 2 ans"=>array(1),
            "2 à 5 ans"=>array(2),
            "5 à 7 ans" =>array(3),
            "+ de 7 ans"=>array(4),
            "7 à 10 ans"=> array(4),
        );


        $out = '';
        $list_secteur = array();
        $sect = 0;

        $subject = '';
        $error_code = '';

        foreach($urls as $array){
            $datas = array();

            //creation fichier temporaire

            $source = $array['url'];
            $header = ('Content-type: text/html, charset=UTF-8');
            $user_agent = "direct-emploi";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $source);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSLVERSION, 0);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_USERAGENT, $user_agent);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE); // Désactive la vérification du certificat ssl
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            $data = curl_exec($ch);

            $error = curl_error($ch);
            curl_close($ch);

            $destination = "/tmp/testtmp" . date("H:i:s") . ".php";
            $file = fopen($destination, "w+");
            fputs($file, $data);
            fclose($file);

            $timeArray[$array['name']] = microtime(true);

            $XMLReader = new XMLReader;
            $XMLReader->open($data);

            $UserUser = $this->doctrine->getRepository(UserUser::class)->findOneBy(
                ['id_user' => $array['id_user']]
            );

            $out.= "<br/>";
            if(!$UserUser){
                echo 'Erreur : utilisateur non reconnu :'.$array['id_user'];
                $out .= 'Erreur : utilisateur non reconnu :'.$array['id_user'];
                $subject = 'Cron import Effektiv Error User unknown';
                continue;
            }

            $out .= "<br/><br/>START id_client : " . $array['id_user'] . " - Nom_client : " . $array['name'] ." - Flux : ". $array['url'] . "<br/><br/>";


            $idsOffresToConserve = array();


            while ($XMLReader->read() && $XMLReader->name !== "job") ;
            while ($XMLReader->name === "job") {

                $data = array();

                $nb_offres_importees++;
                $nb_offres_importees_flux++;
                if($nb_offres_importees > $limit_nombre_integration){
                    continue(2);
                }

                $item = new SimpleXMLElement($XMLReader->readOuterXML());

                if((int)$item->client_id != "1192188"){
                    $XMLReader->next("job");
                    continue;
                }
                
                $contrat = (string)$item->contract_type;
                if($contrat == ""){
                    $contrat = (string)$item->contract_type;
                }
                $education = (string)$item->education;
                if($education == ""){
                    $education = (string)$item->education;
                }
                $experience = (string)$item->experience;
                if($experience == ""){
                    $experience = (string)$item->experience;
                }
                $salaire = (string)$item->salary;
                if($salaire == ""){
                    $salaire = (string)$item->salary;
                }
                
                $out.= "<br/>";

                //Correspondance xml -> bdd

                $data['reference'] = (string)$item->reference;
                $data['id_type_contrat'] = $corr_type_contrat[$contrat];

                // INTITULE
                if( strpos($item->title, ' H/F -') ) {
                    $data['intitule'] = substr((string)$item->title, 0, strpos((string)$item->title, ' H/F -') + 4 );
                } elseif( strpos($item->title, ' (H/F)') ) {
                    $data['intitule'] = substr((string)$item->title, 0, strpos((string)$item->title, ' (H/F)') + 6 );
                } else {
                    $data['intitule'] = (string)$item->title;
                }
                

                //LIEU
                $lieu = (string)$item->city;

                if(stristr($lieu," Marseille ") != false)
                {
                    $lieu = "Marseille";
                }
            
                $ville_find = $this->localisation($lieu, $out, null, null, null);

                $dep_array = array();
                $idep = 0;
                if(is_array($ville_find["id_departements"]) && (count($ville_find["id_departements"])>1)){
                    foreach($ville_find["id_departements"] as $dep){
                        $dep_array[$idep] = $dep[0];
                        $idep++;
                    }
                } else {
                    $dep_array = $ville_find["id_departements"];
                }
            
                $reg_array = array();
                $idreg = 0;
                if(is_array($ville_find["id_regions"])){
                    foreach($ville_find["id_regions"] as $regs){
                        if(is_array($regs)){
                            $reg_array[$idreg] = $regs[0];
                            $idreg++;
                        } else {
                            $reg_array[$idreg] = $regs;
                            $idreg++;
                        }
                    }
                }
            
                if($ville_find){
                    $data['id_departements'] = $dep_array;
                    $data['postale_code'] = $ville_find["postale_code"];
                    $data['id_regions'] = $reg_array;
                    $data['geo_ville'] = $ville_find["geo_ville"]; 
                } else {
                    echo "ville non trouvé = ".$lieu;
                }

                $data['id_secteurs'] = array();
                $data['id_sous_secteurs'] = array();

                // SECTEURS
                $secteur = 'informatique';
                $secteur_array = $this->remplirTableScriptSecteursAll($secteur, $array['id_user'],$data['intitule']);
                $data['id_secteurs'] = $secteur_array[0];
                $data['id_sous_secteurs'] = $secteur_array[1];
                $out.= "Secteur manquant ".$array['name']." / ".$data['intitule']."\r\n";

                // Autres informations
                $data['mission'] =  (string)$item->description;
                $data['searched_profile'] =  (string)$item->profile;
                $data['rep_url'] =  (string)$item->apply_link;
                $data['rep_mail'] =  null;
                $data['handicap'] = (string)$item->handicap;
                $data['date_debut_contrat'] =(string)$item->start_date;
                
                $datas[]=$data;

                $XMLReader->next("job");
            }

            $array_id_users[] = $array['id_user'];
            $info=$this->saveInDatabase($datas,$array['id_user'],"");
            $out.=$info[0];
            $idsOffresToConserve=$info[1];
            $out= $this->supprimerOffres($out, $offreMaj = 0,$offreCrees = 0, $idsOffresToConserve, $array['id_user']);
            $this->saveInfoStatScript("NextgenRh",$array['id_user'], $offreSupprimees = 0,$offreMaj,$offreCrees);
            $OffreSupprimees+= $OffreSupprimees;
            $OffreMaj+=$OffreMaj;
            $OffreCrees+=$OffreCrees;
            // on supprime (mettre en -2) les offres de cet user qui ne sont pas dans le flux

            $out.= "<br/> Nombre d'offres importees pour cet url= ".$nb_offres_importees_flux;
            $nb_offres_importees_flux = 0;

        }

        $this->saveInfoScript("NextgenRh",$out,$OffreSupprimees,$OffreMaj,$OffreCrees,$array_id_users, $subject);

        $this->outStatscrawl('NextgenRh');
    }
}

?>