<?php

namespace App\Script;

use App\Entity\GeoDepartement;
use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use XMLReader;
use SimpleXMLElement;
use Exception;

class MichaelPageInterim extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }

    public function MichaelPageInterimScript()
    {
        $logId = $this->logScriptStart('MichaelPageInterim'); // Démarre un log
        $offersProcessed = 0;
        $errorMessage = null;
        try {
        set_time_limit(0);
        error_reporting(E_ALL);

        $out = '';
        $subject = '';
        $this->inStatscrawl('MichaelPage');

        $array_all = array(
            //MichaelPage (1207824) :
            array(
                'url' => "https://batchaws.adcourier.com/services/?q=U2FsdGVkX19zregTG4j1IiwDSBWxU_O9COAIeOOSQLBokjqFb_5MZNRBmgXbaX5FNcfq_TrjcVuVG5oA4Ssw7g",
                'id_user' => '1207824',
                'name' => 'MichaelPage',
            )
        );

        $id_client = array(1212869);

        $corr_type_contrat = array(
            'Intermittent' => 2,
            'CDD / Saisonnier' => 1,
            'CDD' => 1,
            'Travail temporaire' => 4,
            'Alternance' => 6,
            'CDI' => 2,
            'Stage' => 4,
            'Job Ã©tudiant' => 9,
            'Job étudiant' => 9,
        );


        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $array_all[0]['url'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        $destination = "/tmp/import_michaelPage" . date("H:i:s") . "php";
        $file = fopen($destination, "w+");
        fputs($file, $response);
        fclose($file);



        foreach($array_all as $array){
        $XMLReader = new XMLReader;
        $XMLReader->open($destination);
        $UserUser = $this->doctrine->getRepository(UserUser::class)->findOneBy(
            [   'id_user' => $array['id_user']]
        );

        $out .= "<br/>";
        if (!$UserUser) {
            echo 'Erreur : utilisateur non reconnu :' . $array['id_user'];
            $subject = 'Cron import MichaelPage Error User unknown';
            continue;
        }
        $out .= "<br/><br/>START id_client : " . $array['id_user'] . " - Nom_client : " . $array['name'] . " - Flux : " . $array['url'] . "<br/><br/>";

        while ($XMLReader->read() && $XMLReader->name !== "client");
        while ($XMLReader->name === "client") {

            //sleep(1);

            $data = array();
            $item = new SimpleXMLElement($XMLReader->readOuterXML());

            if (!in_array((string)$item->job->id_recruteur, $id_client)) {
                $XMLReader->next("client");
                continue;
            }
            // Que des offres en Interim
            if ((int)$item->job->type_contrat !== 4) {
                $XMLReader->next("client");
                continue;
            }
            $data['reference'] = (string)$item->job->reference_offre == '' ? null : (string)$item->job->reference_offre;
            $data['intitule'] = (string)$item->job->libelle_poste == '' ? null : (string)$item->job->libelle_poste;
            $data['id_type_contrat'] = (int)$item->job->type_contrat == 0 ? null : (int)$item->job->type_contrat;

            $mission = (string)$item->job->descriptif_poste == '' ? null : (string)$item->job->descriptif_poste;

            $mission = str_ireplace('data-aura-rendered-by="1958:0"', "", $mission);
            $mission = str_ireplace('style="text-align: center;"', "", $mission);
            $mission = strip_tags($mission);

            $data['mission'] = $mission;
            $data['searched_profile'] = (string)$item->job->profil == '' ? null : (string)$item->job->profil;
            $data['rep_url'] = (string)$item->job->url == '' ? null : (string)$item->job->url;
            $data['rep_mail'] = (string)$item->job->aplitrakmail == '' ? null : (string)$item->job->aplitrakmail;
            $data['handicap'] = (int)$item->job->handicap == 0 ? null : (int)$item->job->handicap;
            $data['anonym'] = (int)$item->job->anonyme == 0 ? 0 : (int)$item->job->anonyme;
            $data['id_departements'] = (int)$item->job->location_departement == 0 ? array() : array((int)$item->job->location_departement);
            $data['date_debut_contrat'] = (string)$item->job->date_debut == '' ? null : (string)$item->job->date_debut;
            $data['duree_contrat'] = (string)$item->job->periode == '' ? null : (string)$item->job->periode;
            $data['id_pays'] = (int)$item->job->pays == 0 ? array(1) : array((int)$item->job->pays);
            $data['id_experience_requise'] = (int)$item->job->niveau_experience == 0 ? null : array((int)$item->job->niveau_experience);
            $data['salaire'] = (int)$item->job->salaire == 0 ? null : (int)$item->job->salaire;
            $data['nom_societe'] = (string)$item->job->companyname == '' ? null : (string)$item->job->companyname;
            $data['company'] = "Michael Page Interim";

            $data['id_fourchette_remuneration'] = (int)$item->job->salaire;

            $data['id_secteurs'][] = (int)$item->job->secteur == 0 ? 31 : (int)$item->job->secteur;
            $data['id_sous_secteurs'][] = (int)$item->job->sous_industrie;




            //Ville
            if ($item->job->location_city != '') {

                $lieu = $item->job->location_city;
                if (stripos($lieu, "Paris") !== false) {
                    $lieu = "Paris";
                }
                if (stripos($lieu, "Lyon-") !== false) {
                    $lieu = "Lyon";
                }

                $ville_find = $this->localisation($lieu, null, null, null, $out);
                if ($ville_find) {
                    $data['id_departements'] = $ville_find["id_departements"];
                    $data['postale_code'] = $ville_find["postale_code"];
                    $data['id_regions'] = $ville_find["id_regions"];
                    $data['geo_ville'] = $ville_find["geo_ville"];
                } else {
                    echo "ville non trouvé = " . $lieu;
                }
            }

            $datas[(string)$item->job->id_recruteur][] = $data;
            $offersProcessed++;

            $XMLReader->next("client");
        }

        foreach ($datas as $key => $data_all) {

            $info = $this->saveInDatabase($data_all, $key, $array['name']);
            $out .= $info[0];
            $idsOffresToConserve = $info[1];
            $entityManager = $this->doctrine->getManager();
            $out = $this->supprimerOffresV2($out, $offreMaj = 0, $offreCrees = 0, $idsOffresToConserve, $key, $entityManager);
            // $out = $this->supprimerOffres($out, $offreMaj = 0, $offreCrees = 0, $info[1], $key);
        }
        

        unlink($destination);

        $this->saveInfoScript("MichaelPage", $out, $offreSupprimees = 0, $offreMaj, $offreCrees, $subject);

        $this->outStatscrawl("MichaelPage");
        }
        $this->logScriptEnd($logId, true, $offersProcessed, null); // Enregistre la fin avec succès
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $this->logScriptEnd($logId, false, $offersProcessed, $errorMessage); // Enregistre la fin avec erreur
        }
        
    }
    private function logScriptStart($scriptName)
    {
        $connection = $this->doctrine->getConnection();

        $queryCheck = "
            SELECT id
            FROM script_logs
            WHERE script_name = :script_name
            LIMIT 1
        ";
        $existingLog = $connection->fetchOne($queryCheck, [
            'script_name' => $scriptName,
        ]);

        if ($existingLog) {
            $queryUpdate = "
                UPDATE script_logs
                SET start_time = NOW(),
                    end_time = NULL,
                    status = FALSE,
                    offers_processed = 0,
                    error_message = NULL,
                    created_at = NOW()
                WHERE id = :id
            ";
            $connection->executeStatement($queryUpdate, [
                'id' => $existingLog,
            ]);

            return $existingLog;
        } else {
            $queryInsert = "
                INSERT INTO script_logs (script_name, start_time, status)
                VALUES (:script_name, NOW(), FALSE)
            ";
            $connection->executeStatement($queryInsert, [
                'script_name' => $scriptName,
            ]);

            return $connection->lastInsertId();
        }
    }

    private function logScriptEnd($logId, $status, $offersProcessed, $errorMessage = null)
    {
        $connection = $this->doctrine->getConnection();
        $query = "
            UPDATE script_logs
            SET end_time = NOW(),
                status = :status,
                offers_processed = :offers_processed,
                error_message = :error_message
            WHERE id = :id
        ";
        $connection->executeStatement($query, [
            'status' => $status,
            'offers_processed' => $offersProcessed,
            'error_message' => $errorMessage,
            'id' => $logId,
        ]);
    }
}