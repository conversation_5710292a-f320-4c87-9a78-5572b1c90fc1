<?php
namespace App\Script;

use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use XMLReader;
use SimpleXMLElement;

class REVELYO extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }
    private function logScriptStart($scriptName)
    {
        $connection = $this->doctrine->getConnection();

        $queryCheck = "
            SELECT id
            FROM script_logs
            WHERE script_name = :script_name
            LIMIT 1
        ";
        $existingLog = $connection->fetchOne($queryCheck, [
            'script_name' => $scriptName,
        ]);

        if ($existingLog) {
            $queryUpdate = "
                UPDATE script_logs
                SET start_time = NOW(),
                    end_time = NULL,
                    status = FALSE,
                    offers_processed = 0,
                    error_message = NULL,
                    created_at = NOW()
                WHERE id = :id
            ";
            $connection->executeStatement($queryUpdate, [
                'id' => $existingLog,
            ]);

            return $existingLog;
        } else {
            $queryInsert = "
                INSERT INTO script_logs (script_name, start_time, status)
                VALUES (:script_name, NOW(), FALSE)
            ";
            $connection->executeStatement($queryInsert, [
                'script_name' => $scriptName,
            ]);

            return $connection->lastInsertId();
        }
    }

    private function logScriptEnd($logId, $status, $offersProcessed, $errorMessage = null)
    {
        $connection = $this->doctrine->getConnection();
        $query = "
            UPDATE script_logs
            SET end_time = NOW(),
                status = :status,
                offers_processed = :offers_processed,
                error_message = :error_message
            WHERE id = :id
        ";
        $connection->executeStatement($query, [
            'status' => $status,
            'offers_processed' => $offersProcessed,
            'error_message' => $errorMessage,
            'id' => $logId,
        ]);
    }
    public function REVELYOScript()
    {

        $regions = [
            ["id_region" => 237, "libelle" => "Alsace"],
            ["id_region" => 238, "libelle" => "Aquitaine"],
            ["id_region" => 239, "libelle" => "Auvergne"],
            ["id_region" => 37, "libelle" => "Auvergne-Rhône-Alpes"],
            ["id_region" => 240, "libelle" => "Bourgogne"],
            ["id_region" => 31, "libelle" => "Bourgogne-Franche-Comté"],
            ["id_region" => 12, "libelle" => "Bretagne"],
            ["id_region" => 30, "libelle" => "Centre-Val-de-Loire"],
            ["id_region" => 241, "libelle" => "Champagne-Ardenne"],
            ["id_region" => 38, "libelle" => "Corse"],
            ["id_region" => 242, "libelle" => "Franche-Comté"],
            ["id_region" => 34, "libelle" => "Grand-Est"],
            ["id_region" => 200, "libelle" => "Guadeloupe"],
            ["id_region" => 198, "libelle" => "Guyane française"],
            ["id_region" => 33, "libelle" => "Hauts-de-France"],
            ["id_region" => 17, "libelle" => "Ile-de-France"],
            ["id_region" => 228, "libelle" => "La Réunion"],
            ["id_region" => 243, "libelle" => "Languedoc-Roussillon"],
            ["id_region" => 244, "libelle" => "Limousin"],
            ["id_region" => 245, "libelle" => "Lorraine"],
            ["id_region" => 227, "libelle" => "Martinique"],
            ["id_region" => 229, "libelle" => "Mayotte"],
            ["id_region" => 246, "libelle" => "Midi-Pyrénées"],
            ["id_region" => 247, "libelle" => "Nord-Pas-de-Calais"],
            ["id_region" => 32, "libelle" => "Normandie"],
            ["id_region" => 35, "libelle" => "Nouvelle-Aquitaine"],
            ["id_region" => 232, "libelle" => "Nouvelle-Calédonie"],
            ["id_region" => 36, "libelle" => "Occitanie"],
            ["id_region" => 23, "libelle" => "Pays-de-Loire"],
            ["id_region" => 248, "libelle" => "Poitou-Charentes"],
            ["id_region" => 236, "libelle" => "Polynésie française"],
            ["id_region" => 26, "libelle" => "Provence-Alpes-Côte d'Azur"],
            ["id_region" => 249, "libelle" => "Rhône-Alpes"]
        ];
        $logId = $this->logScriptStart('REVELYO'); // Démarre un log
        $offersProcessed = 0;
        $errorMessage = null;
        try {
        set_time_limit(0);
        error_reporting(E_ALL);

        // Variables
        $out = '';
        $OffreMaj = 0;
        $OffreCrees = 0;
        $OffreSupprimees = 0;
        $subject = '';
        $array_id_users = array();
        $datas = array();
        $limit_nombre_integration = 1000000;
        $nb_offres_importees = 0;
        $nb_offres_importees_flux = 0;

        // URL et informations utilisateur
        $urls = array(
            array(
                'url'     => 'https://master.nicoka.com/jobboards/directemploi.xml',
                'id_user' => '1258936',
                'name'    => 'REVELYO',
            )
        );

        // Correspondances de contrat et salaire
        $corr_type_contrat = array(
            'CDI' => 2,
            'CDD' => 1,
            'INTERIM' => 4,
            'STAGE' => 8,
            'ALTERANCE' => 6,
            'INDEPENDANT' => 3,
        );

        $corr_salary = array(
            '15' => 1,
            '<20k€' => 1,
            '20 à 30k€' => 2,
            '30 à 40k€' => 3,
            '40 à 50k€' => 4,
            '50 à 60k' => 5,
            '>60 k€' => 6,
            '>70 k€' => 7,
        );

        // Fonction pour supprimer les emojis
        function removeEmojis($text)
        {
            return preg_replace('/[\x{1F600}-\x{1FAFF}]/u', '', $text);
        }

        foreach ($urls as $array) {
            $source = $array['url'];
            $XMLReader = new XMLReader;
            $XMLReader->open($source);

            $UserUser = $this->doctrine->getRepository(UserUser::class)->findOneBy(
                ['id_user' => $array['id_user']]
            );

            $out .= "<br/><br/>START id_client : " . $array['id_user'] . " - Nom_client : " . $array['name'] . "<br/><br/>";

            if (!$UserUser) {
                echo 'Erreur : utilisateur non reconnu : ' . $array['id_user'];
                $out .= 'Erreur : utilisateur non reconnu : ' . $array['id_user'];
                $subject = 'Cron import ALPHYR Error User unknown';
                continue;
            }

            $idsOffresToConserve = array();

            // Passer à l'élément <job>
            while ($XMLReader->read() && $XMLReader->name !== "job");
            while ($XMLReader->name === "job") {
                $data = array();
                $nb_offres_importees++;
                $nb_offres_importees_flux++;
                if ($nb_offres_importees > $limit_nombre_integration) {
                    continue (2);
                }

                $item = new SimpleXMLElement($XMLReader->readOuterXML());
                if ((string)$item->id !== "1258936") {
                    $XMLReader->next("job");
                    continue;
                }
                // Mise à jour des champs pour correspondre à la nouvelle structure
                $data['reference'] = (string)$item->reference;
                $data['intitule'] = (string)$item->name;
                $data['rep_mail'] = (string)$item->email;
                $data['rep_url'] = (string)$item->link;
                $data['mission'] = removeEmojis((string)$item->description);
                $data['company'] = (string)$item->company;
                $data['id_type_contrat'] = (isset($item->jobtype) && strtoupper((string)$item->jobtype) === 'TEMPS PLEIN') ? 2 : null;

                // Traitement du salaire
                $salary = (string)$item->salary;
                $id_fourchette_remuneration = 0;
                foreach ($corr_salary as $value => $id) {
                    if ($salary > $value) {
                        $id_fourchette_remuneration = $id;
                    }
                }
                $data['id_fourchette_remuneration'] = $id_fourchette_remuneration;

                // Localisation : Ville et Région
                $ville = (string)$item->city;
                $regionname = (string)$item->region;
                $data['id_regions'] = $this->getRegionId($regionname, $regions);
                $data['geo_ville'] = $ville;

                print_r($data['id_regions'],$data['geo_ville']);
                /*
                $ville_find = $this->localisation($ville, $region, null, null, $out);
                if ($ville_find) {
                    $data['id_departements'] = $ville_find["id_departements"];
                    $data['postale_code'] = $ville_find["postale_code"];
                    $data['id_regions'] = $ville_find["id_regions"];
                    $data['geo_ville'] = $ville_find["geo_ville"];
                } else {
                    echo "Ville non trouvée : " . $ville;
                }
*/
                $datas[] = $data;
                $offersProcessed++;

                $XMLReader->next("job");

            }

            $array_id_users[] = $array['id_user'];
            $info = $this->saveInDatabase($datas, 1258936, $array['name']);
            $out .= $info[0];
            $idsOffresToConserve = $info[1];
            $entityManager = $this->doctrine->getManager();
            $out = $this->supprimerOffresV2($out, $offreMaj = 0, $offreCrees = 0, $idsOffresToConserve, $array['id_user'], $entityManager);
            $OffreSupprimees += $offreSupprimees = 0;
            $OffreMaj += $offreMaj;
            $OffreCrees += $offreCrees;

            $out .= "<br/> Nombre d'offres importées pour cet URL= " . $nb_offres_importees_flux;
            $nb_offres_importees_flux = 0;

            // Fin du traitement pour cet utilisateur
            $this->saveInfoScript("REVELYO", $out, $OffreSupprimees, $OffreMaj, $OffreCrees, $array_id_users, $subject);
        }$this->logScriptEnd($logId, true, $offersProcessed, null); // Enregistre la fin avec succès
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $this->logScriptEnd($logId, false, $offersProcessed, $errorMessage); // Enregistre la fin avec erreur
        }
    }
    function getRegionId($regionName, $regions)
    {
        // Normaliser le nom de la région en supprimant les espaces et mettant en minuscules
        $normalizedRegionName = strtolower(str_replace([' ', 'é'], ['-', 'e'], trim($regionName)));

        foreach ($regions as $region) {
            $normalizedLibelle = strtolower(str_replace([' ', 'é'], ['-', 'e'], trim($region['libelle'])));

            if ($normalizedRegionName === $normalizedLibelle) {
                return $region['id_region']; // Retourne l'ID si trouvé
            }
        }
        return null; // Retourne null si la région n'est pas trouvée
    }
}
