<?php 

namespace App\Script;

use App\Entity\GeoDepartement;
use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use Exception;
use Pharse;

class HistoireDor extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }

    public function HistoireDorScript()
{
    $logId = $this->logScriptStart('HistoireDor'); // Démarre un log
    $offersProcessed = 0;
    $errorMessage = null;
    try {

        set_time_limit(0);
    error_reporting(E_ALL);

    echo "Début du script HistoireDor...\n";
    echo "Initialisation des variables...\n";

    $OffreMaj = 0;
    $OffreCrees = 0;
    $offreSupprimees = 0;
    $datas = array();
    $subject = "";
    $t_initial = time();

    $corr_contrat = array(
        "CDI - Contrat a Durée Indéterminée" => 2,
        "CDI - Contrat a Durée Indéterminée " => 2,
        "CDD - Contrat à durée déterminée " => 1,
        "CDD - Contrat a Durée déterminée" => 1,
        "CDD - Contrat à durée déterminée" => 1,
        "CDD - Contrat à durée déterminée" => 1,
        "Alternance" => 6,
        "Stage" => 4,
        "Alternance " => 6,
        "Stage " => 4,
    );

    $urls = array(
        array(
            'url' => 'https://histoiredor.easycruit.com/',
            'id_user' => '1207361',
            'name' => 'HistoireDor',
        ),
    );

    $this->inStatscrawl('HistoireDor');
    $out = '';

    foreach ($urls as $array) {
        echo "Récupération des données depuis : " . $array['url'] . "\n";

        $datas = array();
        $page = 1;
        $hasMorePages = true;
        $maxPages = 1; // Nombre de pages maximum à scraper

        while ($hasMorePages && $page <= $maxPages) {
            $pageUrl = $array['url'];
            echo "Scraping de la page : " . $page . "\n";
            
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $pageUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTPHEADER => array(
                    "Cookie: 603ae3bca210bee6241540d7076e7253=davgh2chdk7k26i9bv8bkqs293"
                ),
                CURLOPT_SSL_VERIFYPEER => false,
            ));

            $response = curl_exec($curl);
            curl_close($curl);

            $html = Pharse::str_get_dom($response);
            $offerLinks = $html('div.joblist a');

            if (count($offerLinks) == 0) {
                echo "Aucune offre trouvée à la page " . $page . ". Fin de la récupération.\n";
                $hasMorePages = false;
                break;
            } else {
                echo "Nombre d'offres trouvées à la page " . $page . ": " . count($offerLinks) . "\n";
            }

            foreach ($offerLinks as $li_offre) {
                echo "Récupération des détails pour l'offre : " . (string)$li_offre->getAttribute('href') . "\n";

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://histoiredor.easycruit.com' . (string)$li_offre->getAttribute('href'),
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => "",
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => "GET",
                    CURLOPT_HTTPHEADER => array(
                        "Cookie: 603ae3bca210bee6241540d7076e7253=3pl3ot6e9g3ge9j3ho2d11aj76"
                    ),
                    CURLOPT_SSL_VERIFYPEER => false,
                ));

                $response = curl_exec($curl);
                curl_close($curl);

                $html_offre = Pharse::str_get_dom($response);

                $data['reference'] = null;

                if ($html_offre("div[class='jd-workhours']", 0)) {
                    $temps_travail = $html_offre("div[class='jd-workhours']", 0)->getPlainText();
                } else {
                    $temps_travail = "";
                }

                if ($html_offre("H1", 0)) {
                    $data['intitule'] = $html_offre("H1", 0)->getPlainText();
                } else {
                    echo "Erreur : intitulé non trouvé.\n";
                }

                if ($html_offre("div[class='jd-description']", 0)) {
                    $data['mission'] = $html_offre("div[class='jd-description']", 0)->getPlainText() . "</br>" . $temps_travail;
                } else {
                    echo "Erreur : mission non trouvée.\n";
                }
    
                if ($html_offre("div[class='jd-counties']", 0)) {
    
                    if (stripos($html_offre("div[class='jd-counties'] h3", 0)->getPlainText(), "réference") !== false) {
                        $data['reference'] = $html_offre("div[class='jd-counties'] p", 0)->getPlainText();
                    }
                    if (stripos($html_offre("div[class='jd-counties'] h3", 0)->getPlainText(), "Région") !== false) {
                        $lieu_reg = str_replace("Région", "", $html_offre("div[class='jd-counties'] ul > li", 0)->getPlainText());
                    }
                }
    
                if ($html_offre("div[class='jd-counties']", 1)) {
    
                    if (stripos($html_offre("div[class='jd-counties'] h3", 1)->getPlainText(), "réference") !== false) {
                        $data['reference'] = $html_offre("div[class='jd-counties'] p", 1)->getPlainText();
                    }
                    if (stripos($html_offre("div[class='jd-counties'] h3", 1)->getPlainText(), "Région") !== false) {
                        //echo $url_offre . "</br>";
                        $reg_url = $html_offre("div[class='jd-counties']", 1);
                        $lieu_reg = str_replace("Région", "", $reg_url("ul > li", 0)->getPlainText());
                    }
                }
    
                //Localisation
                if ($html_offre("div[class='jd-location']", 0)) {
                    $lieu = $html_offre("div[class='jd-location']", 0)->getPlainText();
                    $clean = ltrim(str_ireplace(array("Lieu de travail:", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", ")", "("), "", $lieu));
                    $ville_find = $this->localisation($clean, null, null, null, $out);
                    if ($ville_find) {
                        $data['id_departements'] = $ville_find["id_departements"];
                        $data['postale_code'] = $ville_find["postale_code"];
                        $data['id_regions'] = $ville_find["id_regions"];
                        $data['geo_ville'] = $ville_find["geo_ville"];
                    } else {
                        echo "ville non trouvee = " . $lieu;
                    }
                } else {
                    $data['geo_ville'] = "France";
                }
    
                //Localisation avec Région
                if ($data['geo_ville'] == "France") {
    
                    if ($lieu_reg) {
                        $ville_find = $this->localisation($lieu_reg, null, null, null, $out);
                        if ($ville_find) {
                            $data['id_departements'] = $ville_find["id_departements"];
                            $data['postale_code'] = $ville_find["postale_code"];
                            $data['id_regions'] = $ville_find["id_regions"];
                            $data['geo_ville'] = $ville_find["geo_ville"];
                        } else {
                            echo "ville non trouvee = " . $lieu;
                        }
                    }
                }
    
                $secteur = 'distribution';
                $secteur_array = $this->remplirTableScriptSecteursAll($secteur, $array['id_user'], $data['intitule']);
                $data['id_secteurs'] = $secteur_array[0];
                $data['id_sous_secteurs'] = $secteur_array[1];
    
                if (!$html_offre("div[class='jd-type']", 0)) {
                    echo "Erreur : type de contrat non trouvé.\n";
                } else {
                    $contrat = (string)$html_offre("div[class='jd-type']", 0)->getPlainText();
                    $contrat = ltrim(str_replace("Type d'emploi:", "", $contrat));
                    $data['id_type_contrat'] = $corr_contrat[$contrat];
                }
                // var_dump($html_offre("div[class='jd-type']", 0)->getPlainText());
                // echo $html_offre("div[class='jd-type']", 0)->getPlainText() . "  /  " . $url_offre . "</br>";
                // $data['id_type_contrat'] = $corr_contrat[ltrim(str_replace("Type d'emploi:", "", $html_offre("div[class='jd-type']", 0)->getPlainText()))];
                // echo "</br></br>";
                
                $tracking = "&source=directemploi";
    
                $data['rep_url'] = str_ireplace("/vacancy/", "/vacancy/application/send/", $array['url'] . (string)$li_offre->getAttribute('href')) . $tracking;
                $data['rep_mail'] = null;
                $data['searched_profile'] = null;
                $data['date_debut'] = null;
                $data['duree_contrat'] = null;
                $data['id_experience_requise'] = null;
                $data['id_fourchette_remuneration'] = null;
                $lieu_reg = null;
    
                if ($data['reference'] == null) {
                    $ref_md5 = md5($data['intitule']);
                    $data['reference'] = "HDO-" . $ref_md5[6] . $ref_md5[0] . $ref_md5[1] . $ref_md5[2] . $ref_md5[3];
                }
    
                $datas[] = $data;
                $offersProcessed++;

            }

            echo "Toutes les pages jusqu'à la page " . $maxPages . " ont été traitées.\n";

            $page++;  // Passer à la page suivante
        }
        
            echo "Toutes les pages ont été traitées.\n";

            // Sauvegarder les données en base de données
            $info = $this->saveInDatabase($datas, $array['id_user'], $array['name']);
            $out .= $info[0];
            $idsOffresToConserve = $info[1];
            $entityManager = $this->doctrine->getManager();
            $out = $this->supprimerOffresV2($out, $offreMaj = 0, $offreCrees = 0, $idsOffresToConserve, $array['id_user'], $entityManager);

            echo "Les données ont été sauvegardées et les offres obsolètes ont été supprimées.\n";
        }

        $t_final = time();
        echo "Temps total écoulé : " . $this->time_elapsed_A($t_final - $t_initial) . "\n";

        $this->saveInfoScript("HistoireDor", $out, $offreSupprimees = 0, $offreMaj, $offreCrees, $subject);
        $this->logScriptEnd($logId, true, $offersProcessed, null); // Enregistre la fin avec succès
    } catch (\Exception $e) {
        $errorMessage = $e->getMessage();
        $this->logScriptEnd($logId, false, $offersProcessed, $errorMessage); // Enregistre la fin avec erreur
    }
        $this->outStatscrawl('HistoireDor');

        echo "Fin du script HistoireDor.\n";
    }
    private function logScriptStart($scriptName)
    {
        $connection = $this->doctrine->getConnection();

        $queryCheck = "
            SELECT id
            FROM script_logs
            WHERE script_name = :script_name
            LIMIT 1
        ";
        $existingLog = $connection->fetchOne($queryCheck, [
            'script_name' => $scriptName,
        ]);

        if ($existingLog) {
            $queryUpdate = "
                UPDATE script_logs
                SET start_time = NOW(),
                    end_time = NULL,
                    status = FALSE,
                    offers_processed = 0,
                    error_message = NULL,
                    created_at = NOW()
                WHERE id = :id
            ";
            $connection->executeStatement($queryUpdate, [
                'id' => $existingLog,
            ]);

            return $existingLog;
        } else {
            $queryInsert = "
                INSERT INTO script_logs (script_name, start_time, status)
                VALUES (:script_name, NOW(), FALSE)
            ";
            $connection->executeStatement($queryInsert, [
                'script_name' => $scriptName,
            ]);

            return $connection->lastInsertId();
        }
    }

    private function logScriptEnd($logId, $status, $offersProcessed, $errorMessage = null)
    {
        $connection = $this->doctrine->getConnection();
        $query = "
            UPDATE script_logs
            SET end_time = NOW(),
                status = :status,
                offers_processed = :offers_processed,
                error_message = :error_message
            WHERE id = :id
        ";
        $connection->executeStatement($query, [
            'status' => $status,
            'offers_processed' => $offersProcessed,
            'error_message' => $errorMessage,
            'id' => $logId,
        ]);
    }
}
