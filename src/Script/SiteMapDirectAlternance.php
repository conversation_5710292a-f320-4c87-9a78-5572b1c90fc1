<?php

namespace App\Script;

use App\Entity\GeoVille;
use Doctrine\Persistence\ManagerRegistry;
use App\Service\ToolsService;
use App\Entity\GeoDepartement;
use App\Entity\OffOffre;
use App\Entity\EntrEntreprise;
use App\Entity\GeoVillePrincipale;
use App\Entity\MetierMetier;
use App\Entity\GeoListeRegion;
use App\Entity\Conseil;
use App\Entity\EditArticle;
use App\Repository\MetierMetierRepository;
use Doctrine\ORM\EntityManagerInterface;
use DOMDocument;


class SiteMapDirectAlternance extends ToolsService
{

    public int $limit_per_file = 15000;
    public array $uniqueUrls = [];

    public function __construct(ManagerRegistry $doctrine, private EntityManagerInterface $entityManager)
    {
        parent::__construct($doctrine);
        $this->entityManager = $entityManager;
    }

    public function endFile($old_dom, $old_xml_file_name, $index_file, $dom_index, $root_index) {
        $old_dom->save($old_xml_file_name);
        $dom = new DOMDocument();
        $dom->encoding = 'utf-8';
        $dom->xmlVersion = '1.0';
        $dom->formatOutput = true;
        $xml_file_name = './public/sitemap/sitempas_DirectAlternance/sitemap'.$index_file.'.xml';
        $root = $dom->createElement('urlset');
        $root->setAttribute("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9");

        $sitemapindex_node = $dom_index->createElement('sitemap');
        $sitemapindex_node->appendChild($dom_index->createElement('loc', "https://www.directalternance.com/sitemap/sitempas_DirectAlternance/sitemap".$index_file.".xml"));
        $sitemapindex_node->appendChild($dom_index->createElement('lastmod', date('Y-m-d')));
        $root_index->appendChild($sitemapindex_node);
        $dom_index->appendChild($root_index);

        return array($dom, $root, $xml_file_name, $dom_index, $root_index);
    }

    public function struct_home_search($string) {
        $string = iconv('UTF-8', 'ASCII//TRANSLIT', $string);
        $string = preg_replace('~[^\-\w]+~', '-', $string);
        $string = trim($string, '-');
        $string = strtolower($string);
        return $string;
    }





    public function strtourl(?string $str): string {
        if (is_null($str)) {
            return '';
        }

        // Translittération et nettoyage
        $str = iconv('UTF-8', 'ASCII//TRANSLIT', $str);
        $str = preg_replace('~[^\-\w]+~', '-', $str);

        // Supprimer les tirets en excès et convertir en minuscules
        $str = strtolower(trim($str, '-'));

        return $str;
    }


    public function creationSiteMapDirectAlternance()
    {

        set_time_limit(0);
        error_reporting(E_ALL);

        $count_url = 0;
        $count_file = 1;
        $this->limit_per_file = 15000; // Par exemple, limiter le nombre d'URLs à 50 000 pour rester sous 5 Mo, ajustez selon vos besoins.
        $max_file_size = 5 * 1024 * 1024; // 5 Mo en octets

        $array_prefix = array(
            "/Dis/lists-offre/alternance-",
        );


        //Suppression ancien SiteMap
        //unlink("./public/sitemap_index.xml");
        array_map('unlink', glob("./public/sitemap/sitempas_DirectAlternance/sitemap*"));


        //Articles
        //Conseils
        //Pages de recherches Optmimisés
        //Ville
        //Region
        //Metier
        //Departement
        //Toutes les offres
        //Page Thématiques
        //Page Statiques
        //Fiche Entreprise
        //Page recherche Alpha entreprises
        //Page D'accueil

        $dom = new DOMDocument();
        $dom->encoding = 'utf-8';
        $dom->xmlVersion = '1.0';
        $dom->formatOutput = true;
        $xml_file_name = './public/sitemap/sitempas_DirectAlternance/sitemap'.$count_file.'.xml';
        $root = $dom->createElement('urlset');
        $root->setAttribute("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9");


        $dom_index = new DOMDocument();
        $dom_index->encoding = 'utf-8';
        $dom_index->xmlVersion = '1.0';
        $dom_index->formatOutput = true;
        $xml_file_name_index = './public/sitemapDirectAlternance_index.xml';
        $root_index = $dom_index->createElement('sitemapindex');
        $root_index->setAttribute("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9");
        $sitemapindex_node = $dom_index->createElement('sitemap');
        $sitemapindex_node->appendChild($dom_index->createElement('loc', "https://www.directalternance.com/sitemap/sitempas_DirectAlternance/sitemap".$count_file.".xml"));
        $sitemapindex_node->appendChild($dom_index->createElement('lastmod', date('Y-m-d')));
        $root_index->appendChild($sitemapindex_node);
        $dom_index->appendChild($root_index);


        //Page Static
        $urls_static = array(
            "https://www.directalternance.com/AL/",
            "https://www.directalternance.com/AL/pageCadre",
            "https://www.directalternance.com/AL/entreprises",
            "https://www.directalternance.com/AL/actualites",

            //"https://www.directalternance.com/AL/article/list?mot_cle=Emploi" //504 Gateway on l'exclus pour le moment.

            "https://www.directalternance.com/AL/conseil/",
            "https://www.directalternance.com/AL/conseil/cv",
            "https://www.directalternance.com/AL/conseil/rechercheEmploi",
            "https://www.directalternance.com/AL/conseil/lettreMotivation",
            "https://www.directalternance.com/AL/conseil/entretienEmbauche",
            "https://www.directalternance.com/AL/conseil/listeDomaines",
            "https://www.directalternance.com/AL/conseil/listeMetiers",

            "https://www.directalternance.com/AL/quisommenous",
            "https://www.directalternance.com/AL/informationlegal",
            "https://www.directalternance.com/AL/confidentialite",
            "https://www.directalternance.com/AL/chartecookies",
            "https://www.directalternance.com/AL/plansite",
            "https://www.directalternance.com/AL/contact",
            "https://www.directalternance.com/AL/societe",
            "https://www.directalternance.com/AL/index-mot-cle/"

        );

        foreach($urls_static as $url_static){
            if($count_url == $this->limit_per_file) {
                $count_url = 0;
                $count_file++;
                $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                $dom = $new_file_function[0];
                $root = $new_file_function[1];
                $xml_file_name = $new_file_function[2];
                $dom_index = $new_file_function[3];
                $root_index = $new_file_function[4];
            }

            $escapedUrl = htmlspecialchars($url_static, ENT_XML1, 'UTF-8');

            if (!in_array($escapedUrl, $this->uniqueUrls)) {
                $this->uniqueUrls[] = $escapedUrl;

                $url_node = $dom->createElement('url');
                $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                $url_node->appendChild($dom->createElement('changefreq', 'weekly'));
                $root->appendChild($url_node);
                $dom->appendChild($root);
                $count_url++;
            }
        }
        $array_secteurs = array(
            array('1-20', 'Achat / Logistique / Transport'),
            array('30-15', 'Administratif / Secrétariat / Public'),
            array('29', 'Agriculture / Agronomie / Alimentaire'),
            array('38', 'Aide à la personne / Social'),
            array('3', 'Banque / Assurance / Mutuelle'),
            array('4-12', 'BTP / Environnement / Génie civil'),
            array('5', 'Chimie / Matériaux / Plasturgie'),
            array('16-47-48', 'Commerces / Grande distribution / Retail'),
            array('6-13', 'Commercial / Vente / Export'),
            array('8', 'Dirigeant / Postes de direction'),
            array('11-27', "Electronique / Electricité / Télécom"),
            array('14-32', "Finance / Audit / Comptabilité"),
            array('17', 'Hôtellerie / Restauration'),
            array('46-18', 'Immobilier / Urbanisme / Architecture'),
            array('2-39-24-23', 'Industrie / Qualité / Sécurité / Auto'),
            array('19-10', "Informatique / Multimédia / Internet"),
            array('21-7-45-44', 'Marketing / Communication / Médias'),
            array('22', 'Production / Mécanique / Maintenance'),
            array('25-9', "RH / Formation / Juridique / Fiscalité"),
            array('26', "Santé / Paramédical / Pharmacie"),
            array('28', 'Tourisme / Loisirs / Sport / Culture'),
        );
        foreach($array_secteurs as $secteur){
            foreach ($array_prefix as $prefix) {
                if($count_url == $this->limit_per_file) {
                    $count_url = 0;
                    $count_file++;
                    $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                    $dom = $new_file_function[0];
                    $root = $new_file_function[1];
                    $xml_file_name = $new_file_function[2];
                    $dom_index = $new_file_function[3];
                    $root_index = $new_file_function[4];
                }

                $url = "https://www.directalternance.com" . $prefix . "france-metiers-" . $secteur[0] . "-" . self::struct_home_search($secteur[1]) ;
                $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');

                if (!in_array($escapedUrl, $this->uniqueUrls)) {
                    $this->uniqueUrls[] = $escapedUrl;

                    $url_node = $dom->createElement('url');
                    $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                    $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                    $url_node->appendChild($dom->createElement('changefreq', 'daily'));
                    $root->appendChild($url_node);
                    $dom->appendChild($root);
                    $count_url++;
                }
            }
        }


        // Liste recherche Metier
        $listeMetiers = $this->doctrine->getRepository(MetierMetier::class)->getMetierAccueilSitemaps();
        $villes = [
            "france", "paris", "marseille", "lyon", "toulouse", "nice", "nantes"
        ];

        foreach ($listeMetiers as $metier) {
            foreach ($array_prefix as $prefix) {
                foreach ($villes as $ville) {
                    if ($count_url == $this->limit_per_file) {
                        $count_url = 0;
                        $count_file++;
                        $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                        $dom = $new_file_function[0];
                        $root = $new_file_function[1];
                        $xml_file_name = $new_file_function[2];
                        $dom_index = $new_file_function[3];
                        $root_index = $new_file_function[4];
                    }

                    $url = "https://www.directalternance.com" . $prefix . $ville . "-metier-" . self::struct_home_search($metier['libelle_metier']) . "-secteurs";
                    $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');

                    if (!in_array($escapedUrl, $this->uniqueUrls)) {
                        $this->uniqueUrls[] = $escapedUrl;

                        $url_node = $dom->createElement('url');
                        $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                        $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                        $url_node->appendChild($dom->createElement('changefreq', 'daily'));
                        $root->appendChild($url_node);
                        $dom->appendChild($root);
                        $count_url++;
                    }
                }
            }
        }



        // liste recherche metier except accueil
        $listeMetiersExcludeAccueil = $this->doctrine->getRepository(MetierMetier::class)->excludeMetierAccueil();
        foreach($listeMetiersExcludeAccueil as $metier) {
            if($count_url == $this->limit_per_file) {
                $count_url = 0;
                $count_file++;
                $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                $dom = $new_file_function[0];
                $root = $new_file_function[1];
                $xml_file_name = $new_file_function[2];
                $dom_index = $new_file_function[3];
                $root_index = $new_file_function[4];
            }

            $url = "https://www.directalternance.com/AL/lists-offre/?contrat=&q=" . self::struct_home_search($metier->getLibelleMetier()) . "&localisation=&localisation_field=";

            $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');

            if (!in_array($escapedUrl, $this->uniqueUrls)) {
                $this->uniqueUrls[] = $escapedUrl;

                $url_node = $dom->createElement('url');
                $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                $url_node->appendChild($dom->createElement('changefreq', 'daily'));
                $root->appendChild($url_node);
                $dom->appendChild($root);
                $count_url++;
            }
        }




        //Liste recherche Departement
        $listeDepartements = $this->doctrine->getRepository(GeoDepartement::class)->findBy(
            [],
            ['libelle' => 'ASC']
        );

        foreach($listeDepartements as $departement) {
            foreach($array_prefix as $prefix) {
                if($count_url == $this->limit_per_file) {
                    $count_url = 0;
                    $count_file++;
                    $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                    $dom = $new_file_function[0];
                    $root = $new_file_function[1];
                    $xml_file_name = $new_file_function[2];
                    $dom_index = $new_file_function[3];
                    $root_index = $new_file_function[4];
                }

                $url = "https://www.directalternance.com" . $prefix . self::struct_home_search($departement->getLibelle()) . "-metiers-secteurs";
                $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');
                if (!in_array($escapedUrl, $this->uniqueUrls)) {
                    $this->uniqueUrls[] = $escapedUrl;

                    $url_node = $dom->createElement('url');
                    $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                    $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                    $url_node->appendChild($dom->createElement('changefreq', 'daily'));
                    $root->appendChild($url_node);
                    $dom->appendChild($root);
                    $count_url++;
                }
            }
        }
        //Liste recherche region
        $listeRegions = $this->doctrine->getRepository(GeoListeRegion::class)->getOffresParRegionAll();

        foreach($listeRegions as $region) {
            foreach($array_prefix as $prefix) {
                if($count_url == $this->limit_per_file) {
                    $count_url = 0;
                    $count_file++;
                    $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                    $dom = $new_file_function[0];
                    $root = $new_file_function[1];
                    $xml_file_name = $new_file_function[2];
                    $dom_index = $new_file_function[3];
                    $root_index = $new_file_function[4];
                }

                $urlPart = $region[1] != null ? self::struct_home_search($listeRegions[$region[1]][0]) : self::struct_home_search($region[0]);
                $url = "https://www.directalternance.com" . $prefix . $urlPart . "-metiers-secteurs";
                $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');

                if (!in_array($escapedUrl, $this->uniqueUrls)) {
                    $this->uniqueUrls[] = $escapedUrl;

                    $url_node = $dom->createElement('url');
                    $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                    $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                    $url_node->appendChild($dom->createElement('changefreq', 'daily'));
                    $root->appendChild($url_node);
                    $dom->appendChild($root);
                    $count_url++;
                }
            }
        }


        //Liste recherche ville
        $listeVilles = $this->doctrine->getRepository(GeoVillePrincipale::class)->getCodePostal();

        //Complete ville (Hamza)
        $allVilleOffresPerWeek1 = $this->entityManager->getRepository(OffOffre::class)->getVilleOffresPerWeek();
        // Extraire les noms des villes des deux listes
        $listeVillesNoms = array_column($listeVilles, 'libelle'); // Assurez-vous que 'libelle' est le bon champ
        $allVilleOffresNoms = array_column($allVilleOffresPerWeek1, 'geo_ville');
        // Filtrer les villes de la deuxième liste qui n'existent pas dans la première liste
        $filteredVilleOffres = array_filter($allVilleOffresPerWeek1, function($offre) use ($listeVillesNoms) {
            return !in_array($offre['geo_ville'], $listeVillesNoms);
        });
        $villes = array_unique(array_column($filteredVilleOffres, 'geo_ville'));
        foreach ($villes as $ville) {
            $postalCode = $this->entityManager->getRepository(GeoVille::class)->FindPostalCodeVille($ville);

            // Vérifier que le code postal est trouvé et qu'il est un tableau
            if (is_array($postalCode) && isset($postalCode['code_postal'])) {
                $villesWithPostalCodes[] = [
                    'ville' => $ville,
                    'code_postal' => $postalCode['code_postal']
                ];
            }
        }

        foreach($listeVilles as $ville) {
            foreach($array_prefix as $prefix) {
                if($count_url == $this->limit_per_file) {
                    $count_url = 0;
                    $count_file++;
                    $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                    $dom = $new_file_function[0];
                    $root = $new_file_function[1];
                    $xml_file_name = $new_file_function[2];
                    $dom_index = $new_file_function[3];
                    $root_index = $new_file_function[4];
                }

                $url = "https://www.directalternance.com/AL/lists-offre/" . self::struct_home_search($ville->getLibelle()) . "-(" . $ville->getCodePostal() . ")-metiers-secteurs";
                $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');

                if (!in_array($escapedUrl, $this->uniqueUrls)) {
                    $this->uniqueUrls[] = $escapedUrl;

                    $url_node = $dom->createElement('url');
                    $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                    $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                    $url_node->appendChild($dom->createElement('changefreq', 'daily'));
                    $root->appendChild($url_node);
                    $dom->appendChild($root);
                    $count_url++;
                }
            }
        }
        foreach($villesWithPostalCodes as $ville) {
            foreach($array_prefix as $prefix) {
                if($count_url == $this->limit_per_file) {
                    $count_url = 0;
                    $count_file++;
                    $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                    $dom = $new_file_function[0];
                    $root = $new_file_function[1];
                    $xml_file_name = $new_file_function[2];
                    $dom_index = $new_file_function[3];
                    $root_index = $new_file_function[4];
                }

                $url = "https://www.directalternance.com/AL/lists-offre/" . self::struct_home_search($ville['ville']) . "-(" . $ville['code_postal'] . ")-metiers-secteurs";
                $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');

                if (!in_array($escapedUrl, $this->uniqueUrls)) {
                    $this->uniqueUrls[] = $escapedUrl;

                    $url_node = $dom->createElement('url');
                    $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                    $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                    $url_node->appendChild($dom->createElement('changefreq', 'daily'));
                    $root->appendChild($url_node);
                    $dom->appendChild($root);
                    $count_url++;
                }
            }
        }


        //Pages détail des offres
        $today = new \DateTime();
        $offers_on_line = $this->doctrine->getRepository(OffOffre::class)->findBy([
            'statut' => '1',
            'date_soumission' => $today
        ]);



        if($offers_on_line) {
            foreach($offers_on_line as $offer_on_line) {
                if($count_url == $this->limit_per_file) {
                    $count_url = 0;
                    $count_file++;
                    $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                    $dom = $new_file_function[0];
                    $root = $new_file_function[1];
                    $xml_file_name = $new_file_function[2];
                    $dom_index = $new_file_function[3];
                    $root_index = $new_file_function[4];
                }
                $date = $offer_on_line->getDateSoumission();
                $url = "https://www.directalternance.com/AL/candidatOffre/" . (string)$offer_on_line->getIdOffre();
                $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');
                if (!in_array($escapedUrl, $this->uniqueUrls)) {
                    $this->uniqueUrls[] = $escapedUrl;

                    $url_node = $dom->createElement('url');
                    $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                    $url_node->appendChild($dom->createElement('lastmod', $date->format('Y-m-d')));
                    $url_node->appendChild($dom->createElement('changefreq', 'daily'));
                    $root->appendChild($url_node);
                    $dom->appendChild($root);
                    $count_url++;
                }
            }


        }


        //Pages recherche Alpha entreprise
        $alphabetical_character = range("A","Z");
        foreach($alphabetical_character as $character){
            if($count_url == $this->limit_per_file) {
                $count_url = 0;
                $count_file++;
                $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                $dom = $new_file_function[0];
                $root = $new_file_function[1];
                $xml_file_name = $new_file_function[2];
                $dom_index = $new_file_function[3];
                $root_index = $new_file_function[4];
            }

            $url = "https://www.directalternance.com/AL/entreprises?lettre=" . $character;
            $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');

            if (!in_array($escapedUrl, $this->uniqueUrls)) {
                $this->uniqueUrls[] = $escapedUrl;

                $url_node = $dom->createElement('url');
                $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                $url_node->appendChild($dom->createElement('changefreq', 'weekly'));
                $root->appendChild($url_node);
                $dom->appendChild($root);
                $count_url++;
            }
        }

        //Pages détails entreprise
        $entreprises = $this->doctrine->getRepository(EntrEntreprise::class)->entrepriseListeEntreprises('');

        if($entreprises) {
            foreach($entreprises as $entreprise) {
                if($count_url == $this->limit_per_file) {
                    $count_url = 0;
                    $count_file++;
                    $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                    $dom = $new_file_function[0];
                    $root = $new_file_function[1];
                    $xml_file_name = $new_file_function[2];
                    $dom_index = $new_file_function[3];
                    $root_index = $new_file_function[4];
                }
                $raison_sociale = iconv(mb_detect_encoding($entreprise['raison_sociale'], mb_detect_order(), true), "UTF-8", $entreprise['raison_sociale']);
                // Nettoyage supplémentaire si nécessaire
                $raison_sociale = preg_replace('/[^A-Za-z0-9\-]/', '', $raison_sociale);
                $url = "https://www.directalternance.com/AL/entreprise/" . $entreprise['id_entreprise'] . "/" . str_replace(array(' ', '/', '-'), '', $raison_sociale);
                $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');

                if (!in_array($escapedUrl, $this->uniqueUrls)) {
                    $this->uniqueUrls[] = $escapedUrl;

                    $url_node = $dom->createElement('url');
                    $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                    $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                    $url_node->appendChild($dom->createElement('changefreq', 'weekly'));
                    $root->appendChild($url_node);
                    $dom->appendChild($root);
                    $count_url++;
                }
            }
        }

        //Pages articles Conseils
        $conseils = $this->doctrine->getRepository(Conseil::class)->findAll();

        if($conseils) {
            foreach($conseils as $conseil){
                if($count_url == $this->limit_per_file) {
                    $count_url = 0;
                    $count_file++;
                    $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                    $dom = $new_file_function[0];
                    $root = $new_file_function[1];
                    $xml_file_name = $new_file_function[2];
                    $dom_index = $new_file_function[3];
                    $root_index = $new_file_function[4];
                }

                $url = "https://www.directalternance.com/AL/conseil/page/" .$conseil->getId();
                $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');
                if (!in_array($escapedUrl, $this->uniqueUrls)) {
                    $this->uniqueUrls[] = $escapedUrl;

                    $url_node = $dom->createElement('url');
                    $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                    $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                    $url_node->appendChild($dom->createElement('changefreq', 'monthly'));
                    $root->appendChild($url_node);
                    $dom->appendChild($root);
                    $count_url++;
                }
            }
        }
        //Extraction des offres de la semaine pour lien sitemaps


        //Pages articles actus
        $all_articles = $this->doctrine->getRepository(EditArticle::class)->findAll();

        if($all_articles) {
            foreach($all_articles as $article){
                if($count_url == $this->limit_per_file) {
                    $count_url = 0;
                    $count_file++;
                    $new_file_function = self::endFile($dom, $xml_file_name, $count_file, $dom_index, $root_index);
                    $dom = $new_file_function[0];
                    $root = $new_file_function[1];
                    $xml_file_name = $new_file_function[2];
                    $dom_index = $new_file_function[3];
                    $root_index = $new_file_function[4];
                }

                $url = "https://www.directalternance.com/AL/article/" . $article->getIdArticle() . "/" . self::strtourl($article->getTitre());
                $escapedUrl = htmlspecialchars($url, ENT_XML1, 'UTF-8');

                if (!in_array($escapedUrl, $this->uniqueUrls)) {
                    $this->uniqueUrls[] = $escapedUrl;

                    $url_node = $dom->createElement('url');
                    $url_node->appendChild($dom->createElement('loc', $escapedUrl));
                    $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                    $url_node->appendChild($dom->createElement('changefreq', 'monthly'));
                    $root->appendChild($url_node);
                    $dom->appendChild($root);
                    $count_url++;
                }
            }
        }

/*
        $AlloffresperWeek = $this->doctrine->getRepository(OffOffre::class)->getOffresPerWeek();

        foreach ($AlloffresperWeek as $offre) {
            // Nettoyage et encodage des caractères spéciaux dans l'intitulé et la ville
            $intitule = $this->clean_string($offre['intitule']);
            $geo_ville = $this->clean_string($offre['geo_ville']);

            // Construction de l'URL
            $url = "https://www.directalternance.com/AL/lists-offre/?contrat=" . $offre['id_type_contrat']
                . "&q=" . $intitule
                . "&localisation=" . $geo_ville
                . "&localisation_field=";

            if (!in_array($url, $this->uniqueUrls)) {
                $this->uniqueUrls[] = $url;

                // Création des éléments XML pour l'URL
                $url_node = $dom->createElement('url');
                $loc_node = $dom->createElement('loc');
                $loc_node->appendChild($dom->createCDATASection($url)); // Utilisation de CDATA pour l'URL
                $url_node->appendChild($loc_node);

                $url_node->appendChild($dom->createElement('lastmod', date('Y-m-d')));
                $url_node->appendChild($dom->createElement('changefreq', 'daily'));
                $root->appendChild($url_node);
                $dom->appendChild($root);

                $count_url++;

                // Vérification de la taille du fichier et création d'un nouveau fichier si nécessaire
                if ($count_url == $this->limit_per_file || (file_exists($xml_file_name) && filesize($xml_file_name) > $max_file_size)) {
                    list($dom, $root, $xml_file_name, $dom_index, $root_index) = $this->endFile2($dom, $xml_file_name, ++$count_file, $dom_index, $root_index);
                    $count_url = 0;
                }

            }
        }
*/
        $dom->save($xml_file_name);
        $dom_index->save($xml_file_name_index);
    }





}
