<?php 

namespace App\Script;

use App\Entity\GeoDepartement;
use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use XMLReader;
use SimpleXMLElement;
use Exception;
use Pharse;

class Leclerc extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }
    private function logScriptStart($scriptName)
    {
        $connection = $this->doctrine->getConnection();

        $queryCheck = "
            SELECT id
            FROM script_logs
            WHERE script_name = :script_name
            LIMIT 1
        ";
        $existingLog = $connection->fetchOne($queryCheck, [
            'script_name' => $scriptName,
        ]);

        if ($existingLog) {
            $queryUpdate = "
                UPDATE script_logs
                SET start_time = NOW(),
                    end_time = NULL,
                    status = FALSE,
                    offers_processed = 0,
                    error_message = NULL,
                    created_at = NOW()
                WHERE id = :id
            ";
            $connection->executeStatement($queryUpdate, [
                'id' => $existingLog,
            ]);

            return $existingLog;
        } else {
            $queryInsert = "
                INSERT INTO script_logs (script_name, start_time, status)
                VALUES (:script_name, NOW(), FALSE)
            ";
            $connection->executeStatement($queryInsert, [
                'script_name' => $scriptName,
            ]);

            return $connection->lastInsertId();
        }
    }

    private function logScriptEnd($logId, $status, $offersProcessed, $errorMessage = null)
    {
        $connection = $this->doctrine->getConnection();
        $query = "
            UPDATE script_logs
            SET end_time = NOW(),
                status = :status,
                offers_processed = :offers_processed,
                error_message = :error_message
            WHERE id = :id
        ";
        $connection->executeStatement($query, [
            'status' => $status,
            'offers_processed' => $offersProcessed,
            'error_message' => $errorMessage,
            'id' => $logId,
        ]);
    }



    public function LeclercScript()
    {
        $logId = $this->logScriptStart('Leclerc'); // Démarre un log
        $offersProcessed = 0;
        $errorMessage = null;
        try {
        set_time_limit(0);
        error_reporting(E_ALL);

        echo "Début du script Leclerc...\n";
        echo "Initialisation des variables...\n";

        $OffreMaj=0;
        $OffreCrees=0;
        $offreSupprimees=0;
        $datas = array();
        $subject = "";
        $t_initial = time();
        $limit_nombre_integration = 500000;
        $nb_offres_importes = 0;
        $list_secteur = array();
        $nb_offres = 0;
        $tab = array();

        $urls = array(
            array(
                'url' => 'https://www.recrutement.leclerc/les-offres/',
                'id_user' => '1254913',
                'name' => 'Leclerc',
            ),
        );

        $this->inStatscrawl('Leclerc');

        $out = '';
        $subject = '';
        $error_code = '';

        foreach ($urls as $array) {
            echo "Récupération des données depuis : " . $array['url'] . "\n";

            $page = 1;
            $hasMorePages = true;

            while ($hasMorePages) {
                $url = $array['url'] . '?pagination=' . $page; // Ajouter le numéro de page à l'URL
                echo "Récupération des données depuis : " . $url . "\n";

                $datas = array();

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => "",
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => "GET",
                    CURLOPT_HTTPHEADER => array(
                        "Cookie: 603ae3bca210bee6241540d7076e7253=davgh2chdk7k26i9bv8bkqs293"
                    ),
                    CURLOPT_SSL_VERIFYPEER => false,
                ));

                $response = curl_exec($curl);
                curl_close($curl);

                $html = Pharse::str_get_dom($response);

                $offre = $html('a[class="card-offre__link jobLink"]');

                if (count($offre) === 0) {
                    $hasMorePages = false;
                    continue; // Arrêtez la boucle si aucune offre n'est trouvée
                }

                foreach($offre as $li_offre){
                    $url = $li_offre->getAttribute('href');
                    $complete_url = "https://www.recrutement.leclerc" . $url;
                    echo "Récupération de l'offre : " . $li_offre->getPlainText() . "\n";

                    $data = array();
                   

                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                        CURLOPT_URL => $complete_url,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "POST",
                        CURLOPT_HTTPHEADER => array(
                            "Cookie: 603ae3bca210bee6241540d7076e7253=3pl3ot6e9g3ge9j3ho2d11aj76"
                        ),
                        CURLOPT_SSL_VERIFYPEER => false,
                    ));

                    $response = curl_exec($curl);
                    curl_close($curl);

                    $html = Pharse::str_get_dom($response);

                    $element = $html('h1[class="jobContent-header__title"]', 0);
                    if ($element) {
                        $data['intitule'] = $element->getPlainText();
                    } else {
                        $data['intitule'] = "Pas de titre";
                    }


                    // Gestion du type de contrat
                    $contract = $html('span[class="jobContent-header__contract"]');

                    if (!empty($contract)) {
                        $contrat = $contract[0]->getPlainText();
                    } else {
                        $contrat = 'CDI';
                    }

                    if (preg_match('/CDD/', $contrat)) {
                        $data['id_type_contrat'] = 1;
                    } elseif (preg_match('/CDI/', $contrat)) {
                        $data['id_type_contrat'] = 2;
                    } else {
                        $data['id_type_contrat'] = 4;
                    }


                    $data['reference'] = 'LECLERC-'.substr(md5($data['intitule']),0,5);

                    $url = (string)$li_offre->getAttribute('href');

                    // /detail-offre/scrum-master-cdi-h-f-2460372/"
                    $url = substr($url, 0, -1);                    

                    $data['rep_url'] = 'https://www.recrutement.leclerc' . $url . '?s_o=DirectEmploi';

                    $mission = $html('div.jobContent-block', 0);

                    if ($mission) {
                        $paragraphs = $mission('p');
                        $full_text = "";
                    
                        // Boucle sur les paragraphes pour récupérer le texte
                        foreach ($paragraphs as $p) {
                            $full_text .= $p->getPlainText() . "\n";  // Concatène le texte de chaque <p>
                        }
                    }

                    $data['mission'] = $full_text;

                    $profil = $html('div.jobContent-block', 1);

                    if ($profil) {
                        $paragraphs = $profil('p');
                        $full_text = "";
                    
                        // Boucle sur les paragraphes pour récupérer le texte
                        foreach ($paragraphs as $p) {
                            $full_text .= $p->getPlainText() . "\n";  // Concatène le texte de chaque <p>
                        }
                    }

                    $data['searched_profile'] = $full_text;

                    // // Localisation
                    // $lieu = $html('p[class="paragraph job-ad-location-block__address"]', 0)->getPlainText();
                    // $parts = explode(',', $lieu);
                    // $ville = trim($parts[count($parts) - 2]);
                    // // on supprime le code postal
                    // $ville = preg_replace('/[0-9]+/', '', $ville);
                    // $ville = trim($ville);
                    // $ville = str_replace(' ', '-', $ville);

                    $lieu = $html('h2[class="jobContent-header__city"]', 0)->getPlainText();
                    // on enleve les parenthèses et ce qu'il y a dedans
                    $lieu = preg_replace('/\([^)]+\)/', '', $lieu);
                    $lieu = trim($lieu);

                    $data['geo_ville'] = $lieu;

                    $ville_find = $this->localisation($lieu, null, null, null, $out);
                    if($ville_find){
                        $data['id_departements'] = $ville_find["id_departements"];
                        $data['postale_code'] = $ville_find["postale_code"];
                        $data['id_regions'] = $ville_find["id_regions"];
                        $data['geo_ville'] = $ville_find["geo_ville"]; 
                    } else {
                        echo "ville non trouvé = ".$lieu;
                    }

                    // Secteur
                    $data['id_secteurs'] = 16;

                    // INFORMATIONS NON FOURNIES
                    $data['date_debut'] = null;
                    $data['duree_contrat'] = null;
                    $data['id_experience_requise'] = null;
                    $data['id_fourchette_remuneration'] = null;
                    $offersProcessed++;


                    $datas[] = $data;
                }

                echo "Nombre d'offres récupérées : " . count($datas) . "\n";

                $info =  $this->saveInDatabase($datas, $array['id_user'], $array['name']);
                $out.=$info[0];
                $out=  $this->supprimerOffres($out, $offreMaj = 0, $offreCrees = 0, $info[1], $array['id_user']);

                $page++; // Passer à la page suivante
            }

            // $info =  $this->saveInDatabase($datas, $array['id_user'], $array['name']);
            // $out.=$info[0];
            // $idsOffresToConserve = $info[1];
            // $entityManager = $this->doctrine->getManager();
            // $out = $this->supprimerOffresV2($out, $offreMaj = 0, $offreCrees = 0, $idsOffresToConserve, $array['id_user'], $entityManager);
            // $out=  $this->supprimerOffres($out, $offreMaj = 0, $offreCrees = 0, $info[1], $array['id_user']);

            //$t_final = time();
            //$out .= "<br>temps total = " .  $this->time_elapsed_A($t_final - $t_initial);

            $this->saveInfoScript("Leclerc", $out, $offreSupprimees = 0, $offreMaj, $offreCrees, $subject);
        }$this->logScriptEnd($logId, true, $offersProcessed, null); // Enregistre la fin avec succès
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $this->logScriptEnd($logId, false, $offersProcessed, $errorMessage); // Enregistre la fin avec erreur
        }

        $this->outStatscrawl('Leclerc');
    }

}
