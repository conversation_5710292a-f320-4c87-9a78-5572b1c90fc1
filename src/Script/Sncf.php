<?php

namespace App\Script;

use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use XMLReader;
use SimpleXMLElement;
use Exception;

class Sncf extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }

    public function SncfScript()
    {
        set_time_limit(0);
        error_reporting(E_ALL);
        ini_set('user_agent', 'My-Application/2.5');

        $t_initial = time();
        $out = "";
        $subject = "";

        $urls = array(

                'url' => "https://flux.twinin.fr/sncf-recrute-cadres.php",
                'id_user' => '1207397 ', //ID CLIENT
                // 'id_user' => '1205950', //TEST
                'name' => 'Sncf',
        );

        $this->inStatscrawl('Sncf');

        $corr_type_contrat = array(
            'Alternance' => 6,
            'Autre' => 2,
            'CDD' => 1,
            'CDI' => 2,
            'cdi' => 2,
            'cdd' => 1,
            'IntÃ©rim' => 4,
            'interim' => 4,
            'Interim' => 4,
            'Vacation' => 4,
            'Contrat Professionnalisation' => 6,
            'Contrat profesionnalisation' => 6,
            "Contrat d'apprentissage" => 6,
            'Stage' => 8,
            'temporaryWork' => 4, 
            'permanentContract' => 2,
            'Travail temporaire' => 4
        );

        $corr_exp = array(
            'Moins de 1 an' => array(1),
            '1-2 ans' => array(1),
            '2-3 ans' => array(1),
            '1-5 ans' => array(1),
            '5-7 ans' => array(3),
            '3-4 ans' => array(2),
            '6 mois - 1 an' => array(1),
            '< 6 mois' => array(1),
            '4-5 ans' => array(2),
            '7-10 ans' => array(3),
            '5-10 ans' => array(3),
            'Plus de 10 ans' => array(3),
            '' => array(null),
        );

            $datas = array();    

            $XMLReader = new XMLReader;
            $XMLReader->open($urls['url']);

            $UserUser = $this->doctrine->getRepository(UserUser::class)->findOneBy(
            ['id_user' => $urls['id_user']]
            );

            if (!$UserUser) {
                echo 'Erreur : utilisateur non reconnu :' . $urls['id_user'];
                $subject = 'Cron import Alterego Error User unknown';
            }

            $out .= "\r\n<br/><br/>START id_client : " . $urls['id_user'] . " - Nom_client : " . $urls['name'] . " - Flux : " . $urls['url'] . "<br/><br/>";


            while ($XMLReader->read() && $XMLReader->name !== "offer") ;
            while ($XMLReader->name === "offer") {

                $data = array();
                $item = new SimpleXMLElement($XMLReader->readOuterXML());

                $data['intitule'] = (string)$item->jobDescription->title;
                

                $data['reference'] = (string)$item->reference;
                
                $data['id_type_contrat'] = $corr_type_contrat[(string)$item->jobDescription->contract];
                $data['mission'] = (string)$item->jobDescription->description;
                $data['searched_profile'] = (string)$item->jobDescription->customFields->LongText3Formatted;  
                $data['rep_url'] = (string)$item->directUrl;
                $data['rep_mail'] = NULL;

                $data['id_experience_requise'] = $corr_exp[(string)$item->applicantCriteria->experienceLevel];


                $lieu = (string)$item->jobDescription->location->jobLocation;
                $ville_find = $this->localisation($lieu, $out, null, null, null);

                if($ville_find){
                    $data['id_departements'] = $ville_find["id_departements"];
                    $data['postale_code'] = $ville_find["postale_code"];
                    $data['id_regions'] = $ville_find["id_regions"];
                    $data['geo_ville'] = $ville_find["geo_ville"]; 
                } else {
                    echo "ville non trouvee = ".$lieu;
                }

                if($data['geo_ville'] == "France"){
                    $lieu = (string)$item->jobDescription->location->departements->departement;
                    $ville_find = $this->localisation($lieu, $out, null, null, null);

                    if($ville_find){
                        $data['id_departements'] = $ville_find["id_departements"];
                        $data['postale_code'] = $ville_find["postale_code"];
                        $data['id_regions'] = $ville_find["id_regions"];
                        $data['geo_ville'] = $ville_find["geo_ville"]; 
                    } else {
                        echo "ville non trouvee = ".$lieu;
                    }
                }
            
                $secteur = (string)$item->jobDescription->customFields->list1;
                $secteur_array = $this->remplirTableScriptSecteursAll($secteur, $urls['id_user'], $data['intitule']);
                $data['id_secteurs'] = $secteur_array[0];
                $data['id_sous_secteurs'] = $secteur_array[1];
                
                
                $datas[] = $data;

                $XMLReader->next("offer");

            }

            $info = $this->saveInDatabase($datas, $urls['id_user'], $urls['name']);
            $fin = time();
            $out .= $info[0];
            $out = $this->supprimerOffres($out, $offreMaj = 0, $offreCrees = 0, $info[1], $urls['id_user']);

            $XMLReader->close();

        
        $t_final = time();
        $out .= "<br>temps total = " . $this->time_elapsed_A($t_final - $t_initial);

        $this->saveInfoScript("Sncf",$out,$offreSupprimees = 0,$offreMaj,$offreCrees, $array_id_users = null,$subject);

        $this->outStatscrawl('Sncf');
    }
}
?>