<?php 

namespace App\Script;

use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use XMLReader;
use SimpleXMLElement;

class IteaRH extends ToolsService {

    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }

    public function IteaRHScript()
    {
        $logId = $this->logScriptStart('IteaRH'); // Démarre un log
        $offersProcessed = 0;
        $errorMessage = null;
        set_time_limit(0);
        error_reporting(E_ALL);
        try {


        echo "Début du script IteaRH...\n";
        echo "Initialisation des variables...\n";

        $OffreMaj = 0;
        $OffreCrees = 0;
        $offreSupprimees = 0;
        $datas = array();
        $subject = "";
        $t_initial = time();
        $out = '';

        $urls = array(
            array(    
            'url' => 'http://recrutement.itea-rh.fr/_wsc/rrh_default.xml',
            'id_user' => '1050164', // 1050164
            'name' => 'IteaRH',
            ),    
        );

        foreach($urls as $array){

            echo "Ouverture de l'URL XML...\n";
            $XMLReader = new XMLReader;
            $XMLReader->open($array['url']);

            $UserUser = $this->doctrine->getRepository(UserUser::class)->findOneBy(
                ['id_user' => $array['id_user']]
            );

            $out .= "<br/>";
            if (!$UserUser) {
                echo 'Erreur : utilisateur non reconnu :' . $array['id_user'];
                $out .= 'Erreur : utilisateur non reconnu :' . $array['id_user'];
                $subject = 'Cron import IteaRH Error User unknown';
                continue;
            }
            
            $out .= "<br/><br/>START id_client : " . $array['id_user'] . " - Nom_client : " . $array['name'] . " - Flux : " . $array['url'] . "<br/><br/>";
            while ($XMLReader->read() && $XMLReader->name !== "job");
            while ($XMLReader->name === "job") {
                
                $data = array();
                $item = new SimpleXMLElement($XMLReader->readOuterXML());

                $data['intitule'] = utf8_decode((string)ucfirst(strtolower($item->title)));
                $data['reference'] = utf8_decode((string)$item->reference);

                $data['rep_url'] = utf8_decode((string)$item->links->job_apply);

                $description = utf8_decode((string)$item->job_details->description);
                $data['mission'] = html_entity_decode($description, ENT_QUOTES, 'UTF-8');

                $profile = utf8_decode((string)$item->job_details->profile);
                $data['searched_profile'] = html_entity_decode($profile, ENT_QUOTES, 'UTF-8');

                $ville = (string)$item->location->town;

                $ville_find = $this->localisation($ville, null, null, null, $out);
                if ($ville_find) {
                    $data['id_departements'] = $ville_find["id_departements"];
                    $data['postale_code'] = $ville_find["postale_code"];
                    $data['id_regions'] = $ville_find["id_regions"];
                    $data['geo_ville'] = $ville_find["geo_ville"];
                } else {
                    echo "ville non trouvé = " . $ville;
                }

                $data['id_secteurs'] = 69; // Informatique

                $data['date_debut'] = null;
                $data['duree_contrat'] = null;
                $data['id_experience_requise'] = null;
                $data['id_fourchette_remuneration'] = null;

                $datas[] = $data;
                $offersProcessed++; // Compte les offres traitées

                $XMLReader->next('job');
            }
            
            echo "Sauvegarde en base de données...\n";
            $info = $this->saveInDatabase($datas, $array['id_user'], $array['name']);
            $out .= $info[0];
            $idsOffresToConserve = $info[1];
            $entityManager = $this->doctrine->getManager();
            $out = $this->supprimerOffresV2($out, $offreMaj = 0, $offreCrees = 0, $idsOffresToConserve, $array['id_user'], $entityManager);
            // $out = $this->supprimerOffres($out, $offreMaj = 0, $offreCrees = 0, $info[1], $array['id_user']);
            
            echo "Données sauvegardées et offres obsolètes supprimées.\n";
        }

        $t_final = time();
        echo "Temps total écoulé : " . $this->time_elapsed_A($t_final - $t_initial) . "\n";

        $this->saveInfoScript("IteaRH", $out, $offreSupprimees = 0, $offreMaj, $offreCrees, $subject);
            $this->logScriptEnd($logId, true, $offersProcessed, null); // Enregistre la fin avec succès
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $this->logScriptEnd($logId, false, $offersProcessed, $errorMessage); // Enregistre la fin avec erreur
        }
        $this->outStatscrawl('IteaRH');

        echo "Fin du script IteaRH.\n";

    }
    private function logScriptStart($scriptName)
    {
        $connection = $this->doctrine->getConnection();

        $queryCheck = "
            SELECT id 
            FROM script_logs 
            WHERE script_name = :script_name
            LIMIT 1
        ";
        $existingLog = $connection->fetchOne($queryCheck, [
            'script_name' => $scriptName,
        ]);

        if ($existingLog) {
            $queryUpdate = "
                UPDATE script_logs
                SET start_time = NOW(),
                    end_time = NULL,
                    status = FALSE,
                    offers_processed = 0,
                    error_message = NULL,
                    created_at = NOW()
                WHERE id = :id
            ";
            $connection->executeStatement($queryUpdate, [
                'id' => $existingLog,
            ]);

            return $existingLog;
        } else {
            $queryInsert = "
                INSERT INTO script_logs (script_name, start_time, status)
                VALUES (:script_name, NOW(), FALSE)
            ";
            $connection->executeStatement($queryInsert, [
                'script_name' => $scriptName,
            ]);

            return $connection->lastInsertId();
        }
    }

    private function logScriptEnd($logId, $status, $offersProcessed, $errorMessage = null)
    {
        $connection = $this->doctrine->getConnection();
        $query = "
            UPDATE script_logs
            SET end_time = NOW(),
                status = :status,
                offers_processed = :offers_processed,
                error_message = :error_message
            WHERE id = :id
        ";
        $connection->executeStatement($query, [
            'status' => $status,
            'offers_processed' => $offersProcessed,
            'error_message' => $errorMessage,
            'id' => $logId,
        ]);
    }
}