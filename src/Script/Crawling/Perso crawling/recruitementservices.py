import requests
from bs4 import BeautifulSoup
import json
import time
import re
from urllib.parse import urljoin
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
import os
from tqdm import tqdm
import uuid
import subprocess
import cloudscraper
from dotenv import load_dotenv
import tempfile
import shutil


# Load environment variables
load_dotenv()

# API configuration
API_URL = os.getenv("API_URL")
API_KEY = os.getenv("API_KEY")
rs = int(os.getenv("rs_id"))
BATCH_SIZE = 100

# Set up logging to suppress all but critical messages
logging.basicConfig(level=logging.CRITICAL, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
logging.getLogger("urllib3").setLevel(logging.CRITICAL)

class JobScraper:
    def __init__(self, chromedriver_path=None):
        self.base_url = "https://carrieres.recrutementservices.com"
        self.search_url = f"{self.base_url}/search"
        self.processed_urls = set()  # Track processed job URLs
        self.processed_references = set()  # Track processed job references
        self.chromedriver_path = chromedriver_path

        chrome_options = Options()
        chrome_options.add_argument('--headless=new')  # <-- OBLIGATOIRE SUR VPS
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)


        # User data dir unique
        self.temp_profile_dir = tempfile.mkdtemp()
        chrome_options.add_argument(f"--user-data-dir={self.temp_profile_dir}")

        try:
            if chromedriver_path and os.path.exists(chromedriver_path):
                service = Service(chromedriver_path)
            else:
                service = Service()
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 30)
        except WebDriverException as e:
            logger.critical(f"Failed to initialize ChromeDriver: {e}")
            raise Exception("ChromeDriver setup failed. Ensure ChromeDriver is installed and accessible.")

        # Contract type mappings
        self.contract_types = {
            'Autre': 2,
            'CDD': 1,
            'CDI': 2,
            'cdi': 2,
            'cdd': 1,
            'Intérim': 4,
            'interim': 4,
            'Vacation': 4,
            'Contrat Professionnalisation': 6,
            'Contrat profesionnalisation': 6,
            "Contrat d'apprentissage": 6,
            'Stage': 8,
            'temporaryWork': 4,
            'permanentContract': 2,
            'Travail temporaire': 4
        }

        # Sector mappings
        self.sectors = {
            'Achat / Logistique / Transport': [1, 20],
            'Administratif / Secrétariat / Public': [30, 15],
            'Agriculture / Agronomie / Alimentaire': [29],
            'Aide à la personne / Social': [38],
            'Banque / Assurance / Mutuelle': [3],
            'BTP / Environnement / Génie civil': [4, 12],
            'Chimie / Matériaux / Plasturgie': [5],
            'Commerces / Grande distribution / Retail': [16, 47, 48],
            'Commercial / Vente / Export': [6, 13],
            'Dirigeant / Postes de direction': [8],
            'Electronique / Electricité / Télécom': [11, 27],
            'Finance / Audit / Comptabilité': [14, 32],
            'Hôtellerie / Restauration': [17],
            'Immobilier / Urbanisme / Architecture': [46, 18],
            'Industrie / Qualité / Sécurité / Auto': [2, 39, 24, 23],
            'Informatique / Multimédia / Internet': [19, 10],
            'Marketing / Communication / Médias': [21, 7, 45, 44],
            'Production / Mécanique / Maintenance': [22],
            'RH / Formation / Juridique / Fiscalité': [25, 9],
            'Santé / Paramédical / Pharmacie': [26],
            'Tourisme / Loisirs / Sport / Culture': [28],
        }

    def get_salary_range_id(self, salary_from, salary_to):
        """Get salary range ID based on salary values"""
        if salary_from is None or salary_to is None:
            return None
        if salary_from >= 15000 and salary_to <= 20000:
            return 1
        elif salary_from > 20000 and salary_to <= 30000:
            return 2
        elif salary_from > 30000 and salary_to <= 40000:
            return 3
        elif salary_from > 40000 and salary_to <= 50000:
            return 4
        elif salary_from > 50000 and salary_to <= 60000:
            return 5
        elif salary_from > 60000 and salary_to <= 70000:
            return 6
        elif salary_from > 70000 and salary_to <= 100000:
            return 7
        elif salary_from > 100000:
            return 8
        return None

    def extract_salary_from_text(self, text):
        """Extract salary range from text"""
        if not text:
            return None, None

        salary_patterns = [
            r'(\d{2,3}[\s,.]?\d{3})\s*(?:€|euros?)',  # 25000€ or 25 000€
            r'(\d{2,3})k\s*(?:€|euros?)',  # 25k€
            r'(\d{1,3})\s*(?:000|k)\s*(?:€|euros?)',  # 25 000€ or 25k€
        ]

        salaries = []
        text_lower = text.lower()

        for pattern in salary_patterns:
            matches = re.findall(pattern, text_lower)
            for match in matches:
                try:
                    if 'k' in text_lower or '000' in match:
                        salary = int(re.sub(r'[^\d]', '', match)) * 1000
                    else:
                        salary = int(re.sub(r'[^\d]', '', match))
                    if 10000 <= salary <= 200000:
                        salaries.append(salary)
                except ValueError:
                    continue

        if len(salaries) >= 2:
            return min(salaries), max(salaries)
        elif len(salaries) == 1:
            return salaries[0], salaries[0]

        return None, None

    def get_contract_type_id(self, contract_text):
        """Get contract type ID from text"""
        if not contract_text:
            return 4  # Default to Intérim
        contract_lower = contract_text.lower()
        for contract_type, type_id in self.contract_types.items():
            if contract_type.lower() in contract_lower:
                return type_id
        return 4

    def get_sector_ids(self, job_title, description):
        """Get sector IDs based on job title and description"""
        text = f"{job_title} {description or ''}".lower()
        for sector_name, sector_ids in self.sectors.items():
            keywords = sector_name.lower().split(' / ')
            if any(keyword in text for keyword in keywords):
                return sector_ids
        return [22]  # Default to Production / Mécanique / Maintenance

    def wait_for_page_load(self):
        """Wait for page to fully load"""
        try:
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            self.wait.until(EC.any_of(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='job-item']")),
                EC.presence_of_element_located((By.CSS_SELECTOR, ".job-item")),
                EC.presence_of_element_located((By.CSS_SELECTOR, ".list-offers")),
                EC.presence_of_element_located((By.CSS_SELECTOR, "[class*='offer']"))
            ))
            time.sleep(3)
        except TimeoutException:
            pass  # Suppressed logging

    def find_job_listings(self, soup):
        """Find job listing elements with improved selectors"""
        job_selectors = [
            "[data-testid='job-item']",
            ".job-item",
            ".offer-item",
            ".job-card",
            ".offer-card",
            ".list-offers > div",
            ".list-offers > li",
            "article[class*='job']",
            "article[class*='offer']",
            "div[class*='job-listing']",
            "div[class*='offer']",
            "li[class*='job']",
            "li[class*='offer']",
            ".jobs-list > div",
            ".jobs-list > li"
        ]

        invalid_job_pattern = re.compile(r'\d+\s*(offres|jobs|emplois)\s*(?:à\s*pourvoir|disponibles?)', re.IGNORECASE)

        for selector in job_selectors:
            elements = soup.select(selector)
            if elements:
                valid_elements = []
                for elem in elements:
                    text = elem.get_text(strip=True).lower()
                    link = elem.find('a', href=re.compile(r'/offer/'))
                    if link and not invalid_job_pattern.search(text):
                        valid_elements.append(elem)
                if valid_elements:
                    return valid_elements[:20]

        all_elements = soup.find_all(['div', 'article', 'li'], class_=re.compile(r'job|offer|position', re.I))
        if all_elements:
            valid_elements = []
            for elem in all_elements:
                text = elem.get_text(strip=True).lower()
                link = elem.find('a', href=re.compile(r'/offer/'))
                if link and not invalid_job_pattern.search(text):
                    valid_elements.append(elem)
            if valid_elements:
                return valid_elements[:20]

        potential_jobs = []
        for elem in soup.find_all(['div', 'article', 'section']):
            text = elem.get_text(strip=True).lower()
            link = elem.find('a', href=re.compile(r'/offer/'))
            if (link and
                any(keyword in text for keyword in ['poste', 'emploi', 'cdi', 'cdd', 'intérim', 'stage'])
                and 50 < len(text) < 2000
                and not invalid_job_pattern.search(text)):
                potential_jobs.append(elem)

        return potential_jobs[:10]

    def extract_clean_text(self, element, max_length=None):
        """Extract and clean text from an element"""
        if not element:
            return ""

        text = element.get_text(separator=' ', strip=True)
        text = re.sub(r'\s+', ' ', text).strip()

        if max_length and len(text) > max_length:
            text = text[:max_length] + '...'

        return text

    def extract_job_url(self, card):
        """Extract job URL and reference from card"""
        link_selectors = [
            'a[href*="/offer/"]',
            'a[href*="/job/"]',
            'a[href*="/position/"]',
            'a[class*="job-link"]',
            'a[class*="offer-link"]',
            'a[class*="title"]',
            'h3 a',
            'h2 a',
            'a'
        ]

        for selector in link_selectors:
            link_elem = card.select_one(selector)
            if link_elem and 'href' in link_elem.attrs:
                href = link_elem['href']
                if any(keyword in href.lower() for keyword in ['/offer/', '/job/', '/position/']):
                    full_url = urljoin(self.base_url, href)
                    reference_match = re.search(r'9051-([a-zA-Z0-9\-]+)(?:\?|$)', href)
                    reference = reference_match.group(1) if reference_match else str(uuid.uuid4())
                    return full_url, href, reference

        return None, None, str(uuid.uuid4())

    def extract_location_from_text(self, text, soup=None):
        """Extract location information from text with improved parsing"""
        if not text and not soup:
            return "", ""

        # Specific selectors for location in job page
        if soup:
            location_selectors = [
                '[class*="location"]',
                '[class*="city"]',
                '[class*="address"]',
                '.location',
                '.city',
                '.address',
                'div[class*="offer-info"]',
                'span[class*="location"]'
            ]

            for selector in location_selectors:
                loc_elem = soup.select_one(selector)
                if loc_elem:
                    loc_text = self.extract_clean_text(loc_elem)
                    if loc_text:
                        city, postal = self.parse_location_text(loc_text)
                        if city:
                            return city, postal

        # Fallback to text-based extraction
        city, postal = self.parse_location_text(text)
        return city, postal

    def parse_location_text(self, text):
        """Parse location text to extract city and postal code"""
        if not text:
            return "", ""

        # Known cities in Moselle and nearby regions
        known_cities = {
            'metz', 'nancy', 'sarreguemines', 'dieuze', 'moselle', 'henriville',
            'forbach', 'thionville', 'saint-avold', 'freyming-merlebach'
        }

        # Patterns to match location formats
        location_patterns = [
            r'(\d{5})\s+([A-Za-zÀ-ÿ\s\'-]+?)(?:\s|$)',  # 57400 Sarreguemines
            r'([A-Za-zÀ-ÿ\s\'-]+?)\s+\((\d{5})\)',  # Sarreguemines (57400)
            r'(?:à|sur|en|secteur\s*(?:de|d\'))\s+([A-Za-zÀ-ÿ\s\'-]+?)(?:\s|$|[.,;])',  # à Sarreguemines
            r'([A-Za-zÀ-ÿ\s\'-]+?)\s+(?:Intérim|CDI|CDD|Stage)(?:\s|$)',  # Dieuze Intérim
        ]

        exclude_words = {
            'présentiel', 'hybride', 'télétravail', 'remote', 'temps plein', 'temps partiel',
            'contrat', 'mission', 'poste', 'emploi', 'offre', 'h/f', 'intérim', 'cdi', 'cdd',
            'concepteur', 'vendeur', 'chauffagiste', 'manutentionnaire', 'recrutement services',
            'technicien', 'maintenance', 'soignant', 'pouvant', 'acier', 'emboutissage',
            'profilage', 'manutention', 'assemblage', 'qualité', 'sécurité', 'cadences',
            'autonomie', 'équipe', 'débutant', 'diplôme', 'expérience', 'client'
        }

        region_postal = {
            'moselle': '57000',
            'dieuze': '57260',
            'sarreguemines': '57400',
            'metz': '57000',
            'nancy': '54000',
            'henriville': '57450',
            'forbach': '57600',
            'thionville': '57100',
            'saint-avold': '57500',
            'freyming-merlebach': '57800'
        }

        for pattern in location_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                if len(match.groups()) >= 2:
                    if match.group(1).isdigit():
                        city = match.group(2).strip()
                        postal = match.group(1)
                    else:
                        city = match.group(1).strip()
                        postal = region_postal.get(city.lower(), "")
                else:
                    city = match.group(1).strip()
                    postal = region_postal.get(city.lower(), "")

                # Clean city name
                city = re.sub(r'^(h|f|h/f|H/F)\s+', '', city, flags=re.IGNORECASE)
                city = re.sub(r'^\W+', '', city)
                city = re.sub(r'\W+$', '', city)
                city = city.strip()

                if (len(city) >= 3 and
                    city.lower() in known_cities and
                    not any(exclude_word in city.lower() for exclude_word in exclude_words) and
                    not re.search(r'\b\d+\b', city)):
                    return city.capitalize(), postal

        # Fallback: Check for known cities in text
        text_lower = text.lower()
        for city in known_cities:
            if city in text_lower:
                return city.capitalize(), region_postal.get(city, "")

        return "", ""

    def clean_mission_text(self, text):
        """Clean mission text by removing boilerplate and normalizing"""
        if not text:
            return None

        boilerplate_patterns = [
            r'Recrutement Services recherche pour l’un de ses clients[^\.]*\.',
            r'L’agence de recrutement Lorraine Services recherche pour l’un de ses clients[^\.]*\.',
            r'Pour l’un de nos clients[^\.]*\.',
            r'et Missions\s*',
            r'VOS MISSIONS\s*:\s*',
            r'Rémunération\s*:[^\.]*\.',
            r'Salaire\s*:[^\.]*\.',
            r'Contactez nous au[^\.]*\.',
            r'Travail posté en [^\.]*\.',
            r'Horaire\s*:[^\.]*\.',
            r'Profil\s*recherché\s*[:\-]?.*',
            r'VOTRE\s*PROFIL\s*[:\-]?.*',
            r'APTITUDES\s*PERSONNELLES\s*[:\-]?.*',
            r'DIPLÔMES\s*[:\-]?.*',
            r'COMPETENCES\s*[:\-]?.*',
            r'panier\s*de\s*jour',
            r'Vous\s*appréciez\s*le\s*travail\s*en\s*équipe\s*[!\.]*'
        ]

        cleaned_text = text
        for pattern in boilerplate_patterns:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE | re.DOTALL)

        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        cleaned_text = re.sub(r'[.,;]$', '', cleaned_text)

        return cleaned_text if len(cleaned_text) > 20 else None

    def extract_job_data_from_element(self, card):
        """Extract job data from a job card"""
        job_data = {
            'reference': str(uuid.uuid4()),
            'id_type_contrat': 2,
            'intitule': '',
            'geo_ville': '',
            'postale_code': '',
            'id_fourchette_remuneration': None,
            'id_secteurs': [22],
            'id_sous_secteurs': [22],
            'mission': None,
            'description': None,
            'searched_profile': None,
            'rep_url': '',
            'rep_mail': '',
            'duree_contrat': '',
            'company': 'Recrutement Services'
        }

        # Extract job URL and reference
        job_url, href, reference = self.extract_job_url(card)
        if job_url:
            job_data['rep_url'] = job_url
            job_data['reference'] = reference

        # Extract job title
        title_selectors = [
            'h3',
            'h2',
            'h1',
            '[class*="title"]',
            '[class*="job-title"]',
            '[class*="offer-title"]',
            'a[class*="title"]'
        ]

        for selector in title_selectors:
            title_elem = card.select_one(selector)
            if title_elem:
                title_text = self.extract_clean_text(title_elem)
                if len(title_text) > 5 and title_text.lower() != 'unknown job':
                    job_data['intitule'] = title_text
                    break

        # Extract location
        location_selectors = [
            '[class*="location"]',
            '[class*="city"]',
            '[class*="address"]',
            '.location',
            '.city',
            '.address'
        ]

        location_found = False
        for selector in location_selectors:
            loc_elem = card.select_one(selector)
            if loc_elem:
                location_text = self.extract_clean_text(loc_elem)
                if location_text and len(location_text) > 2:
                    city, postal = self.extract_location_from_text(location_text)
                    job_data['geo_ville'] = city
                    job_data['postale_code'] = postal
                    location_found = True
                    break

        if not location_found:
            card_text = self.extract_clean_text(card)
            city, postal = self.extract_location_from_text(card_text)
            job_data['geo_ville'] = city
            job_data['postale_code'] = postal

        # Extract contract type
        card_text = self.extract_clean_text(card)
        job_data['id_type_contrat'] = self.get_contract_type_id(card_text)

        # Extract company
        company_selectors = [
            '[class*="company"]',
            '[class*="employer"]',
            '[class*="entreprise"]',
            '.company',
            '.employer'
        ]

        for selector in company_selectors:
            company_elem = card.select_one(selector)
            if company_elem:
                company_text = self.extract_clean_text(company_elem)
                if len(company_text) > 2:
                    job_data['company'] = company_text
                    break

        # Extract salary information
        salary_from, salary_to = self.extract_salary_from_text(card_text)
        job_data['id_fourchette_remuneration'] = self.get_salary_range_id(salary_from, salary_to)

        return job_data

    def scrape_job_details(self, job_url):
        """Scrape detailed information from individual job page"""
        try:
            self.driver.get(job_url)
            time.sleep(3)

            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            details = {
                'mission': None,
                'description': None,
                'searched_profile': None,
                'rep_mail': '',
                'company': 'Recrutement Services',
                'geo_ville': '',
                'postale_code': '',
                'duree_contrat': '',
                'reference': str(uuid.uuid4())
            }

            # Target the main content area
            content_element = soup.select_one('article[data-testid="cvc-offer-description"]')
            if not content_element:
                content_element = soup.select_one('main') or soup

            content_text = self.extract_clean_text(content_element, max_length=5000)

            # Extract description
            details['description'] = self.clean_mission_text(content_text)

            # Extract mission
            mission_section = None
            for h3 in content_element.find_all(['h3', 'h2']):
                h3_text = h3.get_text(strip=True).lower()
                if any(keyword in h3_text for keyword in ['missions', 'vos missions', 'votre mission', 'tâches', 'description du poste']):
                    mission_section = []
                    for sibling in h3.find_next_siblings():
                        sibling_text = sibling.get_text(strip=True).lower()
                        if sibling.name in ['h3', 'h2'] and any(keyword in sibling_text for keyword in ['profil', 'compétences', 'diplômes', 'aptitudes', 'avantages', 'informations']):
                            break
                        if sibling.name in ['p', 'ul', 'li']:
                            mission_section.append(sibling)
                    break

            if mission_section:
                mission_text = ' '.join(self.extract_clean_text(elem) for elem in mission_section)
                mission_text = self.clean_mission_text(mission_text)
                if mission_text and len(mission_text) > 20:
                    details['mission'] = mission_text

            if not details['mission']:
                mission_patterns = [
                    r'(?:MISSIONS|VOS\s*MISSIONS|VOTRE\s*MISSION|TÂCHES|Description\s*du\s*poste\s*et\s*Missions)[:\s\-]*(.*?)(?=(?:Profil\s*recherché|VOTRE\s*PROFIL|APTITUDES|Qualifications|Compétences|Avantages|Contact|Candidature|Informations\s*utiles|Entreprise|$))'
                ]

                for pattern in mission_patterns:
                    match = re.search(pattern, content_text, re.DOTALL | re.IGNORECASE)
                    if match:
                        mission_text = match.group(1).strip()
                        mission_text = self.clean_mission_text(mission_text)
                        if mission_text and len(mission_text) > 20:
                            details['mission'] = mission_text
                            break

            # Extract searched profile
            profile_section = None
            for h3 in content_element.find_all(['h3', 'h2']):
                h3_text = h3.get_text(strip=True).lower()
                if any(keyword in h3_text for keyword in ['profil recherché', 'profil', 'votre profil', 'compétences', 'diplômes', 'aptitudes']):
                    profile_section = []
                    for sibling in h3.find_next_siblings():
                        sibling_text = sibling.get_text(strip=True).lower()
                        if sibling.name in ['h3', 'h2'] and any(keyword in sibling_text for keyword in ['avantages', 'informations', 'contact', 'entreprise']):
                            break
                        if sibling.name in ['p', 'ul', 'li']:
                            profile_section.append(sibling)
                    break

            if profile_section:
                profile_text = ' '.join(self.extract_clean_text(elem) for elem in profile_section)
                profile_text = re.sub(r'\s+', ' ', profile_text).strip()
                if len(profile_text) > 20:
                    details['searched_profile'] = profile_text[:1500] + '...' if len(profile_text) > 1500 else profile_text

            if not details['searched_profile']:
                profile_patterns = [
                    r'(?:Profil\s*recherché|VOTRE\s*PROFIL|APTITUDES\s*PERSONNELLES\s*REQUISES|COMPETENCES\s*REQUISES|DIPLÔMES)[:\s\-]*(.*?)(?=(?:Avantages|Contact|Candidature|Informations\s*utiles|Entreprise|Rémunération|Salaire|$))'
                ]

                for pattern in profile_patterns:
                    match = re.search(pattern, content_text, re.DOTALL | re.IGNORECASE)
                    if match:
                        profile_text = match.group(1).strip()
                        profile_text = re.sub(r'\s+', ' ', profile_text)
                        if len(profile_text) > 20:
                            details['searched_profile'] = profile_text[:1500] + '...' if len(profile_text) > 1500 else profile_text
                            break

            # Extract email
            email_patterns = [
                r'[\w\.-]+@[\w\.-]+\.\w+',
                r'mailto:([\w\.-]+@[\w\.-]+\.\w+)'
            ]

            page_text = soup.get_text()
            for pattern in email_patterns:
                match = re.search(pattern, page_text, re.IGNORECASE)
                if match:
                    details['rep_mail'] = match.group(1) if 'mailto' in pattern else match.group(0)
                    break

            # Extract contract duration
            duration_patterns = [
                r'(?:durée|duree|contrat)[^\n:]*[:\-]?\s*(\d+\s*(?:mois|semaines|jours|ans))',
                r'(\d+\s*(?:mois|semaines|jours|ans))\s*(?:de\s*)?(?:contrat|mission)'
            ]

            for pattern in duration_patterns:
                match = re.search(pattern, content_text, re.IGNORECASE)
                if match:
                    details['duree_contrat'] = match.group(1)
                    break

            # Extract company information
            company_patterns = [
                r'(?:Entreprise|Client|Employeur|Société)[:\s\-]+([A-Za-zÀ-ÿ\s\'\-\.]+?)(?=(?:Description|Mission|Profil|Avantages|$))',
                r'RECRUTEMENT\s*SERVICES\s+([A-Za-zÀ-ÿ\s\'\-\.]+?)(?:\s|$)'
            ]

            for pattern in company_patterns:
                match = re.search(pattern, content_text, re.DOTALL | re.IGNORECASE)
                if match:
                    company_text = match.group(1).strip()
                    company_text = re.sub(r'\s+', ' ', company_text)
                    if (len(company_text) > 2 and
                        len(company_text) < 100 and
                        not any(keyword in company_text.lower() for keyword in ['mission', 'profil', 'tâches', 'vous', 'équipe', 'débutant'])):
                        details['company'] = company_text
                        break

            # Extract location from job page
            city, postal = self.extract_location_from_text(content_text, soup)
            details['geo_ville'] = city
            details['postale_code'] = postal

            # Extract reference from URL
            reference_match = re.search(r'9051-([a-zA-Z0-9\-]+)(?:\?|$)', job_url)
            details['reference'] = reference_match.group(1) if reference_match else str(uuid.uuid4())

            return details

        except Exception:
            reference_match = re.search(r'9051-([a-zA-Z0-9\-]+)(?:\?|$)', job_url)
            return {
                'mission': None,
                'description': None,
                'searched_profile': None,
                'rep_mail': '',
                'company': 'Recrutement Services',
                'geo_ville': '',
                'postale_code': '',
                'duree_contrat': '',
                'reference': reference_match.group(1) if reference_match else str(uuid.uuid4())
            }

    def scrape_search_page(self, page=1):
        """Scrape a single search results page"""
        url = f"{self.search_url}?page={page}" if page > 1 else self.search_url

        try:
            self.driver.get(url)
            self.wait_for_page_load()

            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            job_cards = self.find_job_listings(soup)

            if not job_cards:
                return []

            jobs = []
            invalid_job_pattern = re.compile(r'\d+\s*(offres|jobs|emplois)\s*(?:à\s*pourvoir|disponibles?)', re.IGNORECASE)

            for card in tqdm(job_cards, desc=f"Processing page {page}", leave=False):
                try:
                    job_data = self.extract_job_data_from_element(card)

                    # Skip invalid jobs
                    if (not job_data['rep_url'] or
                        invalid_job_pattern.search(job_data['intitule'].lower()) or
                        job_data['reference'] in self.processed_references or
                        job_data['rep_url'] in self.processed_urls or
                        len(job_data['intitule']) < 5 or
                        job_data['intitule'].lower() == 'unknown job'):
                        continue

                    self.processed_urls.add(job_data['rep_url'])
                    self.processed_references.add(job_data['reference'])

                    details = self.scrape_job_details(job_data['rep_url'])
                    job_data.update(details)

                    sector_ids = self.get_sector_ids(
                        job_data.get('intitule', ''),
                        job_data.get('mission', '') or ''
                    )
                    job_data['id_secteurs'] = sector_ids
                    job_data['id_sous_secteurs'] = sector_ids

                    jobs.append(job_data)
                    time.sleep(1)

                except Exception:
                    continue

            return jobs

        except Exception:
            return []

    def scrape_all_pages(self, max_pages=None):
        """Scrape all pages with progress bar"""
        all_jobs = []
        page = 1
        consecutive_empty_pages = 0

        # Use tqdm for page progress bar
        pbar = tqdm(total=max_pages, desc="Scraping pages", unit="page")

        while True:
            jobs = self.scrape_search_page(page)

            if not jobs:
                consecutive_empty_pages += 1
                if consecutive_empty_pages >= 3:
                    break
            else:
                consecutive_empty_pages = 0
                all_jobs.extend(jobs)

            if max_pages and page >= max_pages:
                break

            # Try to find next page
            next_url = None
            try:
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(3)

                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                next_page_selectors = [
                    'a.cvcatcher-pagination-next[rel="next"]',
                    'a[rel="next"]',
                    'a[aria-label="Next page"]',
                    'a[class*="pagination-next"]',
                    'a[class*="next"]'
                ]

                for selector in next_page_selectors:
                    next_page = soup.select_one(selector)
                    if next_page and 'href' in next_page.attrs:
                        next_url = urljoin(self.base_url, next_page['href'])
                        break

                if not next_url:
                    next_url = f"{self.search_url}?page={page + 1}"

                self.driver.get(next_url)
                time.sleep(5)

            except Exception:
                break

            page += 1
            pbar.update(1)

            if not next_url:
                break

        pbar.close()
        return all_jobs

    def save_to_json(self, jobs, filename='jobs_data.json'):
        """Save jobs data to JSON file"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(jobs, f, ensure_ascii=False, indent=2)

    def prepare_payload(self, all_jobs, id_user, company_name):
        """Prepare the payload for API submission"""
        return {
            "id_user": id_user,
            "company": company_name,
            "datas": all_jobs
        }

    def post_job_to_api(self, job_data, batch_number):
        """Post job data to API"""
        try:
            # Save job_data to a JSON file
            with open(f'recrutement_services_job_data_batch_{batch_number}.json', 'w', encoding='utf-8') as f:
                json.dump(job_data, f, indent=4, ensure_ascii=False)

            scraper = cloudscraper.create_scraper()
            headers = {
                "X-API-KEY": API_KEY,
                "Accept": "application/json",
                "Content-Type": "application/json"
            }

            response = scraper.post(
                API_URL,
                headers=headers,
                json=job_data,
                timeout=1000
            )
            response.raise_for_status()
            with tqdm(total=1, desc="Successfully posted jobs to API") as api_pbar:
                api_pbar.update(1)
            return True

        except Exception as e:
            with tqdm(total=1, desc=f"Error posting to API: {e}") as api_error_pbar:
                api_error_pbar.update(1)
            return False

    def close_driver(self):
        """Ensure proper ChromeDriver cleanup with retries"""
        max_retries = 5
        for attempt in range(max_retries):
            try:
                if hasattr(self, 'driver') and self.driver:
                    self.driver.quit()
                    self.driver = None
                    return
            except Exception:
                if attempt < max_retries - 1:
                    time.sleep(3)
                else:
                    # Fallback: Force kill ChromeDriver and Chrome processes
                    os.system("pkill -f chromedriver")
                    os.system("pkill -f chrome")
                    self.driver = None
        if hasattr(self, 'temp_profile_dir'):
            shutil.rmtree(self.temp_profile_dir, ignore_errors=True)
    
    def __del__(self):
        """Ensure cleanup"""
        self.close_driver()
    
    def run(self, max_pages=None, output_file='recrutement_services_jobs.json'):
        """Main execution method"""
        try:
            jobs = self.scrape_all_pages(max_pages)
            if jobs:
                # Save all jobs to JSON
                self.save_to_json(jobs, output_file)
                
                # Process jobs in batches
                for i in range(0, len(jobs), BATCH_SIZE):
                    batch_jobs = jobs[i:i + BATCH_SIZE]
                    batch_number = i // BATCH_SIZE + 1
                    payload = self.prepare_payload(
                        batch_jobs,
                        id_user=rs,
                        company_name="Recrutement Services"
                    )
                    self.post_job_to_api(payload, batch_number)
            
            return jobs
        except Exception:
            return []
        finally:
            self.close_driver()

if __name__ == "__main__":
    chromedriver_path = "/usr/bin/chromedriver"
    
    try:
        scraper = JobScraper(chromedriver_path=chromedriver_path)
        jobs_data = scraper.run(max_pages=20, output_file='recrutement_services_jobs.json')
        print(f"{len(jobs_data)} offres récupérées.")
    except Exception as e:
        import traceback
        traceback.print_exc()
