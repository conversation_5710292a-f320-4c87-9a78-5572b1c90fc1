import requests
import chromedriver_autoinstaller
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchWindowException, WebDriverException
from bs4 import BeautifulSoup
import re
import json
import os
from datetime import datetime
from tqdm import tqdm
from urllib.parse import urljoin
import time
from dotenv import load_dotenv
load_dotenv()
# API Configuration
API_URL = os.getenv("API_URL")
API_KEY = os.getenv("API_KEY")
RENAULT_ID = int(os.getenv("renault_id"))  # Default ID if not set

class RenaultJobScraper:
    def __init__(self):
        self.driver = None
        self.base_url = "https://www.renaultgroup.com"
        self.search_url = "https://www.renaultgroup.com/carrieres/nos-offres-monde/?countries=France&cities=&q="
        self.scraped_references = set()
        self.scraped_urls = set()
        self.api_send_count = 0
        self.api_failed_count = 0
        
        # Contract type mappings
        self.contract_types = {
            'Autre': 2, 'CDD': 1, 'CDI': 2, 'cdi': 2, 'cdd': 1, 'Intérim': 4, 'interim': 4,
            'Vacation': 4, 'Contrat Professionnalisation': 6, 'Contrat profesionnalisation': 6,
            "Contrat d'apprentissage": 6, 'Alternance': 6, 'alternance': 6, 'Stage': 8,
            'stage': 8, 'temporaryWork': 4, 'permanentContract': 2, 'Travail temporaire': 4,
            'Full-time': 2, 'Temps plein': 2, 'Temps partiel': 2, 'Part-time': 2,
            'Internship': 8, 'Apprenticeship': 6, 'Contract': 1,  # Changed from 'Contract': 1
            'Apprentissage': 6, 'apprentissage': 6  # Add these new mappings
        }
        
        # Sector mappings
        self.sector_keywords = {
            'ingénieur': [1, 19], 'ingenieur': [1, 19], 'engineer': [1, 19], 'automobile': [1],
            'automotive': [1], 'véhicule': [1], 'vehicle': [1], 'moteur': [1, 19], 'engine': [1, 19],
            'mécanique': [1, 19], 'mechanical': [1, 19], 'recherche': [1, 19], 'développement': [1, 19],
            'innovation': [1, 19], 'conception': [1, 19], 'design': [1, 18], 'informatique': [19],
            'développeur': [19], 'developer': [19], 'système': [19], 'system': [19], 'digital': [19],
            'it ': [19], 'data': [19, 32], 'cybersécurité': [19], 'cybersecurity': [19], 'devops': [19],
            'scrum': [19], 'commercial': [6, 13], 'vente': [6, 13], 'sales': [6, 13], 'conseiller': [6, 13],
            'advisor': [6, 13], 'financier': [3, 14], 'finance': [3, 14], 'comptable': [32],
            'accounting': [32], 'contrôleur': [32], 'controller': [32], 'communication': [21],
            'marketing': [21], 'web': [21], 'ressources humaines': [25], 'rh': [25], 'hr': [25],
            'recrutement': [25], 'recruitment': [25], 'juridique': [9], 'legal': [9], 'conformité': [9],
            'compliance': [9], 'manager': [8], 'directeur': [8], 'director': [8], 'responsable': [8],
            'chef': [8], 'lead': [8], 'leader': [8], 'production': [1, 5], 'manufacturing': [1, 5],
            'qualité': [1, 5], 'quality': [1, 5], 'logistique': [5, 24], 'logistics': [5, 24],
            'supply': [5, 24], 'électrique': [1, 19], 'electric': [1, 19], 'mobilité': [1, 24],
            'mobility': [1, 24], 'batterie': [1, 19], 'battery': [1, 19], 'thermique': [1, 19],
            'thermal': [1, 19]
        }

    def initialize_driver(self):
        """Initialize Chrome driver with optimized settings"""
        try:
            chromedriver_autoinstaller.install()
        except Exception:
            return None
        
        options = Options()
        options.add_argument("--headless=new")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--window-size=1920,1080")
        options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.page_load_strategy = 'eager'
        
        try:
            self.driver = webdriver.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.implicitly_wait(3)
            return self.driver
        except Exception:
            return None

    def close_driver(self):
        """Close the driver safely"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None

    def get_contract_type_id(self, contract_text):
        """Get contract type ID from text with fallback to CDI"""
        if not contract_text:
            return 2
        contract_text = contract_text.strip()
        return self.contract_types.get(contract_text, 2)

    def extract_contract_type_from_title(self, title):
        """Extract contract type from job title"""
        if not title:
            return 2
        title_upper = title.upper()
        if 'STAGE' in title_upper or 'STAGIAIRE' in title_upper or 'INTERN' in title_upper:
            return 8
        elif ('ALTERNANCE' in title_upper or 'APPRENTISSAGE' in title_upper or 
            'APPRENTI' in title_upper or 'ALTERNANT' in title_upper):
            return 6
        elif 'CDD' in title_upper:
            return 1
        elif 'INTERIM' in title_upper or 'INTÉRIM' in title_upper:
            return 4
        elif 'VACATION' in title_upper:
            return 4
        elif 'CDI' in title_upper:
            return 2
        return 2

    def get_sector_ids(self, job_description, title, profile_text=""):
        """Determine sector IDs based on job content"""
        content = f"{title} {job_description} {profile_text}".lower()
        matched_sectors = set()
        for keyword, sector_ids in self.sector_keywords.items():
            if keyword in content:
                matched_sectors.update(sector_ids)
        result = sorted(list(matched_sectors))
        if len(result) > 6:
            title_lower = title.lower()
            priority_sectors = []
            for keyword, sector_ids in self.sector_keywords.items():
                if keyword in title_lower:
                    priority_sectors.extend(sector_ids)
            if priority_sectors:
                result = sorted(list(set(priority_sectors)))[:4]
        if not result:
            result = [1]
        return result[:4]

    def clean_text(self, text):
        """Clean and normalize text content"""
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text.strip())
        text = re.sub(r'[•*-]\s*', '', text)
        text = re.sub(r'[,;]{2,}', ',', text)
        return text

    def extract_job_description(self, soup):
        """Extract detailed job description from the job posting"""
        desc_container = soup.select_one("[data-automation-id='jobPostingDescription']")
        if not desc_container:
            return ""
        
        # Extract all text content and clean it
        full_text = desc_container.get_text(separator='\n').strip()
        
        # Split by common section headers
        sections = re.split(r'\n(?=(?:Société|Descriptif du poste|Votre contexte de travail|Vos missions|Votre profil|Métier|Description|Job Description|Company|Role|Responsibilities))', full_text)
        
        description_parts = []
        
        for section in sections:
            section = section.strip()
            if not section:
                continue
                
            # Include sections that contain job description content
            if any(keyword in section.lower() for keyword in [
                'descriptif', 'contexte', 'missions', 'description', 'role', 
                'responsibilities', 'poste', 'travail', 'projet', 'équipe'
            ]):
                # Clean up the section
                section = re.sub(r'^(Descriptif du poste|Votre contexte de travail|Vos missions|Description|Job Description)', '', section, flags=re.IGNORECASE).strip()
                if section:
                    description_parts.append(section)
        
        # Join all description parts
        full_description = '\n'.join(description_parts).strip()
        
        # If no specific description sections found, take the first significant paragraph
        if not full_description:
            paragraphs = [p.strip() for p in full_text.split('\n') if len(p.strip()) > 50]
            if paragraphs:
                full_description = paragraphs[0]
        
        return self.clean_text(full_description) if full_description else ""

    def get_job_details_from_workday(self, job_url):
        """Get detailed job information from Workday job page with retry logic"""
        for attempt in range(3):
            try:
                self.driver.get(job_url)
                
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-automation-id='jobPostingHeader']"))
                )
                
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                details = {}
                
                # Extract job title
                title_elem = soup.select_one("[data-automation-id='jobPostingHeader']")
                details['title'] = self.clean_text(title_elem.text) if title_elem else ""
                
                # Extract location
                location_elem = soup.select_one("[data-automation-id='locations'] dd")
                details['location'] = self.clean_text(location_elem.text) if location_elem else "France"
                
                # Extract detailed job description
                details['description'] = self.extract_job_description(soup)
                if not details['description']:
                    details['description'] = f"Offre d'emploi pour le poste de {details.get('title', 'N/A')} chez Renault Group."
                
                # Extract job requirements/profile
                desc_container = soup.select_one("[data-automation-id='jobPostingDescription']")
                if desc_container:
                    all_text = desc_container.get_text(separator='\n').strip()
                    sections = re.split(r'\n(?=(?:Votre profil|Profil recherché|Profile|Requirements|Qualifications|Skills))', all_text)
                    profile_parts = []
                    for section in sections:
                        section = section.strip()
                        if any(keyword in section.lower() for keyword in ['profil', 'compétence', 'expérience', 'formation', 'requirements', 'qualifications', 'skills']):
                            section = re.sub(r'^(Votre profil|Profil recherché|Profile|Requirements|Qualifications)', '', section, flags=re.IGNORECASE).strip()
                            if section:
                                profile_parts.append(section)
                    details['requirements'] = self.clean_text('\n'.join(profile_parts)) if profile_parts else "Profil recherché selon les critères de l'offre."
                else:
                    details['requirements'] = "Profil recherché selon les critères de l'offre."
                
                # Extract contract type
                time_elem = soup.select_one("[data-automation-id='time'] dd")
                details['contract_type'] = self.clean_text(time_elem.text) if time_elem else ""
                
                return details
            except (NoSuchWindowException, WebDriverException, TimeoutException) as e:
                if attempt == 2:
                    return {'title': '', 'location': 'France', 'description': '', 'requirements': '', 'contract_type': ''}
                time.sleep(2)  # Wait before retry
                continue
        return {'title': '', 'location': 'France', 'description': '', 'requirements': '', 'contract_type': ''}

    def scrape_jobs_from_search_page(self, page_num=1):
        """Scrape jobs from search results page using Selenium"""
        try:
            search_url = f"{self.search_url}&page={page_num}" if page_num > 1 else self.search_url
            self.driver.get(search_url)
            
            if not self.wait_for_jobs(timeout=120):
                print(f"⚠️ Timeout waiting for jobs on page {page_num}")
                return []
            
            # Scroll to ensure all jobs are loaded
            for scroll_attempt in range(3):
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
            
            # Wait specifically for Renault job cards to load
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".BlockOffersSectionCard_component__3Y9JY"))
                )
            except TimeoutException:
                print(f"⚠️ Renault job cards not found on page {page_num}")
            
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            jobs = []
            
            # Target the specific Renault job card structure
            job_cards = soup.select('.BlockOffersSectionCard_component__3Y9JY')
            
            if job_cards:
                print(f"📄 Found {len(job_cards)} job cards on page {page_num}")
                for card in tqdm(job_cards, desc=f"Jobs P{page_num}", unit="job", leave=False):
                    try:
                        # Extract title from h3 with specific class
                        title_elem = card.select_one('h3.BlockOffersSectionCard_title__k2NVu')
                        title = self.clean_text(title_elem.get_text()) if title_elem else ""
                        
                        if not title or len(title) < 5:
                            continue
                        
                        # Skip non-job content
                        if any(keyword in title.lower() for keyword in ['trophées', 'actualité', 'magazine', 'news', 'lancement', 'édition']):
                            continue
                        
                        # Extract location from the location icon span
                        location_elem = card.select_one('svg.icon-location-16 + span')
                        location = self.clean_text(location_elem.get_text()) if location_elem else "France"
                        
                        # Extract contract type from the work icon span
                        contract_elem = card.select_one('svg.icon-work-16 + span')
                        contract_type = self.clean_text(contract_elem.get_text()) if contract_elem else ""
                        
                        # Extract job URL from the "Voir plus" button
                        link_elem = card.select_one('a[href*="myworkdayjobs.com"]')
                        job_url = link_elem.get('href') if link_elem else ""
                        
                        if not job_url or job_url in self.scraped_urls:
                            continue
                        
                        self.scraped_urls.add(job_url)
                        
                        # Get detailed information from the job page
                        job_details = self.get_job_details_from_workday(job_url)
                        
                        if job_details.get('title') and len(job_details['title']) >= 5:
                            # Use the detailed info, but fallback to card info if needed
                            final_job = {
                                'title': job_details.get('title') or title,
                                'location': job_details.get('location') or location,
                                'description': job_details.get('description') or f"Offre d'emploi pour le poste de {title} chez Renault Group.",
                                'requirements': job_details.get('requirements') or "Profil recherché selon les critères de l'offre.",
                                'contract_type': job_details.get('contract_type') or contract_type,
                                'url': job_url
                            }
                            jobs.append(final_job)
                        else:
                            # Fallback to card information if detailed scraping fails
                            job_data = {
                                'title': title,
                                'location': location,
                                'description': f"Offre d'emploi pour le poste de {title} chez Renault Group.",
                                'requirements': "Profil recherché selon les critères de l'offre.",
                                'contract_type': contract_type,
                                'url': job_url
                            }
                            jobs.append(job_data)
                        
                        time.sleep(1)  # Rate limiting
                        
                    except Exception as e:
                        print(f"❌ Error processing job card: {e}")
                        continue
            else:
                print(f"⚠️ No job cards found on page {page_num}")
                return []
            
            return jobs
            
        except Exception as e:
            print(f"❌ Error scraping page {page_num}: {e}")
            return []

    def wait_for_jobs(self, timeout=120):
        """Wait for Renault job elements to load with extended timeout"""
        try:
            # Wait specifically for Renault job cards
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_all_elements_located((
                    By.CSS_SELECTOR, 
                    ".BlockOffersSectionCard_component__3Y9JY"
                ))
            )
            return True
        except TimeoutException:
            # Fallback to generic selectors
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_all_elements_located((
                        By.CSS_SELECTOR, 
                        "a[href*='myworkdayjobs.com'], [class*='job'], [class*='offer'], [class*='position'], .card, article"
                    ))
                )
                return True
            except TimeoutException:
                return False

    def process_job_data(self, job_data):
        """Process job data into required format"""
        try:
            title = job_data.get('title', '')
            if any(keyword in title.lower() for keyword in ['trophées', 'actualité', 'magazine', 'news', 'lancement', 'édition']):
                return None
            
            reference = str(abs(hash(job_data.get('url', title))))[:8]
            if reference in self.scraped_references:
                return None
            self.scraped_references.add(reference)
            
            location = job_data.get('location', 'France')
            description = job_data.get('description', '')
            requirements = job_data.get('requirements', '')
            contract_type = job_data.get('contract_type', '')
            
            location_clean = self.clean_text(location)
            postal_match = re.search(r'(\d{5})', location_clean)
            postal_code = postal_match.group(1) if postal_match else ""
            city = re.sub(r'\d{5}', '', location_clean).strip()
            city = re.sub(r'[,-]\s*$', '', city).strip()
            if not city:
                city = location_clean or "France"
            
            contract_type_id = self.extract_contract_type_from_title(title)
            if contract_type:
                text_contract = self.get_contract_type_id(contract_type)
                # Only use text_contract if it's not the default CDI (2)
                if text_contract != 2:
                    contract_type_id = text_contract
                # If text_contract is 2 but we found a more specific type from title, keep title-based
                elif contract_type_id != 2:
                    contract_type_id = contract_type_id
                else:
                    contract_type_id = text_contract
            
            sectors = self.get_sector_ids(description, title, requirements)
            
            if not description or len(description.strip()) < 20:
                description = f"Offre d'emploi pour le poste de {title} chez Renault Group."
            if not requirements or len(requirements.strip()) < 10:
                requirements = "Profil recherché selon les critères de l'offre."
            
            processed_data = {
                'reference': reference,
                'id_type_contrat': contract_type_id,
                'intitule': self.clean_text(title),
                'geo_ville': city,
                'postale_code': postal_code,
                'id_fourchette_remuneration': None,
                'id_secteurs': sectors,
                'id_sous_secteurs': sectors,
                'mission': description,
                'searched_profile': requirements,
                'description': description,  # Added description field
                'rep_url': job_data.get('url', ''),
                'rep_mail': '',
                'duree_contrat': '',
                'company': 'Renault Group'
            }
            
            if not processed_data['intitule'].strip() or len(processed_data['intitule']) < 5:
                return None
            if not processed_data['geo_ville'].strip():
                return None
            
            return processed_data
        except Exception as e:
            print(f"❌ Error processing job data: {e}")
            return None

    def send_batch_to_api(self, jobs_batch, batch_number):
        """Send a batch of jobs to API"""
        if not API_URL or not API_KEY or not jobs_batch:
            return False
        
        payload = prepare_payload(jobs_batch, RENAULT_ID, "Renault Group")
        with tqdm(total=1, desc=f"API Batch {batch_number}", unit="req", leave=False) as api_pbar:
            success = False  # Keep this as False as requested
            success = post_job_to_api(payload)  # Commented out since server not ready
            
            if success:
                self.api_send_count += 1
                api_pbar.set_postfix({'Status': f'✅ Success ({len(jobs_batch)} jobs)'})
            else:
                self.api_failed_count += 1
                api_pbar.set_postfix({'Status': f'❌ Failed ({len(jobs_batch)} jobs)'})
            api_pbar.update(1)
        
        return success

    def scrape_all_jobs(self, max_pages=42, batch_size=100):
        """Scrape all job offers from Renault with improved pagination and batching"""
        all_jobs = []
        current_batch_jobs = []
        batch_number = 1
        consecutive_empty_pages = 0
        max_consecutive_empty = 3  # Stop after 3 consecutive empty pages
        
        try:
            if not self.initialize_driver():
                print("❌ Failed to initialize Chrome driver")
                return []
            
            print(f"🚀 Starting scraping with batch size: {batch_size} jobs")
            
            with tqdm(total=max_pages, desc="Pages", unit="page", position=0) as page_pbar:
                page = 1
                while page <= max_pages and consecutive_empty_pages < max_consecutive_empty:
                    page_pbar.set_description(f"Page {page}")
                    scraped_jobs = self.scrape_jobs_from_search_page(page)
                    
                    if not scraped_jobs:
                        consecutive_empty_pages += 1
                        print(f"⚠️ Empty page {page} (consecutive: {consecutive_empty_pages})")
                        if consecutive_empty_pages >= max_consecutive_empty:
                            print(f"🛑 Stopping after {max_consecutive_empty} consecutive empty pages")
                            break
                        page += 1
                        page_pbar.update(1)
                        continue
                    else:
                        consecutive_empty_pages = 0  # Reset counter
                    
                    page_jobs = []
                    with tqdm(total=len(scraped_jobs), desc=f"Processing P{page}", unit="job", position=1, leave=False) as job_pbar:
                        for job_raw in scraped_jobs:
                            job_pbar.update(1)
                            processed_job = self.process_job_data(job_raw)
                            if processed_job:
                                all_jobs.append(processed_job)
                                page_jobs.append(processed_job)
                                current_batch_jobs.append(processed_job)
                                
                                # Send batch when it reaches batch_size
                                if len(current_batch_jobs) >= batch_size:
                                    # Save current batch to JSON
                                    self.save_jobs_to_json(current_batch_jobs, batch_number, is_batch=True)
                                    
                                    # Send to API
                                    if API_URL and API_KEY:
                                        self.send_batch_to_api(current_batch_jobs, batch_number)
                                    
                                    batch_number += 1
                                    current_batch_jobs = []
                    
                    page_pbar.set_postfix({
                        'Jobs this page': len(page_jobs),
                        'Total jobs': len(all_jobs),
                        'Current batch': len(current_batch_jobs),
                        'API Success': self.api_send_count,
                        'API Failed': self.api_failed_count
                    })
                    
                    page += 1
                    page_pbar.update(1)
                    
                    # Small delay between pages to avoid being blocked
                    time.sleep(2)
        
        except KeyboardInterrupt:
            print("\n⚠️ Scraping interrupted by user")
        except Exception as e:
            print(f"❌ Unexpected error during scraping: {e}")
        finally:
            self.close_driver()
        
        # Send remaining jobs in final batch
        if current_batch_jobs and API_URL and API_KEY:
            print(f"📤 Sending final batch of {len(current_batch_jobs)} jobs")
            self.save_jobs_to_json(current_batch_jobs, batch_number, is_batch=True)
            self.send_batch_to_api(current_batch_jobs, batch_number)
        
        # Save all jobs to final JSON
        if all_jobs:
            with tqdm(total=1, desc="Final save", unit="task") as final_pbar:
                final_pbar.set_description("Saving final JSON")
                filename = self.save_jobs_to_json(all_jobs, is_final=True)
                final_pbar.update(1)
                print(f"💾 Final JSON saved: {filename}")
        
        return all_jobs

    def save_jobs_to_json(self, jobs, batch_num=None, is_final=False, is_batch=False):
        """Save jobs to JSON file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if is_final:
            filename = f'renault_jobs_final_{timestamp}.json'
        elif is_batch:
            filename = f'renault_jobs_batch_{batch_num}_{timestamp}.json'
        else:
            filename = f'renault_jobs_page_{batch_num}_{timestamp}.json'
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(jobs, f, indent=2, ensure_ascii=False)
            return filename
        except Exception as e:
            print(f"❌ Error saving to {filename}: {e}")
            return None

def prepare_payload(job_list, id_user, company_name):
    """Prepare payload for API submission"""
    return {
        "id_user": id_user,
        "company": company_name,
        "datas": job_list
    }

def post_job_to_api(job_data):
    """Post job data to API using requests"""
    try:
        headers = {
            "X-API-KEY": API_KEY,
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        response = requests.post(API_URL, headers=headers, json=job_data, timeout=1000)
        response.raise_for_status()
        return True
    except Exception as e:
        print(f"❌ API Error: {e}")
        return False

def main():
    """Main execution function"""
    scraper = RenaultJobScraper()
    print("🚀 Starting Renault Group job scraping...")
    print(f"📡 API configured: {'✅' if API_URL and API_KEY else '❌'}")
    
    all_jobs = scraper.scrape_all_jobs(max_pages=42,batch_size=100)
    
    if not all_jobs:
        print("⚠️ No jobs found!")
        return
    
    print(f"\n✅ Successfully scraped {len(all_jobs)} jobs total")
    if all_jobs:
        print("\n📋 Sample job data:")
        sample_job = all_jobs[0]
        print(f"   Title: {sample_job['intitule']}")
        print(f"   Location: {sample_job['geo_ville']}")
        print(f"   Sectors: {sample_job['id_secteurs']}")
        print(f"   Contract: {sample_job['id_type_contrat']}")
        print(f"   Description length: {len(sample_job['description'])} chars")
    
    if API_URL and API_KEY:
        print(f"📡 API Summary:")
        print(f"   ✅ Successful sends: {scraper.api_send_count}")
        print(f"   ❌ Failed sends: {scraper.api_failed_count}")
    else:
        print("⚠️ API not configured - jobs saved to files only")

if __name__ == "__main__":
    main()