import requests
from bs4 import BeautifulSoup
import json
import re
import time
from urllib.parse import urljoin
import logging
from tqdm import tqdm
import os
import cloudscraper
from dotenv import load_dotenv
load_dotenv()
# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HeinekenJobScraper:
    def __init__(self):
        self.base_url = "https://careers.theheinekencompany.com"
        self.search_url = "https://careers.theheinekencompany.com/France/search/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Contract type mapping
        self.contract_types = {
            'Autre': 2, 'CDD': 1, 'CDI': 2, 'cdi': 2, 'cdd': 1,
            'Intérim': 4, 'interim': 4, 'Vacation': 4,
            'Contrat Professionnalisation': 6, 'Contrat profesionnalisation': 6,
            "Contrat d'apprentissage": 6, 'Stage': 8,
            'temporaryWork': 4, 'permanentContract': 2,
            'Travail temporaire': 4, 'SAISON': 4, 'ALTERNANCE': 6
        }
        
        # Sector mapping
        self.sectors = {
            'Supply Chain': [1, 20], 'Commerce': [6, 13],
            'Autres fonctions': [30, 15], 'Production': [22],
            'Maintenance': [2, 39, 24, 23], 'Qualité': [2, 39, 24, 23],
            'Sécurité': [2, 39, 24, 23], 'Industrie': [2, 39, 24, 23],
            'Auto': [2, 39, 24, 23]
        }

        # API Configuration
        self.api_url = os.getenv("API_URL")
        self.api_key = os.getenv("API_KEY")
        self.heineken_id = int(os.getenv("heiniken_id"))

    def get_salary_range_id(self, salary_from, salary_to):
        if salary_from is None or salary_to is None:
            return None
        if salary_from >= 15000 and salary_to <= 20000:
            return 1
        elif salary_from > 20000 and salary_to <= 30000:
            return 2
        elif salary_from > 30000 and salary_to <= 40000:
            return 3
        elif salary_from > 40000 and salary_to <= 50000:
            return 4
        elif salary_from > 50000 and salary_to <= 60000:
            return 5
        elif salary_from > 60000 and salary_to <= 70000:
            return 6
        elif salary_from > 70000 and salary_to <= 100000:
            return 7
        elif salary_from > 100000:
            return 8
        return None

    def extract_contract_type_id(self, job_title, job_description=""):
        text = f"{job_title} {job_description}".lower()
        for contract_type, type_id in self.contract_types.items():
            if contract_type.lower() in text:
                return type_id
        return 2

    def extract_sector_ids(self, department):
        if department in self.sectors:
            return self.sectors[department]
        return [2, 39, 24, 23]

    def extract_location_info(self, location_text):
        if not location_text:
            return None, None
        location_clean = location_text.replace(', FR', '').strip()
        postal_match = re.search(r'\b(\d{5})\b', location_clean)
        postal_code = postal_match.group(1) if postal_match else None
        city = re.sub(r',?\s*\d{5}', '', location_clean).strip()
        return city, postal_code

    def parse_job_row(self, row):
        try:
            title_link = row.find('a', class_='jobTitle-link')
            if not title_link:
                return None
            job_title = title_link.get_text(strip=True)
            job_url = urljoin(self.base_url, title_link.get('href', ''))
            department_span = row.find('span', class_='jobDepartment')
            department = department_span.get_text(strip=True) if department_span else 'Autres fonctions'
            location_span = row.find('span', class_='jobLocation')
            location = location_span.get_text(strip=True) if location_span else ''
            date_span = row.find('span', class_='jobDate')
            job_date = date_span.get_text(strip=True) if date_span else ''
            city, postal_code = self.extract_location_info(location)
            url_parts = job_url.split('/')
            reference = url_parts[-2] if len(url_parts) > 1 else job_title.replace(' ', '-')
            mission, searched_profile = self.get_job_details(job_url)
            time.sleep(0.5)
            job_data = {
                'reference': reference,
                'id_type_contrat': self.extract_contract_type_id(job_title, mission),
                'intitule': job_title,
                'geo_ville': city,
                'postale_code': postal_code,
                'id_fourchette_remuneration': None,
                'id_secteurs': self.extract_sector_ids(department),
                'id_sous_secteurs': self.extract_sector_ids(department),
                'mission': mission,
                'description': f"Poste: {job_title}\nType: Emploi\nVille: {city}\nEntreprise: Heineken",
                'searched_profile': searched_profile,
                'rep_url': job_url,
                'rep_mail': None,
                'duree_contrat': None,
                'company': 'Heineken'
            }
            return job_data
        except Exception as e:
            logger.error(f"Error parsing job row: {e}")
            return None

    def get_job_details(self, job_url):
        try:
            response = self.session.get(job_url, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')
            mission = ''
            searched_profile = ''
            description_span = soup.find('span', itemprop='description')
            if description_span:
                mission, searched_profile = self._extract_from_description_span(description_span)
            if not mission and not searched_profile:
                job_desc_div = soup.find('div', class_=re.compile(r'job.*description', re.I))
                if job_desc_div:
                    mission, searched_profile = self._extract_from_job_desc_div(job_desc_div)
            if not mission and not searched_profile:
                content_selectors = [
                    'div[class*="content"]', 'div[class*="description"]',
                    'div[class*="detail"]', 'div[class*="job"]',
                    'section[class*="content"]', 'section[class*="description"]',
                    '.job-content', '.job-description', '.position-description',
                    '#job-description', '#job-content'
                ]
                for selector in content_selectors:
                    elements = soup.select(selector)
                    for element in elements:
                        text_content = element.get_text(strip=True)
                        if len(text_content) > 100:
                            mission, searched_profile = self._extract_from_generic_content(text_content)
                            if mission or searched_profile:
                                break
                    if mission or searched_profile:
                        break
            if not mission and not searched_profile:
                all_containers = soup.find_all(['div', 'section', 'article'])
                for container in all_containers:
                    text_content = container.get_text(strip=True)
                    if len(text_content) > 200:
                        if container.find_parent(['nav', 'header', 'footer']):
                            continue
                        links = container.find_all('a')
                        if len(links) > 5 and sum(len(link.get_text()) for link in links) > len(text_content) * 0.5:
                            continue
                        mission, searched_profile = self._extract_from_generic_content(text_content)
                        if mission or searched_profile:
                            break
            if not mission and not searched_profile:
                content_tags = soup.find_all(['p', 'ul', 'ol', 'li'])
                substantial_content = []
                for tag in content_tags:
                    text = tag.get_text(strip=True)
                    if len(text) > 50:
                        substantial_content.append(text)
                if substantial_content:
                    all_text = ' '.join(substantial_content)
                    mission, searched_profile = self._extract_from_generic_content(all_text)
            if not mission and not searched_profile:
                for script in soup(["script", "style", "nav", "header", "footer"]):
                    script.decompose()
                all_text = soup.get_text(separator=' ', strip=True)
                if len(all_text) > 100:
                    mission, searched_profile = self._extract_from_generic_content(all_text)
            mission = self._clean_and_truncate(mission, 800)
            searched_profile = self._clean_and_truncate(searched_profile, 800)
            if mission and not searched_profile and len(mission) > 400:
                mission, searched_profile = self._balance_content(mission)
            elif searched_profile and not mission and len(searched_profile) > 400:
                searched_profile, mission = self._balance_content(searched_profile)
            return mission, searched_profile
        except Exception as e:
            logger.error(f"Error getting job details from {job_url}: {e}")
            return '', ''

    def _extract_from_description_span(self, description_span):
        try:
            full_text = description_span.get_text(separator=' ', strip=True)
            return self._split_content_by_keywords(full_text)
        except Exception as e:
            logger.error(f"Error extracting from description span: {e}")
            return '', ''

    def _extract_from_job_desc_div(self, job_desc_div):
        try:
            full_text = job_desc_div.get_text(separator=' ', strip=True)
            return self._split_content_by_keywords(full_text)
        except Exception as e:
            logger.error(f"Error extracting from job desc div: {e}")
            return '', ''

    def _extract_from_generic_content(self, text_content):
        return self._split_content_by_keywords(text_content)

    def _balance_content(self, long_content):
        if not long_content or len(long_content) < 200:
            return long_content, ''
        sentences = long_content.split('. ')
        if len(sentences) > 3:
            mid_point = len(sentences) // 2
            first_half = '. '.join(sentences[:mid_point]) + '.'
            second_half = '. '.join(sentences[mid_point:])
            return first_half.strip(), second_half.strip()
        mid_point = len(long_content) // 2
        for i in range(mid_point - 50, mid_point + 50):
            if i < len(long_content) and long_content[i] == ' ':
                return long_content[:i].strip(), long_content[i:].strip()
        return long_content[:mid_point].strip(), long_content[mid_point:].strip()

    def _split_content_by_keywords(self, full_text):
        if not full_text:
            return '', ''
        mission_keywords = [
            'vos missions', 'missions', 'responsabilités', 'votre rôle',
            'description du poste', 'activités principales', 'tâches',
            'objectifs', 'fonctions', 'poste à pourvoir', 'vous serez',
            'au quotidien', 'vos activités', 'description', 'rôle',
            'job description', 'responsibilities', 'duties', 'role',
            'what you will do', 'about the role', 'position overview'
        ]
        profile_keywords = [
            'profil recherché', 'profil souhaité', 'votre profil', 'compétences',
            'qualifications', 'expérience', 'formation', 'diplôme',
            'nous recherchons', 'candidat idéal', 'prérequis', 'exigences',
            'vous avez', 'vous êtes', 'vous disposez', 'requis',
            'what we are looking for', 'requirements', 'qualifications',
            'ideal candidate', 'you have', 'you are', 'skills',
            'experiencezak, required', 'education', 'preferred qualifications'
        ]
        text_lower = full_text.lower()
        mission_start = -1
        mission_end = len(full_text)
        mission_keyword_found = ''
        for keyword in mission_keywords:
            pos = text_lower.find(keyword.lower())
            if pos != -1:
                mission_start = pos
                mission_keyword_found = keyword
                break
        profile_start = -1
        profile_keyword_found = ''
        for keyword in profile_keywords:
            pos = text_lower.find(keyword.lower())
            if pos != -1:
                profile_start = pos
                profile_keyword_found = keyword
                if mission_start != -1 and pos > mission_start:
                    mission_end = pos
                break
        mission = ''
        searched_profile = ''
        if mission_start != -1:
            mission = full_text[mission_start:mission_end].strip()
            if mission.lower().startswith(mission_keyword_found.lower()):
                mission = mission[len(mission_keyword_found):].strip()
                if mission.startswith(':'):
                    mission = mission[1:].strip()
        if profile_start != -1:
            searched_profile = full_text[profile_start:].strip()
            if searched_profile.lower().startswith(profile_keyword_found.lower()):
                searched_profile = searched_profile[len(profile_keyword_found):].strip()
                if searched_profile.startswith(':'):
                    searched_profile = searched_profile[1:].strip()
        if not mission and not searched_profile and len(full_text) > 200:
            separators = ['\n\n', '•', '◦', '▪', '–', '—', 'Profil :', 'Mission :']
            for separator in separators:
                if separator in full_text:
                    parts = full_text.split(separator, 1)
                    if len(parts) == 2 and len(parts[0]) > 50 and len(parts[1]) > 50:
                        mission = parts[0].strip()
                        searched_profile = parts[1].strip()
                        break
            if not mission and not searched_profile:
                mid_point = len(full_text) // 2
                mission = full_text[:mid_point].strip()
                searched_profile = full_text[mid_point:].strip()
        elif not mission and not searched_profile:
            mission = full_text.strip()
        if mission and searched_profile:
            if len(mission) < 50 and len(searched_profile) > 300:
                combined = f"{mission} {searched_profile}"
                mission, searched_profile = self._balance_content(combined)
            elif len(searched_profile) < 50 and len(mission) > 300:
                combined = f"{mission} {searched_profile}"
                mission, searched_profile = self._balance_content(combined)
        return mission, searched_profile

    def _clean_and_truncate(self, text, max_length=800):
        if not text:
            return ''
        text = re.sub(r'\s+', ' ', text).strip()
        if len(text) > max_length:
            text = text[:max_length] + '...'
        return text

    def get_total_pages(self, soup):
        try:
            pagination = soup.find('ul', class_='pagination')
            if not pagination:
                return 1
            page_links = pagination.find_all('a')
            max_page = 1
            for link in page_links:
                href = link.get('href', '')
                if 'startrow=' in href:
                    startrow_match = re.search(r'startrow=(\d+)', href)
                    if startrow_match:
                        startrow = int(startrow_match.group(1))
                        page_num = (startrow // 25) + 1
                        max_page = max(max_page, page_num)
            return max_page
        except Exception as e:
            logger.error(f"Error extracting total pages: {e}")
            return 1

    def scrape_page(self, page_num=1):
        try:
            startrow = (page_num - 1) * 25
            params = {
                'q': '',
                'sortColumn': 'referencedate',
                'sortDirection': 'desc'
            }
            if startrow > 0:
                params['startrow'] = startrow
            response = self.session.get(self.search_url, params=params, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')
            job_rows = soup.find_all('tr', class_='data-row')
            jobs = []
            for row in job_rows:
                job_data = self.parse_job_row(row)
                if job_data:
                    jobs.append(job_data)
                time.sleep(0.5)
            return jobs, soup
        except Exception as e:
            logger.error(f"Error scraping page {page_num}: {e}")
            return [], None

    def prepare_payload(self, job_list, id_user, company_name):
        return {
            "id_user": id_user,
            "company": company_name,
            "datas": job_list
        }

    def post_job_to_api(self, job_data):
        try:
            scraper = cloudscraper.create_scraper()
            headers = {
                "X-API-KEY": self.api_key,
                "Accept": "application/json",
                "Content-Type": "application/json"
            }
            response = scraper.post(
                self.api_url,
                headers=headers,
                json=job_data,
                timeout=1000
            )
            response.raise_for_status()
            return True
        except Exception as e:
            logger.error(f"Error posting to API: {e}")
            return False

    def save_to_json(self, jobs, filename):
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(jobs, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error saving to JSON: {e}")

    def scrape_all_jobs(self, max_pages=None):
        all_jobs = []
        batch_size = 100
        batch_count = 0
        try:
            jobs, soup = self.scrape_page(1)
            all_jobs.extend(jobs)
            total_pages = self.get_total_pages(soup) if soup else 1
            if max_pages:
                total_pages = min(total_pages, max_pages)
            with tqdm(total=total_pages, desc="Scraping pages") as pbar:
                pbar.update(1)
                if len(all_jobs) >= batch_size:
                    batch_jobs = all_jobs[:batch_size]
                    all_jobs = all_jobs[batch_size:]
                    batch_count += 1
                    self.save_to_json(batch_jobs, f'heineken_jobs_batch_{batch_count}.json')
                    payload = self.prepare_payload(batch_jobs, self.heineken_id, 'Heineken')
                    self.post_job_to_api(payload)
                for page_num in range(2, total_pages + 1):
                    jobs, _ = self.scrape_page(page_num)
                    all_jobs.extend(jobs)
                    while len(all_jobs) >= batch_size:
                        batch_jobs = all_jobs[:batch_size]
                        all_jobs = all_jobs[batch_size:]
                        batch_count += 1
                        self.save_to_json(batch_jobs, f'heineken_jobs_batch_{batch_count}.json')
                        payload = self.prepare_payload(batch_jobs, self.heineken_id, 'Heineken')
                        self.post_job_to_api(payload)
                    time.sleep(1)
                    pbar.update(1)
            if all_jobs:
                batch_count += 1
                self.save_to_json(all_jobs, f'heineken_jobs_batch_{batch_count}.json')
                payload = self.prepare_payload(all_jobs, self.heineken_id, 'Heineken')
                self.post_job_to_api(payload)
            self.save_to_json(all_jobs, 'heineken_jobs.json')
            return all_jobs
        except Exception as e:
            logger.error(f"Error in scrape_all_jobs: {e}")
            if all_jobs:
                self.save_to_json(all_jobs, 'heineken_jobs.json')
            return all_jobs

if __name__ == "__main__":
    scraper = HeinekenJobScraper()
    jobs = scraper.scrape_all_jobs()