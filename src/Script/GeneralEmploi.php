<?php 

namespace App\Script;

use App\Entity\GeoDepartement;
use App\Entity\OffOffre;
use App\Entity\ScriptPrincipaux;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\UserUser;
use App\Service\ToolsService;
use XMLReader;
use SimpleXMLElement;
use Exception;
use Pharse;

class GeneralEmploi extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }

    public function GeneralEmploiScript()
    {

        $logId = $this->logScriptStart('GeneralEmploi'); // Démarre un log
        $offersProcessed = 0;
        $errorMessage = null;
        try {
        set_time_limit(0);
        error_reporting(E_ALL);
        //FONCTION UTILISEES DANS LE BATCH
        function extractEmailsFromString($sChaine)
        {
            if (false !== preg_match_all('`\w(?:[-_.]?\w)*@\w(?:[-_.]?\w)*\.(?:[a-z]{2,4})`', $sChaine, $aEmails)) {
                if (is_array($aEmails[0]) && sizeof($aEmails[0]) > 0) {
                    return array_unique($aEmails[0]);
                }
            }
            return null;
        }
        function multiexplode($delimiters, $string)
        {
            $ready = str_replace($delimiters, $delimiters[0], $string);
            $launch = explode($delimiters[0], $ready);
            return $launch;
        }
        function get_between($input, $start, $end)
        {
            $substr = substr($input, strlen($start)+strpos($input, $start), (strlen($input) - strpos($input, $end))*(-1));
            return $substr;
        }
        //VARIABLES
        $datas= array();
        $t_initial = time();
        $limit_nombre_integration = 100000;
        $nb_offres_importes = 0;
        $list_secteur = array();
        $nb_offres = 0;
        $tab = array();
        $newUrls = array();
        $i = 0;
        //VARIABLES INFOS DE CORRESPONDANCE MANQUANTES
        $secteur_manquant = array();
        $lieu_manquant = array();
        $formation_manquante = array();
        //$secteur_tab = $info[1];
        $corSecteur = array();
        $subject = "";
        $csv = 'jobinterim_secteurs.csv';
        $this->ouvrirCsvSecteur($csv, $corSecteur);
        //Tableau contenant les informations du client
        // (tableau de tableaux dans le cas o� il y a pls client en m�me temps)

        $corr_exp = array(
            '5 ans mini' => array(3),
            '1 ans' => array(1),
            'Débutant accepté' => array(1),
            '2 ans' => array(1),
            '1 an' => array(1),
            '2 à 3 ans' => array(1),
            '1 an souhaité' => array(1),
            '3 ans ou plus' => array(2),
            'débutant accepté' => array(1),
            '6 mois minimum' => array(1),
            '3 à 5 ans' => array(2),
            '2' => array(1),
            'Confirmé' => array(4),
            '2 à 5 ans' => array(2),
            '1an' => array(1),
            'débutant(e) accepté(e)' => array(1),
            '5 ans' => array(3),
            '3 mois souhaité' => array(1),
            '2 ans mini' => array(2),
            
        );


        $urls = array(
            array(
                // NomClient (???) :
                'url' => 'https://www.generalemploi.fr/offres-emploi',
                //Url de la page contenant les offres du client
                'id_user' => '1109773',
                //'id_user' => '1109804', //ID DE TEST POUR LE LOCAL, User qui est s�r d'�tre pr�sent sur ton local
                'name' => 'GeneralEmploi',
            ),
        );

        $i=100;

        foreach ($urls as $array) {
            $UserUser = $this->doctrine->getRepository(UserUser::class)->findOneBy(
                ['id_user' => $array['id_user']]
            );
            if (!$UserUser) {
                echo 'Erreur : l utilisateur n est pas reconnu : ' . $array['id_user'];
                continue;
            }

            //Curl sur la première page
            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => "https://www.generalemploi.fr/offres-emploi",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => "",
            CURLOPT_HTTPHEADER => array(
                "Postman-Token: 2418312a-d93b-4e27-a66f-c027598a57bf",
                "cache-control: no-cache"
            ),
            ));
            
            $response = curl_exec($curl);
            $err = curl_error($curl);
            
            curl_close($curl);
                
        

                $html = Pharse::str_get_dom($response);
                $out = '';
                $out .= "<br/><br/>START id_client : " . $array['id_user'] . " - Nom_client : " . $array['name'] . "<br/><br/>";

                //Récupération des urls de toute les pages ( 1 à 4)
                $page[] = "https://www.generalemploi.fr/offres-emploi";
                foreach($html('li[class="pager__item"]') as $index => $item ){
                
                    $page[] =  "https://www.generalemploi.fr/offres-emploi" . $item('a',0)->getAttribute('href');
            }

            
            //Parcours de toute les pages ( 1 à 4) 
            foreach($page as $t){
            
                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                    CURLOPT_URL => $t,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => "",
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => "GET",
                    CURLOPT_POSTFIELDS => "",
                    CURLOPT_HTTPHEADER => array(
                        "Postman-Token: 2418312a-d93b-4e27-a66f-c027598a57bf",
                        "cache-control: no-cache"
                    ),
                    ));
                    
                    $response = curl_exec($curl);
                    $err = curl_error($curl);
                    
                    curl_close($curl);
                
                    $curl = curl_init();


                    $html = Pharse::str_get_dom($response);
                    //Récupération de toute les offres
                    foreach($html('div[class="views-field views-field-title"]') as $index => $item ){
                        $offre[] = "https://www.generalemploi.fr" . $item ('a',0)->getAttribute('href');
                    }
                    
                
            }
                    //Parcours de toutes les offres
                    foreach( $offre as $off){
                    $i++;

                        $curl = curl_init();

                        curl_setopt_array($curl, array(
                        CURLOPT_URL => $off,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 30,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "GET",
                        CURLOPT_POSTFIELDS => "",
                        CURLOPT_HTTPHEADER => array(
                            "Postman-Token: f379abd8-363e-4495-9ba6-fcf6deec0ea6",
                            "cache-control: no-cache"
                        ),
                        ));
                        
                        $response = curl_exec($curl);
                        $err = curl_error($curl);
                        
                        curl_close($curl);
                    
                        $html = Pharse::str_get_dom($response);
                    
                        if($html('li[class="active"]')){
                            $data['intitule'] = trim($html('li[class="active"]',0)->getPlainText());
                        }
                        
                        $ref = "ge-" . $html('input[name="numero"]',0)->getAttribute('value');
                        $data['reference'] = $ref;
                        
                        if($html('div[class="region region-hp-contenu"]')){
                            $essai = $html('div[class="region region-hp-contenu"]',0)->getPlainText();
                        }
                    
                        if (stripos($essai, 'Type de contrat') !== false){
                            if (stripos($essai, "Secteur d'activité") !== false){
                                $type= strstr($essai,"Type de contrat");
                                $type= strstr($type, "Secteur" , true);
                                $type=str_ireplace("Type de contrat","", $type); 
                            } else{
                                $type= strstr($essai,"Type de contrat");
                                $type= strstr($type, "Date de publication" , true);
                                $type=str_ireplace("Type de contrat","", $type); 
                            }
                        }else{
                            $type = null;
                        }

                        //Type contrat
                        if(stripos($type, 'Intérim ') !== false)
                                { 
                                    $data['id_type_contrat'] = 4;
                                }
                                
                                else if(stripos($type, 'CDD') !== false)
                                { 
                                    $data['id_type_contrat'] = 1;
                                }
                                else if(stripos($type, 'CDI') !== false){
                                    $data['id_type_contrat'] = 2;
                                }



                                if (strpos($essai,"Description")){
                                    $posdet= strpos($essai,"Détail de");
                                }else{
                                    $posdet="1000000000000000";
                                }
                                
                                if (strpos($essai,"salaire")){
                                    $posal= strpos($essai,"Statut");
                                }else{
                                    $posal="1000000000000000";
                                }

                                //Lieux
                                if (stripos($essai, 'Lieu de travail') !== false){
                                
                                    if (stripos($essai, "Salaire") !== false){
                                        $lieux= strstr($essai,"Lieu de travail");
                                        $lieux= strstr($lieux, "Salaire" , true);
                                        
                                        if (stripos($lieux, "Description") !== false){
                                            $lieux= strstr($lieux, "Description" , true);
                                            $lieux=str_ireplace("Lieu de travail","", $lieux); 
                                        }else{
                                            $lieux=str_ireplace("Lieu de travail","", $lieux); 
                                        }
                                        
                                            
                                    }else if ( $posdet < $posal){
                                    
                                        $lieux= strstr($essai,"Lieu de travail");
                                        $lieux= strstr($lieux, "Description" , true);
                                        $lieux=str_ireplace("Lieu de travail","", $lieux); 
                                    }
                                    else{
                                        $lieux= strstr($essai,"Lieu de travail");
                                        $lieux=str_ireplace("Lieu de travail","", $lieux); 
                                    }
                                }else{
                                    $lieux = null;
                                }
                    
                                $ville_find = $this->find_city($lieux);
                                                if($ville_find) {
                                                    $data['geo_ville'] = $ville_find["ville"];
                                                    $data['id_departements'] = $ville_find["dep_id"];
                                                    $data['id_regions'] = $ville_find["region_id"];
                                                    $data['postale_code'] = $ville_find["pos_code"];
                                                } 
                                                
                                                
                                                
                                                else {
                                                    $dep_find = $this->find_dep($lieux);
                                                    if($dep_find) {
                                                        $data['id_departements'] = $dep_find["dep_id"];
                                                        $data['id_regions'] = $dep_find["region_id"];
                                                        $data['geo_ville'] = $dep_find["geo_ville"];
                                                        $data['postale_code'] = $dep_find["postale_code"];
                                                    }  else {
                                                        $array_villes = explode(" ", $lieux);
                                                        foreach($array_villes as $array_ville){
                                                            $ville_find = $this->find_city($array_ville);
                                                            if($ville_find){
                                                                $data['geo_ville'] = $ville_find["ville"];
                                                                $data['id_departements'] = $ville_find["dep_id"];
                                                                $data['id_regions'] = $ville_find["region_id"];
                                                                $data['postale_code'] = $ville_find["pos_code"];
                                                                break;
                                                            } else {

                                                                $data['geo_ville'] = $lieux;
                                                                $out .= "ville inconnue :".$lieux."\n";
                                                                $data['postale_code'] = null;
                                                                
                                                            }
                                                        }
                                                    }
                                                }
                                                                                
                                                // Missions
                                                if (mb_strpos($essai, "Compétences") !== false){
                                                
                                                    $mission = strstr($essai,"Description du poste");
                                                    $mission = strstr($mission,"Compétences",true);
                                                    $mission=str_ireplace("Description du poste","", $mission); 
                                                }else if (mb_strpos($essai, "Durée") !== false){
                                                
                                                    $mission = strstr($essai,"Description du poste");
                                                    $mission = strstr($mission,"Durée",true);
                                                    $mission=str_ireplace("Description du poste","", $mission); 
                                                }
                                                else if (mb_strpos($essai, "Profil") !== false){
                                                
                                                    $mission = strstr($essai,"Description du poste");
                                                    $mission = strstr($mission,"Profil",true);
                                                    $mission=str_ireplace("Description du poste","", $mission); 
                                                }else if (mb_strpos($essai, "Expérience") !== false){
                                                
                                                    $mission = strstr($essai,"Description du poste");
                                                    $mission = strstr($mission,"Expérience",true);
                                                    $mission=str_ireplace("Description du poste","", $mission); 
                                                }else{
                                                
                                                    $mission = strstr($essai,"Description du poste");
                                                    $mission=str_ireplace("Description du poste","", $mission); 
                                                }
                                                
                                                $mission = trim($mission);

                                                if (stripos($mission, "  ") !== false){
                                                    $mission=str_ireplace("  ","", $mission); 
                                                }

                                                if ($mission[strlen($mission) - 1] !="."){
                                                    $mission= $mission . ".";
                                                }
                                                $data['mission'] = $mission;
                                                



                                                //Profil
                                                if (mb_strpos($essai, "Profil ") !== false) {
                                                    $profil = strstr($essai,"Profil");
                                                // $profil = str_ireplace("Profil","", $profil); 
                                                

                                                    $profil = trim($profil);
                                                    if (stripos($profil, "  ") !== false){
                                                        $profil=str_ireplace("  ","", $profil); 
                                                    }
                                                    $profil = trim($profil);
            
            
                                                    if (stripos($profil, "  ") !== false){
                                                        $profil=str_ireplace("  ","", $profil); 
                                                    }
            
                                                    if ($profil[strlen($profil) - 1] !="."){
                                                        $profil= $profil . ".";
                                                    }
                                                }
                                                else if (mb_strpos($essai, "PROFIL") !== false) {
                                                    $profil = strstr($essai,"PROFIL");
                                                // $profil = str_ireplace("Profil","", $profil); 
                                                    

                                                    $profil = trim($profil);
                                                    if (stripos($profil, "  ") !== false){
                                                        $profil=str_ireplace("  ","", $profil); 
                                                    }
                                                    $profil = trim($profil);
            
            
                                                    if (stripos($profil, "  ") !== false){
                                                        $profil=str_ireplace("  ","", $profil); 
                                                    }
            
                                                    if ($profil[strlen($profil) - 1] !="."){
                                                        $profil= $profil . ".";
                                                    }
                                                }else{
                                                    $profil = null;
                                                }

                                            

                                                $data['searched_profile'] = $profil;
                                            
                                                $data['nom_societe']= "GeneralEmploi";
                                                
                                                $data['handicap'] = null;
                                                $data['anonym'] = null;


                                                //Secteur
                                                if (stripos($essai, "Secteur d'activité") !== false){
                                                    $secteur = strstr($essai,"Secteur d'activité");
                                                    $secteur = strstr($secteur,"Date de publication",true);
                                                    $secteur= str_ireplace("Secteur d'activité","", $secteur); 
                                                }else{
                                                    $secteur = $data['intitule'];
                                                }
                                                
                                                $secteur  = trim($secteur);
                                            
                                                if(isset($secteur)) {
                                                    $secteur_array = $this->remplirTableScriptSecteursAll($secteur, $array['id_user'],$data['intitule']);
                                                    $data['id_secteurs'] = $secteur_array[0];
                                                    $data['id_sous_secteurs'] = $secteur_array[1];
                                        
                                                }
                                            
                                                $data['id_fourchette_remuneration']= null;

                                                $debut = strstr($essai,"Date de début");
                                                $debut = strstr($debut,"Lieu de travail",true);
                                                $debut = str_ireplace("Date de début","", $debut);
                                            

                                                $data['date_debut_contrat']= $debut;

                                                $duree = strstr($essai,"Durée");
                                                $duree = strstr($duree,"Statut",true);
                                                
                                                if (stripos($duree, "Profil") !== false){
                                                    $duree= strstr($duree, "Profil" , true);
                                                    $duree = str_ireplace("Durée","", $duree);
                                                }else{
                                                $duree = str_ireplace("Durée","", $duree);
                                                }
                                            
                                            
                                                $data['duree_contrat']= $duree;

                                                if (stripos($essai, "Expérience") !== false){
                                                    $experience = strstr($essai, "Expérience");
                                                    $experience = str_ireplace("Expérience","", $experience);
                                                    
                                                    if(stripos($experience,  'Débutant')  !== false)
                                                    {  
                                                        $data['id_experience_requise']= array(1);
                                                    }
                                                    else if(stripos($experience,  '2')  !== false)
                                                    {  
                                                        $data['id_experience_requise']= array(2);
                                                    }
                                                    else if(stripos($experience,  '1')  !== false)
                                                    {  
                                                    $data['id_experience_requise']= array(1);
                                                    }
                                                    else if(stripos($experience,  '3')  !== false)
                                                    {  
                                                        $data['id_experience_requise']= array(2);
                                                    }
                            
                                                    else if(stripos($experience,  '5')  !== false)
                                                    {  
                                                        $data['id_experience_requise']= array(3);
                                                    }
                                                }else{
                                                    $data['id_experience_requise']= null;
                                                }
                                                
                                        
                                                $urlstat = '?utm_source=directemploi&utm_medium=jobboard&utm_campaign=directemploi&utm_content=' . $array['id_user'];
                                                $data['rep_url'] = $off . $urlstat;
                                            
                                        
                                                $data['rep_mail']= null;
                                                $datas[]=$data;
                        $offersProcessed++; // Compte les offres traitées


                    }
                    


                $info = $this->saveInDatabase($datas, $array['id_user'], $array['name']);
                $fin = time();
                $out .= $info[0];
                $idsOffresToConserve = $info[1];
                $entityManager = $this->doctrine->getManager();
                $out = $this->supprimerOffresV2($out, $offreMaj = 0, $offreCrees = 0, $idsOffresToConserve, $array['id_user'], $entityManager);
                // $out= $this->supprimerOffres($out, $offreMaj = 0, $offreCrees = 0, $info[1], $array['id_user']);

                
                }

            $this->saveInfoScript("GeneralEmploi", $out, $offreSupprimees = 0, $offreMaj, $offreCrees, $subject);
            $this->logScriptEnd($logId, true, $offersProcessed, null); // Enregistre la fin avec succès
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $this->logScriptEnd($logId, false, $offersProcessed, $errorMessage); // Enregistre la fin avec erreur
        }
            }
    private function logScriptStart($scriptName)
    {
        $connection = $this->doctrine->getConnection();

        $queryCheck = "
            SELECT id 
            FROM script_logs 
            WHERE script_name = :script_name
            LIMIT 1
        ";
        $existingLog = $connection->fetchOne($queryCheck, [
            'script_name' => $scriptName,
        ]);

        if ($existingLog) {
            $queryUpdate = "
                UPDATE script_logs
                SET start_time = NOW(),
                    end_time = NULL,
                    status = FALSE,
                    offers_processed = 0,
                    error_message = NULL,
                    created_at = NOW()
                WHERE id = :id
            ";
            $connection->executeStatement($queryUpdate, [
                'id' => $existingLog,
            ]);

            return $existingLog;
        } else {
            $queryInsert = "
                INSERT INTO script_logs (script_name, start_time, status)
                VALUES (:script_name, NOW(), FALSE)
            ";
            $connection->executeStatement($queryInsert, [
                'script_name' => $scriptName,
            ]);

            return $connection->lastInsertId();
        }
    }

    private function logScriptEnd($logId, $status, $offersProcessed, $errorMessage = null)
    {
        $connection = $this->doctrine->getConnection();
        $query = "
            UPDATE script_logs
            SET end_time = NOW(),
                status = :status,
                offers_processed = :offers_processed,
                error_message = :error_message
            WHERE id = :id
        ";
        $connection->executeStatement($query, [
            'status' => $status,
            'offers_processed' => $offersProcessed,
            'error_message' => $errorMessage,
            'id' => $logId,
        ]);
    }
}
