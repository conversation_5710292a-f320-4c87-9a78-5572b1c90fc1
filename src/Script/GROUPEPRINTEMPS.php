<?php

namespace App\Script;

use App\Entity\UserUser;
use App\Service\ToolsService;
use Doctrine\Persistence\ManagerRegistry;
use XMLReader;
use SimpleXMLElement;

class GROUPEPRINTEMPS extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }

    public function GROUPEPRINTEMPSScript()
    {
        $logId = $this->logScriptStart('GROUPEP RINTEMPS'); // Démarre un log
        $offersProcessed = 0;
        $errorMessage = null;
        try {
        set_time_limit(0);
        error_reporting(E_ALL);

        $t_initial = time();
        $out = '';
        $subject = '';
        $offreMaj = 0;
        $offreCrees = 0;
        $offreSupprimees = 0;



        $urls = [
            [
                'id_user' => '1233095',
                'url' => "http://www.mytalentplug.com/xml.aspx?jbID=pTrVyRtYtwU=",
                'name' => 'GROUPE PRINTEMPS',
            ],
        ];

        foreach ($urls as $array) {
            $datas = [];
            $source = $array['url'];

            // Utilisation de cURL via PHP pour obtenir le flux XML
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $source);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSLVERSION, 0);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_USERAGENT, 'direct-emploi');
            $data = curl_exec($ch);
            curl_close($ch);

            $destination = "/tmp/testtmp" . date("H:i:s") . ".php";
            file_put_contents($destination, $data);

            $XMLReader = new XMLReader();
            $XMLReader->open($destination);

            // Vérification de l'utilisateur
            $UserUser = $this->doctrine->getRepository(UserUser::class)->find($array['id_user']);
            if (!$UserUser) {
                echo 'Erreur : utilisateur non reconnu :' . $array['id_user'];
                continue;
            }

            $out .= "<br/><br/>START id_client : " . $array['id_user'] . " - Nom_client : " . $array['name'] . " - Flux : " . $array['url'] . "<br/><br/>";

            while ($XMLReader->read() && $XMLReader->name !== "offre");
            while ($XMLReader->name === "offre") {
                $item = new SimpleXMLElement($XMLReader->readOuterXML());

                // Vérifie si le client_id est celui recherché
                if ((string)$item->client_id !== "1233095") {
                    $XMLReader->next("offre");
                    continue; // Passe à l'offre suivante si le client_id ne correspond pas
                }
                $data = [];

                // Récupération des informations générales
                $data['reference'] = trim((string)$item->reference);
                $data['intitule'] = trim((string)$item->intitule);
                $data['id_type_contrat'] =  trim((string)$item->job_contract);

                // Secteur d'activité
                $data['id_secteurs'] = 21;
                $data['id_fourchette_remuneration'] = null;

                    if (isset($item->salary)) {
                $salaryFrom = (float)trim((string)$item->salary->salary_from);
                $salaryTo = (float)trim((string)$item->salary->salary_to);
                $salaryPer = strtoupper(trim((string)$item->salary->salary_per));

                    if ($salaryPer === 'HEURE') {
                        // Conversion heure -> année : 13€/heure -> 13 * 35 * 52
                        $salaryFrom = $salaryFrom * 35 * 52;
                        $salaryTo = $salaryTo * 35 * 52;
                    } elseif ($salaryPer === 'MOIS') {
                        // Conversion mois -> année : 2000€/mois -> 2000 * 12
                        $salaryFrom = $salaryFrom * 12;
                        $salaryTo = $salaryTo * 12;
                    }

                    // Maintenant tout est en € annuel
                    if ($salaryFrom >= 15000 && $salaryTo <= 20000) {
                        $data['id_fourchette_remuneration'] = 1;
                    } elseif ($salaryFrom > 20000 && $salaryTo <= 30000) {
                        $data['id_fourchette_remuneration'] = 2;
                    } elseif ($salaryFrom > 30000 && $salaryTo <= 40000) {
                        $data['id_fourchette_remuneration'] = 3;
                    } elseif ($salaryFrom > 40000 && $salaryTo <= 50000) {
                        $data['id_fourchette_remuneration'] = 4;
                    } elseif ($salaryFrom > 50000 && $salaryTo <= 60000) {
                        $data['id_fourchette_remuneration'] = 5;
                    } elseif ($salaryFrom > 60000 && $salaryTo <= 70000) {
                        $data['id_fourchette_remuneration'] = 6;
                    } elseif ($salaryFrom > 70000 && $salaryTo <= 100000) {
                        $data['id_fourchette_remuneration'] = 7;
                    } elseif ($salaryFrom > 100000) {
                    $data['id_fourchette_remuneration'] = 8;
                }
            }


                if (isset($item->secteurs->secteur_domaine) && trim((string)$item->secteurs->secteur_domaine) !== '') {
                    $data['id_secteurs'] = [trim((int)$item->secteurs->secteur_domaine)];
                } elseif (isset($item->secteurs->secteur_domaine2) && trim((string)$item->secteurs->secteur_domaine2) !== '') {
                    $data['id_secteurs'] = [trim((int)$item->secteurs->secteur_domaine2)];
                } elseif (isset($item->secteurs->secteur_domaine3) && trim((string)$item->secteurs->secteur_domaine3) !== '') {
                    $data['id_secteurs'] = [trim((int)$item->secteurs->secteur_domaine3)];
                }

                // URLs
                $data['rep_url'] = trim((string)$item->rep_url);;
                $data['rep_mail'] =trim((string)$item->rep_mail);

                // Profil et mission
                $data['searched_profile'] = trim((string)$item->profil);
                $mission = trim((string)$item->job_description);

                $data['mission'] = $mission;

                // Localisation
                $data['geo_ville'] = trim((string)$item->geo_ville);
                $data['postale_code'] = "";
                $data['company'] = "GROUPEPRINTEMPS";
                $data['intitule'] .= ' - ' . $data['geo_ville'];

                // Ajout des données
                $datas[] = $data;
                // Incrémentation du compteur
                $offersProcessed++; // <- Correctement incrémenté ici
                $XMLReader->next("offre");
            }


            $info = $this->saveInDatabase($datas, $array['id_user'], $array['name']);
            $this->logScriptEnd($logId, true, $offersProcessed, null); // Passe la bonne valeur de $offersProcessed

            $out .= $info[0];
            $entityManager = $this->doctrine->getManager();

            $out = $this->supprimerOffresV2($out, $offreMaj, $offreCrees, $info[1], $array['id_user'],$entityManager);
        }

        $this->envoyerMail($out, "GROUPEPRINTEMPS", $offreMaj, $offreCrees);
        $this->saveInfoScript("GROUPEPRINTEMPS", $out, $subject, $offreSupprimees, $offreMaj, $offreCrees);
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $this->logScriptEnd($logId, false, $offersProcessed, $errorMessage);
        }
    }
    private function logScriptStart($scriptName)
    {
        $connection = $this->doctrine->getConnection();

        // Vérifier si une ligne pour ce script existe déjà
        $queryCheck = "
        SELECT id 
        FROM script_logs 
        WHERE script_name = :script_name
        LIMIT 1
    ";
        $existingLog = $connection->fetchOne($queryCheck, [
            'script_name' => $scriptName,
        ]);

        if ($existingLog) {
            // Mettre à jour les champs de la ligne existante
            $queryUpdate = "
            UPDATE script_logs
            SET start_time = NOW(),
                end_time = NULL,
                status = FALSE,
                offers_processed = 0,
                error_message = NULL,
                created_at = NOW()
            WHERE id = :id
        ";
            $connection->executeStatement($queryUpdate, [
                'id' => $existingLog,
            ]);

            return $existingLog; // Retourner l'ID de la ligne mise à jour
        } else {
            // Insérer une nouvelle ligne si aucune ligne existante n'est trouvée
            $queryInsert = "
            INSERT INTO script_logs (script_name, start_time, status)
            VALUES (:script_name, NOW(), FALSE)
        ";
            $connection->executeStatement($queryInsert, [
                'script_name' => $scriptName,
            ]);

            return $connection->lastInsertId(); // Retourner l'ID de la nouvelle ligne
        }
    }


    private function logScriptEnd($logId, $status, $offersProcessed, $errorMessage = null)
    {
        $connection = $this->doctrine->getConnection();
        $query = "
            UPDATE script_logs
            SET end_time = NOW(),
                status = :status,
                offers_processed = :offers_processed,
                error_message = :error_message
            WHERE id = :id
        ";
        $connection->executeStatement($query, [
            'status' => $status,
            'offers_processed' => $offersProcessed,
            'error_message' => $errorMessage,
            'id' => $logId
        ]);
    }
    public function saveInDatabase($datas, $id_user, $name)
    {
        return parent::saveInDatabase($datas, $id_user, $name);
    }

    public function supprimerOffres($out, $nombreOffreMaj, $nombreOffreCrees, $idsOffresToConserve, $id_user)
    {
        return parent::supprimerOffres($out, $nombreOffreMaj, $nombreOffreCrees, $idsOffresToConserve, $id_user);
    }

    private function envoyerMail($out, $name_crawl, $nb_offres_maj, $nb_offres_crees)
    {
        global $subject;
        global $t_initial;

        if ($nb_offres_maj > 1 || $nb_offres_crees > 1) {
            $to = '<EMAIL>';
            $subject = 'Crawl import ' . $name_crawl . " offre : " . ($nb_offres_maj + $nb_offres_crees);

            if (isset($t_initial)) {
                $t_mail = time();
                $subject .= "  temps total :" . $this->time_elapsed_A($t_mail - $t_initial);
            }

            $headers = 'From: <EMAIL>' . "\r\n";
            $headers .= 'Reply-To: <EMAIL>' . "\r\n";
            $headers .= 'MIME-Version: 1.0' . "\r\n";
            $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
            mail($to, $subject, $out, $headers);
        } else {
            $to = '<EMAIL>';
            $subject = '!!!!!!!!Crawl vide!!!!!!! ' . $name_crawl;
            $headers = 'From: <EMAIL>' . "\r\n";
            $headers .= 'Reply-To: <EMAIL>' . "\r\n";
            $headers .= 'MIME-Version: 1.0' . "\r\n";
            $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
            mail($to, $subject, $out, $headers);
        }
    }

    public function saveInfoScript($nom_script, $out, $offresSuppprimees, $nb_offres_maj, $nb_offres_crees, $id_user = null, $subject = "crawl")
    {
        return parent::saveInfoScript($nom_script, $out, $offresSuppprimees, $nb_offres_maj, $nb_offres_crees, $id_user, $subject);
    }

}
