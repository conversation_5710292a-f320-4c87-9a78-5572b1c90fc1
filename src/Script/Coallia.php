<?php

namespace App\Script;

use App\Entity\UserUser;
use App\Service\ToolsService;
use Doctrine\Persistence\ManagerRegistry;
use XMLReader;
use SimpleXMLElement;
use Exception;

class Coallia extends ToolsService
{
    public function __construct(ManagerRegistry $doctrine)
    {
        parent::__construct($doctrine);
    }

    public function CoalliaScript()
    {

        $logId = $this->logScriptStart('Coallia'); // Démarre un log
        $offersProcessed = 0;
        $errorMessage = null;
        try {
        $url = "https://batchaws.adcourier.com/services/?q=U2FsdGVkX1_lPkPjW38d0cfDIybdM6_uu1Jn_CKjH7gAHHfvK_kZOIpL5aAqax1nEt4UAIj37ug";

        // Lecture du flux XML
        $XMLReader = new XMLReader;
        $XMLReader->open($url);

        $listOffers = [];
        while ($XMLReader->read() && $XMLReader->name !== "client");
        while ($XMLReader->name === "client") {
            try {
                $item = new SimpleXMLElement($XMLReader->readOuterXML());
            } catch (Exception $e) {
                $XMLReader->next("client");
                continue;
            }

            foreach ($item as $offre) {
                $id_index = $offre->id_recruteur ?? null;
                if ($id_index == 1255433){
                $listOffers[(int)$id_index][] = $offre;
                }
            }

            $XMLReader->next("client");
        }
        $XMLReader->close();



            foreach ($listOffers as $indexClient => $clientOffers) {




                $UserUser = $this->doctrine->getRepository(UserUser::class)->findOneBy(['id_user' => $indexClient]);
                if (!$UserUser) {
                    throw new Exception("Utilisateur non trouvé : {$indexClient}");
                }

                $datas = [];
                foreach ($clientOffers as $clientOffre) {
                    if (strtolower((string)$clientOffre->id_recruteur) == 1255433) {
                        $data['reference'] = (string)$clientOffre->reference_offre ?? null;
                        $data['intitule'] = (string)$clientOffre->libelle_poste ?? null;
                        $data['id_type_contrat'] = (int)$clientOffre->type_contrat ?? null;
                        $data['mission'] = (string)$clientOffre->descriptif_poste ?? null;
                        $data['geo_ville'] = (string)$clientOffre->location_city ?? null;
                        $data['rep_url'] = (string)$clientOffre->url ?? null;
                        $data['rep_mail'] = (string)$clientOffre->aplitrakmail;
                        $data['nom_societe'] = (string)$clientOffre->companyname ?? null;
                        $data['company'] = (string)$clientOffre->companyname ?? null;
                        $data['id_departements'] = (int)$clientOffre->location_departement ?? null;
                        $data['id_secteurs'] = [(int)$clientOffre->secteur] ;
                        $data['id_sous_secteurs'] = [(int)$clientOffre->secteur];
                        $data['id_fourchette_remuneration'] = 0;
                        $data['date_debut_contrat'] = (string)$clientOffre->date_debut ?? null;
                        $data['duree_contrat'] = (string)$clientOffre->periode ?? null;
                        $offersProcessed++;
                        if ($data) {
                            $datas[] = $data;

                        }
                    }



                }

                $info = $this->saveInDatabase($datas, 1255433, "Coallia");
                $this->logScriptEnd($logId, true, $offersProcessed, null); // Passe la bonne valeur de $offersProcessed

                $idsOffresToConserve = $info[1];
                $entityManager = $this->doctrine->getManager();
                $this->supprimerOffresV2("", 0, 0, $idsOffresToConserve, $indexClient, $entityManager);


        } } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $this->logScriptEnd($logId, false, $offersProcessed, $errorMessage);
        }

    }



    private function logScriptStart($scriptName)
    {
        $connection = $this->doctrine->getConnection();

        // Vérifier si une ligne pour ce script existe déjà
        $queryCheck = "
        SELECT id 
        FROM script_logs 
        WHERE script_name = :script_name
        LIMIT 1
        ";
        $existingLog = $connection->fetchOne($queryCheck, [
            'script_name' => $scriptName,
        ]);

        if ($existingLog) {
            // Mettre à jour les champs de la ligne existante
            $queryUpdate = "
            UPDATE script_logs
            SET start_time = NOW(),
                end_time = NULL,
                status = FALSE,
                offers_processed = 0,
                error_message = NULL,
                created_at = NOW()
            WHERE id = :id
            ";
            $connection->executeStatement($queryUpdate, [
                'id' => $existingLog,
            ]);

            return $existingLog; // Retourner l'ID de la ligne mise à jour
        } else {
            // Insérer une nouvelle ligne si aucune ligne existante n'est trouvée
            $queryInsert = "
            INSERT INTO script_logs (script_name, start_time, status)
            VALUES (:script_name, NOW(), FALSE)
            ";
            $connection->executeStatement($queryInsert, [
                'script_name' => $scriptName,
            ]);

            return $connection->lastInsertId(); // Retourner l'ID de la nouvelle ligne
        }
    }

    private function logScriptEnd($logId, $status, $offersProcessed, $errorMessage = null)
    {
        $connection = $this->doctrine->getConnection();
        $query = "
            UPDATE script_logs
            SET end_time = NOW(),
                status = :status,
                offers_processed = :offers_processed,
                error_message = :error_message
            WHERE id = :id
        ";
        $connection->executeStatement($query, [
            'status' => $status,
            'offers_processed' => $offersProcessed,
            'error_message' => $errorMessage,
            'id' => $logId
        ]);
    }


}
