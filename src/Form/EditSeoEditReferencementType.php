<?php

namespace App\Form;

use App\Entity\SeoEditReferencement;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\Range;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;

class EditSeoEditReferencementType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $isSearchForm = $options['is_search_form'] ?? false;

        if ($isSearchForm) {
            $builder
                ->add('url', TextType::class, [
                    'required' => false,
                ])
                ->add('nom', TextType::class, [
                    'required' => false,
                ])
                ->add('edit_app_id_app', IntegerType::class, [
                    'required' => false,
                ]);
        } else {
            $builder
                ->add('url', TextType::class, [
                    'constraints' => [
                        new NotBlank(),
                    ],
                    'required'   => false
                ])
                ->add('nom', TextType::class, [
                    'required'   => false
                ])
                ->add('code', TextType::class, [
                    'required'   => false
                ])
                ->add('photo_path', TextType::class, [
                    'required'   => false
                ])
                ->add('keywords', TextType::class, [
                    'required'   => false
                ])
                ->add('paragraphe_titre', TextareaType::class, [
                    'required'   => false,
                    'label'      => 'titre'
                ])
                ->add('paragraphe_contenu', TextareaType::class, [
                    'required'   => false,
                    'label'      => 'contenu'
                ])
                ->add('edit_app_id_app', IntegerType::class, [
                    'constraints' => [
                        new Range([
                            'min' => 1,
                            'max' => 9,
                            'minMessage' => 'La valeur doit être au moins {{ limit }}.',
                            'maxMessage' => 'La valeur doit être au plus {{ limit }}.',
                        ]),
                    ],
                    'required'   => false
                ]);
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => SeoEditReferencement::class,
            'is_search_form' => false, 
        ]);
    }
}

