<?php

namespace App\Form;

use Doctrine\DBAL\Types\TextType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CandidatOffreRechercheType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('Typedecontrat', ChoiceType::class, [
                'choices'  => [
                    'CDD' => 1,
                    'CDI' => 2,
                    'FreeLance / Indépendant' => 3,
                    'Interim' => 4,
                    'Alternance' => 6,
                    'Stage' => 8,
                ],
                'placeholder' =>  'Type de contrat',
            ])
            ->add('Localisation', TextType::class, [
                'placeholder' => 'Localisation'
            ])
            ->add('Poste recherché', TextType::class, [
                'placeholder' => 'Poste recherché'
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            // Configure your form options here
        ]);
    }
}
