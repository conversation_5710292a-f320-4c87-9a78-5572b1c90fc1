{# {% extends 'directemploi/new_layout.html.twig' %}
{% set page = app.request.query.get('page', 'initial') %}
{% block title %}{{newTitle}}{% endblock %}
{% block title_og %}{{newTitle}}{% endblock %}
{% block title_twitter %}{{newTitle}}{% endblock %}

{% set description_seo = "Toute l'actualité emploi. Restez informé à traver nos articles." ~ " Theme " ~ theme_recherche %}
{% block meta_description %}{{ description_seo[:155] }}{% endblock %}
{% block meta_description_og %}{{ description_seo[:155] }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo[:155] }}{% endblock %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/new_actualite.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_entreprises.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_responsive.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_offres_stage_alternance.min.css') }}">

    <style>

    .pagination {
    width: 100%;
    height: 100%;
    display: flex;
      background-color: #44A2C6;
    float: none;
	}

	.pagination a.active {
		background-color: white;
        color: #4ba8c5;
	}

	.pagination:hover {
        background-color: #44A2C6;
	}

    a {
        color: #4ba8c5;
    }
    </style>
{% endblock %}

{% block content %}
<section>
<div class="container liste_entreprises">
    {% if breadcrumb is defined %}
        {% include '/_partials/_breadcrumb.html.twig'  %}
    {% endif %}
    <br/>
	<h1 class="ordi" style="text-align: center; font-size: 28px">
		Les actualités de l'emploi
    </h1>
    <hr>
    <div class="article_offres-stage">
        <p>
            Chaque semaine, vous pouvez découvrir dans notre rubrique <strong>« actualités »</strong>, en une de Direct Emploi, <strong>nos nouveaux articles</strong>. 
            Direct emploi étant un site généraliste, nous traitons <strong>tous les thèmes de l’emploi et du recrutement</strong> et pour tous les domaines d’activité. 
            Tous les sujets abordés correspondent le plus souvent aux nombreuses questions que vous vous posez comme par exemple, les nouveaux métiers, les régions qui recrutent,
            les villes où il fait bon vivre et travailler, les secteurs d’activité porteurs, sans oublier les problématiques spécifiques concernant les jeunes diplômés qui 
            démarrent leur carrière, les sans diplômes et les profils séniors. <br><br>Nous tentons de vous répondre avec notre expérience de plus de <strong>20 ans d’existence
            sur le marché de l’emploi et du recrutement</strong> sur internet. Les entreprises font aussi parler d’elles et <strong>grâce à nos interviews</strong>, 
            elles présentent leur politique image employeur et leur processus de recrutement pour les rejoindre.
        </p>
    </div>
    <hr>
	<div class="contenu">		
		<div class="box_gris box_rech_actus" id='recherche-article'>
			<form method="post" id="form" style="text-align: center; margin-bottom: 1em;">
                <label>Rechercher un article :</label>
			    <input type="text" name="mot_cle"/>
                <input type="submit" name="commit" value="Rechercher" class="submit sub_small"/>
            </form>
		</div>
        <div class="div_theme_article_list">
            <h2 style="text-align: center; font-size: 28px; margin-top: 0.4em;">Les thèmes Emploi</h2>
            <div class="container_article_theme" style="grid-gap: 0;">
                <span class="theme_list"><a href="/article/list?mot_cle=Emploi">Emploi</a></span>
                <span class="theme_list"><a href="/article/list?mot_cle=Interim">Interim</a></span>
                <span class="theme_list"><a href="/article/list?mot_cle=Stage">Stage</a></span>
                <span class="theme_list"><a href="/article/list?mot_cle=Alternance">Alternance</a></span>
                <span class="theme_list"><a href="/article/list?mot_cle=Formation_continue">Formation</a></span>
                <span class="theme_list"><a href="/article/list?mot_cle=Ville">Ville | Région</a></span>
                <span class="theme_list"><a href="/article/list?mot_cle=Métiers">Métiers</a></span>
                <span class="theme_list"><a href="/article/list?mot_cle=Handicap">Handicap</a></span>
            </div>
        </div>


        <div id="zoneListingArticles" class="margT">
            <div id="liste_offres" class="mag_liste">
                <div class="table list" id="tableListingArticles">
                    {% for article in articlesDTO %}
                    <article>
                        <div class="row">
                            <div class="div_actus">
                                <div class="img_actus">
                                    <div class="img">
                                        <a href={{ "/article/" ~ article.id ~ "/" ~ strtourl(article.titre) }}>
                                            <img src={{ "/uploads/article/" ~ article.image_petite }} alt="Image Article" style="max-width: 94%;"/>
                                        </a>
                                    </div>
                                </div>
                                <div class="texte_actus">
                                    <div class="intitule">
                                        <a href={{ "/article/" ~ article.id ~ "/" ~ strtourl(article.titre) }} title="{{ article.titre }}">{{article.titre}}</a>
                                    </div>
                                    <span>thème : 
                                        <a href={{ "/article/list?mot_cle=" ~ theme_recherche }}> 
                                            {{ theme_recherche }}
                                        </a> 
                                    {% if app.user and "ROLE_ADMIN" in app.user.getRoles() %}
                                       - id | {{article.id}}
                                    {% endif %}
                                    </span>
                                    <div class="text" style='margin-left:0px;width: 600px;text-align: justify;'>
                                        {{article.chapo|u.truncate(250, '...', false)}}
                                    </div>	
                                    <p style="margin-top:5px;font-weight:bold"> <a href={{ "/article/" ~ article.id ~ "/" ~ strtourl(article.titre) }}>Lire l'article</a> </p>
                                </div>
                            </div>
                        </div>
                    </article>
                    {% endfor %}
                </div>
                <div align="center" id="pagination_de">
                    {{ knp_pagination_render(articlesDTO, 'directemploi/composants/paginationTemplate.html.twig') }}
                </div>
		    </div>
	    </div>

    </div>
</div>
</section>

{% endblock %} #}
{# {% extends 'carriereBTP/base.html.twig' %} #}
{% extends 'directemploi/new_layout.html.twig' %}
{% set page = app.request.query.get('page', 'initial') %}

{% block title %}Les actualités du monde de l'emploi, du recrutement et de la formation | Direct Emploi | Page : {{ page }}{% endblock %}
{% block title_og %}Les actualités du monde de l'emploi, du recrutement et de la formation | Direct Emploi | Page : {{ page }}{% endblock %}
{% block title_twitter %}Les actualités du monde de l'emploi, du recrutement et de la formation | Direct Emploi | Page : {{ page }}{% endblock %}


{% set description_seo = "Toute l'actualité emploi. Restez informé à traver nos articles, conseils et interviews sur le recrutement pour mieux appréhender le marché de l'emploi." ~ " Theme " ~ theme_recherche ~ " | Page : " ~ page %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/new_actualite.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_entreprises.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_responsive.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_offres_stage_alternance.min.css') }}">

    <style>

    .pagination {
    width: 100%;
    height: 100%;
    display: flex;
    background: none;
    float: none;
	}

    .pagination a {
        color: #44a2c6;
    }

	.pagination:hover {
        background: none;
	}

    a {
        color: #4ba8c5;
    }
    
    a:hover {
        color: #4ba8c5;
    }

    .actualites-header .header-h1 {
        margin-top: 105px;
        margin-bottom: 100px;
        font-size: 1.75rem!important;
        font-weight: 600;
    }
    .conseils-cards img {
        width: 100%; /* ou une taille fixe comme '300px' */
        height: auto; /* pour garder les proportions */
        max-width: 300px; /* Limiter la largeur si nécessaire */
        object-fit: cover; /* ou 'contain' selon votre besoin */
    }
    .card p{
        font-size: 13px!important;
    }
    .card h3{
        font-weight: 600!important;
        font-size: 20px;
        line-height: 1.4;
    }
    </style>
{% endblock %}

{% block content %}

<div class="is-sticky">
<section class="actualites-header" id="" style="background-image: url('{{ asset('directEmploi_new/Front/images/actualites-de.jpg') }}');">
    <h1 class="text-center text-uppercase text-white header-h1"><span style="color:#44a2c6">Actu</span>alités</h1>
    <div class="header-text">
        <div class="container ">
            <h1 class="text-uppercase text-white" style="font-size: 1.25rem; line-height: 1.5;">Retrouver nos articles et interviews</h1>
            <p style="color: white;">
                Chaque semaine, vous pouvez découvrir dans notre rubrique <strong>« actualités »</strong>, en une de Direct Emploi, <strong>nos nouveaux articles</strong>. 
                Direct emploi étant un site généraliste, nous traitons <strong>tous les thèmes de l’emploi et du recrutement</strong> et pour tous les domaines d’activité. 
                Tous les sujets abordés correspondent le plus souvent aux nombreuses questions que vous vous posez comme par exemple, les nouveaux métiers, les régions qui recrutent,
                les villes où il fait bon vivre et travailler, les secteurs d’activité porteurs, sans oublier les problématiques spécifiques concernant les jeunes diplômés qui 
                démarrent leur carrière, les sans diplômes et les profils séniors. <br><br>Nous tentons de vous répondre avec notre expérience de plus de <strong>20 ans d’existence
                sur le marché de l’emploi et du recrutement</strong> sur internet. Les entreprises font aussi parler d’elles et <strong>grâce à nos interviews</strong>, 
                elles présentent leur politique image employeur et leur processus de recrutement pour les rejoindre.
            </p>
        </div>
    </div>
</section>
<section class="recherche-offre mb-3">
    <div class="container" style="padding-top: 1rem; padding-bottom: 1rem;">
    <div class=" offres_listing_page-offres">
        {% if breadcrumb is defined %}
            {% include '/_partials/_breadcrumb.html.twig'  %}
        {% endif %}

    </div>
    <div class=" ">
        <form method="post" id="form">
            <div class="row align-items-end">
                <div class="col-lg-9">
                    <div class="form-group mb-2">
                        <label for="inputarticle">Rechercher un article</label>
                        <input type="text" class="form-control" name="mot_cle" aria-describedby="articleHelp" placeholder="Nom de l'article">
                    </div>
                </div>
                <div class="col-lg-2">
                    <div class="text-center mb-2">
                        <button style="background-color: #44A2C6; margin-left: 1em; color: white;" type="submit" class="btn w-100 px-4 button-black mx-md-0 mx-sm-0">Recherche</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    </div>
</section>
<section class="conseils-cards">
    <div class="container">
        {% for article in articlesDTO %}
        <a style="text-decoration: none;" href={{ "/article/" ~ article.id ~ "/" ~ strtourl(article.titre) }}>
            <div class="card">
                <div class="row">
                    <div class="col-lg-4 col-sm-12">
                        <img
                                src={{ "/uploads/article/" ~ article.image_petite }}
                                alt="{{ article.titre }}"
                                style="width: 300px; height: 200px; object-fit: cover;"
                        />
                    </div>
                    <div class="col-lg-7">
                        <div class="vertical-center">
                            <div>  <h3 class="mb-3">{{ article.titre }}</h3>
                                <p class="justified">{{article.chapo|u.truncate(250, '...', false)}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </a>
        {% endfor %}
    </div>
</section>
    <div align="center" id="pagination_de">
        {{ knp_pagination_render(articlesDTO, 'directemploi/composants/paginationTemplate.html.twig') }}
    </div>
</div>
{% endblock %}