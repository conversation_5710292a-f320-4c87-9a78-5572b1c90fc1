{% extends 'carriereInformatique/base.html.twig' %}
{% set title_seo = "Moteur de recherche avancée d'offres un emploi| Carriere BTP" %}
{% set title_seo = "Moteur de recherche avancée d'offres un emploi| Carriere BTP" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% set description_seo = "Vous recherchez une offre d'emploi BTP ? Retrouvez les offres un emploi sur Carrière Industrie. Recherchez toutes les annonces d'emploi : CDD, CDI, alternance, stage, intérim..." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
<div class="is-sticky">
    {% block head %}
    {% include 'carriereInformatique/header.html.twig' %}
{% endblock %}
    <section class="emploi-header d-flex justify-content-center align-items-center " id="">
        <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#25546D">Trouver un emploi</span> près de chez vous</h1>
    </section>
    <br>
        {{ render(controller('App\\Controller\\carriereBTP\\AccueilController::offresemplois', {'path': app.request.pathinfo})) }}

    <section class="recherche-offre">
        <div class="container">
            <form id="searchForm" action="{{ path('app_BTP_candidat_search') }}" method="get">
                <div class="h-flex">
                    <div class="col-sm-2"><span><strong>Type de contrat</strong></span></div>
                    <div class="h-flex">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="contrat" value="1" id="cdiRadioButton">
                            <label class="form-check-label" for="cdiRadioButton">
                                CDI
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="contrat" value="2" id="cddRadioButton">
                            <label class="form-check-label" for="cddRadioButton">
                                CDD
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="contrat" value="4" id="interimRadioButton">
                            <label class="form-check-label" for="interimRadioButton">
                                Interim
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="contrat" value="3" id="freeRadioButton">
                            <label class="form-check-label" for="freeRadioButton">
                                Freelance / Indépendant
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="contrat" value="6" id="alternanceRadioButton">
                            <label class="form-check-label" for="alternanceRadioButton">
                                Alternance
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="contrat" value="8" id="stageRadioButton">
                            <label class="form-check-label" for="stageRadioButton">
                                Stage
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group row mt-3">
                    <span for="inputPassword" class="col-sm-2 col-form-label"><strong>Localisation</strong></span>
                    <div class="col-sm-4">
                        <select class="form-select form-control" id="activiteFormControl">
                            <option value="">Sélectionnez un département</option>
                            {% for departement in listeDepartements %}
                                {% set cp = departement.CodePostal|raw %}
                                {% if cp|length == 1 %}
                                    {% set cp = cp ~ "0" %}
                                    {% set cp = cp|reverse %}
                                {% endif %}
                                <option value="{{ departement.libelle }}">
                                    {{ departement.libelle ~ " (" ~ cp ~ ")" }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="text-center "><button class="btn button-black">Rechercher</button></div>
            </form>
        </div>

    </section>

    <!-- Include jQuery and Bootstrap JS from CDNJS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            var form = this;

            // Récupérer tous les inputs de type checkbox qui sont cochés.
            var checkboxesChecked = [];
            var checkboxes = form.querySelectorAll('input[type="radio"]:checked');
            for (var checkbox of checkboxes) {
                checkboxesChecked.push(checkbox.value);
            }
            console.log(checkboxesChecked)
            // Construire la chaîne de requête des contrats sélectionnés.
            var contratQuery = checkboxesChecked.join('&contrat=');

            // Récupérer le département sélectionné.
            var localisation = document.getElementById('activiteFormControl').value;
            // Construire l'URL finale.
            var actionUrl = form.getAttribute('action');
            var finalUrl = `${actionUrl}?contrat=${contratQuery}&localisation=${localisation}`;
            console.log(finalUrl)
            // Rediriger vers l'URL construite.
            window.location.href = finalUrl;
        });
    </script>
    {% block footer %}
        {% include 'carriereInformatique/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}

