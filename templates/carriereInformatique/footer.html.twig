
<footer class="site-footer">
    <div class="text-center image-footer">
        <img class="img-fluid" alt="logo noir" src="{{ asset('carriereInformatique/Front/images/logo noir avec text.png') }}"/>
    </div>
    <div class="container">
        <div class="footer-all-menus">

            <div class="d-flex flex-column align-items-left footer-menu">
                <p  class="footer-menu-title">À propos</p>
                <a href="{{ path('app_Inf_quisommenous') }}"  class="footer-menu-text">Qui sommes nous ?</a>
                <a href="{{ path('app_Inf_informationlegal') }}" class="footer-menu-text">Informations légales</a>
                <a href="{{ path('app_Inf_confidentialite') }}"  class="footer-menu-text"  >Politique de confidentialité</a>
                <a href="{{ path('app_Inf_chartecookies') }}" class="footer-menu-text">Charte Cookies</a>
                <a href="{{ path('app_Inf_plansite') }}" class="footer-menu-text">Plan du site</a>
                <a href="{{ path('app_Inf_contact') }}"  class="footer-menu-text">Contact</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <p  class="footer-menu-title">Recruteurs</p>
                <a href="javascript:void(0);" onclick="showSweetAlert(this)"      class="footer-menu-text">S'inscrire</a>
                <a href="{{ path('app_Inf_societe') }}" class="footer-menu-text">Carrière Informatique - la société</a>
                <a  href="javascript:void(0);" onclick="showSweetAlert(this)" class="footer-menu-text">Le réseau Direct Emploi</a>
                <a href="javascript:void(0);" onclick="showSweetAlert(this)" class="footer-menu-text">Les offres proposées</a>
                <a href="{{ path('app_Inf_chartedurecrutement') }}" class="footer-menu-text">Charte du recrutement</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <p  class="footer-menu-title">Candidats</p>
                <a href="{{ path('app_Inf_register_candidat_e1') }}" class="footer-menu-text">S'inscrire</a>
                {# <a href="{{ path('app_Inf_Parcourirlesoffresdemploi') }}" class="footer-menu-text">Parcourir les offres d'emploi</a>#}
                <a href="{{ path('app_Inf_ParcourirlesoffresdemploiInterim') }}" class="footer-menu-text">Offres d'emploi en intérim</a>
                <a href="{{ path('app_Inf_conseilsCV') }}"class="footer-menu-text">Conseils CV</a>
                <a href="{{ path('app_Inf_conseilsmotivation') }}" class="footer-menu-text">Lettre de motivation</a>
                <a href="{{ path('app_Inf_conseilsembauche') }}" class="footer-menu-text">Entretien d'embauche</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <p href="#" class="footer-menu-title">L'emploi</p>
                <a href="{{ path('app_Inf_Parcourirlesoffresdemploi') }}" class="footer-menu-text">Offres d'emploi par métier</a>
                <a href="{{ path('app_Inf_ParcourirlesoffresdemploiVille') }}" class="footer-menu-text">Offres d'emploi par ville</a>
                <a href="{{ path('app_Inf_entreprises') }}" class="footer-menu-text">Offres d'emploi par entreprise</a>
                <a  href="{{ path('app_Inf_ParcourirlesoffresdemploiVilleMotCle') }}" title="Genie civil"  class="footer-menu-text">Offres d'emploi par mots clés</a>

            </div>

        </div>
       
    </div>

    <div class="copyright-section">
        <p class="text-center copyright-text">© Carriere Informatique 2010 - 2024</p>
    </div>
    <script>
        // Function to be executed when the <a> element is clicked
        function handleLinkClick(titleValue) {
            const url = `https://www.carriere-btp.com/BTP/lists-offre/?contrat=&localisation=&localisation_field=&q=${titleValue}`;
            // Redirect to the constructed URL
            window.location.href = url;
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
    <script>
        function showSweetAlert(link) {
            const linkText = link.innerText || link.textContent;
            if (linkText==="Recruteurs")
            {
                Swal.fire({
                    icon: 'success',
                    title: 'Carriere Informatiqueest un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png')}}" alt="Carriere InformatiqueLogo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/inscriptionRecruteur';
                });
            }else if (linkText==="Connexion")
            {
                Swal.fire({
                    icon: 'success',
                    title: 'Carriere Informatiqueest un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png')}}" alt="Carriere InformatiqueLogo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/login';
                });
            }else if (linkText==="Création de compte")
            {
                Swal.fire({
                    icon: 'success',
                    title: 'Carriere Informatiqueest un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png')}}" alt="Carriere InformatiqueLogo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/inscriptionRecruteur';
                });
            }
            else if (linkText === "S'inscrire") {
                Swal.fire({
                    icon: 'success',
                    title: 'Carriere Informatiqueest un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png') }}" alt="Carriere InformatiqueLogo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/login';
                });
            } else if (linkText === "Le réseau Direct Emploi") {
                Swal.fire({
                    icon: 'success',
                    title: 'Carriere Informatiqueest un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png') }}" alt="Carriere InformatiqueLogo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/lereseau';
                });
            } else if (linkText === "Les offres proposées") {
                Swal.fire({
                    icon: 'success',
                    title: 'Carriere Informatiqueest un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png') }}" alt="Carriere InformatiqueLogo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/offresproposees';
                });
            }
        }
    </script>
</footer>