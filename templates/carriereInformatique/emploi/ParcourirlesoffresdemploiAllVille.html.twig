{% extends 'carriereInformatique/base.html.twig' %}
{% set lettre = app.request.query.get('lettre') %}

    {% set title_seo = "Offres Emploi par Ville dans le Domaine Informatique | Carriere Informatique" %}
    {% set description_seo = "Consultez les Dernières Offres d'Emploi par ville dans le domaine Informatique sur Carriere Informatique. ✓ Postulez en ligne dès Maintenant ✓ Candidature Simple & Rapide !" %}

{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .alphabet-filter {
            text-align: center;
            padding: 10px;
            margin: 10px;
        }

        .alphabet-filter a {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border: 0.5px solid #ddd; /* Couleur de la bordure */
            border-radius: 4px; /* Coins arrondis */
            text-decoration: none; /* Supprime le soulignement des liens */
            color: #333; /* Couleur du texte */
            font-weight: bold; /* Rend le texte en gras */
            transition: background-color 0.3s, color 0.3s; /* Animation au survol */
        }

        .alphabet-filter a:hover {
            background-color: #25546D; /* Couleur de fond au survol */
            color: #fff; /* Couleur du texte au survol */
        }

    </style>
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereInformatique/header.html.twig' %}
        {% endblock %}
        <section class="emploi-header d-flex justify-content-center align-items-center " id="">
            <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#25546D">Offres d’emploi</span> par ville pour le domaine Informatique</h1>
        </section>
        <br>
        <section class="section-embauche">

            <a href="#" onclick="redirectToInformatique('Informatique')"><h6 class="text-center text-uppercase mb-5"><span style="color: #25546D;">Offres d'emploi  </span>Informatique</h6></a>

            <div class="container">
                <div class="row offres-list">
                    {% set taille_liste = villes|length %}
                    {% set taille_groupe = (taille_liste / 3)|round(0, 'ceil') %}

                    {% for i in 0..2 %}
                        <div class="col-lg-4 col-md-6">
                            {% for ville in villes|slice(i * taille_groupe, taille_groupe) %}
                                <div style="margin-bottom: 20px;">
                                    <ul style="list-style: none; padding: 0;">
                                        <li onclick="redirectTo('{{ ville.libelle | replace({"Offres d'emploi ": ''}) | escape('js') }}')">

                                            <a href="#" >Offres d'emploi Informatique {{ ville.libelle }}</a>
                                        </li>
                                    </ul>
                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        </section>




        <!-- Include jQuery and Bootstrap JS from CDNJS -->
        <script>
            function redirectTo(value) {
                // Encode the value to include it in the URL
                const url = `https://www.carriere-informatique.com/Inf/lists-offre/?contrat=&localisation=${value}&localisation_field=&q=`;
                // Redirect to the constructed URL
                window.location.href = url;
            }
            function redirectToInformatique(value) {
                // Encode the value to include it in the URL
                const url = `https://www.carriere-informatique.com/Inf/lists-offre/?contrat=&localisation=&localisation_field=&q=${value}`;
                // Redirect to the constructed URL
                window.location.href = url;
            }
        </script>

        {% block footer %}
            {% include 'carriereInformatique/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}

