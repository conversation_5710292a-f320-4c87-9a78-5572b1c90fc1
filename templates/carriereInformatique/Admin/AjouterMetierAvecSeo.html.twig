{% extends 'carriereBTP/base.html.twig' %}
{% block title %}Nos conseils recrutement et emploi entretien d'embauche | Carriere BTP{% endblock %}

{% block body %}
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereBTP/header.html.twig' %}
        {% endblock %}
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
        <section class="ajouter-cv-header" id="">
            <div class="container">
                <h5 class="text-center text-uppercase mb-3"><span style="color:#FAAF40">Ajouter  </span>un metier</h5>
            </div>
        </section>
    <div class="container">
        {# votre_template.twig #}

        <form method="post" action="{{ path('app_BTP_admin_pushMetiertobase') }}" enctype="multipart/form-data">
            <div class="info-section">
                <h6>Des informations sur le métier</h6>
                <hr>
                <div class="form-group">
                    <label for="nomMetier">Nom Metier *</label>
                    <input type="text" class="form-control" id="nomMetier" name="nomMetier" placeholder="Nom Metier">
                </div>
                <div class="form-group">
                    <label for="numeroSecteur">Numéro de Secteur *</label>
                    <select class="form-control" id="numeroSecteur" name="numeroSecteur">
                        <option value="4"> 4 : #BTP / Construction / Génie Civil</option>
                        <option value="11"> 11 : #Electronique / Electricité</option>
                        <option value="12"> 12 : #Energie / Environnement</option>
                        <option value="18"> 18 : #Immobilier / Urbanisme / Aménagement</option>
                        <option value="46"> 46 : #Architecture / Art / Design</option>
                    </select>
                </div>
            </div>

            <div class="info-section">
                <h6>Partie référencement SEO</h6>
                <hr>
                <div class="form-group">
                    <label for="preposition">Preposition *</label>
                    <input type="text" class="form-control" id="preposition" name="preposition" placeholder="(de,l',en,de)...">
                </div>
                <div class="form-group">
                    <label for="imageMetier">Ajouter une image pour le metier</label>
                    <input type="file" class="form-control" id="imageMetier" name="imageMetier">
                </div>
                <div class="form-group">
                    <label for="motsCles">Mots clés *</label>
                    <input type="text" class="form-control" id="motsCles" name="motsCles" placeholder="Mots clés">
                </div>
                <div class="form-group">
                    <label for="paragraphe">Paragraphe *</label>
                    <textarea class="form-control" id="paragraphe" name="paragraphe" rows="4"></textarea>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn mb-2 px-4 button-black">Ajouter</button>
            </div>
        </form>

    </div>
        {% block footer %}
            {% include 'carriereBTP/footer.html.twig' %}
        {% endblock %}
    </div>
<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const successMessage = '{{ app.session.flashBag.get("msg1") | first }}';
            const idmetier = '{{ app.session.flashBag.get("idmetier") | first }}';
            const idseo = '{{ app.session.flashBag.get("idseo") | first }}';

            if (successMessage) {
                Swal.fire({
                    icon: 'error',
                    title: successMessage,
                    showConfirmButton: false,
                    showCloseButton: true,  // Show the "Fermer" button
                }).then(function() {
                    Swal.fire({
                        title: 'Voulez vous allez vers la page de modification ?',
                        showDenyButton: true,
                        showCancelButton: false,
                        confirmButtonText: `Oui`,
                        denyButtonText: `Non`,
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = `https://www.carriere-btp.com/BTP/admin/UpdateMetier/${idmetier}/${idseo}`;
                        } else if (result.isDenied) {
                            window.location.href = `https://www.carriere-btp.com/BTP/admin/EditMetier`;
                        }
                    });
                });
            }
        });
    </script>


{% endblock %}