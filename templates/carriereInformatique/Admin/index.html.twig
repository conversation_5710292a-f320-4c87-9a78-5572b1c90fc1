{% extends 'carriereBTP/base.html.twig' %}
{% block title %}Nos conseils recrutement et emploi entretien d'embauche | Carriere BTP{% endblock %}

{% block body %}
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereBTP/header.html.twig' %}
        {% endblock %}
        <section class="ajouter-cv-header" id="">
            <div class="container">
                <h5 class="text-center text-uppercase mb-3"><span style="color:#FAAF40">Espace </span>Admin</h5>
                <p class="justified mb-0"> Un espace administrateur est une section cruciale d'un site web ou d'une application, conçu spécifiquement pour les gestionnaires et les modérateurs. Cette interface offre des fonctionnalités avancées pour superviser les opérations, gérer les utilisateurs, et analyser les données de l'application. Avec une présentation claire et des outils puissants, cet espace est essentiel pour le maintien de la bonne marche et la sécurité du système.</p>

            </div>


        </section>
        <section class="section-embauche">
            <div class="container">
                <h6 class="text-uppercase text-center mb-4"><span style="color: #FAAF40;">Control</span> Panel</h6>
                <div class="row ">
                    <div class="col-lg-6 col-sm-12 mb-3">
                        <a href="{{ path('app_BTP_admin_EditMetier') }}">
                            <div class="w-100 info-button">
                                <p class="text-center button-title">Gérer la liste des métiers</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-6 col-sm-12 mb-3">
                        <a href="{{ path('app_BTP_admin_Fichemetier') }}">
                            <div class="w-100 info-button">
                                <p class="text-center button-title">Module fiche metier</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </section>
        {% block footer %}
            {% include 'carriereBTP/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}