{% extends 'carriereInformatique/base.html.twig' %}
{% set title_seo = article.titre[0:54] ~ " | Carrière Informatique" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% set description_seo = article.titre[0:54] ~"|"~ " Actualité de l'emploi : " ~ article.chapo|slice(0, 120) ~ "..." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}

{% block body %}

<style>
.contenu_article h2,
.contenu_article h3.h2 {
    clear: both;
    color: #2D6C9F;
    font-size: 1.4em;
    margin: 30px 0 20px 0;
    padding: 0 13px 10px 13px;
    border-bottom: 1px solid #c2c2c2;
}

.contenu_article p {
    padding: 0px 13px;
}
.gray-background {
    background-color: #f0f0f0; /* Couleur de fond grise */
    padding: 20px; /* Optionnel: ajout d'un padding pour plus de confort visuel */
}
</style>

    <div class="is-sticky">
        {% block head %}
            {% include 'carriereInformatique/header.html.twig' %}
        {% endblock %}
        <section class="ajouter-cv-header" id="">
            <div class="container gray-background">
                <h1 class="text-center text-uppercase mb-3" style="font-size: 2rem; margin-top: 1rem; line-height: 1.4;">{{ article.titre }}</h1>
                <p class="justified mb-0"><strong>{{ article.chapo }}</strong></p>
            </div>
        </section>
        <section class="content">
            <div class="container">

                <div class="header-and-text contenu_article ">
                    {{ article.contenu|replace({'http://': 'https://'})|raw }}
                </div>
            </div>
        </section>
        <section class="article-section">
            <div class="container">
                <h6 class="text-center text-uppercase "><span class="yellow">D'autres articles qui</span>  pourraient vous plaire</h6>
                <div class="row">
                    {% set count = 1 %}
                    {% for recent in recents %}
                    <div class="col-lg-4 col-md-12 mb-3">
                        <div class="card">
                            <div style="border-radius: 1.25rem; height: 230px; background-image: url('{{ asset("/uploads/article/" ~ recent.getImagePetite()) }}'); background-position: center; background-size: cover;" alt="{{ recent.getImagePetite() }}">
                            </div>
                            <div class="card-body p-0 pt-2 text-center">
                                <div class="d-flex flex-column justify-content-between h-100">
                                    <p class="text-center">{{ recent.getTitre() }}</p>
                                    <a href={{ "/Inf/actualitesdetails/" ~ recent.getIdArticle() ~ "/" ~ strtourl(recent.getTitre()) }} style="color:#25546D">En savoir plus</a>
                                </div>
                            </div>
                        </div>
                    </div>
                        {% set count = count + 1 %}
                    {% endfor %}
                </div>
                <div class="text-center mt-4"><a href="{{ path('app_Inf_actualites') }}" class="btn button-black">Voir tous les articles  </a></div>
            </div>
        </section>
{% block footer %}
    {% include 'carriereInformatique/footer.html.twig' %}
{% endblock %}
</div>
{% endblock %}