{% extends 'carriereInformatique/base.html.twig' %}

{% set lettre = app.request.query.get('lettre') %}
{% set page = app.request.query.get('page') %}

{% if lettre and page %}
    {% set title_seo = "Trouvez tous les métiers Informatique qui recrutent | Carriere Informatique| pour les metiers qui commencent par la lettre '" ~ lettre ~ "'. Page : " ~ page %}
    {% set description_seo = "Quels sont les métiers qui commencent par la lettre '" ~ lettre ~ "' et qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines…  dans la page :"~ page  %}
{% elseif lettre and page is null %}
    {% set title_seo = "Trouvez tous les métiers Informatique qui recrutent | Carriere Informatique| pour les metiers qui commencent par la lettre '" ~ lettre ~ "'"  %}
    {% set description_seo = "Quels sont les métiers qui commencent par la lettre '" ~ lettre ~ "' et qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines…  "  %}
{% elseif lettre is null and page %}
    {% set title_seo = "Trouvez tous les métiers Informatique qui recrutent | Carriere Informatique| pour la page'" ~ page ~ "'"  %}
    {% set description_seo = "Quels sont les métiers pour la page '" ~ page ~ "' et qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines…  "  %}
{% else %}
    {% set title_seo = "Trouvez tous les métiers Informatique qui recrutent | Carriere Informatique" %}
    {% set description_seo = "Quels sont les métiers Informatique qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines… "  %}


{% endif %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .alphabet-filter {
            text-align: center;
            padding: 10px;
            margin: 10px;
        }

        .alphabet-filter a {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border: 0.5px solid #ddd; /* Couleur de la bordure */
            border-radius: 4px; /* Coins arrondis */
            text-decoration: none; /* Supprime le soulignement des liens */
            color: #333; /* Couleur du texte */
            font-weight: bold; /* Rend le texte en gras */
            transition: background-color 0.3s, color 0.3s; /* Animation au survol */
        }

        .alphabet-filter a:hover {
            background-color: #25546D; /* Couleur de fond au survol */
            color: #fff; /* Couleur du texte au survol */
        }

    </style>
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereInformatique/header.html.twig' %}
        {% endblock %}


        <section class="btp-header d-flex flex-column align-items-center" id="">
            <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.4;"> Liste des métiers des domaines  <span style="color:#25546D">Électronique / Électricité</span></h1>
        </section>

        <section class="section-embauche env">


            <div class="container">
                <div class="row">
                    {% for metier in metiersParPage %}
                        <div class="col-lg-4 col-md-6 col-sm-12">
                            <a href="#" class="metier-link" data-metier="{{ metier }}">
                                <div class="w-100 info-button text-center">
                                    <h6>{{ metier }}</h6>
                                </div>
                            </a>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->

            </div>
        </section>

        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                document.querySelectorAll('.metier-link').forEach(function(link) {
                    link.addEventListener('click', function(event) {
                        event.preventDefault();
                        var metier = this.getAttribute('data-metier');
                        Swal.fire({
                            title: 'Voulez-vous voir la fiche métier ou bien les offres ?',
                            showDenyButton: true,
                            showCancelButton: false,
                            confirmButtonText: 'Voir fiche métier',
                            denyButtonText: 'Voir les offres'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                fetch('/Inf/api/metier', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({ nom_metier: metier })
                                })
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.success) {
                                            window.location.href = '/BTP/FicheMetier/' + data.id;
                                        } else {
                                            Swal.fire('Erreur', 'Fiche métier non disponible pour le moment', 'error');
                                        }
                                    })
                                    .catch(error => {
                                        Swal.fire('Erreur', 'Une erreur est survenue', 'error');
                                    });
                            } else if (result.isDenied) {
                                window.location.href = '/Inf/lists-offre/?q=' + metier;
                            }
                        });
                    });
                });
            });
        </script>
        {% block footer %}
            {% include 'carriereInformatique/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}