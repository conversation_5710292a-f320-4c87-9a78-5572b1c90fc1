<style>
    .dropdown-emploi:hover .dropdown-menu {
        display: block;
    }
</style>
{% set current_path = app.request.attributes.get('_route') %}

<nav class="navbar navbar-expand-lg" style="position:fixed; z-index: 999;">
    <div class="container">
        <button  class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <a href="{{ path('app_Inf_accueil') }}" class="navbar-brand mx-auto mx-lg-0"><img class="logo" alt="logo" src=""/></a>
        <div class="collapse navbar-collapse ms-auto" id="navbarNav">
            <ul class="navbar-nav ">
                <li class="nav-item">
                    <a class="nav-link {% if current_path == 'app_Inf_accueil' %}active{% endif %}" href="{{ path('app_Inf_accueil') }}">Accueil</a>
                </li>
                <div class="dropdown dropdown-conseil">
                    <li class="nav-item">
                        <a class="nav-link {% if current_path starts with 'app_Inf_candidat_search' %}active{% endif %} " href="{{ path('app_Inf_candidat_search') }}" >Emploi</a>
                    </li>
                  
                </div>
                <li class="nav-item">
                    <a class="nav-link {% if current_path == 'app_Inf_entreprises' %}active{% endif %}" href="{{ path('app_Inf_entreprises') }}">Entreprises</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if current_path == 'app_Inf_actualites' %}active{% endif %}" href="{{ path('app_Inf_actualites') }}">Actualités</a>
                </li>
                <div class="dropdown dropdown-conseil">
                    <li class="nav-item separator">
                        <a class="nav-link " href="{{ path('app_Inf_conseils') }}">Conseils</a>
                    </li>
                    <div class="dropdown-content content-conseil"style="left: 0;">
                        <a href="{{ path('app_Inf_conseilsCV') }}">Le CV</a>
                        <a href="{{ path('app_Inf_conseilsmotivation') }}">La lettre de motivation</a>
                        <a href="{{ path('app_Inf_conseilsembauche') }}">L'entretien d'embauche</a>
                        <a href="{{ path('app_Inf_conseilsrechercheEmploi') }}">La recherche d'emploi</a>
                    </div>
                </div>
                <!-- Authentication and user-specific links -->
                {% if app.user is null %}

                <div class="dropdown dropdown-recruteur">
                    <li class="nav-item">
                        <a class="nav-link  click-scroll" href="{{ path('app_Inf_login') }}"><img class="nav-icon" alt="mdi-worker" src="{{ asset('carriereInformatique/Front/images/mdi-worker.svg')}}" />Candidats</a>
                    </li>
                    <div class="dropdown-content content-recruteur" style="left: 0;">
                        <a href="{{ path('app_Inf_login') }}">Connexion</a>
                        <a href="{{ path('app_Inf_register_candidat_e1') }}">Création de compte</a>
                    </div>
                </div>
                <div class="dropdown dropdown-candidat" style="float:right;">
                    <li class="nav-item">
                        <a class="nav-link click-scroll" href="javascript:void(0);" onclick="showSweetAlert(this)"> <!-- Pass the 'this' reference to the function -->
                            <img class="nav-icon" alt="dashicons-businessman" src="{{ asset('carriereInformatique/Front/images/dashicons-businessman.svg')}}" />Recruteurs
                        </a>
                    </li>
                    <div class="dropdown-content content-candidat">
                        <a href="javascript:void(0);" onclick="showSweetAlert(this)">Connexion</a>
                        <a href="javascript:void(0);" onclick="showSweetAlert(this)">Création de compte</a>
                        <div class="px-3">
                            <hr>
                        </div>
                        <a href="javascript:void(0);" onclick="showSweetAlert(this)">Les offres proposées</a>
                        <a href="javascript:void(0);" onclick="showSweetAlert(this)">Le réseau Direct Emploi</a>
                    </div>
                </div>
                {% else %}
                    <div class="dropdown dropdown-recruteur">
                        <li class="nav-item">
                            <a href="{{ path('app_Inf_espaceCandidat') }}" class="nav-link  click-scroll" ><img class="nav-icon" alt="worker" src="{{ asset('carriereInformatique/Front/images/mdi-worker.svg')}}" />{{ app.user.nom }} {{ app.user.prenom }}</a>
                        </li>
                        <div class="dropdown-content content-recruteur" style="left: 0;float:right;">
                            <a href="{{ path('app_Inf_espaceCandidat') }}">Mon Profil</a>
                            <a href="{{ path('app_Inf_espaceCandidatprofilcv') }}">Mon CV</a>
                            <a href="{{ path('app_Inf_espaceCandidatprofiloffre') }}">Mes offres</a>
                            <a href="{{ path('app_Inf_espaceCandidatprofilmail') }}">Mes alertes mail</a>
                            <div class="px-3">
                                <hr class="py-0 my-1">
                            </div>
                            <a href="{{ path('app_Inf_logout') }}" class="text-danger">Se deconnecter</a>
                        </div>
                    </div>
                {% endif %}

            </ul>
        </div>
    </div>
</nav>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
<script>
    function showSweetAlert(link) {
        const linkText = link.innerText || link.textContent;

        if (linkText==="Recruteurs")
        {
            Swal.fire({
                icon: 'success',
                title: 'Carriere BTP est un site du réseau Direct Emploi',
                html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                    '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png')}}" alt="Carriere BTP Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                timer: 3000,
                showConfirmButton: false,
            }).then(() => {
                window.location.href = 'https://www.directemploi.com/inscriptionRecruteur';
            });
        }else if (linkText==="Connexion")
        {
            Swal.fire({
                icon: 'success',
                title: 'Carriere BTP est un site du réseau Direct Emploi',
                html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                    '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png')}}" alt="Carriere BTP Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                timer: 3000,
                showConfirmButton: false,
            }).then(() => {
                window.location.href = 'https://www.directemploi.com/login';
            });
        }else if (linkText==="Création de compte")
        {
            Swal.fire({
                icon: 'success',
                title: 'Carriere BTP est un site du réseau Direct Emploi',
                html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                    '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png')}}" alt="Carriere BTP Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                timer: 3000,
                showConfirmButton: false,
            }).then(() => {
                window.location.href = 'https://www.directemploi.com/inscriptionRecruteur';
            });
        }else if (linkText==="Les offres proposées")
        {
            console.log(linkText);
            Swal.fire({
                icon: 'success',
                title: 'Carriere BTP est un site du réseau Direct Emploi',
                html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                    '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png')}}" alt="Carriere BTP Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                timer: 3000,
                showConfirmButton: false,
            }).then(() => {
                window.location.href = 'https://www.directemploi.com/offresproposees';
            });
        }
        else if (linkText==="Le réseau Direct Emploi")
        {
            Swal.fire({
                icon: 'success',
                title: 'Carriere BTP est un site du réseau Direct Emploi',
                html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                    '<img src="{{ asset('carriereInformatique/Front/images/logo-black.png')}}" alt="Carriere BTP Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                timer: 3000,
                showConfirmButton: false,
            }).then(() => {
                window.location.href = 'https://www.directemploi.com/lereseau';
            });
        }
    }
</script>

