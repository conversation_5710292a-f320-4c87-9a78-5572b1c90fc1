{% extends 'carriereInformatique/base.html.twig' %}

{% set title_seo = "Carrière BTP - Offres d'emploi - Formation continue dans le secteur de BTP" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% set description_seo = "sur Carrière BTP, recherchez toutes les annonces d'emploi : CDD, CDI, alternance, stage, intérim..." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}

{% block body %}
    <style>
        .single-logo
        {
            width: 60%;
            margin: auto;
            display: block;
        }
        .single-logo img
        {

        }
        .section-padding{
            padding:60px 0;
        }

        .owl-dots{
            text-align: center;
        }

        .owl-dot {
            display: inline-block;
            height: 15px !important;
            width: 15px !important;
            background-color: #222222 !important;
            opacity: 0.8;
            border-radius: 50%;
            margin: 0 5px;
        }

        .owl-dot.active {
            background-color: #25546D !important;
        }
    </style>
<div class="is-sticky">

    {% block head %}
    {% include 'carriereInformatique/header.html.twig' %}
{% endblock %}
    <section class="espace-ingenieur-header" id="">
        <h1 class="text-center text-uppercase text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#25546D">ESPACE INGÉNIEURS : </span> Des opportunités de carrières</h1>
        <div class="header-text">
            <div class="container ">
                <h6 class="text-uppercase text-white">BIENVENUE DANS NOTRE ESPACE INGénieurs</h6>
                <p class="text-white justified">Les postes d’ingénieurs informatiques sont très demandés Nous avons choisi de sélectionner une vingtaine de métiers d’ingénieurs en informatique, les plus représentatifs et souvent les plus recherchés par les entreprises. En un clic, vous pouvez postuler aux offres en relation avec ces métiers d’informaticiens. Vous pouvez également retrouver régulièrement des témoignages et interviews de professionnels du recrutement qui recherchent des ingénieurs en informatique dans le cadre de mission de recrutement pour de nombreuses entreprises, utilisateurs, éditeurs de logiciel ou ESN. Avec nos zoom métiers, nous vous donnons toutes les informations pour découvrir des métiers de d’ingénieurs en informatique dans des horizons très différents.</p>
            </div>
        </div>
    </section>

    <section class="section-embauche">
        <h6 class="text-center text-uppercase mb-5"><span style="color: #25546D;">LES POSTES</span> PROPOSés</h6>
        <div class="container">

            <div class="row offres-list">
                <div class="col-lg-3 col-md-6">
                    <!-- Première colonne -->
                    <ul style="list-style: none; padding: 0;">
                        <li onclick="redirectTo('Ingénieur e-commerce')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur e-commerce</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur web')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur web</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur SEO')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur SEO</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur UX/UI')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur UX/UI</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur multimédia')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur multimédia</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en gestion de données')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en gestion de données</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en analyse de trafic web')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en analyse de trafic web</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en publicité en ligne')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en publicité en ligne</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur big data')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur big data</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur intelligence artificielle')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur intelligence artificielle</a>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6">
                    <!-- Deuxième colonne -->
                    <ul style="list-style: none; padding: 0;">
                        <li onclick="redirectTo('Ingénieur en électronique')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en électronique</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur électrotechnique')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur électrotechnique</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en robotique')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en robotique</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur électricien')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur électricien</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en microélectronique')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en microélectronique</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en conception de circuits intégrés')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en conception de circuits intégrés</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en domotique')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en domotique</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en nanotechnologie')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en nanotechnologie</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en énergies renouvelables')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en énergies renouvelables</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en systèmes embarqués')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en systèmes embarqués</a>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6">
                    <!-- Troisième colonne -->
                    <ul style="list-style: none; padding: 0;">
                        <li onclick="redirectTo('Ingénieur en systèmes d\'information')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en systèmes d'information</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur logiciel')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur logiciel</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en cybersécurité')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en cybersécurité</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur cloud')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur cloud</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur DevOps')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur DevOps</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en data science')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en data science</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en machine learning')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en machine learning</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en blockchain')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en blockchain</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur architecte logiciel')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur architecte logiciel</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en systèmes de gestion de bases de données')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en systèmes de gestion de bases de données</a>
                        </li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6">
                    <!-- Quatrième colonne -->
                    <ul style="list-style: none; padding: 0;">
                        <li onclick="redirectTo('Ingénieur télécoms')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur télécoms</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur réseaux')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur réseaux</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur transmission de données')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur transmission de données</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur réseaux sans fil')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur réseaux sans fil</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en 5G')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en 5G</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur fibre optique')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur fibre optique</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en câblage réseaux')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en câblage réseaux</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en antennes et téléphonie mobile')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en antennes et téléphonie mobile</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en systèmes satellitaires')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en systèmes satellitaires</a>
                        </li>
                        <li onclick="redirectTo('Ingénieur en cybersécurité des réseaux')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #25546D;font-size: 7px;"></i>
                            <a href="#">Ingénieur en cybersécurité des réseaux</a>
                        </li>
                    </ul>
                </div>
            </div>

        </div>
    </section>

    <section class="zoom-metier">
        <div class="container">
            <h6 class="text-center text-uppercase mb-5"><span style="color: #25546D;">ZOOM</span> Métier</h6>
            <div class="row">
                <div class="col-lg-6 img-container">
                    <img src="{{ imgpath }}" alt="{{ Article.titre }}"/>

                </div>
                <div class="col-lg-6 txt-container v-flex justify-content-center align-items-center">
                    <div class="">
                        {% if Article is defined and Article %}
                            <h6 class="justified text-uppercase mb-4">{{ Article.titre }}</h6>
                            <p>{{ Article.chapo|length > 50 ? Article.chapo|slice(0, 300) ~ '...' : Article.chapo }}</p>
                            <div class="text-center mt-4">
                                <a href="{{ articlepath }}" class="btn button-black">
                                    Lire la suite
                                </a>
                            </div>
                        {% else %}
                            <p>Aucun article disponible.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section>
        <div class="container">
        <h6 class="text-center"><span style="color: #25546D;">ILS RECRUTENT</span> DANS L'INFORMATIQUE</h6>
        <div class="brand-carousel section-padding owl-carousel">
            {% for boite in boites %}
                <div class="single-logo">
                    <img src="{{ asset("/uploads/" ~ boite.image) }}"  alt="{{boite.image}}" class="img-fluid" style="width: 94px; height: 75px;" />
                </div>
            {% endfor %}
        </div>
        </div>
    </section>
    <section class="la-une">
        <div class="container">
            <h6 class="text-center text-uppercase  mb-5"><span style="color: #25546D;">l’entreprise</span> à la une</h6>
            <div class="w-100 card-offre">
                <div class="row">
                    <div class="col-lg-2 col-sm-12">
                        <div class="logo-carde vertical-center">
                            <img class="img-fluid " src="{{ asset('carriereInformatique/Front/images/michael-page-10896.png') }}"  alt="michael"/>
                        </div>
                    </div>
                    <div class="col-lg-10 col-sm-12">
                        <div class="h-flex justify-content-between w-100">
                            {% if offresBtpNgeIng is not empty %}
                                {% set total_offres = offresBtpNgeIng|length %}
                                {% set offres_par_ul = (total_offres / 3)|round(0, 'ceil') %}
                                <div>
                                    <ul>
                                        {% for i in 0..offres_par_ul-1 %}
                                            {% if offresBtpNgeIng[i] is defined %}
                                                <li>
                                                    <i class="fa-solid fa-circle fa-2xs" style="color: #25546D; font-size: 7px;"></i>
                                                    <span><a href="https://www.carriere-informatique.com/Inf/submitCandidat/{{ offresBtpNgeIng[i].id_offre }}">{{ offresBtpNgeIng[i].intitule }}</a></span>
                                                </li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </div>
                                <div>
                                    <ul>
                                        {% for i in offres_par_ul..(2 * offres_par_ul - 1) %}
                                            {% if offresBtpNgeIng[i] is defined %}
                                                <li>
                                                    <i class="fa-solid fa-circle fa-2xs" style="color: #25546D; font-size: 7px;"></i>
                                                    <span><a href="https://www.carriere-informatique.com/Inf/submitCandidat/{{ offresBtpNgeIng[i].id_offre }}">{{ offresBtpNgeIng[i].intitule }}</a></span>
                                                </li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </div>
                                <div>
                                    <ul>
                                        {% for i in (2 * offres_par_ul)..(3 * offres_par_ul - 1) %}
                                            {% if offresBtpNgeIng[i] is defined %}
                                                <li>
                                                    <i class="fa-solid fa-circle fa-2xs" style="color: #25546D; font-size: 7px;"></i>
                                                    <span><a href="https://www.carriere-informatique.com/Inf/submitCandidat/{{ offresBtpNgeIng[i].id_offre }}">{{ offresBtpNgeIng[i].intitule }}</a></span>
                                                </li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% else %}
                                <p>Aucune offre disponible actuellement.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

            </div>
            <div class="text-center mt-4"><a href="{{ path('app_Inf_candidat_search') }}" class="btn button-black">Voir toutes les offres</a></div>
        </div>
    </section>
</div>
    <script>
        function redirectTo(value) {
            // Encode the value to include it in the URL
            const encodedValue = encodeURIComponent(value);
            // Construct the URL with the encoded value
            const url = `https://www.carriere-informatique.com/Inf/lists-offre/?contrat=&localisation=&localisation_field=&q=${encodedValue}`;
            // Redirect to the constructed URL
            window.location.href = url;
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
    <script>
        $('.brand-carousel').owlCarousel({
            loop: true,
            margin: 10,
            autoplay: true,
            autoplayTimeout: 1500, // Augmentez cette valeur pour ralentir la vitesse
            slideBy:2,
            responsive: {
                0: {
                    items: 1
                },
                600: {
                    items: 3
                },
                1000: {
                    items: 8
                }
            }
        })

    </script>
    {% block footer %}
        {% include 'carriereInformatique/footer.html.twig' %}
    {% endblock %}
    {% endblock %}
