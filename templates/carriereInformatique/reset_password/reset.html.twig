{% extends 'carriereInformatique/base.html.twig' %}
{% block title %}Se connecter{% endblock %}
{% set title = "Valider votre mot de passe" %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}

{% set description_seo = "Valider votre mot de passe" %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}

<div class="is-sticky">

    {% block head %}
        {% include 'carriereInformatique/header.html.twig' %}
    {% endblock %}
        <br>
        <br>
        <br>
        <br>
        <br>
        <br>
        <br>
        <br>
    {{ form_errors(resetForm) }}
    {{ form_start(resetForm) }}
    <div class="container">
    <div class="info-section">
        <div class="row">
            <h1 style="font-size: 1.25rem; line-height: 1.5;">Changer votre mot de passe</h1>

            <div class="col-lg-6 col-sm-12">

                <div class="form-group">
                    {{ form_row(resetForm.plainPassword.first) }}
                </div>
            </div>
            <div class="col-lg-6 col-sm-12">
                <div class="form-group">
                    {{ form_row(resetForm.plainPassword.second) }}
                </div>
            </div>
        </div>
    </div>
    <button id="validate-button" class="btn btn-primary">Reset password</button>
        {{ form_end(resetForm) }}

</div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var canSubmit = true;  // Flag to track form submission

            document.getElementById("validate-button").addEventListener("click", function (event) {
                // Reset the flag on each form submission attempt
                canSubmit = true;
                var password1 = document.getElementById("{{ resetForm.plainPassword.first.vars.id }}").value;
                var password2 = document.getElementById("{{ resetForm.plainPassword.second.vars.id }}").value;
                var validationResult = validatePassword(password1, password2);
                if (validationResult) {
                    showError(validationResult);
                    canSubmit = false;
                }
                if (!canSubmit) {
                    event.preventDefault();  // Prevent form submission
                }
            });

            // Function to display error message
            function showError(message) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur de saisie',
                    text: message,
                });
            }

            function validatePassword(password1, password2) {
                // Validation that the password length is at least 8 characters
                if (password1.length < 8) {
                    return "Le mot de passe doit contenir au moins 8 caractères.";
                }

                // Validation that both passwords are identical
                if (password1 !== password2) {
                    return "Les mots de passe ne correspondent pas.";
                }

                // If all validations pass, return an empty string (no error)
                return "";
            }
        });
    </script>

    {% block footer %}
        {% include 'carriereInformatique/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}
