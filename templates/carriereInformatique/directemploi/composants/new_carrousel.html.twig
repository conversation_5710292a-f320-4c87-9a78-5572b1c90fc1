<div id="carousel" class="carousel carousel-dark slide" data-bs-ride="carousel" style="padding-top: 10px; padding-bottom: 10px">
    <div class="carousel-inner">
        <div class="carousel-item active">
            <div class="wrapper_carrousel">
            {% for key, boite in boites[0:8] %}
                <div class="logo_recrut">
                    <a target="_blank"  href="{{ boite.url }}">
                        <img src="{{ asset("/uploads/" ~ boite.image) }}" class="d-block w-100 h-100" alt="{{boite.image}}">
                    </a>
                </div>
            {% endfor %}
            </div>
        </div>
        <div class="carousel-item">
            <div class="wrapper_carrousel">
            {% set i = 1 %}
            {% for key, boite in boites[8:] %}
                <div class="logo_recrut">
                    <a target="_blank"  href="{{ boite.url }}">
                        <img src="{{ asset("/uploads/" ~ boite.image) }}" class="d-block w-100 h-100" alt="{{boite.image}}">
                    </a>
                </div>
                {% if i % 8 == 0 and i != 56 %}
            </div>
        </div>
        <div class="carousel-item">
            <div class="wrapper_carrousel">
                {% endif %}
            {% set i = i + 1 %}
            {% endfor %}
            </div>
        </div>
    </div>
</div>