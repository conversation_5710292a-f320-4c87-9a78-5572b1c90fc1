{% if pageCount > 1 %}
    <div class="pagination-center text-center">
        <div class="pagination">

            {# Lien pour la page précédente #}
            {% if previous is defined %}
                <a href="{{ path(route, query|merge({(pageParameterName): previous})) }}">&laquo;</a>
            {% else %}
                <a href="#">&laquo;</a>
            {% endif %}

            {# Liens pour les numéros de page #}
            {% for page in pagesInRange %}
                {% if page != current %}
                    <a href="{{ path(route, query|merge({(pageParameterName): page})) }}">{{ page }}</a>
                {% else %}
                    <a href="#" class="active">{{ page }}</a>
                {% endif %}
            {% endfor %}

            {# Lien pour la page suivante #}
            {% if next is defined %}
                <a href="{{ path(route, query|merge({(pageParameterName): next})) }}">&raquo;</a>
            {% else %}
                <a href="#">&raquo;</a>
            {% endif %}

        </div>
    </div>
{% endif %}
