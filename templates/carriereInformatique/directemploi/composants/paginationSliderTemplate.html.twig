{% block stylesheets %}
{% endblock %}

{% if pageCount > 1 %}
    <table style="margin-right:auto; margin-left:auto;">
	    <tbody>
            <tr>
            {% if previous is defined %}
                <td style="margin-right: 30px;">
                    <div class="pagination" style="width: 90px; line-height: 22px;">
                        <a href="{{ path(route, query|merge({(pageParameterName): previous})) }}">{{ 'label_previous'|trans({}, 'KnpPaginatorBundle') }}</a>
                    </div>
                </td>
            {% endif %}

            {% if next is defined %}
                <td style="padding-left: 30px;">
                    <div class="pagination" style="width: 90px; line-height: 22px;">
                        <a rel="next" href="{{ path(route, query|merge({(pageParameterName): next})) }}">{{ 'label_next'|trans({}, 'KnpPaginatorBundle') }}</a>
                    </div>
                </td>
            {% endif %}
            </tr>
        </tbody>
    </table>
{% endif %}