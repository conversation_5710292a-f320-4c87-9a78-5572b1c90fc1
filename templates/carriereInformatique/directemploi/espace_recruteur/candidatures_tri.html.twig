<h2>Candidatures non triées</h2>

{% if (message_candidature_tri == null) %}
	<table>
		<thead>
			<tr>
				<td> Nom Candidat </td>
				<td> </td>
				<td> Prénom </td>
				<td> Date de postulation </td>
				<td> Nom de l'offre </td>
				<td> Tri </td>
			</tr>
		</thead>

		{% set compteur_couleur = 0 %}
		{% set compteur_modal = 0 %}
		<tbody id="liste_candidature_tri">
		{% for key, infos_candidature in infos_finales_candidatures %}
			{% set divname = key|replace({' ': '_'}) %}
				{% if (compteur_couleur % 2 == 0) %}
					<tr class='couleur1' id="{{ divname }}">
				{% else %}
					<tr class='couleur2' id="{{ divname }}">
				{% endif %}
				<td>
					{% set modal = "modal" ~ compteur_modal %}
					<a href="#" value="{{ modal }}" class="js-modal-open" onclick="openModal(event, '{{ modal }}', '{{ infos_candidature.cv_candidature }}')"> {{ infos_candidature.nom_candidat }} </a>
					<aside value="{{ modal }}" class="modal js-modal-close" id="{{ modal }}" aria-hidden="true" role="dialog" aria-modal="false" aria-labelledby="titlemodal" style="display: none;">
						<div class="modal_wrapper">
							<div class="bouton_fermer_modal">
								<a href="#" value="{{ modal }}" class="js-modal-close" onclick="closeModal(event, '{{ modal }}')">fermer la fenetre</a>
							</div>
							<h1 id="titlemodal"> CV du candidat </h1>
							{% if infos_candidature.cv_candidature == null %}
								<p>Désolé mais l'aperçu du CV n'est pas disponible</p>
							{% else %}
								{% if ".doc" in infos_candidature.cv_candidature %}
									<iframe id="{{ "iframe_" ~ modal }}" name="{{ infos_candidature.cv_candidature }}"></iframe>
								{% else %}
									<iframe src={{ infos_candidature.cv_candidature }}></iframe>
								{% endif %}
							{% endif %}
						</div>
					</aside>
				<td/>
				<td> {{ infos_candidature.prenom_candidat }} </td>
				<td> {{ (infos_candidature.date_candidature)|date('Y-m-d') }} </td>
				<td> {{ infos_candidature.nom_offre }} </td>
				<td>
					{% set formname = "form" ~ divname %}
					<form name="form" id="{{ formname }}">
						<input type="hidden" name="id_user" id="id_user" value="{{ id }}" />
						<input type="hidden" name="id_candidature_tri" id="id_candidature_tri" value="{{ key }}" />

						<input type="image" src="/images/directemploi/tri_check_green.png" alt="submit" id="interessant" onclick="sendForm(event, {{ formname }}, 'interessant', {{ divname }})"/>

						<input type="hidden" name="id_user" id="id_user" value="{{ id }}" />
						<input type="hidden" name="id_candidature_tri" id="id_candidature_tri" value="{{ key }}" />

						<input type="image" src="/images/directemploi/tri_check_red.png" alt="submit" id="ininteressant" onclick="sendForm(event, {{ formname }}, 'ininteressant', {{ divname }})"/>
					</form>
				</td>

				{% set compteur_couleur = compteur_couleur + 1 %}
				{% set compteur_modal = compteur_modal + 1 %}
			{% endfor %}
		</tbody>
	</table>

{% else %}

	<div class="message_candidature">
		{{ message_candidature_tri }}
	</div>

{% endif %}

<script type="text/javascript">

	const sendForm = function(event, formname, status, Elem2Hide) {
		formdata = new FormData(formname);
		var datas = {
			"status": status,
			"id_user": formdata.get("id_user"),
			"id_candidature_tri": formdata.get("id_candidature_tri")
		};

		$.ajax({
			type: "POST",
			url: "espaceRecruteur",
			data: datas,
		}).done(function (data) {
		});
		event.preventDefault();
		hideElement(Elem2Hide);
	};

	const openModal = function(e, modal, cvname) {
		e.preventDefault()
		if (cvname.includes(".doc")) {
			let download = document.getElementById('iframe_' + modal);
			download.src = download.name;
		} else {
			var target = document.getElementById(modal);
			target.style.display = "block"
			target.removeAttribute('aria-hidden')
			target.setAttribute('aria-modal', 'true')
		}
	}

	const closeModal = function(e, modal) {
		e.preventDefault();
		var target = document.getElementById(modal);
		target.style.display = "none";
		target.setAttribute('aria-hidden', 'true');
		target.setAttribute('aria-modal', 'false');
	}

</script>