{% if cv_saves|length > 0 %}
<table width="100%" class="listing_offres" cellspacing="0" cellpadding="0">
	<tbody>
		{% for key, cv_save in cv_saves %}
			<tr class="listing_offre {{ key % 2 == 0 ? 'even' : 'odd' }}">
				<td style="padding:10px" class="intitule">
					{{ cv_save.libelle }}
				</td>
				<td id="note_cv_up">
                    <a {# href="{{ path('cvtheque/list?search= ~ cv_save.getIdRecruteurCvVeille()') }}" #} title="Voir les profils">Voir les profils</a>
                    |
                    <a {# href="{{ path('espaceRecruteur/editAlerteCv?id_recruteur_cv_veille= ~ cv_save.getIdRecruteurCvVeille()') }}" #} title="Modifier les critères">Modifier les critères</a>
					|
                    <a {# href="{{ path('espaceRecruteur/deleteAlerteCv?id_recruteur_cv_veille= ~ cv_save.getIdRecruteurCvVeille()') }}" #} class="rouge" title="Supprimer">Supprimer</a>
                        {# /\ #}
                        {# ajouter ça a la ligne #}
    					{# 'update' => 'list_alerte_cv' #}
                    </span>
				</td>
			</tr>
			<tr><td height="10px" colspan="3"></TD></tr>
		{% endfor %}
	</tbody>
</table>
{% else %}
    <div id="txt_cv_no_com">
        Vous n'avez aucune recherche enregistrée actuellement
    </div>
{% endif %}