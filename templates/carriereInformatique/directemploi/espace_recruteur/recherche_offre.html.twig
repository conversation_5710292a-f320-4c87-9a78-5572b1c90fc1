{% block stylesheets %}

{% endblock %}

<div id="zoneListingOffres">

	{% if tri_offres|length > 0 %}
		<table width="100%" class="listing_offres" id="tableListingOffres" cellspacing="0" cellpadding="0">
			<tbody>
				{% for key, tri_offre in tri_offres %}
					<tr> <td height="10px" colspan="3"></td> </tr>
					<tr {{ key % 2 == 0 ? 'class="even"' : 'class="odd"' }}>
						<td class="listing_offre" width="340px">
							<div class="intitule">
								<a href={{ "candidatOffre/" ~ tri_offre.id_offre }}> {{ tri_offre.intitule|raw }} </a>
							</div>	
							<span class="localisation">Type offre :
								{{ tri_offre.type_offre }}
							</span>
							&nbsp;
							- &nbsp;<span class="ref">Ref : {{ tri_offre.reference|raw }}</span><br/>
							<span class="list_date"><PERSON><PERSON>e le {{ tri_offre.date_creation|date('d/m/Y') }}</span><br/>
							{% if (tri_offre.date_creation != tri_offre.date_soumission and tri_offre.statut == 1) %}
								<span class="list_date">Mise à jour le {{ tri_offre.date_soumission|date('d/m/Y') }}</span>
							{% endif %}
						</td>
					<td width="35px" align="center">
						{% if tri_offre.statut == 1 %}
							<div class="statut_on">&nbsp;</div>
						{% else %}
							<div class="statut_off">&nbsp;</div>
						{% endif %}
					</td>
					<td class="act" style="white-space: normal;">
						<a href={{ "/espaceRecruteur/editerUneOffre/" ~ tri_offre.id_offre_commerciale ~ "/" ~ tri_offre.id_offre }}> Modifier </a>
						|
						<a href={{ "/espaceRecruteur/dupliquerUneOffre/" ~ tri_offre.id_offre_commerciale ~ "/" ~ tri_offre.id_offre }}> Dupliquer </a>
						|
						<form id="renouveler{{ tri_offre.id_offre }}" style="display:inline">
							<a href="{{ path('app_renouveller_off', {id_offer: tri_offre.id_offre}) }}" onclick="if(confirm('Êtes vous sur de vouloir renouveller cette offre ?'))"> Renouveller </a>
						</form>
						|
						{% if tri_offre.statut == 1 %}
							<form id="suspendre{{ tri_offre.id_offre }}" style="display:inline">
								<a href="{{ path('app_suspend_off', {id_offer: tri_offre.id_offre}) }}" onclick="if(confirm('Êtes vous sur de vouloir suspendre cette offre ?'))"> Suspendre </a>|
							</form>
						{% endif %}
						
						<form id="delete{{ tri_offre.id_offre }}" style="display:inline">
							<a href="{{ path('app_remove_off', {id_offer: tri_offre.id_offre}) }}" class="rouge" onclick="if(confirm('Êtes vous sur de vouloir supprimer cette offre ?'))">Supprimer</a>
						</form>
					</td>
					<td>
						<div class="admin_candidature">
							{# {% if (tri_offre.nb_candids > 0) %}
								&nbsp;
								{% set txt_lien = tri_offre.nb_candids ~ " candidature" %}
								{% if (tri_offre.nb_candids > 1) %}
									{% set txt_lien = txt_lien ~ "s" %}
								{% endif %}
								<a href={{ "/espaceRecruteur/showCandidatures/" ~ tri_offre.id_offre }}> {{ txt_lien }} </a>
							{% endif %} #}
						</div>
					</td>
					{% if tri_offres|length > 1 %}
						<td class="checkbox">
							<input type="checkbox" id="select_{{ tri_offre.id_offre }}" value="{{ tri_offre.id_offre }}">
						</td>
					{% endif %}
					</tr>	
				{% endfor %}
			</tbody>
		</table>
	{#{% if tri_offres|length > 1 %}
		<table style="width:100%;text-align:center">
			<tr>
				<form id="edit_offres">
					<td>
						<div class="submit submit_large" id="nombre" style="margin-top:5px">
							<input type="submit" value="Renouveller les offres sélectionnées" onClick="editOffres(event, 'renouveler', 'null');">
						</div>
					</td>
					<td>
						<div class="submit submit_large" id="nombre" style="margin-top:5px">
							<input type="submit" value="Suspendre les offres sélectionnées" onClick="editOffres(event, 'suspendre', 'null');">
						</div>
					</td>
				</form>
			</tr>
		</table>		
	{% endif %}#}

	<div align="center" id="pagination_de">
   		{{ knp_pagination_render(tri_offres, 'directemploi/composants/paginationTemplate.html.twig') }}
	</div>

{% else %}
	<div id="tableListingOffres">
		<div class="nothing">Aucune offre trouvée dans la base de données</div>
	</div>
{% endif %}
</div>

<script type="text/javascript">
	const sendTriForm = function(event, formname, value) {
		formdata = new FormData(formname);
		var datas = {
			"data" : value,
		};

		$.ajax({
			type: "POST",
			url: "espaceRecruteur",
			data: datas,
		}).done(function (data) {
		});
		event.preventDefault();
		$("#liste_offres").load(" #liste_offres");
	};

	const deleteOffer = function(id_offer) {
		let stats_div = document.querySelectorAll('div[name="offres_stat"]');
		if (confirm("Êtes vous sur de vouloir supprimer cette offre ?")) {
			// id_offer = document.getElementById("delete" + id_offer);
			window.location.href = "/espaceRecruteur/removeOffre?id_off=" +/""
			// formdata = new FormData(formname);
			// var datas = {
			// 	"offer_to_delete" : id_offer,
			// };

			// $.ajax({
			// 	type: "POST",
			// 	url: "espaceRecruteur/removeOffre",
			// 	data: datas,
			// }).done(function (data) {
			// });
			// event.preventDefault();
			// $("#liste_offres").load(" #liste_offres");
			// for (var i in stats_div)
			// 	$("#" + stats_div[i].id).load(" #" + stats_div[i].id);
		}
	};

	const editOffres = function(event, operation, unique_id) {

		let id = "";
		let formname = null;
		if (unique_id != 'null') {
			id = unique_id;
			formname = document.getElementById(operation + unique_id)
		} else {
			let count = 0;
			let ids = document.querySelectorAll('input[type="checkbox"]')
			formname = document.getElementById("edit_offres");
			for (var i in ids) {
				if (ids[i].checked) {
					id += ids[i].value;
					id += "_";
				}
			}
			if (id != "")
				id = id.slice(0, -1);
		}
		if (id.length > 0) {
			formdata = new FormData(formname);
			var datas = {
				"ids_to_manage" : id,
				"operation" : operation,
			};

			$.ajax({
				type: "POST",
				url: "espaceRecruteur",
				data: datas,
			}).done(function (data) {
			});
			$("#liste_offres").load(" #liste_offres");
		}
		event.preventDefault();
	}
</script>
