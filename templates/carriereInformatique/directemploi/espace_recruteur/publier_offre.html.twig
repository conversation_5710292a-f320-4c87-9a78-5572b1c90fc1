{% extends 'directemploi/layout.html.twig' %}

{% block title %} Créer votre offre | Direct Emploi {% endblock %}

{% block stylesheets %}
{% endblock %}

{% block content %}

<h1>Déposer une offre</h1>

	<div id="fil_ariane">
		<a href="{{ path('directemploi') }}" title="Accueil">Accueil</a> -
		<a href="{{ path('app_espace_recruteur') }}" title="Accueil">Recruteur</a>
	</div>

{{ form_start(DiffusionOffre) }}
	<div class="form_box" id="top_page">
	<h2>1 - Informations sur le poste</h2>
		<table class="formulaire">
			<tr>
				<td class='label'> {{ form_label(DiffusionOffre.titre_offre,"Titre de l'offre") }} <span class="ob">*</span></td>
				<td> {{ form_widget(DiffusionOffre.titre_offre,{ 'attr': {'placeholder': 'Titre de l\'offre'} }) }} </td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.reference_offre,"Réference") }} <span class="ob">*</span></td>
				<td> {{ form_widget(DiffusionOffre.reference_offre,{ 'attr': {'placeholder': 'Réference'} }) }} </td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.type_contrat_offre,"Type de contrat") }} <span class="ob">*</span></td>
				<td> {{ form_widget(DiffusionOffre.type_contrat_offre,{ 'attr': {'placeholder': 'Sélectionnez votre type de contrat'} }) }} </td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.duree_contrat_offre,"Durée du contrat") }} </td>
				<td> {{ form_widget(DiffusionOffre.duree_contrat_offre,{ 'attr': {'placeholder': 'Durée du contrat'} }) }} </td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.date_debut_contrat_offre,"Date de début") }} </td>
				<td> {{ form_widget(DiffusionOffre.date_debut_contrat_offre,{ 'attr': {'placeholder': 'Date de début'} }) }} </td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.fourchette_remuneration_offre,"Rémuneration") }} </td>
				<td> {{ form_widget(DiffusionOffre.fourchette_remuneration_offre,{ 'attr': {'placeholder': 'Sélectionnez votre date de rémuneration'} }) }} </td>
			</tr>

			<tr>
				<td class='label'><b>Poste accessible aux personnes<br>en situation de handicap</b><span class="ob"> *</span></td>
				<td class='label'> {{ form_widget(DiffusionOffre.handicap_offre[0], { 'attr': {'placeholder': ''} }) }} &nbsp; {{ form_label(DiffusionOffre.handicap_offre[0]) }} <br>
								{{ form_widget(DiffusionOffre.handicap_offre[1], { 'attr': {'placeholder': ''} }) }} &nbsp; {{ form_label(DiffusionOffre.handicap_offre[1]) }}</td>
			</tr>

			<tr>
				<td class='label'><b>Anonymat</b></td>
				<td class='label'>  {{ form_widget(DiffusionOffre.anonymat_offre[0], { 'attr': {'placeholder': ''} }) }} &nbsp; {{ form_label(DiffusionOffre.anonymat_offre[0]) }}
			</tr>

			<tr id="rep_mail">
				<td class='label'>{{ form_label(DiffusionOffre.reponse_mail_offre,"Réponse par mail") }} </td>
				<td id="reponse_mail"> {{ form_widget(DiffusionOffre.reponse_mail_offre,{ 'attr': {'placeholder': 'Votre mail'} }) }} </td>
			</tr>

			<tr id="rep_site">
				<td class='label'> {{ form_label(DiffusionOffre.reponse_site_offre,"Réponse sur le site") }} </td>
				<td id="reponse_site"> {{ form_widget(DiffusionOffre.reponse_site_offre, { 'attr': {'placeholder': 'URL du site'} }) }} </td>
				<div id="res_site" value="{{ DiffusionOffre.reponse_site_offre.vars.value }}" >
			</tr>
		</table>
	</div>

	{% include '/directemploi/espace_recruteur/info_reprise.html.twig' %} {# 2 - #}

	<div class="form_box">   
	<h2>3 - Fonction / secteur et localisation géographique</h2>               
		<table class="formulaire">
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.secteur_activite, "Fonction / Domaine du poste à pourvoir")}} <span class="ob"> *</span></td>
				<td> {{ form_widget(DiffusionOffre.secteur_activite,{ 'attr': {'placeholder': "Fonction / Domaine du poste à pourvoir"} })}} </td>
			</tr>
			{# <tr id="ss_sct">
				<td class='label'>{{ form_label(DiffusionOffre.sous_secteur_offre, "Domaine précis du poste à pourvoir")}} </td>
				<td> {{ form_widget(DiffusionOffre.sous_secteur_offre,{ 'attr': {'placeholder': "Domaine précis du poste à pourvoir"} })}} </td>
			</tr> #}
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.region_offre, "Region")}} <span class="ob"> *</span></td>
				<td> {{ form_widget(DiffusionOffre.region_offre,{ 'attr': {'placeholder': "Region"} })}} </td>
			</tr>
			<tr id="dept">
				<td class='label'>{{ form_label(DiffusionOffre.dept_offre, "Département")}} </td>
				<td> {{ form_widget(DiffusionOffre.dept_offre,{ 'attr': {'placeholder': "Département"} })}} </td>
			</tr>
			<tr id="villes">
				<td class='label'>{{ form_label(DiffusionOffre.villes_offre, "Ville")}} </td>
				<td> {{ form_widget(DiffusionOffre.villes_offre,{ 'attr': {'placeholder': "Ville"} })}} </td>
			</tr>
		</table>
	</div>
	<div class="form_box">
	<h2>4 - Profil du poste et descriptif</h2>
		<table class="formulaire">
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.descriptif_offre, "Descriptif de l'offre")}} <span class="ob"> *</span></td>
				<td> {{ form_widget(DiffusionOffre.descriptif_offre,{ 'attr': {'placeholder': "Descriptif de l'offre"} })}} </td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.type_formation_offre, "Type de formation")}} <span class="ob"> *</span></td>
				<td> {{ form_widget(DiffusionOffre.type_formation_offre,{ 'attr': {'placeholder': "Type de formation"} })}} </td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.exp_requise_offre, "Expérience requise")}} <span class="ob"> *</span></td>
				<td> {{ form_widget(DiffusionOffre.exp_requise_offre,{ 'attr': {'placeholder': "Expérience requise"} })}} </td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.profil_offre, "Profil")}} </td>
				<td> {{ form_widget(DiffusionOffre.profil_offre,{ 'attr': {'placeholder': "Profil"} })}} </td>
			</tr>
		</table>
	</div>
	<div class="act">
		<button type="submit" class="btn" id="btn_submit" >Valider et continuer</button>
	</div>

{{ form_end(DiffusionOffre) }}

<script language="javascript">

    window.onload = () => { // let region = document.querySelector("#inscription_candidat_etape1_geo_liste_region");
		let domaine = document.querySelector("#diffusion_offre_secteur_activite");
		let region = document.querySelector("#diffusion_offre_region_offre");

		region.addEventListener("change", function () {
			let form = this.closest("form");
			let data = this.name + "=" + this.value;

			fetch(form.action, {
				method: form.getAttribute("method"),
				body: data,
				headers: {
				"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
				}
			}).then(response => response.text()).then(html => {
				let content = document.createElement("html");
				content.innerHTML = html;
				let nouveauSelect = content.querySelector("#diffusion_offre_dept_offre");
				document.querySelector("#diffusion_offre_dept_offre").replaceWith(nouveauSelect);
			})
		});

		domaine.addEventListener("change", function () {
			let form = this.closest("form");
			let data = this.name + "=" + this.value;

			fetch(form.action, {
				method: form.getAttribute("method"),
				body: data,
				headers: {
					"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
				}
			}).then(response => response.text()).then(html => {
				let content = document.createElement("html");
				content.innerHTML = html;
				let nouveauSelect = content.querySelector("#diffusion_offre_sous_secteur_offre");
				document.querySelector("#diffusion_offre_sous_secteur_offre").replaceWith(nouveauSelect);
			})
		});
	}


</script>

{% endblock %}
