{% extends 'directemploi/layout.html.twig' %}
{% block title %}inscription pour tests gratuit
{% endblock %}

{% block content %}

<div class="site">
	<h2><PERSON><PERSON><PERSON> mon compte</h2>
	<div class="block_promo">
		<div class="content">Notre offre est accessible uniquement aux entreprises certifiés. Nous contrôlons l'identité de chaque société. Pour l'<strong>inscription</strong>
			et la
			<strong>diffusion d'offres</strong>, l'utilisation d'un email professionnel (ex :
			<b><EMAIL></b>) et non une adresse email générique (ex :
			<b><EMAIL></b>), est obligatoire.</div>
	</div>

	<div class="obli">
		<p>Les champs marqués d'une astérisque
			<span class="ob">
				*</span>
			sont obligatoires</p>
	</div>

	{{ form_start(InscriptionEssaiGratuit) }}

	<div class="form_box">
		<h3>Votre compte recruteur</h3>
		<table class="formulaire">

			<tr>
				<td class="label">{{ form_label(InscriptionEssaiGratuit.login, "Login")}}<span class="ob">
						*</span>
				</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.login,{ 'attr': {'placeholder': 'Login'} })}}</td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.passCrypt,"Mot de passe")}}<span class="ob">
						*</span>
				</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.passCrypt,{ 'attr': {'placeholder': 'Mot de passe'} })}}</td>

			</tr>

		</table>

	</div>
	<div class="form_box">
		<h3>Votre identité</h3>
		<table class="formulaire">
			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.civilite,"Civilité")}}<span class="ob">
						*</span>
				</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.civilite,{ 'attr': {'placeholder': 'Login'} })}}</td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.nom,"Nom")}}<span class="ob">
						*</span>
				</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.nom,{ 'attr': {'placeholder': 'Nom'} })}}</td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.prenom,"Prénom")}}<span class="ob">
						*</span>
				</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.prenom,{ 'attr': {'placeholder': 'Prénom'} })}}</td>
			</tr>
		</table>
	</div>
	<div class="form_box">
		<h3>Vos coordonnées</h3>
		<table class="formulaire">
			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.email,"Email")}}<span class="ob">
						*</span>
				</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.email,{ 'attr': {'placeholder': 'Email'} })}}</td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.telephone_fixe,"Téléphone fixe")}}<span class="ob">
						*</span>
				</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.telephone_fixe,{ 'attr': {'placeholder': 'Téléphone Fixe'} })}}</td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.telephone_portable,"Téléphone portable")}}<span class="ob">
						*</span>
				</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.telephone_portable,{ 'attr': {'placeholder': 'Téléphone Portable'} })}}</td>
			</tr>
		</table>
	</div>
	<div class="form_box">
		<h3>Votre entreprise</h3>
		<table class="formulaire">
			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.logo,"Logo")}}</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.logo,{ 'attr': {'placeholder': 'Logo'} })}}</td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.raison_sociale,"Raison sociale")}}<span class="ob">
						*</span>
				</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.raison_sociale,{ 'attr': {'placeholder': 'Raison sociale'} })}}</td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.site_internet,"Site internet")}}</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.site_internet,{ 'attr': {'placeholder': 'Site internet'} })}}</td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(InscriptionEssaiGratuit.presentation_entreprise,"Présentation de l'entreprise")}}<span class="ob">
						*</span>
				</td>
				<td>{{ form_widget(InscriptionEssaiGratuit.presentation_entreprise,{ 'attr': {'placeholder': 'Présentation de l\'entreprise'} })}}</td>
			</tr>
				<tr>
					<td class='label'>{{ form_label(InscriptionEssaiGratuit.domaine_activite, "Domaine d'activité de l'entreprise")}}<span class="ob"> *</span></td>
					<td>{{ form_widget(InscriptionEssaiGratuit.domaine_activite,{ 'attr': {'placeholder': 'Login'} })}}</td>
				</tr>
				<tr>
					<td class='label'>{{ form_label(InscriptionEssaiGratuit.adresse,"Adresse")}}<span class="ob"> *</span></td>
					<td>{{ form_widget(InscriptionEssaiGratuit.adresse,{ 'attr': {'placeholder': 'Adresse'} })}}</td>
				</tr>
				<tr>
					<td class='label'>{{ form_label(InscriptionEssaiGratuit.complement_adresse, "Complement d'adresse") }}</td>
					<td>{{ form_widget(InscriptionEssaiGratuit.complement_adresse,{ 'attr': {'placeholder': 'Complément d\'adresse'} })}}</td>
				</tr>
				<tr>
					<td class='label'>{{ form_label(InscriptionEssaiGratuit.code_postal,"Code Postal")}}<span class="ob"> *</span></td>
					<td>{{ form_widget(InscriptionEssaiGratuit.code_postal,{ 'attr': {'placeholder': 'Code postal'} })}}</td>
				</tr>
				<tr>
					<td class='label'>{{ form_label(InscriptionEssaiGratuit.ville, "Ville")}}<span class="ob"> *</span></td>
					<td>{{ form_widget(InscriptionEssaiGratuit.ville,{ 'attr': {'placeholder': 'Ville'} })}}</td>
				</tr>

			</table>
		</div>
		<div class="act">
			<button type="submit" class="btn" id="btn_submit">Valider et continuer</button>
		</div>

		{{ form_end(InscriptionEssaiGratuit) }}

		<div class='smaller'>
			<br/>Les informations recueillies à partir de ce formulaire font l’objet d’un traitement informatique destiné à<strong>Direct Emploi – Direct Performance</strong>pour une gestion de<strong>comptes et clientèle</strong>. 
								Le destinataire de ces données est notre<strong>service commercial</strong>.
								Conformément à la loi « informatique et libertés » du 6 janvier 1978 modifiée, vous disposez d’un droit d’accès et de rectification aux informations qui vous concernent.   Vous pouvez accéder aux informations vous concernant en vous adressant à :<a href="mailto:<EMAIL>"><EMAIL></a>.
								Vous pouvez également, pour des motifs légitimes, vous opposer au traitement des données vous concernant.</div>

		<div class="box_alaune ordi" style="margin-top:20px;">
			<h2>Notre réseau</h2>
			<div class="int clearfix">
				<div class="item">
					<img alt="Notre reseau" src="/images/bandeau_reseau2.png">
					<img alt="Apec" src="/images/carre_apec.png">
					<img alt="Pole Emploi" src="/images/carre_poleemploi.png">
				</div>
			</div>
		</div>
	</div>
{% endblock %}
