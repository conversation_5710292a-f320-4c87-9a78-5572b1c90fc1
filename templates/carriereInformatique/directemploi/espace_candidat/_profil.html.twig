
	<h2 style="text-align: center;">Mon profil</h2>
    <br>
	
    <p class="hr_p"><strong>

        	{% if user[0].userCiviliteIdUserCivilite == 1 %}
        	    M. 
        	{% elseif user[0].userCiviliteIdUserCivilite == 2 %}
        	    Mme 
        	{% endif %}
    
        	{{ user[0].prenom }} {{ user[0].nom}}
    
    </strong></p>							

			
			
		<div class="espace_cand_content" style="text-align:left;">

			<ul>	
				<li><strong>Login : </strong> {{ user[0].login}}</li>
				<li><strong>Email : </strong> {{ user[0].email}}</li>
 				<li><strong>CV visible : </strong> {{ (user_cv and (user_cv.isVisible == 1)) ? 'oui' : 'non' }}</li>
  				<li><strong>Profil anonyme : </strong> {{ (user_cv and (user_cv.isAnonym == 1)) ? 'oui' : 'non' }}</li>
			</ul>

			<h3>Mes informations personnelles</h3>

			<ul>
				<li><strong>Date de naissance : </strong>{{ user[0].dateNaissance}}</li>
				<li><strong>Numéro de téléphone : </strong> {{ user[0].telephone}}</li>
				<li><strong>Numéro de portable : </strong> {{ user[0].mobile}}</li>
				<li><strong>Adresse : </strong> {{ user[1].adresse1}}</li>
				<li><strong>Complement d'adresse : </strong> {{ user[1].adresse2}}</li>
				<li><strong>Code postal : </strong> {{ user[1].codePostal}}</li>
				<li><strong>Ville : </strong> {{ user[1].nomVille}}</li>
			</ul>

				<h3>Ma situation actuelle</h3>
				<ul>
					<li><strong>Niveau de formation : </strong>{{ (user_situation["niveau_formation"] is defined) ? user_situation["niveau_formation"] : '' }}</li>
					<li><strong>Etablissement / Spécialité : </strong> {{ (user_situation["etablissement"] is defined) ? user_situation["etablissement"] : '' }} </li>
					<li><strong>Année d'obtention : </strong> {{ (user_situation["annee_obtention"] is defined) ? user_situation["annee_obtention"] : '' }} </li>
					<li><strong>Nom du diplôme : </strong> {{ (user_situation["nom_diplome"] is defined) ? user_situation["nom_diplome"] : '' }} </li>
					<li><strong>Nom du métier : </strong> {{ (user_situation["nom_metier"] is defined) ? user_situation["nom_metier"] : '' }} </li>
					<li><strong>Poste actuel (ou dernier poste) : </strong> {{ (user_situation["poste"] is defined) ? user_situation["poste"] : '' }} </li>
					<li><strong>Secteur d'activité / fillière : </strong> {{ (user_situation["secteur"] is defined) ? user_situation["secteur"] : '' }} </li>
					<li><strong>Salaire du poste : </strong>{{ (user_situation["remu"] is defined) ? user_situation["remu"] : '' }} </li>
					<li><strong>Activité : </strong> {{ (user_situation["activite"] is defined) ? user_situation["activite"] : '' }} </li>
					<li><strong>Nombre d'années d'expérience : </strong> {{ (user_situation["experience"] is defined) ? user_situation["experience"] : '' }} </li>
					<li><strong>Disponibilité : </strong> {{ (user_situation["dispo"] is defined) ? user_situation["dispo"] : '' }} </li>
					<li><strong>Mobilité : </strong> {{ (user_situation["mobilite"] is defined) ? user_situation["mobilite"] : '' }} </li>
				</ul>

				<h3>Mes compétences</h3>
				<ul>
					<li><strong>Langue étrangère 1 : </strong>{{ (user_competences["langue1"] is defined) ? user_competences["langue1"] : '' }}</li>
					<li><strong>Niveau : </strong> {{ (user_competences["niveau_langue1"] is defined) ? user_competences["niveau_langue1"] : '' }} </li>
					<li><strong>Langue étrangère 2 : </strong> {{ (user_competences["langue2"] is defined) ? user_competences["langue2"] : '' }} </li>
					<li><strong>Niveau : </strong>{{ (user_competences["niveau_langue2"] is defined) ? user_competences["niveau_langue2"] : '' }} </li>
					<li><strong>Permis B : </strong> {{ (user_competences["permis_b"] is defined) ? user_competences["permis_b"] : '' }} </li>
					<br>
					<li><strong>(Ces informations ne seront jamais transmises sans votre accord)</strong></li>
				</ul>
		</div>

        <div class="floatR"> <br>
			<a class="submit sub_jaune sub_large" href="/espaceCandidat/gestionCompte" title="Modifier mon profil">Modifier mon profil</a><br>
			<em>Remplissez votre profil<br> candidat pour permetre aux<br> recruteurs de vous contacter</em>
		</div>