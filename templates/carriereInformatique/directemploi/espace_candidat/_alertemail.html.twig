{#% extends 'directemploi/espace_candidat/index.html.twig' %#}

{% block stylesheets %}
	<link rel="stylesheet" href="{{ asset('assets/css/alerte_mail_cand.min.css') }}">
{% endblock %}

<h2>Mes favoris</h2>
<div id="alertmail">

    {% if user_favorites != null %}
        {% for user_fav in user_favorites %}
            {% set idAlert, metier, locType, loc, contrat, url =
            user_fav.getIdAlertemail(), user_fav.getMetier(), user_fav.getTypeRegion(),
            user_fav.getRegion(), user_fav.getContrat(), user_fav.getUrl() %}
            <div class="categorie-tables">
                <form action="{{ (path('app_espace_candidat_favorite_delete')) }}" method="POST">                
                    <div id="search-info">
                    <input name="myIdAlertMail" id="myIdAlertMail" value="{{idAlert}}" readonly hidden>
                        {% set metier = metier ? metier | lowerAllUpperFirst : "Non précisé" %}
                        Domaine : {{metier}}<br>
                        {% if locType is defined and loc != "" %}
                            {#{locType | lowerAllUpperFirst}#}
                            Localisation : {{loc}}<br>
                        {% else %}
                            Localisation : Non précisé<br>
                        {% endif %}
                        Contrat : {{contrat ? allContracts[contrat] : "Non précisé"}}<br>
                    </div>
                    <a href="{{url}}">
                        <span id='url-text'>Voir la recherche</span>
                    </a>
                    <button id='delete-text' style="text-align:center;width:96%;">
                        Supprimer
                    </button>
                </form>
            </div>
        {% endfor %}
    {% else %}
        Aucune recherche n'a été sauvegardée
    {% endif %}
</div>

<div class="floatR"> <br>
	<a class="submit sub_jaune sub_large" href="/lists-offre/" title="Rechercher des jobs">Rechercher des emplois</a><br>
	<em>Trouver de nouveaux<br> jobs et ajoutez-les<br> à votre alerte mail</em>
</div>
