{% extends 'carriereInformatique/base.html.twig' %}
{% block title %}Se connecter{% endblock %}

{% block body %}

<div class="is-sticky">
        {% block head %}
            {% include 'carriereInformatique/header.html.twig' %}
        {% endblock %}
    <nav class="navbar navbar-expand-lg submenu"style="position:fixed; z-index: 998; margin-top:72.5px">
        <div class="container">


            <div class="collapse navbar-collapse ms-auto" id="navbarNavSubMenu">

                <ul class="navbar-nav ">
                    <li class="nav-item">
                        <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidat') }}">Mon profil</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidatprofilcv') }}">Mon CV</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidatprofiloffre') }}">Mes offres</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidatprofilmail') }}">Mes alertes mail</a>
                    </li>

                </ul>
                <a href="#" class=" ml-auto text-white-50">Se désabonner</a>
            </div>

        </div>
    </nav>
    <section class="candidat-section" id="">
        <div class="container">
            <div class="d-flex flex-row justify-content-between align-items-center flex-wrap">
                <h1 style="font-weight: 500;font-size: 1.25rem; line-height: 1.5; ">Vous avez enregistré <span class="yellow">1 CV</span>. Vous êtes abonné à <span class="yellow">1 alerte mail</span>.</h1>
                <a href="{{ path('app_BTP_modifierprofil') }}" class="btn mb-2 px-4 button-black">Modifier mon profil</a>
            </div>
            <div class="w-100 profil-card">
                <div class="body-card">
                    <div class="first-card-section justify-content-between">
                        <div class="user-title text-center">
                            <p class="li-title mb-1">{{ user[0].nom }}{{ user[0].prenom }}</p>
                            <p class="text-muted m-0 ">{{ user[0].login }}</p>
                            <p class="text-muted m-0 ">{{ user[0].email }}</p>
                        </div>
                        {% if user_cv!=null %}
                        <ul>
                            {% if user_cv.isvisible==1 %}
                            <li><strong>CV visible</strong> Oui</li>
                                {% else %}
                            <li><strong>CV visible</strong> Non</li>
                            {% endif %}
                            {% if user_cv.isanonym==1 %}
                            <li><strong>Profil anonyme</strong> Oui</li>
                            {% else %}
                            <li><strong>Profil anonyme</strong> Non</li>
                            {% endif %}

                        </ul>
                        {% endif %}
                    </div>

                    <div class="card-section">
                        <div class="card-divider"></div>
                        <div class="section-list">
                            <p class="li-title">Mes informations personnelles :</p>
                            <ul>
                                <li><strong>Pays : </strong> {{ user[1].pays }}</li>
                                <li><strong>Numéro de portable : </strong>{{ user[0].telephone }}</li>
                                <li><strong>Adresse : </strong>{{ user[1].adresse1 }}</li>
                                <li><strong>Complement : </strong>{{ user[1].adresse2 }}</li>
                                <li><strong>Code postal : </strong> {{ user[1].codepostal }}</li>
                                <li><strong>Ville : </strong> {{ user[1].nomville }}</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-section">
                        <div class="card-divider"></div>
                        <div class="section-list">
                            <p class="li-title">Ma recherche d'emploi :</p>
                            <ul>
                                {% if user[2] is defined and user[2] is not null %}
                               {#  <li><strong>Type de poste recherché</strong> Guatemala</li>#}
                                <li><strong>Type de contrat souhaité</strong> {{ user[2].contrat }}</li>
                                <li><strong>Salaire souhaité</strong> {{user[2].remuneration}}</li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>

                </div>
                <div class="footer-profil-card text-center">
                    <p class="text-muted m-0">(Ces informations ne seront jamais transmises sans votre accord)</p>
                </div>
            </div>
            {% if user_situation is not null %}
            <div class="w-100 profil-card">
                <div class="body-card">
                    <div class="first-card-section">
                        <p class="li-title">Ma situation actuelle :</p>
                        <ul>
                            <li><strong>Niveau de formation : </strong> {{ user_situation.formation | default('N/A') }}</li>
                            <li><strong>Etablissement / Spécialité : </strong>{{user_situation.etablissement | default('N/A')}}</li>
                            <li><strong>Année d'obtention : </strong> {{user_situation.annediplome | default('N/A')}}</li>
                            <li><strong>Nom du diplome : </strong> {{user_situation.nomdiplome | default('N/A')}}</li>
                            <li><strong>Nom du Metier : </strong> {{ user_situation.metier | default('N/A') }}</li>
                            <li><strong>Secteur d'activité / filière : </strong> {{ user_situation.secteuractivite | default('N/A') }}</li>
                        </ul>
                    </div>

                    <div class="card-section">
                        <div class="card-divider"></div>
                        <div class="section-list">
                            <ul>
                                {% if user_situation.salairesouahite is defined %}
                                <li><strong>Salaire du poste</strong>{{user_situation.salairesouahite}}</li>
                                {% endif %}
                                {% if user_situation.acitivite is defined %}
                                <li><strong>Activité</strong> {{user_situation.acitivite}}</li>
                                {% endif %}
                                {% if user_situation.experience is defined %}
                                <li><strong>Expérience</strong>  {{user_situation.experience}}</li>
                                {% endif %}
                                {% if user_situation.dispo is defined %}
                                <li><strong>Disponibilité</strong> {{user_situation.dispo}}</li>
                                {% endif %}
                                {% if user_situation.mobilite is defined %}
                                <li><strong>Mobilité</strong> {{user_situation.mobilite}}</li>
                                {% endif %}

                            </ul>
                        </div>
                    </div>
                    {% endif %}
                    <div class="card-section">
                        <div class="card-divider"></div>
                        <div class="section-list">
                            <ul>
                                {% if user_competences.comp is defined %}
                                <li><strong>Mes compétences: </strong>{{ user_competences.comp }}</li>
                                {% endif %}
                                {% if user_competences.language1 is defined %}
                                <li><strong>Langue étrangère 1</strong> {{ user_competences.language1 }}</li>
                                {% endif %}
                                {% if user_competences.nivlang1 is defined %}
                                <li><strong>Niveau</strong> {{ user_competences.nivlang1 }}</li>
                                {% endif %}
                                {% if user_competences.language2 is defined %}
                                <li><strong>Langue étrangère 2</strong> {{ user_competences.language2 }}</li>
                                {% endif %}
                                {% if user_competences.nivlang2 is defined %}
                                <li><strong>Niveau</strong> {{ user_competences.nivlang2 }}</li>
                                {% endif %}
                                {% if user_competences.permis is defined %}
                                <li><strong>Permis B</strong> {{ user_competences.permis }}</li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </section>
    <section class="section-embauche">
        <div class="container">
            <h6 class="text-uppercase text-center mb-4"><span style="color: #25546D;">Un passeport </span>pour l'embauche</h6>
            <div class="row ">
                <div class="col-lg-6 col-sm-12 mb-3">
                    <a href="{{ path('app_BTP_conseilsCV') }}">

                        <div class="w-100 info-button">
                            <p class="text-center button-title">Conseil CV</p>
                            <p class="text-center m-0">Tout savoir sur la conception d’un cv</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-6 col-sm-12 mb-3">
                    <a href="{{ path('app_BTP_conseilsmotivation') }}">

                        <div class="w-100 info-button">
                            <p class="text-center button-title">Conseils lettre de motivation</p>
                            <p class="text-center m-0">Les clés d'une lettre de motivation réussie</p>
                        </div>
                    </a>

                </div>


                <div class="col-lg-6 col-sm-12 mb-3">
                    <a href="{{ path('app_BTP_conseilsrechercheEmploi') }}">
                        <div class="w-100 info-button">
                            <p class="text-center button-title">Conseils recherche d’emploi</p>
                            <p class="text-center m-0">Comment faire une recherche d'emploi</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-6 col-sm-12  mb-3">

                    <a href="{{ path('app_BTP_conseilsembauche') }}">
                        <div class="w-100 info-button">
                            <p class="text-center button-title">Conseils entretien d’embauche</p>
                            <p class="text-center m-0">L'entretien : un face à face à préparer</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>



    {% block footer %}
        {% include 'carriereInformatique/footer.html.twig' %}
    {% endblock %}
    </div>
{% endblock %}