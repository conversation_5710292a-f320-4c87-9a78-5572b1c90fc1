{% extends 'carriereInformatique/base.html.twig' %}
{% block title %}Se connecter{% endblock %}

{% block body %}

    <div class="is-sticky">
        {% block head %}
            {% include 'carriereInformatique/header.html.twig' %}
        {% endblock %}


        <nav class="navbar navbar-expand-lg submenu"style="position:fixed; z-index: 998; margin-top:72.5px">
            <div class="container">


                <div class="collapse navbar-collapse ms-auto" id="navbarNavSubMenu">

                    <ul class="navbar-nav ">
                        <li class="nav-item">
                            <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidat') }}">Mon profil</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidatprofilcv') }}">Mon CV</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidatprofiloffre') }}">Mes offres</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidatprofilmail') }}">Mes alertes mail</a>
                        </li>

                    </ul>
                    <a href="#" class=" ml-auto text-white-50">Se désabonner</a>
                </div>

            </div>
        </nav>

        <section class="candidat-section profil-cv" id="">
            <div class="container">
                <h1 style="font-weight: 500;font-size: 1.25rem; line-height: 1.5;">Vous avez enregistré <span class="yellow">1 CV</span>. Vous êtes abonné à <span class="yellow">1 alerte mail</span>.</h1>

                <div class="d-flex flex-row justify-content-between align-items-center flex-wrap sub-title">
                    <h6>Liste de mes alertes e-mail</h6>
                    <a href="{{ path('app_BTP_ajouteralertmail') }}" type="submit" class="btn px-4 button-black">S’abonner à une alerte</a>
                </div>
                <hr>

                <div class="row">
                    {% for alert in alerts %}
                    <div class="col-lg-6 col-md-6 col-sm-12">
                        <div class="w-100 profil-card">
                            <div class="body-card ">
                                <div class="first-card-section align-items-center">
                                    <div>
                                        <span class="mr-1" style="font-weight: 700;">{{ alert.libelle }}</span>
                                        {# <span class="mr-1" style="font-weight: 700;">{{ alert.getIdUserOffreVeille }}</span> #}
                                    </div>

                                </div>

                                <div class="card-section">
                                    <div class="card-divider"></div>
                                    <div class="section-list">
                                        <div class="v-flex justify-content-between align-items-lg-start align-items-center ">
                                            <a href="#" class="text-muted" onclick="voirOffres('{{ alert.getIdUserOffreVeille|json_encode|e('js') }}', '{{ alert.getTypeContratSerialized|json_encode|e('js') }}', '{{ alert.getRegionSerialized|json_encode|e('js') }}', '{{ alert.getMotsCles|json_encode|e('js') }}')">Voir les offres</a>
                                            <a href="{{ path('app_BTP_modifieralertmail', {'id': alert.getIdUserOffreVeille}) }}" class="text-muted">Modifier</a>
                                            <form method="post" action="{{ path('alertmail_delete') }}">
                                                <input type="hidden" name="idalert" value="{{ alert.getIdUserOffreVeille }}">
                                            <button type="submit" style="border:none;background: none;padding: 0;margin: 0" class="text-muted">Supprimer</button>
                                            </form>
                                        </div>

                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

            </div>
        </section>
        <section class="section-embauche">
            <div class="container">
                <h6 class="text-uppercase text-center mb-4"><span style="color: #25546D;">Un passeport </span>pour l'embauche</h6>
                <div class="row ">
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <a href="#">

                            <div class="w-100 info-button">
                                <p class="text-center button-title">Conseil CV</p>
                                <p class="text-center m-0">Tout savoir sur la conception d’un cv</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <a href="#">

                            <div class="w-100 info-button">
                                <p class="text-center button-title">Conseils lettre de motivation</p>
                                <p class="text-center m-0">Les clés d'une lettre de motivation réussie</p>
                            </div>
                        </a>

                    </div>
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <a href="#">

                            <div class="w-100 info-button">
                                <p class="text-center button-title">Les métiers de A à Z</p>
                                <p class="text-center m-0">Découvrez notre guide des métiers</p>
                            </div>
                        </a>

                    </div>
                    <div class="col-lg-4 col-md-6 col-sm-12">

                        <a href="#">
                            <div class="w-100 info-button">
                                <p class="text-center button-title">Conseils entretien d’embauche</p>
                                <p class="text-center m-0">L'entretien : un face à face à préparer</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <a href="#">
                            <div class="w-100 info-button">
                                <p class="text-center button-title">Conseils recherche d’emploi</p>
                                <p class="text-center m-0">Comment faire une recherche d'emploi</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <a href="#">
                            <div class="w-100 info-button">
                                <p class="text-center button-title">Les domaines de A à Z</p>
                                <p class="text-center m-0">Découvrez notre guide des domaines</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </section>
        <script>
            function voirOffres(param1, param2, param3, param4) {
                // Désérialiser les paramètres JSON en objets JavaScript
                var id = JSON.parse(param1);
                var contratid = JSON.parse(param2);
                var region = JSON.parse(param3);
                var motcles = JSON.parse(param4);

                var matchContrat = param2.match(/i:(\d+);/);
                var matchRegion = param3.match(/i:(\d+);/);

                if (matchContrat && matchContrat[1] && matchRegion && matchRegion[1]) {
                    contratid = parseInt(matchContrat[1], 10);
                    region = parseInt(matchRegion[1], 10);

                    console.log(contratid, region);

                    // Fetch the data and construct the URL in the callback
                    fetch('/ajax/get-region/' + region)
                        .then(response => response.json())
                        .then(data => {
                            // Use the data to construct the URL
                            const dep = data[0].libelle; // Assuming you want the first element

                            const url = `https://www.carriere-btp.com/BTP/lists-offre/?contrat=${contratid}&localisation=${dep}&localisation_field=&q=${motcles}`;
                            window.location.href = url;
                            console.log(url);

                            // Redirect to the constructed URL
                            // window.location.href = url;
                        })
                        .catch(error => {
                            console.error('Error fetching metiers:', error);
                        });
                } else {
                    console.error("Invalid format or no match found.");
                }
            }
        </script>

        {% block footer %}
    {% include 'carriereInformatique/footer.html.twig' %}
{% endblock %}
</div>
{% endblock %}