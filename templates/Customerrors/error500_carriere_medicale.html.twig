{% extends 'carriereMedicale/base.html.twig' %}
{% block title %}Serveur occupé{% endblock %}

{% block body %}
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereMedicale/header.html.twig' %}
        {% endblock %}

        <section class="not-found">
            <div class="container">
                <div class="mb-5">
                    <h1 class="text-center text-uppercase"><span class="yellow">erreur</span> 500</h1>
                    <p class="text-center">Veuillez nous excuser, Une erreur est survenue.</p>
                    <p class="text-center">Continuez votre visite sur le site !</p>
                </div>
                <div class="mb-5">
                    <a href="{{ path('app_Medicale_candidat_search') }}" class="yellow text-center d-block">Les offres d'emploi</a>
                </div>
                <div class="mb-5">
                    <a href="{{ path('app_Medicale_register_candidat_e1') }}" class="yellow text-center d-block"><strong>C<PERSON>er votre compte candidat et publier votre CV</strong></a>
                    <a href="https://www.directemploi.com/inscriptionRecruteur" class="yellow text-center d-block"><strong>Créer votre compte recruteur et publier vos offres d'emploi</strong></a>

                </div>
                <div class="text-center">
                    <a href="{{ path('app_Medicale_accueil') }}" type="button" class="btn btn-outline-secondary btn-outline-grey px-4">Retour à la page précédente</a>
                </div>
            </div>
        </section>
        {% block footer %}
            {% include 'carriereMedicale/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}