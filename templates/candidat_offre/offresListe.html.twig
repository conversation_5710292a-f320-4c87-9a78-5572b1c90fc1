{% extends 'directemploi/layout.html.twig' %}

{% block stylesheets %}
	<link rel="stylesheet" href="{{ asset('assets/css/main_offre.min.css') }}">
{% endblock %}

{% block content %}
    <h1 class="ordi" id="titre_post_recherche">Offres d'emplois</h1>
    <div id="fil_ariane">
		<a href="{{ path('directemploi') }}" title="Accueil">Accueil</a> &gt;
	</div>
    <div class="contenu">
        <div id="rechercheOffres" style="margin-top: 10px;">
            <div id="cadre">
                <div id="moteur" class="box_rech rech1 rechmulti" style="position: unset">
                    <div class="margB" id="verifRecherche"></div>
                    <div id="moteur_recherche" class="" style="display: block">
                        <div class="inside">
                            <h2 class="h3_title" id="titre_emploi"></h2>
                            <div class="style_nb_title">
                                <div id="nb_result">
                                    <span class="countOffre">{{ listeOffres.getTotalItemCount }}</span> offres d'emploi correspondant à vos critères de recherche
                                </div>
                            </div>
                            <form popup="1" id="myForm" method="get" action="{{ path('app_candidat_search') }}">	
                                <div class="decoration_compte bar"></div>
                                <div class="select_type_contrat">
                                    <select name="id_type_contrat" id="id_type_contrat" onchange="changer_color()" style="color: rgb(117, 117, 117);"><option value="">Type de contrat</option>
                                        <option value="2">CDI</option>
                                        <option value="1">CDD</option>
                                        <option value="4">Interim</option>
                                        <option value="3">FreeLance / Indépendant</option>
                                        <option value="6">Alternance</option>
                                        <option value="8">Stage</option>
                                    </select>
                                    <div class="select_type_contrat_arrow"></div>
                                </div>
				                <div class="text_localisation">
					                <input type="text" name="localisation" id="localisation" value="" autocomplete="off" class="form_input" placeholder="Localisation">
                                    <div id="localisation_auto_complete" class="auto_complete" style="display: none;"></div>
                                </div>
                                <div class="text_mot_cle">
                                    <br>
                                    <input type="text" name="mot_cle" id="mot_cle" value="" class="form_input" placeholder="Poste recherché" style="float: left">
                                </div>
							    <div class="buton_recherche">
                                    <input type="submit" id="DE_form_valid">
                                </div>
									<a href="{{ path('app_candidat_search_avancee') }}" class="form_valid">
									<div>
										<img class="bouton_recherche_avancee_recherche" width="187" height="74" src="{{ asset('images/direct-emploi/LogoLoupe.png') }}">
									</div>
								</a>
								<br><br><br><br><br><br>
						    </form>
                        </div>
                    </div>
                </div>
            </div>
            <div style="height: 100%;" id="form_after" class="form_after after1"></div>
            <div id="zoneListingOffres">
                <div id="1rst_page_result_title">
                    <h3 class="h2_title h2_smart" style="margin-bottom:0px;">Liste des offres</h3>
                </div>
                <div id="liste_offres" class="colL">
                    <div id="1rst_page_result_content">
                        <div>
                            {% for key, offre in listeOffres %}
                            {% if (key % 2 == 0) %}
                                <div class="listing_offre even clearfix">
                                    <div class="img img_liste img_liste_logo">
                                        <div class="int">
                                            <img alt="{{offre['logo']}}" src="{{ asset('uploads/logos/' ~ offre['logo']) }}">
                                        </div>
                                    </div>
                                    <div class="listing_text">
                                        <div class="intitule">
                                            <h3>
                                                <a href="{{ path('app_candidat_search_details', {id: offre[0].idOffre}) }}">{{offre[0].intitule}}</a>
                                            </h3>
                                            <h4>{{offre[0].dateSoumission|format_datetime('long', 'none', locale='fr')}} - {{offre[0].geoVille}} - {{offre['raison_sociale']}}</h4>
                                        </div>
                                        <div class="text">
                                            {{offre[0].mission|myTruncate(offre[0].mission)}}
                                        </div>
                                        <div class="tag">
                                            Tag : 
                                            <a href="{{ path('app_candidat_emploi_contrat', {contrat: offre['id_type_contrat']}) }}">{{offre['contrat']}}</a> -
                                            {% if offre['secteur_alternatif'] != '' %}
                                                 <a href="{{ path('app_candidat_emploi_secteur', {secteur: offre['id_secteur_activite'] }) }}">{{offre['secteur_alternatif']}}</a> -
                                            {% else %}
                                                 <a href="{{ path('app_candidat_emploi_secteur', {secteur: offre['id_secteur_activite'] }) }}">{{offre['secteur']}}</a> - 
                                            {% endif %}
                                            <a href="{{ path('app_candidat_emploi_region', {region: offre['region'] }) }}">{{offre['region']}}</a> -
                                            <a href="{{ path('app_candidat_emploi_departement', {departement: offre['departement'] }) }}">{{offre['departement']}}</a>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="listing_offre odd clearfix">
                                    <div class="img img_liste img_liste_logo">
                                        <div class="int">
                                            <img alt="{{offre['logo']}}" src="{{ asset('/uploads/logos/' ~ offre['logo']) }}">
                                        </div>
                                    </div>
                                    <div class="listing_text">
                                        <div class="intitule">
                                            <h3>
                                                <a href="{{ path('app_candidat_search_details', {id: offre[0].idOffre}) }}">{{offre[0].intitule}}</a>
                                            </h3>
                                            <h4>{{offre[0].dateSoumission|format_datetime('long', 'none', locale='fr')}} - {{offre[0].geoVille}} - {{offre['raison_sociale']}}</h4>
                                        </div>
                                        <div class="text">
                                            {{offre[0].mission|myTruncate(offre[0].mission)}}
                                        </div>
                                        <div class="tag">
                                            Tag : 
                                            <a href="{{ path('app_candidat_emploi_contrat', {contrat: offre['id_type_contrat']}) }}">{{offre['contrat']}}</a> -
                                            {% if offre['secteur_alternatif'] != '' %}
                                                <a href="{{ path('app_candidat_emploi_secteur', {secteur: offre['id_secteur_activite'] }) }}">{{offre['secteur_alternatif']}}</a> -
                                            {% else %}
                                                <a href="{{ path('app_candidat_emploi_secteur', {secteur: offre['id_secteur_activite'] }) }}">{{offre['secteur']}}</a> - 
                                            {% endif %}
                                            <a href="{{ path('app_candidat_emploi_region', {region: offre['region'] }) }}">{{offre['region']}}</a> -
                                            <a href="{{ path('app_candidat_emploi_departement', {departement: offre['departement'] }) }}">{{offre['departement']}}</a>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                            {% endfor %}
                            <script>
                                var divs = document.getElementsByClassName('text');
                                for(i=0; i <= divs.length; i++) {
                                    divs[i].innerHTML = divs[i].innerHTML.substring(0,280) + ' [...]';
                                }
                            </script>
                        </div>
                        <div align="center" id="pagination_de">
                            {{ knp_pagination_render(listeOffres, 'directemploi/composants/paginationTemplate.html.twig') }}
                        </div>
                    </div>
                </div>
                <div class="colR">
                    <div class="referencement">
                        <h5>Contrat de travail</h5>
                        <li>
                            <a href="{{ path('app_candidat_emploi_contrat', {contrat: 2}) }}">CDI</a>
                        </li>
                        <li>
                            <a href="{{ path('app_candidat_emploi_contrat', {contrat: 1}) }}">CDD</a>
                        </li>
                        <li>
                            <a href="{{ path('app_candidat_emploi_contrat', {contrat: 4}) }}">Intérim</a>
                        </li>
                        <li>
                            <a href="{{ path('app_candidat_search') }}">Tous les contrats</a>
                        </li>
                    </div>
                    <div class="creer_alerte" style="padding-bottom: 5px;">
                        <a href="#" class="submit sub_grey" style="font-size: 1em; text-align: center; padding: 5px 4px; margin-bottom: 10px; margin-top: 5px;">Créer une alerte email</a>
                    </div>
                    <div class="creer_compte">
                        <h5>Vous êtes intéressé(e) par nos offres ?</h5>
                        <p>Créez votre compte gratuitement en moins de 5 minutes et déposez votre CV !</p>
                        <a href="/inscriptionCandEtape1" class="submit sub_grey">Créer son compte</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
