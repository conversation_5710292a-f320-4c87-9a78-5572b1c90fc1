{% extends 'directemploi/layout.html.twig' %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/main_offre.min.css') }}">
{% endblock %}

{% block content %}
<h1>Offres d'emplois en {{offres[1]['contrat']}}</h1>
    <div class="cadre">
        <div class="cadre2">
            <div id="fil_ariane">
                <a href="{{ path('directemploi') }}" title="Accueil">Accueil</a> &gt;                    
            </div>
            <div class="contenu">
                <div id="DE_container_rech" style="background-image: url(/images/all/logistique_54870448.jpg);">
                    <div class="search_box box_rech clearfix" id="DE_box_rech" style="bottom: -55.5%;">
                        <div class="inside">
                            <form class="form_emploi" method="get" action="{{ path('app_candidat_search') }}">
                                <div class="style_nb_title">
                                    <div id="nb_result" style="padding: 20px">
                                        <span class="countOffre">{{ offres.getTotalItemCount}}</span> offres d'emploi correspondant à vos critères de recherche
                                    </div>
                                </div>
                                <div class="decoration_compte bar"></div>
                                <div class="select_type_contrat">
                                    <select name="id_type_contrat" id="id_type_contrat" onchange="changer_color()" style="color: rgb(117, 117, 117);"><option value="">Type de contrat</option>
                                        <option value="2">CDI</option>
                                        <option value="1">CDD</option>
                                        <option value="4">Interim</option>
                                        <option value="3">FreeLance / Indépendant</option>
                                        <option value="6">Alternance</option>
                                        <option value="8">Stage</option>
                                    </select>
                                    <div class="select_type_contrat_arrow"></div>
                                </div>
                                <div class="text_localisation">                  
                                    <input type="text" name="localisation" id="localisation" value="" class="form_input" placeholder="Localisation" style="">
                                    <div id="localisation_auto_complete" class="auto_complete" style="display: none;"></div>
                                </div>
                                <div class="text_mot_cle">
                                    <br>
                                    <input type="text" name="mot_cle" id="mot_cle" value="" class="form_input" placeholder="Poste recherché" style="float: left;">
                                </div>
                                <div class="buton_recherche">
                                    <input type="submit" id="DE_form_valid">
                                </div>
                                <a href="{{ path('app_candidat_search_avancee') }}" class="form_valid">
                                    <div>
                                        <img class="bouton_recherche_avancee" src="{{ asset('/images/direct-emploi/LogoLoupe.png') }}">
                                    </div>
                                </a>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="form_after" id="search_box_form_after" style="height: 24em;"></div>
                <div id="msg"></div>
                <div id="zoneListingOffres" class="clearfix">
                    <h3 class="h2_title h2_smart" style="margin-bottom:0px;">Liste des offres</h3>
                    <div id="liste_offres" class="colL">
                        <div>
                            {% for key, offre in offres %}
                            {% if (key % 2 == 0) %}
                                <div class="listing_offre even clearfix">
                                    <div class="img img_liste img_liste_logo">
                                        <div class="int">
                                            <img alt="{{offre['logo']}}" src="{{ asset('/uploads/logos/' ~ offre['logo']) }}">
                                        </div>
                                    </div>
                                    <div class="listing_text">
                                        <div class="intitule">
                                            <h3>
                                                <a href="{{ path('app_candidat_search_details', {id: offre[0].idOffre}) }}">{{offre[0].intitule}}</a>
                                            </h3>
                                            <h4>{{offre[0].dateSoumission|format_datetime('long', 'none', locale='fr')}} - {{offre[0].geoVille}} - {{offre['raison_sociale']}}</h4>
                                        </div>
                                        <div class="text">
                                            {{offre[0].mission|myTruncate(offre[0].mission)}}
                                        </div>
                                        <div class="tag">
                                        Tag : 
                                            <a href="{{ path('app_candidat_emploi_contrat', {contrat: offre['id_type_contrat']}) }}">{{offre['contrat']}}</a> -
                                            {% if offre['secteur_alternatif'] != '' %}
                                                <a href="{{ path('app_candidat_emploi_secteur', {secteur: offre['id_secteur_activite'] }) }}">{{offre['secteur_alternatif']}}</a> -
                                            {% else %}
                                                <a href="{{ path('app_candidat_emploi_secteur', {secteur: offre['id_secteur_activite'] }) }}">{{offre['secteur']}}</a> - 
                                            {% endif %}
                                            <a href="{{ path('app_candidat_emploi_region', {region: offre['region'] }) }}">{{offre['region']}}</a> -
                                            <a href="{{ path('app_candidat_emploi_departement', {departement: offre['departement'] }) }}">{{offre['departement']}}</a>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="listing_offre odd clearfix">
                                    <div class="img img_liste img_liste_logo">
                                        <div class="int">
                                            <img alt="{{offre['logo']}}" src="{{ asset('/uploads/logos/' ~ offre['logo']) }}">
                                        </div>
                                    </div>
                                    <div class="listing_text">
                                        <div class="intitule">
                                            <h3>
                                                <a href="{{ path('app_candidat_search_details', {id: offre[0].idOffre}) }}">{{offre[0].intitule}}</a>
                                            </h3>
                                            <h4>{{offre[0].dateSoumission|format_datetime('long', 'none', locale='fr')}} - {{offre[0].geoVille}} - {{offre['raison_sociale']}}</h4>
                                        </div>
                                        <div class="text">
                                            {{offre[0].mission|myTruncate(offre[0].mission)}}
                                        </div>
                                        <div class="tag">
                                        Tag : 
                                            <a href="{{ path('app_candidat_emploi_contrat', {contrat: offre['id_type_contrat']}) }}">{{offre['contrat']}}</a> -
                                            {% if offre['secteur_alternatif'] != '' %}
                                                <a href="{{ path('app_candidat_emploi_secteur', {secteur: offre['id_secteur_activite'] }) }}">{{offre['secteur_alternatif']}}</a> -
                                            {% else %}
                                                <a href="{{ path('app_candidat_emploi_secteur', {secteur: offre['id_secteur_activite'] }) }}">{{offre['secteur']}}</a> - 
                                            {% endif %}
                                            <a href="{{ path('app_candidat_emploi_region', {region: offre['region'] }) }}">{{offre['region']}}</a> -
                                            <a href="{{ path('app_candidat_emploi_departement', {departement: offre['departement'] }) }}">{{offre['departement']}}</a>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                            {% endfor %}
                            <script>
                                var divs = document.getElementsByClassName('text');
                                for(i=0; i <= divs.length; i++) {
                                    divs[i].innerHTML = divs[i].innerHTML.substring(0,280) + ' [...]';
                                }
                            </script>
                        </div>
                        <div id="zone_indeed"></div>  
                        <div align="center" id="pagination_de">
                            {{ knp_pagination_render(offres, 'directemploi/composants/paginationTemplate.html.twig') }}
                        </div>
                    </div>
                    <div style="text-align:center"></div>
                    <div class="colR">
                        <div class="referencement">     
                            <h5>Contrat de travail</h5>
                                <li>
                                    <a href="{{ path('app_candidat_emploi_contrat', {contrat: 2}) }}">CDI</a>
                                </li>
                                <li>
                                    <a href="{{ path('app_candidat_emploi_contrat', {contrat: 1}) }}">CDD</a>
                                </li>
                                <li>
                                    <a href="{{ path('app_candidat_emploi_contrat', {contrat: 4}) }}">Intérim</a>
                                </li>
                                <li>
                                    <a href="{{ path('app_candidat_search') }}">Tous les contrats</a>
                                </li>
                            {# <h5>Régions</h5>
                                {% for region in listeRegions %}
                                    <li>
                                        <a>{{ region.libelle }}</a>
                                    </li>
                                {% endfor %}
                            {% if listeMetiersDemandes != null %}
                            <h5>Les métiers les plus demandés</h5>
                                {% for metier in listeMetiersDemandes %}
                                <li>
                                    <a>{{ metier.libelleMetierDomaineGroup }}</a>
                                </li>
                                {% endfor %}
                            {% endif %} #}
                        </div>
                        <div class="creer_alerte">
                            <h5>Créer une alerte email avec nos offres</h5>
                            <form action="">
                                <input type="texte" placeholder="Votre email" name="email">
                                <select>
                                    <option>Fréquence</option>
                                    <option value="1">Journalière</option>
                                    <option value="3">Hebdomaidaire</option>
                                    <option value="5">Mensuel</option>
                                </select>
                                <input type="hidden" name="alertes[1][keyword]" id="alertes_1_keyword" value="">
                                <input type="hidden" name="keywords" id="keywords" value="">
                                <input type="hidden" name="param[motcle]" id="param_motcle" value="mot_cle">
                                <input type="hidden" name="param[localisation]" id="param_localisation" value="france">
                                <input type="hidden" name="param[id_secteur]" id="param_id_secteur" value="">
                                <input type="hidden" name="param[id_type_contrat]" id="param_id_type_contrat" value="">
                                <input type="hidden" name="param[id_region]" id="param_id_region" value="">
                                <input id="checkbox" name="condi" type="hidden" value="1">
                                <input id="checkbox_newsletter" name="newsletter" value="1" type="hidden">
                                <input class="sub_grey" type="submit" value="Je crée mon alerte mail">
                            </form>
                            <div class="smaller" align="center">Vous pouvez annuler l'alerte à tout moment</div>
                        </div>
                        <div class="creer_compte">
                            <h5>Vous êtes intéressé(e) par nos offres ?</h5>
                            <p>Créez votre compte gratuitement en moins de 5 minutes et déposez votre CV !</p>
                            <a href="" class="submit sub_grey">Créer son compte</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
{% endblock %}