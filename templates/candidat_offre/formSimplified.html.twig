<h2 class="bloc">Formulaire de réponse à l'offre</h2>
<table class="fiche_offre">
	<tbody>
		{{ form_start(formulaireCandidature) }}
		{{ form_widget(formulaireCandidature.id_offre, {'attr': {'value': offreDetails[0].idOffre }}) }}
		<tr>
			<th>{{ form_label(formulaireCandidature.civilite,"Civilité")}}<span class="ob">
					*</span>
			</th>
			<td>
				{{ form_widget(formulaireCandidature.civilite) }}
			</td>
		</tr>
		<tr>
			<th class="ordi">{{ form_label(formulaireCandidature.prenom,"Prénom")}}<span class="ob">
					*</span>
			</th>
			<td>
				{{ form_widget(formulaireCandidature.prenom,{ 'attr': {'placeholder': 'Prénom'} })}}
			</td>
		</tr>
		<tr>
			<th class="ordi">{{ form_label(formulaireCandidature.nom,"Nom")}}<span class="ob">
					*</span>
			</th>
			<td>
				{{ form_widget(formulaireCandidature.nom,{ 'attr': {'placeholder': 'Nom'} })}}
			</td>
		</tr>
		<tr>
			<th class="ordi">{{ form_label(formulaireCandidature.mobile,"Mobile")}}</th>
			<td>
				{{ form_widget(formulaireCandidature.mobile, { 'attr': {'placeholder': 'Mobile'} })}}
			</td>
		</tr>
		<tr>
			<th class="ordi">{{ form_label(formulaireCandidature.email,"Email")}}<span class="ob">
					*</span>
			</th>
			<td>
				{{ form_widget(formulaireCandidature.email, { 'attr': {'placeholder': 'Email'} })}}
			</td>
		</tr>
		<tr>
			<th class="ordi">{{ form_label(formulaireCandidature.code_postal,"Code Postal")}}<span class="ob">*</span>
			</th>
			<td>
				{{ form_widget(formulaireCandidature.code_postal, { 'attr': {'placeholder': 'Code Postal'} })}}
			</td>
		</tr>
		<tr>
			<td colspan="2">
				<hr id="separateur_fiche">
			</td>
		</tr>
		<tr>
			<th>{{ form_label(formulaireCandidature.liste_formation,"Formation")}}<span class="ob">*</span>
			</th>
			<td class="select">
				{{ form_widget(formulaireCandidature.liste_formation, { 'attr': {'style': 'width: 70%', } }) }}
			</td>
		</tr>
		<tr>
			<th class="ordi">{{ form_label(formulaireCandidature.promo,"Année de promotion")}}<span class="ob">*</span>
			</th>
			<td>
				{{ form_widget(formulaireCandidature.promo, { 'attr': {'placeholder': '1990, 2010, 200.....'} })}}
			</td>
		</tr>
		<tr>
			<th>{{ form_label(formulaireCandidature.domaine_activite, "Domaine d'activite")}}<span class="ob">
					*</span>
			</th>
			<td class="select">
				{{ form_widget(formulaireCandidature.domaine_activite, { 'attr': {'style': 'width: 70%',} })}}
			</td>
		</tr>
		<tr>
			<td colspan="2">
				<hr id="separateur_fiche">
			</td>
		</tr>
		<tr>
			<th>{{ form_label(formulaireCandidature.cv, "CV")}}<span class="ob">*</span>
			</th>
			<td rowspan="2">
				{{ form_widget(formulaireCandidature.cv)}}
				<span class="aide_bt">
					<a style="" id="aide_cv_candidature" href="javascript:void(0)" onmouseout="nd();" onmouseover="return overlib('Fichiers texte ou PDF - Maximum 400 ko',ABOVE, WIDTH, 300);">&nbsp;&nbsp;&nbsp;&nbsp;</a>
					<span id="overDiv" style="position: absolute; visibility: hidden; z-index: 1000; width: 300px; background-color: rgb(235, 235, 235); border: 1px solid rgb(51, 51, 51); background-image: none; left: 817px; top: 2197px;">
						<table width="300" border="0" cellpadding="1" cellspacing="0" class="bgclass" onmouseover="javascript: blockTimeout(); " onmouseout="javascript: TimeoutDelayed(); ">
							<tbody>
								<tr>
									<td>
										<table width="100%" border="0" cellpadding="2" cellspacing="0" class="fgclass">
											<tbody>
												<tr>
													<td valign="TOP">
														<font class="textfontclass">Fichiers texte ou PDF - Maximum 400 ko</font>
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
					</span>
				</span>
			</td>
		</tr>
		<tr class="ordi">
			<td></td>
		</tr>
		<tr>
			<th>{{ form_label(formulaireCandidature.lm)}}</th>
			<td>
				{{ form_widget(formulaireCandidature.lm)}}
				<span class="aide_bt">
					<a style="" id="aide_lm_candidature" href="javascript:void(0)" onmouseout="nd();" onmouseover="return overlib('Fichiers texte ou PDF - Maximum 400 ko',ABOVE, WIDTH, 300);">&nbsp;&nbsp;&nbsp;&nbsp;</a>
					<span id="overDiv" style="position:absolute; visibility:hidden; z-index:1000; width:300px; background-color:#ebebeb; border: 1px solid #333"></span>
				</span>
			</td>
		</tr>
		<tr>
			<td colspan="2">
				<hr id="separateur_fiche">
			</td>
		</tr>
		<tr>
			<td colspan="2">
				{{ form_widget(formulaireCandidature.cgu, { 'attr': { 'style': 'margin-left:3px;margin-right:5px;',} })}}
				<label for="cgu" style="font-weight:normal">J'accepte les
					<a href="https://www.directemploi.com/page/mentions-legales" target="_blank">
						conditions générales
					</a>de Direct Emploi</label>
				<span class="ob">*</span>
				<span>
					<br>
					- Coordonnées transmises aux recruteurs, lecture du CV à des fins de recrutement
					<br>
					- Envois potentiels d’offres d’emploi, stage, alternance, formations et communications partenaires
				</span>
			</td>
		</tr>
		<tr>
			<td align="center" colspan="2">
				<div class="g-recaptcha" data-sitekey="6LfH66QZAAAAAANdrqB2-_cT6d6yMWBdr0LMl5Pg" style="padding-top: 10px;"></div>
				<div id="fiche_postuler" class="postuler">
					<input type="submit" name="commit" value="Postuler maintenant" onclick="ga('send', 'event', 'Candidat', 'Depot candidature');" class="sub_jaune">
				</div>
				<div class="version-light" id="alert_case">
					<span class="red">Attention&nbsp;:</span>
					<ul>
						<li>Nous vous conseillons de désactiver les bloqueurs de publicité de type AdBlock pour assurer la bonne validation du formulaire.</li>
					</ul>
				</div>
			</td>
		</tr>
		{{ form_end(formulaireCandidature) }}
	</tbody>
</table>