{% extends 'directemploi/new_layout.html.twig' %}

{% set title = offreDetails[0].intitule|raw ~ " - " ~  offreDetails[0].reference ~ " - " ~ offreDetails['libelleContrat']|raw %}
{% if offreDetails[0].geoVille is defined and offreDetails[0].geoVille != null %}{% set title = title ~ " " ~ offreDetails[0].geoVille %}
{% elseif departement is defined and departement != null %}{% set title = title ~ " " ~ departement.libelle %}
{% elseif region.libelle is defined and region.libelle != null %}{% set title = title ~ " " ~ region.libelle %}{% endif %}
{% set title = title[:70] %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}

{% set description_string = "retrouver tous les détails de notre offre " ~ offreDetails[0].intitule|raw ~ " " ~ offreDetails[0].geoVille|raw ~ " " ~ offreDetails[0].reference|raw ~ " sur direct emploi. " ~intitule~ " - " ~idoffre %}

{% block meta_description %}{{ description_string }}{% endblock %}
{% block meta_description_og %}{{ description_string }}{% endblock %}
{% block meta_description_twitter %}{{ description_string }}{% endblock %}

	{% block stylesheets %}

    {# <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous"> #}
    {# <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"> #}
    <link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet">

	<link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/bootstrap.min.css') }}">
	<link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/bootstrap-icons.min.css') }}">
	<link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/magnific-popup.min.css') }}">
	<link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">

	{% endblock %}

{% block content %}

<style>
    .logo {
        border-radius: var(--border-radius-small);
        content: "";
        max-height: 100%;
        margin-top: 0;
        margin-bottom: 0;
    }

	.dropdown-menu.show {
		display: flex;
	}

    ul li {
        font-size: 1rem;
    }

	.required-asterisk {
    color: red;
    margin-left: 5px;
	}

    h1 {
        font-size: 2rem;
    }
    .offre-form-header {
        padding-top: 80px;
    }
</style>
    {# Ajout de SweetAlert pour les flash messages #}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    {# Gestion des flash messages #}
    <script>
        {% for message in app.flashes('success') %}
        Swal.fire({
            icon: 'success',
            title: 'Succès',
            text: '{{ message }}',
            confirmButtonText: 'OK'
        });
        {% endfor %}

        {% for message in app.flashes('error') %}
        Swal.fire({
            icon: 'error',
            title: 'Erreur',
            text: '{{ message }}',
            confirmButtonText: 'OK'
        });
        {% endfor %}
    </script>
    <script>
        {% for message in app.flashes('info') %}
        Swal.fire({
            icon: 'info',
            title: 'Candidature déjà envoyée',
            html: `
                <p>Vous avez déjà postulé pour cette offre.</p>
                <a href="https://www.directemploi.com/entreprise/13134/KELTIS" target="_blank" class="btn btn-primary" style="margin-top:10px; padding:10px 15px; text-decoration:none; background-color:#007bff; color:white; border-radius:5px;">
                    Voir les offres de Keltis
                </a>
            `,
            confirmButtonText: 'OK'
        });
        {% endfor %}
    </script>
{% if offreDetails[0].statut != 1 %}
    <section class="offre-form-header" id="">
        <div class="container">
            <div class="alert alert-danger" role="alert" style="background-color: #d1e7dd;border-color: #badbcc;color: #0f5132;">
                Cette offre n'est plus disponible
            </div>
            <div class="row">
                <p class="mb-3">Nous vous invitons à renouveler votre recherche </p>
                <div class="col-lg-7 decouvrir-col"> <div class="decouvrir"> <a href="{{ path('app_candidat_search') }}" class="btn button-offre">Rechercher une offre</a></div></div>
            </div>
        </div>
    </section>
{% else %}
<section class="offre-form-header" id="">
    <div class="container">
		{% for message in app.flashes('msg') %}
		<div class="alert alert-danger" role="alert" style="background-color: #d1e7dd;border-color: #badbcc;color: #0f5132;">
			{{ message }}
		</div>
	{% endfor %}
        <div class="row">
            <div class="col-lg-6 mb-3">        
                <div class="job-info"> <span>{% if offreDetails[0].dateSoumission is defined and offreDetails[0].dateSoumission != null %}
					{{offreDetails[0].dateSoumission|date('d/m/Y')}}{% endif %}
				</span> <span>{{offreDetails.libelleContrat}}</span> <span>{{ offreDetails[0].reference }}</span> </div> 
            <div class="job-title">
            <h1><strong>{{ offreDetails[0].intitule|raw }}</strong> <span style="font-weight: 200;">Chez </span>
				{% if offreDetails['raison_sociale'] is defined and offreDetails['raison_sociale'] != null %}{{offreDetails['raison_sociale']}}{% endif %}</h1>  
        </div>
            <div class="job-info"> <span>{% if region.libelle is defined and region.libelle != null %}<a href="{{ path('app_candidat_emploi_region', {region: region.idRegion }) }}">{{ region.libelle }}</a>{% endif %}
				- {% if offreDetails[0].geoVille is defined and offreDetails[0].geoVille != null %} {{offreDetails[0].geoVille}} {% endif %}</span> </div> 
            {# <a href="#" class="btn button-offre" id="btn-postuler">Passer au formulaire</a> #}
            </div>
            <div class="col-lg-6 decouvrir-col"> <div class="decouvrir"> <a href="#" class="btn button-offre" id="btn-postuler">Passer au formulaire</a></div></div>
        </div>

    </div>
</section>
{# <section class="contrat-travail p-0">
    <div class="container">
        <h6>Contrat de travail</h6>
        <span><a href="/lists-offre/{{ struct_home_search('CDI') }}-france-metiers-secteurs">Emploi en CDI</a></span>
        <span><a href="/lists-offre/{{ struct_home_search('CDD') }}-france-metiers-secteurs">Emploi en CDD</a></span>
        <span><a href="/lists-offre/{{ struct_home_search('INTERIM') }}-france-metiers-secteurs">Emploi en Intérim</a></span>
        <span><a href="{{ path('app_candidat_search') }}">Emploi en Tous les contrats</a></span>

    </div>
</section> #}

    <section class="la-une entreprise-profile">
        <div class="container">
        <div class="w-100 card-entreprise">
            <div class="row">
                <div class="col-lg-8">
                    <h5 class="mb-3">{{offreDetails['raison_sociale']}}</h5>
					{% if offreDetails['adresse1'] is defined and offreDetails['adresse1'] != null and offreDetails['adresse1'] != '-' %}
                        <p class="mb-3">
                            {{ offreDetails['adresse1'] }}
                            {% if offreDetails['code_postal'] is defined and offreDetails['code_postal'] != null %}
                                - {{ offreDetails['code_postal'] }}
                            {% endif %}
                        </p>
                    {% endif %}
					{% if offreDetails['nom_ville'] is defined and offreDetails['nom_ville'] != null %}
					 {{offreDetails['nom_ville']}}
					{% endif %}
					</p>
					{% if offreDetails['site_web'] is defined and offreDetails['site_web'] != null %}
                    <a href="#" class="second-color">{{offreDetails['site_web']}}</a>
					{% endif %}
                </div>
                <div class="col-lg-4">
                    
            <div class="w-100 vertical-center">
                    <div class="logo-entreprise ">
                        <img class="img-fluid" src="{{ asset('/uploads/logos/' ~ offreDetails['logo']) }}" alt="{{offreDetails['logo']}}"/>
                    </div>
            </div>
                </div>
                <div class="decouvrir d-flex justify-content-center"> <a href="{{ path('app_entreprises_details', {id: offreDetails['id_entreprise'], slug: offreDetails['raison_sociale'] | raison }) }}"  style="text-decoration:underline;"> Découvrir l'entreprise, toutes les offres</a></div>
            </div>
        </div>
    </div>
    </section>
    <section>
        <div class="container">
    {% if offreDetails['presentation_societe'] is defined and offreDetails['presentation_societe'] != null and offreDetails['presentation_societe'] != '-' %}
    <div class="header-and-text">
        <h6 >Présentation de l'entreprise</h6>
        <hr>
		{% if (offreDetails[0].isAnonym == 0 and offreDetails[0].nomSociete == null) %}
        <p class="justified">{{offreDetails['presentation_societe']|raw|nl2br}}</p>
		{% endif %}
    </div>
    {% endif %}
    <div class="header-and-text">
        <h6 >Description de l'offre</h6>
        <hr>
        <p class="justified">{{offreDetails[0].mission|raw|nl2br}}</p>
    </div>
    {% if offreDetails[0].profil != '' %}
    <div class="header-and-text">
        <h6 >Profil recherché</h6>
        <hr>
		{% if offreDetails[0].profil != '' %}
        <p class="justified">{{offreDetails[0].profil|raw|nl2br}}</p>
		{% endif %}
        {# <p class="justified">La marque Direct emploi est une marque enregistrée, dont le titulaire est la société Direct Emploi - Direct Performance. Toutes les autres marques qui figurent sur le présent site internet sont la propriété de leurs titulaires respectifs... <a href="#" class="blue">Lire la suite.</a></p> #}
    </div>
    {% endif %}


</div>

</section>
<section class="login-form" id="formulaire">
    <div class="container">

    <h6 >FORMULAIRE DE RÉPONSE À L’OFFRE</h6>
    <hr>
	{{ form_start(formulaireCandidature) }}
                        <div class="info-section mb-0">
							{{ form_widget(formulaireCandidature.id_offre, {'attr': {'value': offreDetails[0].idOffre }}) }}
                            <div class="row">
                                <div class="col-lg-4 col-sm-12">
                                    <div class="form-group">
										{{ form_label(formulaireCandidature.nom, "Nom") }}
										<span class="required-asterisk">*</span>
										{{ form_widget(formulaireCandidature.nom, {'attr': {'class': 'form-control', 'placeholder': 'Nom', 'required': 'required'}}) }}
										{{ form_errors(formulaireCandidature.nom) }}
                                      </div>
                                </div>
                                <div class="col-lg-4 col-sm-12">
                                    <div class="form-group">
										{{ form_label(formulaireCandidature.prenom, "Prénom") }}
										{% if formulaireCandidature.prenom.vars.required %}
											<span class="required-asterisk">*</span>
										{% endif %}
										{{ form_widget(formulaireCandidature.prenom, {'attr': {'class': 'form-control', 'placeholder': 'Prénom'}}) }}
										{{ form_errors(formulaireCandidature.prenom) }}
                                      </div>
                                </div>
                                <div class="col-lg-4 col-sm-12">
                                    <div class="form-group">
										{{ form_label(formulaireCandidature.civilite, "Civilité") }}
										{% if formulaireCandidature.civilite.vars.required %}
											<span class="required-asterisk">*</span>
										{% endif %}
										{{ form_widget(formulaireCandidature.civilite, {'attr': {'class': 'form-select form-control'}}) }}
										{{ form_errors(formulaireCandidature.civilite) }}
                                      </div>
                                </div>
                            </div>
                            <div class="row">

                            <div class="col-lg-4 col-sm-12">
                                <div class="form-group">
									{{ form_label(formulaireCandidature.email, "Email") }}
									{% if formulaireCandidature.email.vars.required %}
										<span class="required-asterisk">*</span>
									{% endif %}
									{{ form_widget(formulaireCandidature.email, {'attr': {'class': 'form-control', 'placeholder': 'Email'}}) }}
									{{ form_errors(formulaireCandidature.email) }}
                                </div>
                            </div>
							<div class="col-lg-4 col-sm-12">
								<div class="form-group">
									{{ form_label(formulaireCandidature.mobile, "Téléphone") }}
									{% if formulaireCandidature.mobile.vars.required %}
										<span class="required-asterisk">*</span>
									{% endif %}
									{{ form_widget(formulaireCandidature.mobile, {'attr': {'class': 'form-control', 'placeholder': 'Téléphone'}}) }}
									{{ form_errors(formulaireCandidature.mobile) }}
								</div>
							</div>
							<div class="col-lg-4 col-sm-12">
								<div class="form-group">
									{{ form_label(formulaireCandidature.code_postal, "Code postal") }}
									{% if formulaireCandidature.code_postal.vars.required %}
										<span class="required-asterisk">*</span>
									{% endif %}
									{{ form_widget(formulaireCandidature.code_postal, {'attr': {'class': 'form-control', 'placeholder': 'Code postal'}}) }}
									{{ form_errors(formulaireCandidature.code_postal) }}
								</div>
							</div>
                           </div>

                        </div>


        <div class="info-section mb-0">
            <hr>
            <div class="row">
                <div class="col-lg-6 col-sm-12">
                    <div class="form-group">
                        {{ form_label(formulaireCandidature.domaine_activite, "Domaine d'expertise") }}
						{% if formulaireCandidature.domaine_activite.vars.required %}
							<span class="required-asterisk">*</span>
						{% endif %}
						{{ form_widget(formulaireCandidature.domaine_activite, {'attr': {'class': 'form-select form-control'}}) }}
						{{ form_errors(formulaireCandidature.domaine_activite) }}
                      </div>
                </div>
                <div class="col-lg-6 col-sm-12">
                    <div class="form-group">
                        {{ form_label(formulaireCandidature.metier_metier, "Métier") }}
						{% if formulaireCandidature.metier_metier.vars.required %}
							<span class="required-asterisk">*</span>
						{% endif %}
						{{ form_widget(formulaireCandidature.metier_metier, {'attr': {'class': 'form-select form-control'}}) }}
						{{ form_errors(formulaireCandidature.metier_metier) }}
                      </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-6 col-sm-12">
                    <div class="form-group">
                        {{ form_label(formulaireCandidature.liste_formation, "Niveau de formation") }}
						{% if formulaireCandidature.liste_formation.vars.required %}
							<span class="required-asterisk">*</span>
						{% endif %}
						{{ form_widget(formulaireCandidature.liste_formation, {'attr': {'class': 'form-select form-control'}}) }}
						{{ form_errors(formulaireCandidature.liste_formation) }}
                      </div>
                </div>
                <div class="col-lg-6 col-sm-12">
                    <div class="form-group">
						{{ form_label(formulaireCandidature.promo, "Année d'obtention du diplôme") }}
						{% if formulaireCandidature.promo.vars.required %}
							<span class="required-asterisk">*</span>
						{% endif %}
						{{ form_widget(formulaireCandidature.promo, {'attr': {'class': 'form-control', 'placeholder': 'Année'}}) }}
						{{ form_errors(formulaireCandidature.promo) }}
                      </div>
                </div>
            </div>

            </div>
            <div class="info-section mb-0">
                <hr>
                <div class="row">
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group" >
                            {{ form_label(formulaireCandidature.cv, "Ajouter votre CV") }}
							{% if formulaireCandidature.cv.vars.required %}
								<span class="required-asterisk">*</span>
							{% endif %}
							{{ form_widget(formulaireCandidature.cv, {'attr': {'class': 'form-control', 'placeholder': 'CV'}}) }}
							{{ form_errors(formulaireCandidature.cv) }}
                            <p class="text-muted">Taille maximale du fichier : 1Mo</p>
						</div>
                    </div>
                <div class="col-lg-6">
                        <div class="form-group" style="margin-bottom:0;">
							{{ form_label(formulaireCandidature.lm, "Lettre de motivation") }}
							{% if formulaireCandidature.lm.vars.required %}
								<span class="required-asterisk">*</span>
							{% endif %}
							{{ form_widget(formulaireCandidature.lm, {'attr': {'class': 'form-control', 'placeholder': 'Lettre de motivation'}}) }}
							{{ form_errors(formulaireCandidature.lm) }}
                            <p class="text-muted">Taille maximale du fichier : 1Mo</p>
                        </div>

                </div>

                </div>
                </div>
                <div class="info-section mb-0">

                    <hr>
                    <div class="row">

                    <div class="col-12">
                        <div class="bottom-login">
                        {# <i class="fa-regular fa-square-check" class="blue" style=" font-size:42px"></i> #}
                        <div>
                            <p><strong>J'accepte <a href="https://www.directemploi.com/page/mentions-legales"class="">les conditions générales</a> de Direct Emploi</strong></p>
                            <p>- Coordonnées transmises aux recruteurs, lecture du CV à des fins de recrutement</p>
                            <p>- Envois potentiels d’offres d’emploi, stage, alternance, formations et communications partenaires</p>
                        </div>
                        <div class="form-check" style="margin-left:auto;">
							{{ form_widget(formulaireCandidature.cgu, {'attr': {'class': 'form-check-input', 'style': 'max-height: 24px;'}}) }}
							<span class="required-asterisk">*</span>
                          </div>
                        </div>
                    </div>
                    </div>

                    </div>
        <div class="info-section mb-0">

                    <hr>
            <div class="row">

                <div class="col-12">
                    <div class="bottom-login">
                        {# <i class="fa-regular fa-square-check" class="blue" style=" font-size:42px"></i> #}
                        <div>
                            <p>Je souhaite recevoir des alertes par email pour des offres similaires à celle-ci.</p>
                        </div>
                        <div class="form-check" style="margin-left:auto;">
                            {{ form_widget(formulaireCandidature.subscribe_alert, {'attr': {'class': 'form-check-input'}}) }}
                        </div>
                    </div>
                </div>
            </div>

                    </div>
                    <div class="text-center">
						<button type="submit" class="btn mb-2 px-4 button-inscription">Postuler mantenant</button>
                    </div>
{{ form_end(formulaireCandidature) }}
<div class="card p-5 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
    <h6 class="mb-3">Attention</h6>
    <ul>
        <li class="mb-2">Nous vous conseillons de désactiver les bloqueurs de publicité de type AdBlock pour assurer la bonne validation du formulaire.</li>
        <li>Si vous ne parvenez pas à postuler, utilisez notre <a href="#" class="second-color">formulaire simplifié.</a></li>

    </ul>
</div>
{% if not app.user %}
<div class="p-5 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
    <h6 class="mb-3">pas encore inscrits ?</h6>
    <p>Créer un compte candidat vous permet :</p>
    <ul>
        <li class="mb-2">l'enregistrement de vos informations pour ne pas avoir à les resaisir plusieurs fois</li>
        <li class="mb-2">la conservation d'un historique de vos candidatures</a></li>
        <li>la possibilité d'être contacté directement par plus de 1000 entreprises</a></li>
    </ul>
    <a href="{{ path('app_register_candidat_e1') }}" class="btn button-inscription">Créer un compte</a> <span style="margin-left: 0.5rem;">ou connectez vous en cliquant <a href="{{ path('app_login') }}" class="second-color">sur le lien de notre page de connexion</a></span>
</div>
{% endif %}
</div>
</section>
</div>
<script src="{{ asset('directEmploi_new/Front/js/jquery.min.js') }}"></script>
{# <script src="{{ asset('directEmploi_new/Front/js/bootstrap.min.js') }}"></script> #}
<script src="{{ asset('directEmploi_new/Front/js/click-scroll.min.js') }}"></script>
<script src="{{ asset('directEmploi_new/Front/js/jquery.magnific-popup.min.js') }}"></script>
<script src="{{ asset('directEmploi_new/Front/js/magnific-popup-options.min.js') }}"></script>
<script src="{{ asset('directEmploi_new/Front/js/custom.min.js') }}"></script>

<script src="{{ asset('assets/js/conseils.min.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    {% set redirect_url = app.flashes('redirect_url')|first %}

    <script>
        console.log("Redirect URL:", "{{ redirect_url }}");  // Vérifie si l'URL est bien définie
    </script>

    {% if redirect_url %}
        <script>
            Swal.fire({
                title: 'Afin que votre candidature soit transmise',
                text: "vous êtes maintenant redirigé vers le formulaire de l’entreprise. Un message de confirmation de candidature vous sera envoyé par l’entreprise",
                imageUrl: 'https://www.directemploi.com/images/direct-emploi/new_logo.png',  // URL de l'image
                imageHeight: 80,  // Hauteur de l'image, ajustable selon tes besoins
                confirmButtonText: 'OK'
            }).then((result) => {
                if (result.isConfirmed) {
                    console.log("Redirection to:", "{{ redirect_url }}");
                    window.location.href = "{{ redirect_url }}";
                }
            });

        </script>
    {% endif %}
<script>
		$(document).ready(function () {
			$('#show-hide-select').on('change', function () {
				var selectedValue = $(this).val();
				console.log('selected val = ' + selectedValue);

				$.ajax({
					url: "{{ path('app_candidat_search_details', {id:offreDetails[0].idOffre}) }}",
					type: 'POST',
					dataType: 'json',
					data: {
						'offId': '{{offreDetails[0].idOffre}}',
						'select': selectedValue,
					},
					success: function (data) {
						location.reload();
					},
					error: function (xhr, status, error) {
						console.log('Error: ' + error);
					},
				});
			});
		});
	
	window.onload = () => { 
		let domaine = document.querySelector("#offre_candidature_domaine_activite");
		
		if (domaine) {
			domaine.addEventListener("change", function () {
			let form = this.closest("form");
			let data = this.name + "=" + this.value;
			
			fetch(form.action, {
				method: form.getAttribute("method"),
				body: data,
				headers: {
					"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
				}
			}).then(response => response.text()).then(html => {
				let content = document.createElement("html");
				content.innerHTML = html;
				let nouveauSelect = content.querySelector("#offre_candidature_metier_metier");
				console.log(nouveauSelect);
				document.querySelector("#offre_candidature_metier_metier").replaceWith(nouveauSelect);
			})
			});
		}
	};

    const btnPostuler = document.getElementById('btn-postuler');

    btnPostuler.addEventListener('click', function(event) {
        event.preventDefault(); 

        const formulaireSection = document.getElementById('formulaire');

        formulaireSection.scrollIntoView({ behavior: 'smooth' });
    });
</script>

{% endif %}

{% endblock %}

	
