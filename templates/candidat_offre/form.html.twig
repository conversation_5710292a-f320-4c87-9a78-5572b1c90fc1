<h2 class="bloc">Formulaire de réponse à l'offre</h2>
	{{ form_start(formulaireCandidature) }}
		<table class="fiche_offre">
			<tbody>
				{{ form_widget(formulaireCandidature.id_offre, {'attr': {'value': offreDetails[0].idOffre }}) }}
				<tr>
					<th>{{ form_label(formulaireCandidature.civilite,"Civilité")}}<span class="ob">
							*</span>
					</th>
					<td>
						{{ form_widget(formulaireCandidature.civilite, { 'attr': {'style' : 'width: 70%'} }) }}
					</td>
				</tr>
				<tr>
					<th class="ordi">{{ form_label(formulaireCandidature.prenom,"Prénom")}}<span class="ob">
							*</span>
					</th>
					<td>
						{{ form_widget(formulaireCandidature.prenom,{ 'attr': {'placeholder': 'Prénom', 'style' : 'width: 21.1em'} })}}
					</td>
				</tr>
				<tr>
					<th class="ordi">{{ form_label(formulaireCandidature.nom,"Nom")}}<span class="ob">
							*</span>
					</th>
					<td>
						{{ form_widget(formulaireCandidature.nom,{ 'attr': {'placeholder': 'Nom', 'style' : 'width: 21.1em'}}) }}
					</td>
				</tr>
				<tr>
					<th class="ordi">{{ form_label(formulaireCandidature.mobile,"Mobile")}}</th>
					<td>
						{{ form_widget(formulaireCandidature.mobile, { 'attr': {'placeholder': 'Mobile', 'style' : 'width: 21.1em'}}) }}
					</td>
				</tr>
				<tr>
					<th class="ordi">{{ form_label(formulaireCandidature.email,"Email")}}<span class="ob">
							*</span>
					</th>
					<td>
						{{ form_widget(formulaireCandidature.email, { 'attr': {'placeholder': 'Email', 'style' : 'width: 21.1em'} })}}
					</td>
				</tr>
				<tr>
					<th class="ordi">{{ form_label(formulaireCandidature.code_postal,"Code Postal")}}<span class="ob">*</span>
					</th>
					<td>
						{{ form_widget(formulaireCandidature.code_postal, { 'attr': {'placeholder': 'Code Postal', 'style' : 'width: 21.1em'}}) }}
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<hr id="separateur_fiche">
					</td>
				</tr>
				<tr>
					<th>{{ form_label(formulaireCandidature.liste_formation,"Formation")}}<span class="ob">*</span>
					</th>
					<td class="select">
						{{ form_widget(formulaireCandidature.liste_formation, { 'attr': {'style': 'width: 70%', } }) }}
					</td>
				</tr>
				<tr>
					<th class="ordi">{{ form_label(formulaireCandidature.promo,"Année d'obtention du diplôme")}}<span class="ob">*</span>
					</th>
					<td>
						{{ form_widget(formulaireCandidature.promo, { 'attr': {'placeholder': '1990, 2010, 200.....', 'style': 'width: 21.1em'} })}}
					</td>
				</tr>
				<tr>
					<th>{{ form_label(formulaireCandidature.domaine_activite, "Domaine d'activite")}}<span class="ob">
							*</span>
					</th>
					<td class="select">
						{{ form_widget(formulaireCandidature.domaine_activite, { 'attr': {'style': 'width: 70%',} })}}
					</td>
				</tr>
				<tr>
					<th>{{ form_label(formulaireCandidature.metier_metier, "Métier")}}
					</th>
					<td class="select">
						{{ form_widget(formulaireCandidature.metier_metier, { 'attr': {'style': 'width: 70%',} })}}
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<hr id="separateur_fiche">
					</td>
				</tr>
				<tr>
					<th>{{ form_label(formulaireCandidature.cv, "CV")}}<span class="ob">*</span>
					</th>
					<td rowspan="2">
						{{ form_widget(formulaireCandidature.cv)}}
						<span class="aide_bt_span">
							<a class="picto-item" href="#" aria-label="Fichiers texte ou PDF - Maximum 400 ko">?</a>
						</span>
					</td>
				</tr>
				<tr class="ordi">
					<td></td>
				</tr>
				<tr>
					<th>{{ form_label(formulaireCandidature.lm)}}</th>
					<td colspan="2">
						{{ form_widget(formulaireCandidature.lm)}}
						<span class="aide_bt_span">
							<a class="picto-item" href="#" aria-label="Fichiers texte ou PDF - Maximum 400 ko">?</a>
						</span>
					</td>
				</tr>

				<tr>
					<td colspan="2">
						<hr id="separateur_fiche">
					</td>
				</tr>
				<tr>
					<td colspan="2">
						{{ form_widget(formulaireCandidature.cgu, { 'attr': { 'style': 'margin-left:3px;margin-right:5px;',} })}}
						<label for="cgu" style="font-weight:normal">J'accepte les
							<a href="https://www.directemploi.com/page/mentions-legales" target="_blank">
								conditions générales
							</a>de Direct Emploi</label>
						<span class="ob">*</span>
						<span>
							<br>
							- Coordonnées transmises aux recruteurs, lecture du CV à des fins de recrutement
							<br>
							- Envois potentiels d’offres d’emploi, stage, alternance et formation
						</span>
					</td>
				</tr>
				<tr>
					<td align="center" colspan="2">
						<div class="g-recaptcha" data-sitekey="6LfH66QZAAAAAANdrqB2-_cT6d6yMWBdr0LMl5Pg" style="padding-top: 10px;"></div>
						<div id="fiche_postuler" class="postuler">
							<input type="submit" name="commit" value="Postuler maintenant" onclick="ga('send', 'event', 'Candidat', 'Depot candidature');" class="sub_jaune">
						</div>
						<!--div class="version-light" id="alert_case">
							<span class="red">Attention&nbsp;:</span>
							<ul>
								<li>Nous vous conseillons de désactiver les bloqueurs de publicité de type AdBlock pour assurer la bonne validation du formulaire.</li>
							</ul>
						</div-->			
						<div class="version-light" id="alert_case">
							<span class="red">Notre&nbsp;conseil&nbsp;:</span>
								Nous vous conseillons de désactiver les bloqueurs de publicité de type AdBlock<br>pour assurer la bonne validation du formulaire.
						</div>
					</td>
				</tr>
			</tbody>
		</table>								
	{{ form_end(formulaireCandidature) }}