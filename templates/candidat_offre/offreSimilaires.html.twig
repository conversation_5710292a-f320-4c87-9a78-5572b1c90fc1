
{% extends 'directemploi/new_layout.html.twig' %}

{% block title %}
	Nos {{offreCount}} propositions d'offres d'emploi | Direct emploi
{% endblock %}

{% block meta_description %}
	{% set desc = "On vous propose " ~ offreCount ~ " offres d'emploi similaires aux caractéristiques de votre dérnière recherche.Trouvez votre prochain poste sur Direct Emploi." %}
	<meta name="description" content="{{desc}}">
{% endblock %}

{% block stylesheets %}
	<link rel="stylesheet" href="{{ asset('assets/css/offres_similaires.min.css') }}">
{% endblock %}

{% block content %}
	<section>
		<div class="container offres_listing_page-offres">
			{#% if breadcrumb is defined %}
				{% include '/_partials/_breadcrumb.html.twig'  %}
			{% endif %#}
		</div>

		<div class="container offres_listing_page-offres">

			<div class="page-header block_desc">
					<h1 class="charted" style="font-size: 1.8em;">
					{{ "Direct Emploi vous propose " ~ offreCount ~ " offres d'emploi supplémentaires"}}
					<br>
					{{ "dans votre domaine d'expertise"}}
					</h1>
				<hr>
			</div>

			<div class="container offres_listing_wrapper_page-offres">
				<div id="div-select-all" class="checkbox_select_all" style="height:30px;padding:15px;">
					{% for flash_message in app.flashes('success') %}
    				<div class="alert alert-success">
        				{{ flash_message }}
					</div>
					{% endfor %}
					{# <button  id="passer" class="manage-selection" onclick="redirectTo({{id}})">Passer</button> #}
					<button  id="select-all" class="manage-selection"  onclick="btnRunner()" style="">Tout sélectionner</button>
				</div>
				<hr>
				<form method="POST" action="{{ path('app_candidat_offres_similaires_apply', {id: id}) }}" class="grid_colonne1">
					
					{% for offre in offres %}
						<div class="listing_offres_page-offres" id={{"similaire-"~offre.id_offre}}>
							<div class="listing_offres_cadre_page-offres">
								<div class="listing_offres_intitule_page-offres text-center">
									<h2 class="title_h5_style">
										<b>
											<a href="{{ path('app_candidat_search_details', {id: offre.id_offre, intitule: offre.intitule|url_encode}) }}">{{offre.intitule}}</a>
										</b>
									</h2>
								</div>
								<div class="listing_offres_details_page-offres">
									<div class="listing_offres_logo_page-offres">
										<div class="int_page-offres" style="overflow: hidden; border-radius: 20%;">
											{% if offre.entreprise.logo ?? false %}
												<img style="width: 100%;height: 100%;" alt="{{offre.intitule}}" src={{ "/uploads/logos/" ~ offre.entreprise.logo }}>
											{% endif  %}
										</div>
									</div>
									<div class="listing_text_page-offres"><!-- TITRE -->
										<div class="listing_info_page-offres">
											<span class="div_date_entr">
												{#{offre.@timestamp|format_datetime('long', 'none', locale='fr') ~ "- "}#}
													{% if offre.ville is defined and offre.ville != null %}
														{{offre.ville}}
														-
													{% endif %}
													{{offre.entreprise.raison_sociale}}
											<span>
										</div>
										<div class="listing_mission_page-offres"> <!-- MISSION -->
											{{offre.mission|myTruncate(offre.mission)}}
										</div>
										<div class="listing_tag_page-offres"><!-- TAG (contrat, Secteur, Region?) -->
											Tag :

											<a href="/lists-offre/{{ struct_home_search(offre.contrat) }}-france-metiers-secteurs">{{offre.contrat}}</a>

											{% if offre.secteur_activite[0] is defined and offre.secteur_activite[0] != null %}
												-
											<a href="/lists-offre/france-metiers-{{ struct_home_search(offre.secteur_activite[0].id_secteur_activite) }}-{{ struct_home_search(offre.secteur_activite[0].libelle) }}">{{ offre.secteur_activite[0].libelle }}</a>
											{% elseif offre.secteur_activite is defined and offre.secteur_activite != null %}
													-
												<a href="/lists-offre/france-metiers-{{ struct_home_search(offre.secteur_activite.id_secteur_activite) }}-{{ struct_home_search(offre.secteur_activite.libelle) }}">{{ offre.secteur_activite.libelle }}</a>
											{% endif %}
										</div>
									</div>
								</div>
								<div id={{"div-"~offre.id_offre}} class="checkbox_select_offers">
									<input type="checkbox" class="checkbox-a-job" name={{"checkbox_name-" ~ offre.id_offre}} value="{{ offre.id_offre }}" id={{"radio-"~offre.id_offre}} style="float:right;width:30px;height:30px;">
								</div>
							</div>
						</div>
					{% endfor %}
					<center>
						<button id="postulator" type="submit" style="text-align:center; background: #4ba8c5;" submit>Postuler aux offres</button>
					</center>
					<input type="hidden" name="allOffres" value="{{ offres|json_encode }}">
				</form>
			</div>
		</div>
	</section>

	<script>
		var selectAll = document.getElementById('select-all');
		var checkboxs = document.querySelectorAll('.checkbox-a-job');
		var divs = document.getElementsByClassName('listing_mission_page-offres');
			
		for (i = 0; i <= divs.length; i ++) {
			divs[i].innerHTML = divs[i].innerHTML.substring(0, 200) + ' [...]';
		}

		function btnRunner()
		{
			if (selectAll.checked == true)  {
				selectAll.checked = false;
			}
			else {
				selectAll.checked = true;
			}
			checkboxs.forEach(checkbox => {
				if (selectAll.checked == true) 
					checkbox.checked = true;
				else
					checkbox.checked = false;
			});
		}

		function redirectTo(offre_id)
		{
			window.location.href = "/candidatOffre/"+offre_id;
		}
	</script>

{% endblock %}