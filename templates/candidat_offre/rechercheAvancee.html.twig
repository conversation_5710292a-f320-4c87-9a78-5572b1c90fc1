{% extends 'directemploi/layout.html.twig' %}

{% block stylesheets %}
	<link rel="stylesheet" href="{{ asset('assets/css/new_main_DE.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_responsive.min.css') }}">
{% endblock %}

{% block javascripts %}
	<script src="{{ asset('assets/js/new_script.min.js') }}" defer></script>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.13.1/jquery-ui.min.js"></script>

    <script>
        function selectSecteur(secteurSlug){
            secteurSlug = secteurSlug.substring("/emploi/france-".length);
            contratSlugById = {
                "2" : "cdi",
                "1": "cdd",
                "4": "interim",
                "5": "freelance--independant",
                "6": "alternance",
                "8": "stage"
            }
            var slugs = [];
            var contratId = $("#id_type_de_contrat").val();
            if(contratId){
                var contratSlug = contratSlugById[contratId];
                slugs.push(contratSlug);
            }
            var geoSlug = $("#departement").val();
            if(!geoSlug){
                geoSlug = "france"
            }
            slugs.push(geoSlug);

            slugs.push(secteurSlug);
            window.location.href = "/lists-offre/emploi/"+slugs.join("-");

        }
    </script>

{% endblock %}

{% block content %}
    <div class="contenu">
        <div id="rechercheOffres" style="margin-top: 10px;">
            <div id="cadre">
                <div id="moteur" class="box_rech rech3 rechmulti" style="height: 160px; position: unset;">
                    <div class="margB" id="verifRecherche"></div>
                    <div id="moteur_recherche" class="page_avance" style="display: block;">
                        <div class="inside">
                            <h2 class="h3_title" id="titre_emploi"></h2>
                            <div class="style_nb_title">
                                <div id="nb_result"></div>
                            </div>
                            <div class="fond_rechercher_avancee">
							    <div style="color: #FFF; font-size: 1.6em; padding-top: 8px; padding-left: 13px; font-weight: bold;">Recherche Avancée</div>
						    </div>
                            <br>
                            <span class="select_rechercher_avancee select_rechercher_avancee-left">
                                <div class="titre_recherche" style="font-size: 1.3em"> Type de contrat :</div>
                                <select name="id_type_de_contrat" id="id_type_de_contrat" onchange="_link_domaine()" class="firefox" style="color: rgb(117, 117, 117);"><option value="">——————</option>
                                    <option value="2">CDI</option>
                                    <option value="1">CDD</option>
                                    <option value="4">Interim</option>
                                    <option value="3">FreeLance / Indépendant</option>
                                    <option value="6">Alternance</option>
                                    <option value="8">Stage</option>
                                </select>
                                <span class="select_rechercher_avancee_arrow"></span>
                            </span>
                            <span class="select_rechercher_avancee">
                                <div class="titre_recherche" style="font-size: 1.3em">Expérience requise :</div>
                                <select name="id_experience" id="id_experience"  class="firefox" style="color: rgb(117, 117, 117);"><option value="">——————</option>
                                    <option value="1">0 à 5 ans</option>
                                    <option value="2">5 à 10 ans</option>
                                    <option value="3">+ 10 ans</option>
                                </select>
                                <span class="select_rechercher_avancee_arrow"></span>
						    </span>
                            <br>
                            <br>
                            <span class="select_rechercher_avancee localisation_rechercher_avancee">
                                <div class="titre_recherche titre_recherche_localisation" style="font-size: 1.3em">Localisation :</div>
                                <select name="departement" id="departement"  class="firefox" style="color: rgb(117, 117, 117);">
                                    <option value="">Selectionner un département</option>
                                    {% for departement in listeDepartements %}
                                        <option value="{{  struct_home_search(departement.libelle) }}">{{ departement.libelle }}({{ departement.codePostal }})</option>
                                    {% endfor %}
                                </select>
                                <span class="select_rechercher_avancee_arrow"></span>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="cadre_domaine_recherche_avancee">
                    <span class="decoration_compte bar2"></span>
                    <div class="text_domaine_recherche_avancee">Sélectionnez votre domaine d'activité pour lancer la recherche</div>
                    <table class="table_recherche_avancee">
                        <tbody>
                        {% for domaine in listeDomaines|slice(0,20) %}
                                {% if domaine.idRechercheAvancee % 2 == 1 %}
                                    <tr>
                                        <td class='td_recherche_avancee'>
                                            <a href="#" onclick="selectSecteur('{{ domaine.lien }}')">
                                                <img alt="{{domaine.image}}" src="{{ asset('images/recherche_avancee/' ~ domaine.image ) }}" class="recherche_avancee_domaine_image_left">
                                                <div class="recherche_avancee_domaine_titre_left">{{ domaine.titre }}</div>
                                                <div class="recherche_avancee_domaine_nuage_left">{{ domaine.nuageMots }}</div>
                                            </a>
                                        </td>
                                {% else %}
                                        <td class='td_recherche_avancee'>
                                            <a  href="#" onclick="selectSecteur('{{ domaine.lien }}')">
                                                <img alt="{{domaine.image}}" src="{{ asset('images/recherche_avancee/' ~ domaine.image ) }}" class="recherche_avancee_domaine_image_right">
                                                <div class="recherche_avancee_domaine_titre_right">{{ domaine.titre }}</div>
                                                <div class="recherche_avancee_domaine_nuage_right">{{ domaine.nuageMots }}</div>
                                            </a>
                                        </td>
                                    </tr>
                                {% endif %}
                        {% endfor %}
                        </tbody>
                    </table>
                    <div class="div_recherche_avancee_center">
                        <a href="{{ path('app_candidat_search_avancee_result', {'id_secteur': listeDomaines[20].offListeSecteurActiviteIdSecteurActivite,'lien':listeDomaines[20].lien }) }}">
                            <img alt="{{listeDomaines[20].image}}" src="{{ asset('images/recherche_avancee/' ~ listeDomaines[20].image ) }}" class="recherche_avancee_domaine_image_mid">
                            <div class="recherche_avancee_domaine_titre_mid">{{ listeDomaines[20].titre }}</div>
                            <div class="recherche_avancee_domaine_nuage_mid">{{ listeDomaines[20].nuageMots }}</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
