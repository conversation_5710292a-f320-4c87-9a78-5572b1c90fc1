{% extends 'carriereDistribution/base.html.twig' %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% set description_seo = "" ~ listeOffres.getTotalItemCount ~ " offres d'emploi " ~ myDomaine ~ (myLocalisation is defined and myLocalisation != null ? ', ' ~ myLocalisation : '') ~ ". Postulez dès maintenant sur carrière distribution, rapide et gratuit." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block javascripts %}
    {% if seoEditReferencement ?? false %}
        <meta name="keywords" content="{{ seoEditReferencement.getKeywords() }}">
    {% endif %}


{% endblock %}
{% block body %}
<style>
    .side-menu {
        margin-right: 20px;
    }
    .menu-items {
        list-style: none;
        padding: 0;
    }
    .menu-items li {
        display: inline;
        margin-right: 5px;
    }
    .menu-items a {
        text-decoration: none;
        color: #000;
        position: relative;
    }
</style>
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereDistribution/header.html.twig' %}
        {% endblock %}

        {% if seoEditReferencement == null or seoEditReferencement.photopath is null %}
            <section class="offres-header d-flex justify-content-center align-items-center">

                {% if app.request.query.get('localisation') is not empty and app.request.query.get('q') is not empty %}
                    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;">Offres d’emploi : {{ app.request.query.get('q') }} - {{ app.request.query.get('localisation') }}</h1>
                {% elseif app.request.query.get('q') is not empty  %}
                    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;">Offres d’emploi {{ app.request.query.get('q') }}</h1>
                {% elseif app.request.query.get('localisation') is not empty  %}
                    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;">Offres d’emploi à {{ app.request.query.get('localisation') }}</h1>
                {% elseif myDomaine is not empty %}
                    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;">Des offres d’emploi : {{ myDomaine }}</h1>
                {% else %}
                    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;">Des offres d’emploi qui correspondent à votre recherche</h1>
                {% endif %}
            </section>
        {% elseif seoEditReferencement.photopath is not null %}
            <section class="offres-header d-flex justify-content-center align-items-center" style="background-image: url('{{ asset("/carriereDistribution/Front/images/seo/" ~ seoEditReferencement.photopath) }}'); background-size: cover; background-position: center;">
                {% if app.request.query.get('localisation') is not empty and app.request.query.get('q') is not empty %}
                    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;">Offres d’emploi : {{ app.request.query.get('q') }} - {{ app.request.query.get('localisation') }}</h1>
                {% elseif app.request.query.get('q') is not empty  %}
                    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;">Offres d’emploi {{ app.request.query.get('q') }}</h1>
                {% elseif app.request.query.get('localisation') is not empty  %}
                    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;">Offres d’emploi à {{ app.request.query.get('localisation') }}</h1>
                {% elseif myDomaine is not empty %}
                    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;">Des offres d’emploi : {{ myDomaine }}</h1>
                {% else %}
                    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;">Des offres d’emploi qui correspondent à votre recherche</h1>
                {% endif %}
            </section>
        {% endif %}

        <section class="recherche-offre">
            <div class="container">
                {% include 'carriereDistribution/offres/recherche_elk.html.twig' %}
            </div>
        </section>
        <div class="container offres_listing_page-offres">
            {% if breadcrumb is defined %}
                {% include '/_partials/_breadcrumb.html.twig'  %}
            {% endif %}

        </div>
        <section class="text-center mt-5 mb-0">

            <div class="container">
                <div class="card p-4 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
                    <div class="card p-4 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
                        {% if seoEditReferencement is not null %}
                            {% if seoEditReferencement.nom is not null %}
                                <h2 style="font-size: 1.25rem; line-height: 1.5;">{{ seoEditReferencement.nom }}</h2>
                            {% endif %}
                            <p class="justified">{{ seoEditReferencement.paragraphecontenu|raw }}</p>
                        {% else %}
                            <h6>Offres d'emploi distribution</h6>
                            <p class="justified">
                                Découvrez des opportunités professionnelles exceptionnelles dans le secteur de la distribution grâce à notre plateforme exclusive d'offres d'emploi. Nous mettons à votre disposition une variété de postes dans l'hôtellerie et la distribution, allant des métiers de la cuisine tels que chef, pâtissier et commis aux postes de gestion de restaurants et de services de traiteur. Trouvez le travail qui correspond à vos compétences et à vos aspirations professionnelles dans le domaine dynamique de la distribution. Explorez nos offres d'emploi dès maintenant pour donner un nouvel élan à votre carrière dans l'industrie de la distribution.
                        {% endif %}
                    </div>
                </div>
            </div>
        </section>
        <section class="contrat-travail">
            <div class="container">
                <aside>
                    {% for sideMenu in sideMenus %}
                        <div class="side-menu">
                            <h6>{{ sideMenu.label }}</h6>
                            <ul class="menu-items">
                                {% for item in sideMenu.items %}
                                    <li>
                                        <a href="{{ item.url }}">
                                            <span>{{ item.label }}</span>
                                            {% if item.count ?? false %}
                                                <span class="item-count" style="color:#e2971b;" >({{ item.count }})</span>
                                            {% endif %}
                                        </a>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endfor %}
                </aside>
            </div>
        </section>

        <section class="login-form">

        <div class="container">
            <div class="row mt-5" id="navigation-root">
                <div class="col-lg-6 " id="offreDiv">
                    {% for offre in listeOffres %}
                    <a id="offrelink">
                        <a href="#" class="offer-link"  data-offre="{{ offre|json_encode }}">
                        <div class="w-100 offre-card " data-offre-loaded="{{ offre|json_encode }}"  >
                            <div class="v-flex">
                                <div class="h-flex-between">
                                    <div class="v-flex">
                                        <span class="mr-1 text-muted mb-2" style="font-weight: 200; font-size:14px; ">{{offre.getDateSoumission()|format_datetime('long', 'none', locale='fr')}}-{{offre.ville}}-{{offre.entreprise.raison_sociale}}</span>
                                        <br>
                                        <span class="mr-1 black" style="font-weight: 700;">{{offre.intitule}}</span>
                                    </div>
                                    {#
                                    {% if offre.entreprise.logo ?? false %}
                                        <img src="{{ asset('uploads/logos/' ~ offre.entreprise.logo ) }}" alt="{{offre.entreprise.logo}}"/>
                                    {% endif  %}
                                    #}
                                    {% if offre.entreprise.logo != null %}
                                    <img alt="{{offre.entreprise.logo}}" src="{{ asset('uploads/logos/' ~ offre.entreprise.logo ) }}" style="max-width: 100px;max-height:80px"/>
                                    {% else %}
                                        <img alt="{{offre.entreprise.logo}}" src="{{ asset('carriereDistribution/Front/images/logo noir.png') }}" style="max-width: 100px;max-height:80px"/>
                                    {% endif %}
                                        </div>
                                <p class="text-justify" >{{ offre.mission|myTruncate(offre.mission)|slice(0, 400) }} ...<span style="color: #ffca2c">Afficher plus</span></p>
                            </div>
                            <div class="h-flex black ">
                                <span class="mr-2" style="font-weight: 700;">Tags:</span>
                                <span class="mr-2" style="font-weight: 500;"><a href="/Dis/lists-offre/{{ struct_home_search(offre.contrat) }}-france-metiers-secteurs">{{offre.contrat}}</a></span>
                                {% for secteur in offre.getSecteurs()  %}
                                    <span class="mr-2" style="font-weight: 500;">/ <a href="/Dis/lists-offre/france-metiers-{{ struct_home_search(secteur.id_secteur_activite) }}-{{ struct_home_search(secteur.libelle) }}">{{ secteur.libelle|replace({'/':'-'}) }}</a></span>
                                {% endfor %}
                            </div>
                        </div>
                    </a>
                    </a>
                    {% endfor %}
                    {% if moreResults %}
                        <div style="text-align:center">
                            <a href="{{ moreResults.url }}" class="btn w-100 px-4 button-black" style="background-color: #e2971b">Élargir votre zone de recherche</a>
                        </div>
                    {% endif %}
                </div>
                <style>
                    .desc-card{
                        visibility: hidden;
                    }
                </style>
                <div class="col-lg-6" id="navigation-parent">
                    <div class="popup display-none" id="mypopup">

                        <div id="navigation" class="card desc-card">
                            <div class="v-flex" id="content">
                                <div id="dynamic-content">
                                    <div class="h-flex-between text-muted" style="font-size:14px;">

                                    </div>
                </div>
                </div>
                </div>
                </div>
                </div>
            </div>

        </div>

    </section>
        <div align="center" id="pagination_de">
            {{ knp_pagination_render(listeOffres, 'carriereDistribution/paginationTemplate.html.twig') }}
        </div>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

        <script>
            async function loadOfferDetails(idoffre) {
                try {
                    // Issue a fetch request to the endpoint with the offer ID
                    const response = await fetch(`https://www.carriere-distribution.com/Dis/view/${idoffre}`);
                    //const response = await fetch(`http://127.0.0.1:8000/Dis/candidatOffre/${idoffre}`);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    // Convert the response into JSON
                    // Return the offreDetails object
                    return await response.json();
                } catch (error) {
                    console.error('Could not load offer details:', error);

                    // You might want to handle the error gracefully here
                    // For example, you could return a default value or throw the error again
                    throw error;
                }
            }

            document.addEventListener('DOMContentLoaded', function () {
                const dynamicContent = document.getElementById('dynamic-content');
                const offerLinks = document.querySelectorAll('.offer-link');
                const offrelink = document.getElementById('offrelink');
                const navigation = document.getElementById('navigation');

                async function updateContent(offerObject) {
                    const idoffre = offerObject.data.id_offre;
                    //const offreDetails = await loadOfferDetails(43599284);
                    const offreDetails = await loadOfferDetails(idoffre);
                    const dateString = offerObject.data.date_soumission;
                    const dateSoumission = new Date(dateString);
                    const options = {day: 'numeric', month: 'long', year: 'numeric'};
                    const dateFormatee = dateSoumission.toLocaleDateString('fr-FR', options);
                    const secteurActivite = offerObject.data.secteur_activite;
                    let libellesConcatenes;
                    if (Array.isArray(secteurActivite)) {
                        const libelles = secteurActivite.map(element => element.libelle);
                        libellesConcatenes = libelles.join('/');
                    } else if (secteurActivite) {
                        libellesConcatenes = secteurActivite.libelle;
                    }else {
                        libellesConcatenes = '';
                    }

                    const dateString1 = offerObject.data.date_soumission;

                    const dateObj = new Date(dateString);

                    const jour = dateObj.getDate().toString().padStart(2, '0');
                    const mois = (dateObj.getMonth() + 1).toString().padStart(2, '0'); // Note: Les mois commencent à partir de zéro
                    const annee = dateObj.getFullYear();

                    const date_soumission = `${jour}/${mois}/${annee}`;

                    var imgalt = offreDetails.offreDetails.logo;
                    var imgsrc = "/uploads/logos/" + offreDetails.offreDetails.logo;
                    console.log(imgalt,imgsrc)
                    // Clear existing content
                    dynamicContent.innerHTML = '';
                    const submitCandidatURL = `/Dis/submitCandidat/${offreDetails.jsonLD.identifier.value}`; // Update with your actual URL path

                    // Create a wrapper div
                    const wrapperDiv = document.createElement('div');
                    wrapperDiv.className = '';
                    wrapperDiv.style.fontSize = '14px';
                    {#
                    try {
                        // Liste des IDs pour lesquels nous ne voulons pas rediriger vers url_direct
                        const idsSansRedirection = [
                            1255997, 1255971, 1245988, 1255189, 1255224, 872634, 1255828,
                            1255197, 1255223, 991195, 984813, 1255823, 1255226, 1255216,
                            1256039, 1255768, 1255840, 1255703, 860993, 1255221, 1255767,
                            1255198, 1255196, 1255965, 1255253, 1255184, 1064982, 1095565,
                            1255227, 1255383, 1254911, 977102, 1255825, 909244, 1255700,
                            1255250, 1255721, 846965, 1256004, 1047570, 1248694, 1013221,
                            933277, 1116637, 1050164, 909713, 4
                        ];

                        if (offreDetails.url_direct != null && !idsSansRedirection.includes(offreDetails.id_user)) {
                            // Redirection vers url_direct si elle est définie et si id_user ne fait pas partie des IDs dans idsSansRedirection
                            var postulerButton = `<a href="${offreDetails.url_direct}" id="postulerButton" target="_blank" class="btn btn-primary px-5 py-2 mb-2" onclick="incrementClicks(${entrepriseId})">Postuler</a>`;

                        } else {
                            // Utilisation de l'URL par défaut dans les autres cas
                            var postulerButton = `<a href="${submitCandidatURL}" target="_blank" class="btn btn-primary px-5 py-2 mb-2">Postuler</a>`;
                        }
                    } catch (error) {
                        var postulerButton = `<a href="${submitCandidatURL}" target="_blank" class="btn btn-primary px-5 py-2 mb-2">Postuler</a>`;
                    }
                    #}
                    var postulerButton = `<a href="${submitCandidatURL}" target="_blank" class="btn btn-primary px-5 py-2 mb-2">Postuler</a>`;
                    // Append your HTML content to the wrapper div
                    wrapperDiv.innerHTML = `
                    <style>
                    .job-description{
                      overflow-y: scroll;
                                          }
                    .job-description > * {
                      margin-bottom:1rem;

                    }
                    @media screen and (min-width: 2501px) and (min-height: 1081px) and (max-height: 1440px) {
                        .job-description {
                            height: 870px;
                        }
                    }

                    @media screen and (min-width: 2501px) and (max-height: 1080px) {
                        .job-description {
                            height: 730px;
                        }
                    }

                    @media screen and (min-width: 2501px) and (min-height: 1441px) {
                        .job-description {
                            height: 1170px;
                        }
                    }
                    @media screen and (min-width: 1900px) and (max-width: 2499px) {
                    .job-description{
                        height:730px;
                                                                  }

                    }
                    @media screen and (min-width: 1440px) and (max-width: 1899px) {
                    .job-description{
                        height:480px;
                                                                  }

                    }
                    @media only screen and (max-width: 1439px) and (min-width: 1300px){
                    .job-description{
                        height:450px;
                                                                  }
                                                                  }

                    @media only screen and (max-width: 1299px) and (min-width: 1200px){
                    .job-description{
                        height:450px;
                                                                  }
                                                                  }
                    @media only screen and (max-width: 1199px) and (min-width: 992px){
                    .job-description{
                        height:270px;
                                                                  }
                    }
                                   </style>
	                            <div>
                                    <span>${dateFormatee}-${offerObject.data.ville}-${offerObject.data.entreprise.raison_sociale}</span>
                                </div>
                                <span style="margin: 2rem 0;">Ref : ${offerObject.data.reference}</span>
                            </div>
                            <h6>${offerObject.data.intitule}</h6>
                            <div class="h-flex align-items-center justify-content-between m-0">
                                <div class="h-flex align-items-end">
                                    <span>partager l'offre sur:</span>
                                    <div class="social-media-links">
                                        <a href="#" ><i class="fa-brands fa-facebook-f" ></i></a>
                                        <a href="#" ><i class="fa-brands fa-twitter" ></i></a>
                                        <a href="#" ><i class="fa-brands fa-google-plus-g"></i></a>
                                        <a href="#" ><i class="fa-brands fa-linkedin-in "></i></a>
                                    </div>
                                </div>
                            ${postulerButton}
                            </div>
                            <hr>
                            <div class="job-description">
                                <div class="h-flex">
                                   <span style="font-weight: 700;">Secteur d'activité :</span><span class="mx-2">${libellesConcatenes}</span>
                                </div>
                                <div class="h-flex">
                                    <span style="font-weight: 700;">Debut de contrat :</span> <span class="mx-2">${date_soumission}</span>
                                </div>
                                <div class="h-flex">
                                    <span style="font-size:18px; font-weight:700;">L'entreprise</span>
                                    <hr>
                                    <p class="justified">${offerObject.data.entreprise.presentation_societe}</p>
                                </div>
                                <div class="h-flex">
                                    <span style="font-size:18px; font-weight:700;">Description de l'offre</span>
                                    <hr>
                                    <p class="justified">${offreDetails.jsonLD.mission}</p>
                                </div>
                                <div class="h-flex">
                                    <span style="font-size:18px; font-weight:700;">Profil recherché</span>
                                    <hr>
                                    <p class="justified">${offreDetails.jsonLD.profil}</p>
                                </div>
                                {% if app.user is null %}
                <div>
                    <span style="font-size:18px; font-weight:700;">Pas encore inscrit ?</span>
                    <hr>
                    <p class="justified">Créer un compte candidat vous permet :
                        <ul>
                            <li>l'enregistrement de vos informations pour ne pas avoir à les resaisir plusieurs fois</li>
                            <li>la conservation d'un historique de vos candidatures</li>
                            <li>la possibilité d'être contacté directement par plus de 1000 entreprises</li>
                        </ul>
                        <div class="text-center">
                            <a  href="{{ path('app_Res_register_candidat_e1') }}" class="btn mb-2 button-black">S'inscrire</a>
                            <p>ou se connecter <a  href="{{ path('app_Res_login') }}" class="yellow" style="text-decoration:underline;">ici</a></p>
                        </div>
                    </p>
                </div>
            {% endif %}


            `;

                    // Append the wrapper div to the dynamic content
                    dynamicContent.appendChild(wrapperDiv);
                    navigation.style.visibility ="visible";


                }

                // Attach click event listeners to each offer link
                offerLinks.forEach(link => {
                    link.addEventListener('click', function (event) {
                        event.preventDefault();

                        // Get the offer object from the data attribute
                        const offerObject = JSON.parse(link.getAttribute('data-offre'));

                        // Update the dynamic content
                        updateContent(offerObject);
                    });
                });
            });
        </script>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@10/dist/sweetalert2.min.css">
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const successMessage = '{{ app.session.flashBag.get("msg") | first }}';

                if (successMessage) {
                    Swal.fire({
                        icon: 'success',
                        title: successMessage,
                        showConfirmButton: false,
                        showCloseButton: true,  // Add this line to show the "Fermer" button
                    });
                }
            });

        </script>
    {% block footer %}
        {% include 'carriereDistribution/footer.html.twig' %}
    {% endblock %}
    </div>
{% endblock %}
