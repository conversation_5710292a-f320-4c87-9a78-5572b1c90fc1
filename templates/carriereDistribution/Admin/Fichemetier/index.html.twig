{% extends 'carriereBTP/base.html.twig' %}
{% block title %}Nos conseils recrutement et emploi entretien d'embauche | Carriere BTP{% endblock %}

{% block body %}
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereBTP/header.html.twig' %}
        {% endblock %}
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

        <style>

            .table {
                width: 100%;
                border: 1px solid #EEEEEE;
            }

            .table-header {
                display: flex;
                width: 100%;
                background: #000;
                padding: 18px 0;
            }

            .table-row {
                display: flex;
                width: 100%;
                padding: 18px 0;
            }
            .table-row:nth-of-type(odd) {
                background: #EEEEEE;
            }

            .table-data, .header__item {
                flex: 1 1 20%;
                text-align: center;
            }

            .header__item {
                text-transform: uppercase;
            }

            .filter__link {
                color: white;
                text-decoration: none;
                position: relative;
                display: inline-block;
                padding-left: 24px;
                padding-right: 24px;
            }
            .filter__link::after {
                content: "";
                position: absolute;
                right: -18px;
                color: white;
                font-size: 12px;
                top: 50%;
                transform: translateY(-50%);
            }
            .filter__link.desc::after {
                content: "(desc)";
            }
            .filter__link.asc::after {
                content: "(asc)";
            }
            .search-box {
                display: flex;
                align-items: center; /* Centre verticalement les éléments */
                gap: 10px; /* Espace entre les éléments */
            }

            #searchInput {
                flex-grow: 1; /* Permet au champ de recherche de prendre la majeure partie de l'espace */
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
            }

            .search-box .fa-search {
                color: #ccc;
            }

            .btn.button-black {
                padding: 10px 20px; /* Ajustez pour correspondre à la hauteur du champ de recherche */
                text-decoration: none; /* Optionnel, pour enlever le soulignement du lien */
                color: white; /* Couleur du texte */
                border-radius: 5px; /* Pour correspondre au champ de recherche */
            }



        </style>
        <section class="ajouter-cv-header" id="">
            <div class="container">
                <h5 class="text-center text-uppercase mb-3"><span style="color:#e2971b">Module </span>Fiche metier</h5>
                <h6 class="text-center text-uppercase mb-3"><span style="color:#e2971b">{{ NbrMetier}}  </span>Métiers disponible pour BTP</h6>
            </div>
        </section>
        <section class="section-embauche" style=" max-width: 2000px;margin-right: auto;margin-left: auto;display: flex;justify-content: center;align-items: center;">


            <div class="table">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="Rechercher un métier...">
                    <i class="fas fa-search"></i>
                </div>

                <div class="table-header">
                    <div class="header__item"><a id="NomMétier" class="filter__link" href="#">Nom Métier</a></div>
                    <div class="header__item"><a id="CodeSecteur" class="filter__link filter__link--number" href="#">Fiche metier</a></div>
                    <div class="header__item"><a id="CodeSecteur" class="filter__link filter__link--number" href="#">Action</a></div>
                </div>
                <div class="table-content">
                    {% for metier in Metiers %}
                        <div class="table-row">
                            <div class="table-data">{{ metier.nom }} </div>
                            {% if metier.hasFichemetier==false %}
                            <div class="table-data">
                                <div class="table-data">
                                        <p><i class="fas fa-times-circle" style="color: red;"></i> Non disponible</p>
                                </div>
                            </div>
                            <div class="table-data">
                                <a href="{{ path('app_BTP_admin_AddFicheMetier', {'idmetier': metier.id_metier}) }}" class="btn button-black"style="background-color: #0b7012">Créer</a>
                            </div>
                            {% else %}
                                <div class="table-data">
                                    <div class="table-data">
                                        <p><i class="fas fa-check-circle" style="color: green;"></i> Disponible</p>
                                    </div>
                                </div>
                                <div class="table-data">
                                    <a href="{{ path('app_BTP_admin_UpdateFicheMetier', {'idmetier': metier.id_metier}) }}" class="btn button-black"style="background-color: #ffca2c">Modifier</a>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>

        </section>
        {% block footer %}
            {% include 'carriereBTP/footer.html.twig' %}
        {% endblock %}
        <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
        <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <script>
            var properties = [
                'NomMetier',
                'UrlReferencement',
                'ParagrapheContenu',
                'Codesecteur',
            ];

            $.each( properties, function( i, val ) {

                var orderClass = '';

                $("#" + val).click(function(e){
                    e.preventDefault();
                    $('.filter__link.filter__link--active').not(this).removeClass('filter__link--active');
                    $(this).toggleClass('filter__link--active');
                    $('.filter__link').removeClass('asc desc');

                    if(orderClass == 'desc' || orderClass == '') {
                        $(this).addClass('asc');
                        orderClass = 'asc';
                    } else {
                        $(this).addClass('desc');
                        orderClass = 'desc';
                    }

                    var parent = $(this).closest('.header__item');
                    var index = $(".header__item").index(parent);
                    var $table = $('.table-content');
                    var rows = $table.find('.table-row').get();
                    var isSelected = $(this).hasClass('filter__link--active');
                    var isNumber = $(this).hasClass('filter__link--number');

                    rows.sort(function(a, b){

                        var x = $(a).find('.table-data').eq(index).text();
                        var y = $(b).find('.table-data').eq(index).text();

                        if(isNumber == true) {

                            if(isSelected) {
                                return x - y;
                            } else {
                                return y - x;
                            }

                        } else {

                            if(isSelected) {
                                if(x < y) return -1;
                                if(x > y) return 1;
                                return 0;
                            } else {
                                if(x > y) return -1;
                                if(x < y) return 1;
                                return 0;
                            }
                        }
                    });

                    $.each(rows, function(index,row) {
                        $table.append(row);
                    });

                    return false;
                });

            });
            function showFullParagraph(paragraph) {
                Swal.fire({
                    title: 'Contenu complet',
                    text: paragraph,
                    showCloseButton: true
                });
            }
            document.addEventListener("DOMContentLoaded", function() {
                var searchInput = document.getElementById('searchInput');

                searchInput.addEventListener('keyup', function(e) {
                    var searchTerm = e.target.value.toLowerCase();
                    var rows = document.querySelectorAll('.table-content .table-row');

                    rows.forEach(function(row) {
                        var metier = row.querySelector('.table-data').textContent.toLowerCase();
                        if(metier.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            });

        </script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const successMessage = '{{ app.session.flashBag.get("msg") | first }}';

                if (successMessage) {
                    Swal.fire({
                        icon: 'success',
                        title: successMessage,
                        showConfirmButton: false,
                        showCloseButton: true,  // Add this line to show the "Fermer" button
                    });
                }
            });
            document.addEventListener('DOMContentLoaded', function () {
                const successMessage = '{{ app.session.flashBag.get("msgdelete") | first }}';

                if (successMessage) {
                    Swal.fire({
                        icon: 'success',
                        title: successMessage,
                        showConfirmButton: false,
                        showCloseButton: true,  // Add this line to show the "Fermer" button
                    });
                }
            });
            document.addEventListener('DOMContentLoaded', function () {
                const successMessage = '{{ app.session.flashBag.get("msginsertfichemetier") | first }}';

                if (successMessage) {
                    Swal.fire({
                        icon: 'success',
                        title: successMessage,
                        showConfirmButton: false,
                        showCloseButton: true,  // Add this line to show the "Fermer" button
                    });
                }
            });
            document.addEventListener('DOMContentLoaded', function () {
                const successMessage = '{{ app.session.flashBag.get("msgupdatefichemetier") | first }}';
                if (successMessage) {
                    Swal.fire({
                        icon: 'success',
                        title: successMessage,
                        showConfirmButton: false,
                        showCloseButton: true,  // Add this line to show the "Fermer" button
                    });
                }
            });
            document.addEventListener('DOMContentLoaded', function () {
                const successMessage = '{{ app.session.flashBag.get("script") | first }}';

                if (successMessage) {
                    Swal.fire({
                        icon: 'success',
                        title: successMessage,
                        showConfirmButton: false,
                        showCloseButton: true,  // Add this line to show the "Fermer" button
                    });
                }
            });
            function confirmDelete(url, nomMetier, seoExcerpt) {
                Swal.fire({
                    title: 'Êtes-vous sûr de vouloir supprimer ce métier ?',
                    html: `Nom métier : <strong>${nomMetier}</strong><br>SEO text : <strong>${seoExcerpt}</strong>`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Oui',
                    cancelButtonText: 'Non'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = url;
                    }
                });
            }

        </script>
        <script>
            function filterBySector(sectorId) {
                var rows = document.querySelectorAll('.table-row');
                rows.forEach(row => {
                    var domaineId = row.querySelector('.table-data:nth-child(4)').textContent;
                    if (domaineId == sectorId) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
        </script>
        <script>
            function filterBySector(sectorId) {
                var rows = document.querySelectorAll('.table-row');
                rows.forEach(row => {
                    var domaineId = row.querySelector('.table-data:nth-child(4)').textContent;
                    if (domaineId == sectorId) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            function showAllRows() {
                var rows = document.querySelectorAll('.table-row');
                rows.forEach(row => {
                    row.style.display = '';
                });
            }
        </script>


    </div>
{% endblock %}