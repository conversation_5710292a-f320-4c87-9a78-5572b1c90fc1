<div class="box_gris chapo_compte clearfix">
	<div class="text" id="offres_stat_compte" name="offres_stat">
	    Bonjour<br/>Vous avez actuellement
		<span class="vert">{{ user_stats.getNbOffresEmploi() }} offres d'emploi</span>
		,
		<span class="vert">{{ user_stats.getNbOffresStage() }} offres de stage</span>
		et
		<span class="vert">{{ user_stats.getNbOffresAlternance() }} offres en alternance</span>
		publiées sur le site
	</div>
	<br>
	<div class="floatR" >
		<a class="submit sub_jaune sub_large" href="{{ path('app_offresproposees') }}" title="Souscrire à un produit">Souscrire à un produit</a>
	</div>
</div>

<h2>Vos produits</h2>
{% if (SouscriptionsCommercialesValides)|length > 0 %}
	<table class="listing_offres" width="100%" cellspacing="0">
		<tbody>
		{% for key, SCV in SouscriptionsCommercialesValides %}
			<tr><td colspan="3" height="10px"></td></tr>
			<tr {{ key % 2 == 0 ? 'class="even"' : 'class="odd"' }}>
				<td class="listing_offre">

					{% set nom_ameliore = SCV['nom_offre'] %}
					{% if SCV['for_offre'] == 1 %}
						{% set nom_ameliore = (SCV['nom_offre'] ~ (SCV['plateforme_diffusion'] == '1' ? ' + Plateforme de diffusion' : '')) %}
					{% endif %}
					<div class="intitule">{{ nom_ameliore }} </div>
					Arrive à échéance le {{ SCV['date_fin_souscription']|date('d/m/Y') }}
				</td>
				{% if (SCV['credit'] > 0) %}
					<td>
					{% if (SCV['etat_paiement'] == '2') %}
						<strong>
							Crédits restants : {{ SCV['credit'] > 50 ? 'illimités' : SCV['credit'] }}
						</strong>
					{% elseif (SCV['etat_paiement'] == '1') %}
						En attente de validation
					{% elseif (SCV['etat_paiement'] == '0') %}
						Bon de commande non imprimé
					{% endif %}
					</td>
					{% if SCV['com_liste_type_offre_commerciale_id_type_offre_commerciale'] != 4 %}
						<td class="floatR">
							{% if (SCV['etat_paiement'] == '2') %}
								{% if (user.getTypeDiffusion() == 1) %}
									<a class="submit sub_jaune sub_large" href="{{ path('app_diffusion_offre_unique', {id_off_co: SCV['com_offre_commerciale_id_offre_commerciale']}) }}" title="Diffuser une offre">Diffuser une offre</a>
								{% else %}
									Vos offres sont diffusés automatiquement
								{% endif %}
							{# {% else %}
								<a href="{{ "/espaceRecruteur/imprimerBonCommande/" ~ SCV['getIdLotSouscription'] }} " title="Imprimer le bon de commande">Imprimer le bon de commande</a> #}
							{% endif %}
						</td>
					{% endif %}
				{% else %}
					<td colspan="2">
						<strong>
							Crédit épuisé
						</strong>
					</td>
				{% endif %}
			</tr>
		{% endfor %}
		</tbody>
	</table>
{% else %}
	<em>Aucune offre commerciale souscrite</em><br/>
{% endif %}
	<h2>Information sur mon compte</h2>
	<div class="floatR">
		<a class="submit sub_jaune sub_large" href="/espaceRecruteur/gestionCompte" title="Modifier mon profil">Modifier mon profil</a>
	</div>

	<div class="item"><strong>Société : </strong> {{ user_entreprise['raison_sociale'] }} </div>
	<div class="item"><strong>Responsable du compte : </strong> {{ user_civilite ~ ' ' ~ ((user.getNom())|lower|capitalize) ~ ' ' ~ ((user.getPrenom())|lower|capitalize) }}</div>

	<div class="item">
	<strong>Description de l'entreprise : </strong><br/>

	{% if not user_entreprise %}
		L'entreprise n'existe plus
	{% else %}
		{{ user_entreprise['presentation_societe']|nl2br }}
	{% endif %}
	</div>
	<br />
	<div class="item">
		<strong>Login : </strong>
		{{ user.login }}
	</div>

	<div class="item">
		<strong>Email : </strong>
			<a href="mailto:{{ user.email }}">{{ user.email }}</a><br />
	</div>
	<div class="item">
		<strong>Tél. : </strong>
		{{ user.telephone }}
	</div>
	{% if user.fax %}
		<div class="item">
			<strong>Fax : </strong>
			{{ user.fax }}
		</div>
	{% endif %}
	
	{% if user.mobile %}
	<div class="item">
		<strong>Mobile : </strong>
			{{ user.mobile }}
	</div>
	{% endif %}

	{% if user_entreprise['site_web'] %}
		<div class="item">
			<strong>Site internet : </strong>
			<a href="{{ user_entreprise['site_web'] }}"/> {{ user_entreprise['site_web'] }} <a/>
		</div>
	{% endif %}
	<br/>
	<div class="item">
	<strong>Adresse : </strong>
	{{ user_entreprise['adresse1'] }}
	{% if user_entreprise['adresse2'] %}
		{{ user_entreprise['adresse2'] }}
	{% endif %}
	{{ user_entreprise['code_postal'] }}
	{{ user_entreprise['nom_ville'] }}
</div>