{% extends 'directemploi/layout.html.twig' %}

{% block title %}Modifiez votre offre - {{offre.getIntitule()}} | Direct Emploi{% endblock %}

{% block stylesheets %}{% endblock %}

{% block content %}
<h1>Déposer une offre</h1>

	<div id="fil_ariane">
		<a href="{{ path('directemploi') }}" title="Accueil">Accueil</a> -
		<a href="{{ path('app_espace_recruteur') }}" title="Accueil">Recruteur</a>
	</div>

{{ form_start(DiffusionOffre) }}
	<div class="form_box" id="top_page">
	<h2>1 - Informations sur le poste</h2>
		<table class="formulaire">
			{% if nom_offre is defined %}
				<tr>
					<td class="label"> Produit :<span class="ob">*</span></td>
					<td>
						{{ nom_offre }}
					</td>
				</tr>
			{% endif %}
			<tr>
				<td class='label'> {{ form_label(DiffusionOffre.titre_offre,"Titre de l'offre") }} <span class="ob">*</span></td>
				<td> {{ form_widget(DiffusionOffre.titre_offre,{ 'attr': {'value': offre.getIntitule() } }) }} </td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.reference_offre,"Réference") }} <span class="ob">*</span></td>
				<td> {{ form_widget(DiffusionOffre.reference_offre,{ 'attr': {'value': offre.getReference() } }) }} </td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.type_contrat_offre,"Type de contrat") }} <span class="ob">*</span></td>
				<td> {{ form_widget(DiffusionOffre.type_contrat_offre)}} </td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.duree_contrat_offre,"Durée du contrat") }} </td>
				<td> {{ form_widget(DiffusionOffre.duree_contrat_offre,{ 'attr': {'value': offre.getDureeContrat() } }) }} </td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.date_debut_contrat_offre,"Date de début") }} </td>
				<td> {{ form_widget(DiffusionOffre.date_debut_contrat_offre,{ 'attr': { 'value': offre.getDateDebut()} }) }} </td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.fourchette_remuneration_offre,"Rémuneration") }} </td>
				<td> {{ form_widget(DiffusionOffre.fourchette_remuneration_offre,{ 'attr': {'value': offre.getOffFourchetteRemunerationIdFourchetteRemuneration() } }) }} </td>
			</tr>

			<tr>	
				<td class='label'><b>Poste accessible aux personnes<br>en situation de handicap</b><span class="ob"> *</span></td>
				<td class='label'> {{ form_widget(DiffusionOffre.handicap_offre[0], { 'attr': {'placeholder': '', 'checked': offre.getIsHandicap() == 1 } }) }} &nbsp; {{ form_label(DiffusionOffre.handicap_offre[0]) }} <br>
								{{ form_widget(DiffusionOffre.handicap_offre[1], { 'attr': {'placeholder': '', 'checked': offre.getIsHandicap() == 0 } }) }} &nbsp; {{ form_label(DiffusionOffre.handicap_offre[1]) }}</td>
			</tr>

			<tr>
				<td class='label'><b>Anonymat</b></td>
				<td class='label'>  {{ form_widget(DiffusionOffre.anonymat_offre[0], { 'attr': {'placeholder': ''} }) }} &nbsp; {{ form_label(DiffusionOffre.anonymat_offre[0]) }}
			</tr>

			<tr id="rep_mail">
				<td class='label'>{{ form_label(DiffusionOffre.reponse_mail_offre,"Réponse par mail") }} </td>
				<td id="reponse_mail"> {{ form_widget(DiffusionOffre.reponse_mail_offre,{ 'attr': {'value': offre.getRepMail() } }) }} </td>
			</tr>

			<tr id="rep_site">
				<td class='label'> {{ form_label(DiffusionOffre.reponse_site_offre,"Réponse sur le site") }} </td>
				<td id="reponse_site"> {{ form_widget(DiffusionOffre.reponse_site_offre, { 'attr': {'value': offre.getRepUrl() } }) }} </td>
				<div id="res_site" value="{{ DiffusionOffre.reponse_site_offre.vars.value }}" >
			</tr>
		</table>
	</div>

	{% include '/directemploi/espace_recruteur/info_reprise.html.twig' %} {# 2 - #}

	<div class="form_box">   
	<h2>3 - Fonction / secteur et localisation géographique</h2>               
		<table class="formulaire">
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.secteur_activite, "Fonction / Domaine du poste à pourvoir")}} <span class="ob"> *</span></td>
				<td> {{ form_widget(DiffusionOffre.secteur_activite,{ 'attr': {'placeholder': "Fonction / Domaine du poste à pourvoir"} })}} </td>
			</tr>
			{# <tr id="ss_sct">
				<td class='label'>{{ form_label(DiffusionOffre.sous_secteur_offre, "Domaine précis du poste à pourvoir")}} </td>
				<td> {{ form_widget(DiffusionOffre.sous_secteur_offre,{ 'attr': {'placeholder': "Domaine précis du poste à pourvoir"} })}} </td>
			</tr> #}
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.region_offre, "Region")}} <span class="ob"> *</span></td>
				<td> {{ form_widget(DiffusionOffre.region_offre,{ 'attr': {'value': offer_datas.id_region} })}} </td>
			</tr>
			<tr id="dept">
				<td class='label'>{{ form_label(DiffusionOffre.dept_offre, "Département")}} </td>
				<td> {{ form_widget(DiffusionOffre.dept_offre,{ 'attr': {'value': offer_datas.id_departement, 'required': 'required'} })}} </td>
			</tr>
			<tr id="villes">
				<td class='label'>{{ form_label(DiffusionOffre.villes_offre, "Ville")}} </td>
				<td> {{ form_widget(DiffusionOffre.villes_offre,{ 'attr': {'value': offre.getGeoVille() } })}} </td>
			</tr>
		</table>
	</div>
	<div class="form_box">
	<h2>4 - Profil du poste et descriptif</h2>
		<table class="formulaire">
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.descriptif_offre, "Descriptif de l'offre")}} <span class="ob"> *</span></td>
				<td> {{ form_widget(DiffusionOffre.descriptif_offre)}} </td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.type_formation_offre, "Type de formation")}} <span class="ob"> *</span></td>
				<td> {{ form_widget(DiffusionOffre.type_formation_offre,{ 'attr': {'value': offer_datas.id_type_formation} })}} </td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.exp_requise_offre, "Epérience requise")}} <span class="ob"> *</span></td>
				<td> {{ form_widget(DiffusionOffre.exp_requise_offre,{ 'attr': {'placeholder': "Epérience requise"}})}} </td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(DiffusionOffre.profil_offre, "Profil")}} </td>
				<td> {{ form_widget(DiffusionOffre.profil_offre)}} </td>
			</tr>
		</table>
	</div>
	<div class="act">
		<button type="submit" class="btn" id="btn_submit" >Valider et continuer</button>
	</div>

{{ form_end(DiffusionOffre) }}

<script language="javascript">

	let domaine = document.querySelector("#diffusion_offre_secteur_activite");
	let region = document.querySelector("#diffusion_offre_region_offre");
	let deps = document.querySelector("#diffusion_offre_dept_offre");
	let rep_mail = document.querySelector("#diffusion_offre_reponse_mail_offre");
	let rep_site = document.querySelector("#diffusion_offre_reponse_site_offre");

    window.onload = () => {
		var whoami = window.location.href.split("/espaceRecruteur/")[1].split("/")[0];
		console.log(whoami);
		
		// MAKE DEPARTEMENT REQUIRED FALSE
		var depField = document.querySelector("#diffusion_offre_dept_offre");
    	depField.removeAttribute('required');


		["change"].forEach((event) => {
			region.addEventListener(event, function () {
				let form = this.closest("form");
				let data = this.name + "=" + this.value;

				fetch(form.action, {
					method: form.getAttribute("method"),
					body: data,
					headers: {
						"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
					}
				}).then(response => response.text()).then(html => {
					let content = document.createElement("html");
					content.innerHTML = html;
					let nouveauSelect = content.querySelector("#diffusion_offre_dept_offre");
					document.querySelector("#diffusion_offre_dept_offre").replaceWith(nouveauSelect);
				})
			});
   		})

		/*rep_mail.addEventListener("click", function () {
			document.getElementById("diffusion_offre_reponse_site_offre").value = null;
		});
		rep_site.addEventListener("click", function () {
			document.getElementById("diffusion_offre_reponse_mail_offre").value = null;
		});
		rep_mail.addEventListener("mouseover", function () {
			document.getElementById("diffusion_offre_reponse_site_offre").style = "color:red";
		});
		rep_site.addEventListener("mouseover", function () {
			document.getElementById("diffusion_offre_reponse_mail_offre").style = "color:red";
		});
		rep_mail.addEventListener("mouseout", function () {
			document.getElementById("diffusion_offre_reponse_site_offre").style = "color:none";
		});
		rep_site.addEventListener("mouseout", function () {
			document.getElementById("diffusion_offre_reponse_mail_offre").style = "color:none";
		});
		*/
		domaine.addEventListener("click", function () {
			showSelection(domaine, "diffusion_offre_sous_secteur_offre", "ss_sct")
			if (whoami == "editerUneOffre" || whoami == "dupliquerUneOffre")
				preloadSelectiveFields('{{ offer_datas.id_sous_secteur_activite }}', 'diffusion_offre_sous_secteur_offre')

		});

		/*region.addEventListener("click", function () {
			showSelection(region, "diffusion_offre_dept_offre", "dept")
			showElement(document.getElementById("villes"));
			if (whoami == "editerUneOffre" || whoami == "dupliquerUneOffre")
				preloadSelectiveFields('{{ offer_datas.id_departement }}', 'diffusion_offre_dept_offre')
		});*/
		
		if (whoami == "editerUneOffre" || whoami == "dupliquerUneOffre") {
			console.log('la');
			//console.log('{{ offre.getIntitule() }}');
			document.querySelector("#diffusion_offre_titre_offre").value = '{{ offre.getIntitule() }}'
			document.querySelector("#diffusion_offre_reference_offre").value = '{{ offre.getReference() }}'
			
			//	start 	type contrat
			var contractForm = document.querySelector("#diffusion_offre_type_contrat_offre");
			var contractValue = '{{offre.getOffListeTypeContratIdTypeContrat()}}';
			Array.from(contractForm.options).forEach(function(option) {
				if (option.value == contractValue)
						option.setAttribute('selected', 'selected');    
			});//	end		type contrat

			//	start 	type secteur
			var formationForm = document.querySelector("#diffusion_offre_secteur_activite");
			var formationValue = '{{ offer_datas.id_secteur_activite }}';
			console.log("formation value = " + formationValue);
			Array.from(formationForm.options).forEach(function(option) {
				if (option.value == formationValue)
						option.setAttribute('selected', 'selected');    
			});//	end		secteur
			
			//	start 	type formation
			var formationForm = document.querySelector("#diffusion_offre_type_formation_offre");
			var formationValue = '{{ offer_datas.id_type_formation }}';
			Array.from(formationForm.options).forEach(function(option) {
				if (option.value == formationValue)
						option.setAttribute('selected', 'selected');    
			});//	end		type formation
			
			//	start 	type experience requise
			var expRequiseForm = document.querySelector("#diffusion_offre_exp_requise_offre");
			var expRequiseValue = '{{ offer_datas.id_exp_req }}';
			Array.from(expRequiseForm.options).forEach(function(option) {
				if (option.value == expRequiseValue)
						option.setAttribute('selected', 'selected');    
			});//	end		type experience requise


			document.querySelector("#diffusion_offre_duree_contrat_offre").value = '{{ offre.getDureeContrat() }}'
			document.querySelector("#diffusion_offre_date_debut_contrat_offre").value = '{{ offre.getDateDebut() }}'
			document.querySelector("#diffusion_offre_fourchette_remuneration_offre").value = '{{ offre.getOffFourchetteRemunerationIdFourchetteRemuneration() }}'
			document.querySelector("#diffusion_offre_reponse_mail_offre").value = '{{ offre.getRepMail() }}'
			document.querySelector("#diffusion_offre_reponse_site_offre").value = '{{ offre.getRepUrl() }}'
			document.querySelector("#diffusion_offre_villes_offre").value = '{{ offre.getGeoVille() }}'

			'{{ offre.getIsHandicap() }}' == 1 ? document.querySelector("#diffusion_offre_handicap_offre_0").checked = true : 0;
			'{{ offre.getIsHandicap() }}' == 0 ? document.querySelector("#diffusion_offre_handicap_offre_1").checked = true : 0;
			'{{ offre.getIsAnonym() }}' == 1 ? document.querySelector("#diffusion_offre_anonymat_offre_0").checked = true : 0;

			// AFFICHER ANCIENNES SELECTIONS
			//preloadSelectiveFields('{{ offer_datas.id_secteur_activite }}', 'diffusion_offre_secteur_activite')
			preloadSelectiveFields('{{ offer_datas.id_region }}', 'diffusion_offre_region_offre')
			preloadSelectiveFields('{{ offer_datas.id_departement }}', 'diffusion_offre_dept_offre')
			//preloadSelectiveFields('{{ offer_datas.id_type_formation }}', 'diffusion_offre_type_formation_offre')
			//preloadSelectiveFields('{{ offer_datas.id_exp_req }}', 'diffusion_offre_exp_requise_offre')

			document.querySelector("#diffusion_offre_descriptif_offre").value = "{{maMission}}";
			document.querySelector("#diffusion_offre_profil_offre").value = "{{monProfil}}";

			showSelection(domaine, "diffusion_offre_sous_secteur_offre", "ss_sct", '{{ offer_datas.id_sous_secteur_activite }}')
			showSelection(region, "diffusion_offre_dept_offre", "dept", '{{ offer_datas.id_departement }}')
			showElement(document.getElementById("villes"));
			
		}
	}
	
	window.addEventListener("load", function() {
		region.dispatchEvent(new Event('change'));
		let deps = document.querySelector("#diffusion_offre_dept_offre");
		var departementValue = '{{ offer_datas.id_departement }}';
		console.log(departementValue)
		Array.from(deps.options).forEach(function(option) {
			if (option.value == departementValue)
					option.setAttribute('selected', 'selected');    
		});//	end		

	});

	function preloadSelectiveFields(data, divname) {
		data = data.split("_");
		for (let i in document.querySelector("#" + divname).options) {
			if (data.includes(document.querySelector("#" + divname).options[i].value))
				document.querySelector("#" + divname).options[i].selected = true;
		}
	}
	
	const hideElement = function(Elem2Hide) {
		Elem2Hide.hidden = true;
	}

	const showElement = function(Elem2Show) {
		Elem2Show.hidden = false;
	}

	hideElement(document.getElementById("ss_sct"));
	hideElement(document.getElementById("dept"));
	hideElement(document.getElementById("villes"));

	const showSelection = function(listener, divname2show, divname2showid, preload=null) {
		var whoami = window.location.href.split("/espaceRecruteur/")[1].split("/")[0];
		let form = listener.closest("form");
		let data = listener.name + "=" + listener.options[listener.selectedIndex].value;

		fetch(form.action, {
			method: form.getAttribute("method"),
			body: data,
			headers: {
				"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
			}
		}).then(response => response.text()).then(html => {
			let content = document.createElement("html");
			content.innerHTML = html;
			let nouveauSelect = content.querySelector("#" + divname2show);
			document.querySelector("#" + divname2show).replaceWith(nouveauSelect);
			let div = document.getElementById(divname2show);
			if (div.options.length > 1) {
				showElement(document.getElementById(divname2showid));
				if (whoami == "editerUneOffre" || whoami == "dupliquerUneOffre")
					preloadSelectiveFields(preload, divname2show)
			} else
				hideElement(document.getElementById(divname2showid));
		})
	};

</script>

{% endblock %}