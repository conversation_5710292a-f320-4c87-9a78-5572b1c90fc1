{% extends 'directemploi/layout.html.twig' %}

{% block title %}Votre espace recruteur | Direct Emploi{% endblock %}

{% block stylesheets %}
	<link rel="stylesheet" href="{{ asset('assets/css/espace_recrut.min.css') }}">
	<link rel="stylesheet" href="{{ asset('assets/css/espace_recrut_cvt.min.css') }}">
{% endblock %}

{% block content %}
<h1>Mon compte</h1>

	<div id="fil_ariane">
		<a href="/" title="Accueil">Accueil</a> -
		<a href="/espaceRecruteur" title="Recruteur">Recruteur</a>
	</div>

{% set page = app.request.get('page') %}


{% if cvtready %}
{% set cvtactive = "active" %}
{% set mon_compte = "" %}
{% set diffu = "" %}
{% elseif diffu %}
{% set cvtactive = "" %}
{% set mon_compte = "" %}
{% set diffu = "active" %}
{% else %}
{% set mon_compte = "active" %}
{% set cvtactive = "" %}
{% set diffu = "" %}

{% endif %}

<div class="onglets">
    <a id="onglet_mon_compte" class="{{mon_compte}}" onclick="showOffer('mon_compte', 'onglet_mon_compte');">Mon compte</a>
    <a id="onglet_offres" class="{{diffu}}" onclick="showOffer('gestion_offres', 'onglet_offres')">Gestion des offres</a>							
    <a id="onglet_candidatures" class="" onclick="showOffer('gestion_candidatures', 'onglet_candidatures');">Gestion des candidatures</a>
    <a id="onglet_CVthèque" class="{{ cvtactive }}" onclick="showOffer('CVthèque', 'onglet_CVthèque');">CVthèque</a>
</div>

<div id="mon_compte" class="contener" style="padding:0px">
	<div class="field2" id="tab_onglet_m1" style="display:block;">
		{% include '/directemploi/espace_recruteur/mon_compte.html.twig' %}
	</div>
</div>

<div id="gestion_offres" class="contener" style="padding:0px">
	<div class="field2" id="tab_onglet_m2" style="display:block;">
		{% include '/directemploi/espace_recruteur/gestion_offres.html.twig' %}
	</div>
</div>

<div id="gestion_candidatures" class="contener" style="padding:0px">
	<div class="field2" id="tab_onglet_m3" style="display:block;">
		{% include '/directemploi/espace_recruteur/candidatures_gestion.html.twig' %}
	</div>
</div>

<div id="CVthèque" class="contener" style="padding:0px">
	<div class="field2" id="tab_onglet_m3" style="display:block;padding:27px">
		{% include '/directemploi/espace_recruteur/cvtheque.html.twig' %}
	</div>
</div>

<script type="text/javascript">

	window.onload = () => {
		var active = document.getElementsByClassName('active')[0].id;
		if (active == "onglet_mon_compte") {
			showOffer('mon_compte', 'onglet_mon_compte')
			console.log("mon_compte")
			}
		if (active == "onglet_offres") {
			showOffer('gestion_offres', 'onglet_offres')
			console.log("gestion_offres")
			}
		if (active == "onglet_candidatures") {
			showOffer('gestion_candidatures', 'onglet_candidatures')
			console.log("gestion_candidatures")
			}
		if (active == "onglet_CVthèque") {
			showOffer('CVthèque', 'onglet_CVthèque')
			console.log("CVthèque")
			}
	}
</script>

{% endblock %}
