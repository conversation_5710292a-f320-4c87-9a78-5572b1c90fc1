	<div class="box_gris chapo_compte clearfix">
	<div class="text" name="offres_stat" id="offres_stat_offres">
	    Bonjour<br/>Vous avez actuellement
		<span class="vert">{{ user_stats.getNbOffresEmploi() }} offres d'emploi</span>
		,
		<span class="vert">{{ user_stats.getNbOffresStage() }} offres de stage</span>
		et
		<span class="vert">{{ user_stats.getNbOffresAlternance() }} offres en alternance</span>
		publiées sur le site
	</div>
	<br>
	<div class="floatR">
		{% if manu != 1 %}
			Vos offres sont diffusées automatiquement
		{% else %}
			{% if (SouscriptionsCommercialesValides)|length > 0 %}
				<select name="offres_dispo" id="offres_dispo" style="margin:8px">
               		<option value="#">Diffuser une offre</label>
					{% for key, SCV in SouscriptionsCommercialesValides %}
						{% if SCV.com_liste_type_offre_commerciale_id_type_offre_commerciale != 4 %}
                   			<option value="{{ SCV.id_offre_commerciale }}" {% if SCV.id_offre_commerciale == 31 %} selected {% endif %}>{{ SCV.nom_offre }}</option>
						{% endif %}
					{% endfor %}
               	</select>
				<br><a class="submit sub_jaune sub_large" href="#" title="Diffuser une offre" onclick="goToDiffusion();">Diffuser une offre</a>
			{% endif %}
		{% endif %}
	</div>

</div>

<div id="rechercheOffres">
	<h2>Rechercher une offre d'emploi / stage / alternance</h2>
	<div id="moteur">
		<table width="100%" cellspacing="0" cellpadding="0" border="0" class="formulaire">
			<tr valign="top">
            	{{ form_start(RechercheOffre) }}
                    <td align="center" valign="middle"> Mot clé {{ form_widget(RechercheOffre.mot_cle,{ 'attr': {'placeholder': '', 'id': 'mc'} }) }} </td>
                    <td align="center" valign="middle"> Contrat {{ form_widget(RechercheOffre.type_offre,{ 'attr': {'placeholder': ''} }) }} </td>
					<td align="center" valign="middle"> Etat de l'offre {{ form_widget(RechercheOffre.etat_offre,{ 'attr': {'placeholder': ''} }) }} </td>
                    <td align="center" valign="middle"> Trier par : {{ form_widget(RechercheOffre.tri_offre,{ 'attr': {'placeholder': ''} }) }} </td>    
					<td align="center" colspan="3" valign="middle">
					<div class="act">
						<button class="ajax btn submit sub_jaune" id="offreSearch">Valider</button>
					</div>
					</td>
                {{ form_end(RechercheOffre) }}
			</tr>
		</table>
	</div>
	<div id="loading" style="display:none;">
		<p align="center">
		    <img src="/images/loading.gif" alt="loading img">
			Recherche en cours &hellip;
		</p>
	</div>
</div>

				{% include 'directemploi/espace_recruteur/off_diffu_tmp.html.twig' %}		
<table>
    <tr>
    	<td colspan="4" id="error_date_cv_download"> Vous pouvez télécharger vos Cvs dans la limite des 3 derniers mois</td></tr>
	<tr>
	<td style="width:45%">
		<h2>Liste des offres</h2>
	</td>
	<td>
		<button id="downloadCv" class="ajax btn submit sub_jaune sub_large" title="Télécharger les Cvs">Télécharger les Cvs</button>
	</td>

    <td style="width:10%;"> &nbsp; à partir du :</td>
	<td>
        &nbsp;<input type="date" id="date" min="{{ 'now' | dateThreeMonth }}" max="{{ 'now'|date("Y-m-d") }}">
    </td>

</table>

<iframe id="download" style="display:none;"></iframe>

{# {% if mssg is defined %} <div style="text-align:center" > <span id="mssg" style="border: 1px solid #4F805D;padding: 5px;color: white;background-color: #4F805D;"> {{ mssg }} </span></div> {% endif %} #}

<div id="ajax-reslits">
{% include '/directemploi/espace_recruteur/recherche_offre.html.twig' %}
</div>


<script language="javascript">

window.addEventListener('load', 
  function() { 
    setTimeout(displyMssg, 7000);
  }, false);
	

function displyMssg() {
	document.getElementById('mssg').style.display = "none";
}



	let date = document.querySelector("#date");
	let download = document.getElementById("download_button");
	let today = new Date();

 	const goToDiffusion = function() {
		let selected = document.getElementById("offres_dispo").value;
		if (selected != "#")
			location.href = "/espaceRecruteur/diffuser/?id_off_co=" + selected;
	}

	const sendDownloadForm = function(event, value) {
		var year = value.toLocaleString("default", { year: "numeric" });
		var month = value.toLocaleString("default", { month: "2-digit" });
		var day = value.toLocaleString("default", { day: "2-digit" });

		var formattedDate = day + "-" + month + "-" + year;
		var datas = {
			"dateDownload" : formattedDate,
		};
		$.ajax({
			type: "POST",
			url: "espaceRecruteur",
			data: datas,
		}).done(function (data) {
			//download files
		});
		event.preventDefault();
	};

	window.addEventListener("load", function(event) {
		error = document.getElementById("error_date_cv_download");
    	hideElement(error);
	});
</script>

<script>
	$(document).on('click', 'button.ajax', function () {
		that = $(this);
		var app_route = $(this).attr("id");
		$.ajax({
			url: 'espaceRecruteur/' + app_route,
			type: "POST",
			dataType: "json",
			data: {
				"onclick": "true",
				"date": document.getElementById("date").value,
				"mc": document.getElementById("recherche_offre_mot_cle").value,
				"type_offre": document.getElementById("recherche_offre_type_offre").value,
				"tri_offre": document.getElementById("recherche_offre_tri_offre").value,
				"etat_offre": document.getElementById("recherche_offre_etat_offre").value
			},
			async: true,
			success: function (data) {

				if(app_route  == 'downloadCv'){
					if(Object.entries(data).length !== 0){
						location.href = data;
					} else {
						alert('Aucun Cv reçu pour la période sélectionnée');
					}
				} else {
					document.getElementById("ajax-reslits").innerHTML = data.output;
				}
				
			}
		});
	return false;
});

</script>
