{% extends 'carriereDistribution/base.html.twig' %}

{% set title = "Réinitialisation de votre mot de passe | Carriere BTP" %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}

{% set description_seo = "Pour réinitialiser votre mot de passe, suivez simplement le lien fourni et entrez vos nouvelles informations de connexion. Cette procédure rapide et sécurisée garantit que votre accès à notre plateforme reste protégé et confidentiel. Carriere BTP" %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
<div class="is-sticky">

{% block head %}
        {% include 'carriereDistribution/header.html.twig' %}
    {% endblock %}
    <section class="password-header d-flex justify-content-center align-items-center mb-0" id="">
        <h1 class="text-center text-uppercase mb-0" style="font-size: 1.25rem; line-height: 1.5;" ><span style="color:#e2971b"> réinitialisation    </span>de votre mot de passe</h1>
    </section>
    <section class="login-form">
        {% for flash_error in app.flashes('reset_password_error') %}
            <div class="alert alert-danger" role="alert">{{ flash_error }}</div>
        {% endfor %}

        <div class="container">
            {{ form_start(requestForm) }}
                <div class="info-section">

                    <h6>Rappels de vos coordonnées</h6>
                    <hr>
                    <div class="row">
                        <div class="col-lg-12 col-sm-12">
                            <div class="form-group">
                                {{ form_row(requestForm.email) }}
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <button type="submit" class="btn mb-2 px-4 button-black">Renvoyez-moi mon mot de de passe</button>
                    </div>
                </div>
            {{ form_end(requestForm) }}
        </div>
    </section>
</div>
    {% block footer %}
        {% include 'carriereDistribution/footer.html.twig' %}
    {% endblock %}
{% endblock %}
