{% extends 'carriereDistribution/base.html.twig' %}

{% set lettre = app.request.query.get('lettre') %}
{% set page = app.request.query.get('page') %}


    {% set title_seo = "Emploi commerce et distribution - Carrière Distribution| Carriere Distribution" %}
    {% set description_seo = "sur Carrière Distribution, recherchez toutes les annonces d'emploi : CDD, CDI, alternance, stage, intérim..."  %}










{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .alphabet-filter {
            text-align: center;
            padding: 10px;
            margin: 10px;
        }

        .alphabet-filter a {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border: 0.5px solid #ddd; /* Couleur de la bordure */
            border-radius: 4px; /* Coins arrondis */
            text-decoration: none; /* Supprime le soulignement des liens */
            color: #333; /* Couleur du texte */
            font-weight: bold; /* Rend le texte en gras */
            transition: background-color 0.3s, color 0.3s; /* Animation au survol */
        }

        .alphabet-filter a:hover {
            background-color: #faaf40; /* Couleur de fond au survol */
            color: #fff; /* Couleur du texte au survol */
        }

    </style>
<div class="is-sticky">
    {% block head %}
        {% include 'carriereDistribution/header.html.twig' %}
    {% endblock %}
<section class="env-header d-flex flex-column align-items-center" id="">

    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.4;"> Les Métiers de bouche</h1>
</section>

    <section class="section-embauche env">


        <div class="container">
            <div class="row">
                {% for metier in metiers %}
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <a href="{{ '/Dis/lists-offre/?q=' ~ metier }}" target="_blank">
                            <div class="w-100 info-button text-center">
                                <h6>{{ metier }}</h6>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            <!-- Pagination Personnalisée -->

        </div>
    </section>

    {% block footer %}
        {% include 'carriereDistribution/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}