{% extends 'carriereDistribution/base.html.twig' %}
{% block title %}Accueil{% endblock %}

{% block body %}
    <div class="is-sticky">
        {% block head %}

            {% include 'carriereDistribution/header.html.twig' %}
        {% endblock %}
        <section class="entreprises-header" id="">
            <h1 class="text-center text-uppercase text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#e2971b">ENTRE</span>PRISES  </h1>
            <div class="header-text">
                <div class="container">
                    <p class="text-white justified">Carrière Restauration est un site emploi spécialisé pour la recherche d’un emploi mais aussi d’un stage ou d’une alternance exclusivement dans le domaine du restauration (Bâtiment, travaux publics, construction, environnement, etc ...).
                    </p>
                    <p class="text-white justified">Gr<PERSON><PERSON> à notre classement par ordre alphabétique, vous pouvez retrouver directement les entreprises qui vous intéressent plus particulièrement. Les postes proposées concernent plus de 200 métiers distincts. Les experts du recrutement, cabinets de recrutement spécialisés et groupes d’intérim sont également très actifs notamment, pour ces derniers, les missions en intérim.</p>
                </div>
            </div>
        </section>
        <section class=" entreprises-carousel-section">
            <div class="container">
                <div class="row">
                    <div class="col-9">
                        <h5 class="mb-3">Les entreprises du restauration se présentent</h5>
                    </div>
                    <div class="col-3 ">
                        <div style="float: right;">
                            <a class="btn btn-primary mb-3 mr-1" data-bs-target="#btpCarousel" role="button" data-bs-slide="prev">
                                <i class="fa fa-arrow-left" style="color: #fff;"></i>
                            </a>
                            <a class="btn btn-primary mb-3 " data-bs-target="#btpCarousel" role="button" data-bs-slide="next">
                                <i class="fa fa-arrow-right" style="color: #fff;"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-12">
                        <div id="btpCarousel" class="carousel slide" data-bs-ride="carousel">
                            <div class="carousel-inner">
                                {% for entreprise in entreprise_btp %}
                                    {% if loop.index0 % 3 == 0 %}
                                        <div class="carousel-item {% if loop.first %}active{% endif %}">
                                            <div class="row">
                                    {% endif %}
                                                <div class="col-md-4 mb-3">
                                                    <div class="card">
                                                        <img class="img-fluid" alt="Logo de {{ entreprise.raison_sociale }}" src="{{ asset('uploads/logos/' ~ entreprise.logo) }}">
                                                        <div class="card-body">
                                                            <div class="text-center">
                                                                <button aria-label="Voir l'entreprise" class="btn button-black"><a href="{{ path('app_Res_entreprisesDetails', {'id': entreprise.id}) }}">Voir l'entreprise</a></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                    {% if loop.index0 % 3 == 2 or loop.last %}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="entreprises-carousel-section">
            <div class="container">
                <div class="row">
                    <div class="col-9">
                        <h5 class="mb-3">Les groupes d'interim se présentent</h5>
                    </div>
                    <div class="col-3">
                        <div style="float: right;">
                            <a class="btn btn-primary mb-3 mr-1" data-bs-target="#interimCarousel" role="button" data-bs-slide="prev">
                                <i class="fa fa-arrow-left" style="color: #fff;"></i>
                            </a>
                            <a class="btn btn-primary mb-3 " data-bs-target="#interimCarousel" role="button" data-bs-slide="next">
                                <i class="fa fa-arrow-right" style="color: #fff;"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-12">
                        <div id="interimCarousel" class="carousel slide" data-bs-ride="carousel">
                            <div class="carousel-inner">
                                {% for entreprise in entreprise_interim %}
                                    {% if loop.index0 % 3 == 0 %}
                                        <div class="carousel-item {% if loop.first %}active{% endif %}">
                                            <div class="row">
                                    {% endif %}
                                                <div class="col-md-4 mb-3">
                                                    <div class="card">
                                                        <img class="img-fluid" alt="Logo de {{ entreprise.raison_sociale }}" src="{{ asset('uploads/logos/' ~ entreprise.logo) }}">
                                                        <div class="card-body">
                                                            <div class="text-center">
                                                                <button aria-label="Voir l'entreprise" class="btn button-black"><a href="{{ path('app_Res_entreprisesDetails', {'id': entreprise.id}) }}">Voir l'entreprise</a></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                    {% if loop.index0 % 3 == 2 or loop.last %}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        
        <section class="entreprises-carousel-section">
            <div class="container">
                <div class="row">
                    <div class="col-9">
                        <h5 class="mb-3">Les cabinets de recrutement se présentent</h5>
                    </div>
                    <div class="col-3 ">
                        <div style="float: right;">
                            <a class="btn btn-primary mb-3 mr-1" data-bs-target="#recruCarousel" role="button" data-bs-slide="prev">
                                <i class="fa fa-arrow-left" style="color: #fff;"></i>
                            </a>
                            <a class="btn btn-primary mb-3 " data-bs-target="#recruCarousel" role="button" data-bs-slide="next">
                                <i class="fa fa-arrow-right" style="color: #fff;"></i>
                            </a>
                        </div>
                    </div>
                    <div class="col-12">
                        <div id="recruCarousel" class="carousel slide" data-bs-ride="carousel">
                            <div class="carousel-inner">
                                {% for entreprise in cabinet_recrutement %}
                                    {% if loop.index0 % 3 == 0 %}
                                        <div class="carousel-item {% if loop.first %}active{% endif %}">
                                            <div class="row">
                                    {% endif %}
                                                <div class="col-md-4 mb-3">
                                                    <div class="card">
                                                        <img class="img-fluid" alt="Logo de {{ entreprise.raison_sociale }}" src="{{ asset('uploads/logos/' ~ entreprise.logo) }}">
                                                        <div class="card-body">
                                                            <div class="text-center">
                                                                <button aria-label="Voir l'entreprise" class="btn button-black"><a href="{{ path('app_Res_entreprisesDetails', {'id': entreprise.id}) }}">Voir l'entreprise</a></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                    {% if loop.index0 % 3 == 2 or loop.last %}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    {% block footer %}
        {% include 'carriereDistribution/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}