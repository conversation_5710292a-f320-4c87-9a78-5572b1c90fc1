{# {% extends 'directemploi/new_layout.html.twig' %} #}
{% extends 'carriereDistribution/base.html.twig' %}

{% set lettre = app.request.query.get('lettre') %}
{% set page = app.request.query.get('page') %}
{% if lettre and page %}
    {% set title_seo = "Annuaire des entreprises actives qui recrutent sur Carrière Distribution | pour les entreprises qui commencent par la lettre '" ~ lettre ~ "' . Page : " ~ page %}
    {% set description_seo = "Ils recrutent sur Carrière Distribution. Trouvez les entreprises qui recrutent actuellement et qui commencent par la lettre '" ~ lettre ~ "', ainsi que toutes leurs offres d'emploi actualisées et classées." %}
{% elseif lettre and page is null %}
    {% set title_seo = "Annuaire des entreprises actives qui recrutent sur Carrière Distribution | pour les entreprises qui commencent par la lettre '" ~ lettre ~ "'" %}
    {% set description_seo = "Ils recrutent sur Carrière Distribution. Trouvez les entreprises qui recrutent actuellement et qui commencent par la lettre '" ~ lettre ~ "', ainsi que toutes leurs offres d'emploi actualisées et classées." %}
{% elseif lettre is null and page %}
    {% set title_seo = "Annuaire des entreprises actives qui recrutent sur Carrière Distribution | Page : '" ~ page ~ "'" %}
    {% set description_seo = "Ils recrutent sur Carrière Distribution. Trouvez les entreprises qui recrutent actuellement dans la page : '" ~ page ~ "', ainsi que toutes leurs offres d'emploi actualisées et classées." %}
{% else %}
    {% set title_seo = "Annuaire des entreprises actives qui recrutent sur Carrière Distribution | pour toutes entreprises" %}
    {% set description_seo = "Ils recrutent sur Carriere Distribution. Trouvez les entreprises qui recrutent actuellement et toutes leurs offres d'emploi actualisées et classées." %}
{% endif %}


{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %} 

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/new_entreprises.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_responsive.min.css') }}">
    <style>
        .bg-image {
            position: relative;
            background-size: cover;
            background-position: center;
            height: 200px;
        }
        .overlay {
            background-color: rgba(0, 0, 0, 0.5);
            height: 100%;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .card:hover {
            transform: translateY(-5px);
            transition: transform 0.3s ease;
        }
        .alphabet-scroll-container {
            overflow-x: auto;
            white-space: nowrap;
            padding: 0.5rem 0;
        }

        .ul_annuaire_entreprises {
            gap: 0.4rem;
        }

        .alphabet-link {
            display: inline-block;
            padding: 0.4rem 0.75rem;
            border: 1px solid #ccc;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
            font-weight: 500;
            background-color: #fff;
            transition: all 0.2s ease;
        }

        .alphabet-link:hover {
            background-color: #FAAF40;
            color: white;
            border-color: #FAAF40;
        }
    </style>
{% endblock %}

{% block body %}
<div class="is-sticky">
    {% block head %}
        {% include 'carriereDistribution/header.html.twig' %}
        {% if canonical is defined %}
        <link rel="canonical" href="{{ path('app_Dis_entreprises', {'lettre': lettre, 'page': page ?: 1}) }}" />
        {% endif %}
        {% if page > 1 %}
            <link rel="prev" href="{{ path('app_Dis_entreprises', {'lettre': lettre, 'page': page - 1}) }}" />
        {% endif %}
        {% if page < nombre_total_pages %}
            <link rel="next" href="{{ path('app_Dis_entreprises', {'lettre': lettre, 'page': page + 1}) }}" />
        {% endif %}
    {% endblock %}
    <section class="entreprises-header" id="">
        <h5 class="text-center text-uppercase text-white"><span style="color:#C10037">ENTRE</span>PRISES  </h5>
        <div class="header-text">
            <div class="container">
                <p class="text-white justified">
                    Carrière Distribution est un site emploi spécialisé pour la recherche d’un emploi mais aussi d’un stage ou d’une alternance exclusivement dans le domaine de la Distribution (hôtellerie, Distribution, services de traiteur, etc ...). Retrouvez dans notre rubrique « Entreprises », les sociétés qui diffusent leurs offres sur Carrière Distribution.
                </p>
                <p class="text-white justified">Grâce à notre classement par ordre alphabétique, vous pouvez retrouver directement les entreprises qui vous intéressent plus particulièrement. Les postes proposées concernent plus de 200 métiers distincts. Les experts du recrutement, cabinets de recrutement spécialisés et groupes d’intérim sont également très actifs notamment, pour ces derniers, les missions en intérim.</p>
            </div>
        </div>
    </section>
    <style>
        .alphabet-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border: 1px solid #ccc;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            color: #333;
            transition: all 0.2s ease-in-out;
        }

        .alphabet-link:hover {
            background-color: #f1f1f1;
        }

        .alphabet-link.active {
            font-weight: bold;
            background-color: #e2971b;
            color: white;
            border-color: #e2971b;
        }

        .ul_annuaire_entreprises li {
            margin: 0 4px;
        }
    </style>

    <section>
        <div class="container liste_entreprises py-4">
            {% if breadcrumb is defined %}
                {% include '/_partials/_breadcrumb.html.twig' %}
            {% endif %}

            <h1 class="text-center" style="font-size:28px;">Annuaire des entreprises</h1>
            <hr>

            <!-- Navigation alphabétique -->
            <!-- Barre alphabétique propre et fluide -->
            <div class="annuaire_entreprises text-center my-3">
                <div class="alphabet-scroll-container">
                    <ul class="ul_annuaire_entreprises d-flex flex-nowrap justify-content-center list-unstyled m-0 p-0">
                        {% for lettre in listeLettresAlpha %}
                            {% if lettre != '0' %}
                                <li class="mx-1">
                                    <a href="{{ path('app_Dis_entreprises', {lettre: lettre}) }}" class="alphabet-link">
                                        {{ lettre }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                    </ul>
                </div>
            </div>


            <!-- Grille d'entreprises -->
            <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
                {% for entreprise in entreprises %}
                    <div class="col">
                        <div class="card border-0 shadow-sm h-100 position-relative overflow-hidden">
                            {% set bg = entreprise['logo_fond']
                                ? '/uploads/logos_fond/' ~ entreprise['logo_fond']
                                : '/images/imagesfond' ~ random(1, 12) ~ '.jpg' %}

                            <div class="bg-image" style="
                                    background-image: url('{{ asset(bg) }}');
                                    background-size: cover;
                                    background-position: center;
                                    height: 200px;
                                    position: relative;">
                                <div class="overlay d-flex justify-content-center align-items-center" style="
            background-color: rgba(0, 0, 0, 0.5);
            height: 100%;
            width: 100%;">
                                    {% if entreprise['logo'] %}
                                        <img src="{{ asset('/uploads/logos/' ~ entreprise['logo']) }}"
                                             alt="{{ entreprise['raison_sociale'] }}"
                                             style="max-height: 70px; max-width: 90%; object-fit: contain;">
                                    {% endif %}
                                </div>
                            </div>

                            <div class="card-body text-center bg-dark">
                                <a href="{{ path('app_Dis_entreprisesDetails', {id: entreprise['id_entreprise'], slug: entreprise['raison_sociale'] | raison }) }}"
                                   class="stretched-link text-decoration-none">
                                    <h5 class="card-title text-white mb-0 py-2" style="font-size: 1.1rem;">
                                        {{ entreprise['raison_sociale'] }}
                                    </h5>
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            <div class="mt-4 text-center">
                {{ knp_pagination_render(entreprises, 'directemploi/composants/paginationSliderTemplate.html.twig') }}
            </div>
        </div>
    </section>
    {% block footer %}
        {% include 'carriereDistribution/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}