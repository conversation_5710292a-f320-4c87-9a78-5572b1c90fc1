<style>
    /* Styles de base pour le conteneur d'autocomplétion */
    .autocomplete {
        position: relative;
        display: inline-block;
        width: 100%;
    }

    /* Style pour le champ de saisie */
    .autocomplete input {
        width: 100%;
        border: 1px solid #d4d4d4;
        background-color: #fff;
        font-size: 16px;
    }

    input[type=text]:focus {
        background: transparent;
        outline: none;
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Style pour la liste d'autocomplétion */
    .autocomplete-items {
        position: absolute;
        border: 1px solid #d4d4d4;
        border-bottom: none;
        border-top: none;
        z-index: 99999 !important; /* Z-index très élevé pour éviter tout conflit */
        background-color: #fff;
        left: 0;
        right: 0;
        top: 100%;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 0 0 4px 4px;
        /* Assurer que la liste reste visible même si le parent a overflow:hidden */
        overflow: visible !important;
    }

    /* Style pour chaque élément de la liste */
    .autocomplete-items div {
        padding: 12px 15px;
        cursor: pointer;
        background-color: #fff;
        border-bottom: 1px solid #e9e9e9;
        text-align: left;
        font-size: 14px;
        color: #333;
        transition: background-color 0.2s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Style pour le survol d'un élément */
    .autocomplete-items div:hover {
        background-color: #f1f8ff;
    }

    /* Style pour l'élément actif lors de la navigation au clavier */
    .autocomplete-active {
        background-color: #1e90ff !important;
        color: #fff !important;
    }

    /* Effet de mise en évidence du texte correspondant à la recherche */
    .autocomplete-items div strong {
        color: #C10037;
        font-weight: 600;
    }

    .autocomplete-active strong {
        color: #fff !important;
    }

    /* Assurer que les conteneurs parents ne masquent pas la liste */
    .form-item {
        position: relative;
        overflow: visible !important;
    }

    .job-form {
        overflow: visible !important;
    }

    .job-search-card {
        overflow: visible !important;
    }

    /* Style pour les petits écrans */
    @media (max-width: 768px) {
        .autocomplete-items {
            position: fixed;
            top: auto;
            left: 10px;
            right: 10px;
            width: calc(100% - 20px);
            max-height: 50vh;
        }
    }
</style>

<div class="form-padding">
    <form autocomplete="off" action="{{ path('app_Dis_candidat_search') }}" method="get">
        <div class="job-search-card">
            <div class="job-icon text-center">
                <i class="fa-solid fa-briefcase" style="color: #ffffff;font-size: 3rem;"></i>
            </div>
            <div class="job-form">
                <div class="form-item ">
                    {{ form_widget(searchForm.contrat, {'attr': { 'class':"form-select", 'name':"contrat", 'id':"contrat"}}) }}

                </div>

                <div class="form-item middle-form">
                    <div class="autocomplete" style="width:300px;">
                        <input id="mot_cle"  type="text"  name="q" class="form-control" placeholder="Métier, domaine, mots clés">
                    </div>
                </div>

                <div class="form-item">
                    <div class="autocomplete" style="width:300px;">
                        <input id="select_box" type="text" name="localisation" class="form-control" placeholder="Région, département, ville">
                    </div>
                    {{ form_widget(searchForm.localisation_field, {'value': ''}) }}
                </div>
            </div>
            <div class="text-center title-col job-button"  >
                <button aria-label="Recherche" type="submit" class="btn w-100 px-4 button-black">Recherche</button>
            </div>
        </div>
    </form>
</div>
<script>
    function normalizeString(str) {
        return str
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/-/g, " ")
            .toLowerCase();
    }
    // Function to fetch job names (metiers)
    async function fetchMetiers(key) {
        try {
            let response = await fetch(`/Dis/api/metier-search?key=${encodeURIComponent(key)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            let data = await response.json();
            return data;
        } catch (error) {
            console.error('Fetching metiers failed:', error);
            return [];
        }
    }

    // Autocomplete function for job names (metiers)
    function autocompleteMetier(inp) {
        var currentFocus;
        inp.addEventListener("input", async function (e) {
            var a, b, i, val = this.value.trim();
            closeAllLists();
            if (!val) {
                return false;
            }
            currentFocus = -1;

            let metiers = await fetchMetiers(val);

            a = document.createElement("DIV");
            a.setAttribute("id", this.id + "autocomplete-list");
            a.setAttribute("class", "autocomplete-items");
            this.parentNode.appendChild(a);

            for (i = 0; i < metiers.length; i++) {
                let name = metiers[i].libelle_metier;

                if (normalizeString(name).includes(normalizeString(val))) {

                    b = document.createElement("DIV");
                    b.innerHTML = "<strong>" + name.substr(0, val.length) + "</strong>";
                    b.innerHTML += name.substr(val.length);
                    b.innerHTML += "<input type='hidden' value='" + name + "'>";
                    b.addEventListener("click", function (e) {
                        inp.value = this.getElementsByTagName("input")[0].value;
                        closeAllLists();
                    });
                    a.appendChild(b);
                }
            }
        });

        inp.addEventListener("keydown", function (e) {
            var x = document.getElementById(this.id + "autocomplete-list");
            if (x) x = x.getElementsByTagName("div");
            if (e.keyCode == 40) { // Arrow down
                currentFocus++;
                addActive(x);
            } else if (e.keyCode == 38) { // Arrow up
                currentFocus--;
                addActive(x);
            } else if (e.keyCode == 13) { // Enter
                e.preventDefault();
                if (currentFocus > -1 && x) {
                    x[currentFocus].click();
                }
            }
        });

        function addActive(x) {
            if (!x) return false;
            removeActive(x);
            if (currentFocus >= x.length) currentFocus = 0;
            if (currentFocus < 0) currentFocus = (x.length - 1);
            x[currentFocus].classList.add("autocomplete-active");
        }

        function removeActive(x) {
            for (var i = 0; i < x.length; i++) {
                x[i].classList.remove("autocomplete-active");
            }
        }

        function closeAllLists(elmnt) {
            var x = document.getElementsByClassName("autocomplete-items");
            for (var i = 0; i < x.length; i++) {
                if (elmnt != x[i] && elmnt != inp) {
                    x[i].parentNode.removeChild(x[i]);
                }
            }
        }

        document.addEventListener("click", function (e) {
            closeAllLists(e.target);
        });
    }

    // Initialize autocomplete for job names (metiers)
    autocompleteMetier(document.getElementById("mot_cle"));
</script>

<script>
    function normalizeString(str) {
        return str
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/-/g, " ")
            .toLowerCase();
    }
    // Function to fetch locations
    async function fetchLocations(key) {
        try {
            let response = await fetch(`/Dis/api/location-search?key=${encodeURIComponent(key)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            let data = await response.json();
            return data;
        } catch (error) {
            console.error('Fetching locations failed:', error);
            return [];
        }
    }

    // Autocomplete function for locations
    function autocompleteLocation(inp) {
        var currentFocus;
        inp.addEventListener("input", async function (e) {
            var a, b, i, val = this.value.trim();
            closeAllLists();
            if (!val) {
                return false;
            }
            currentFocus = -1;

            let locations = await fetchLocations(val);

            a = document.createElement("DIV");
            a.setAttribute("id", this.id + "autocomplete-list");
            a.setAttribute("class", "autocomplete-items");
            this.parentNode.appendChild(a);

            for (i = 0; i < locations.length; i++) {
                let name = locations[i].libelle;
                let type = locations[i].type;
                let codeInsee = locations[i].code_insee || '';

                let displayText = name;
                if (type === 'departement' && codeInsee) {
                    displayText = `${name} - ${codeInsee}`;
                }

                // Recherche sur nom ou code insee
                if (
                    normalizeString(name).includes(normalizeString(val)) ||
                    normalizeString(codeInsee).includes(normalizeString(val))
                ) {
                    b = document.createElement("DIV");

                    let matchStart = normalizeString(displayText).indexOf(normalizeString(val));
                    if (matchStart !== -1) {
                        b.innerHTML =
                            displayText.substr(0, matchStart) +
                            "<strong>" +
                            displayText.substr(matchStart, val.length) +
                            "</strong>" +
                            displayText.substr(matchStart + val.length);
                    } else {
                        b.innerHTML = displayText;
                    }

                    // Stocke le vrai libellé sans le code insee
                    b.innerHTML += `<input type='hidden' value='${name}' data-type='${type}' data-insee='${codeInsee}'>`;

                    b.addEventListener("click", function (e) {
                        inp.value = this.getElementsByTagName("input")[0].value + (type === 'departement' && codeInsee ? ` - ${codeInsee}` : '');
                        let selectedType = this.getElementsByTagName("input")[0].getAttribute('data-type');
                        document.querySelector('[name="localisation_field"]').value = selectedType;
                        closeAllLists();
                    });

                    a.appendChild(b);
                }
            }
        });

        inp.addEventListener("keydown", function (e) {
            var x = document.getElementById(this.id + "autocomplete-list");
            if (x) x = x.getElementsByTagName("div");
            if (e.keyCode == 40) { // Arrow down
                currentFocus++;
                addActive(x);
            } else if (e.keyCode == 38) { // Arrow up
                currentFocus--;
                addActive(x);
            } else if (e.keyCode == 13) { // Enter
                e.preventDefault();
                if (currentFocus > -1 && x) {
                    x[currentFocus].click();
                }
            }
        });

        function addActive(x) {
            if (!x) return false;
            removeActive(x);
            if (currentFocus >= x.length) currentFocus = 0;
            if (currentFocus < 0) currentFocus = (x.length - 1);
            x[currentFocus].classList.add("autocomplete-active");
        }

        function removeActive(x) {
            for (var i = 0; i < x.length; i++) {
                x[i].classList.remove("autocomplete-active");
            }
        }

        function closeAllLists(elmnt) {
            var x = document.getElementsByClassName("autocomplete-items");
            for (var i = 0; i < x.length; i++) {
                if (elmnt != x[i] && elmnt != inp) {
                    x[i].parentNode.removeChild(x[i]);
                }
            }
        }

        document.addEventListener("click", function (e) {
            closeAllLists(e.target);
        });
    }

    // Initialize autocomplete for locations
    autocompleteLocation(document.getElementById("select_box"));

    // Detect location type when the input changes
    document.getElementById("select_box").addEventListener("input", function () {
        detectLocationType(this.value);
    });

    async function detectLocationType(locationInput) {
        let input = locationInput.trim().toLowerCase();
        let locations = await fetchLocations(input);
        let locationField = document.querySelector('[name="localisation_field"]');

        let found = locations.find(loc => loc.libelle.toLowerCase() === input);

        if (found) {
            locationField.value = found.type; // Set the type (ville, département, région)
        } else {
            locationField.value = ""; // Clear if no match
        }

        updateFormAction();
    }

    function updateFormAction() {
        let form = document.querySelector("form");
        let locationType = document.querySelector('[name="localisation_field"]').value;

        let searchParams = new URLSearchParams(new FormData(form));
        if (locationType) {
            searchParams.set("localisation_field", locationType);
        }
        searchParams.set("page", "1");

        form.action = form.action.split("?")[0] + "?" + searchParams.toString();
    }
</script>



