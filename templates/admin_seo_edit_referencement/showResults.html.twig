<!-- templates/admin_seo_edit_referencement/show_results.html.twig -->

{% block content %}
    <h1>Résultats de la recherche SEO</h1>

    {% if results is not empty %}
        <table class="table">
            <thead>
                <tr>
                    <th scope="col">Url</th>
                    <th scope="col">Nom</th>
                    <th scope="col">Preposition</th>
                    <th scope="col">Code</th>
                    <th scope="col">Photo Path</th>
                    <th scope="col">Keywords</th>
                    <th scope="col">Paragraphe Titre</th>
                    <th scope="col">Paragraphe Contenu</th>
                    <th scope="col">Edit App Id App</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for result in results %}
                    <tr>
                        <td>{{ result.url | default('Non spécifié') }}</td>
                        <td>{{ result.nom | default('Non spécifié') }}</td>
                        <td>{{ result.preposition | default('Non spécifié') }}</td>
                        <td>{{ result.code | default('Non spécifié') }}</td>
                        <td>{{ result.photo_path | default('Non spécifié') }}</td>
                        <td>{{ result.keywords | default('Non spécifié') }}</td>
                        <td>{{ result.paragraphe_titre | default('Non spécifié') }}</td>
                        <td>{{ result.paragraphe_contenu | default('Non spécifié') }}</td>
                        <td>{{ result.editAppIdApp | default('Non spécifié') }}</td>
                        <td>
                            <a href="{{ path('app_admin_seo_edit_single', {'id_seo_edit_referencement': result.idSeoEditReferencement}) }}" class="btn btn-primary">Modifier</a>
                            <a href="{{ path('app_admin_seo_delete', {'id_seo_edit_referencement': result.idSeoEditReferencement}) }}" class="btn btn-danger" onclick="return confirm('Voulez-vous vraiment supprimer ?')">Supprimer</a>
                            <a href="{{ path('app_admin_seo_add') }}" class="btn btn-success">Ajouter</a>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
        <a href="{{ path('app_admin_seo_edit') }}" class="btn btn-secondary">Retour</a>
    {% else %}
        <p>Aucun résultat trouvé.</p>       
        <a href="{{ path('app_admin_seo_edit') }}" class="btn btn-secondary">Retour</a>
    {% endif %}
{% endblock %}
