<footer class="site-footer">
    <div class="text-center image-footer">
        <img class="img-fluid" alt="logo noir" src="{{ asset('DirectAlternance/Front/images/logo-black.png') }}"/>
    </div>
    <div class="container">
        <div class="footer-all-menus">

            <div class="d-flex flex-column align-items-left footer-menu">
                <a href="#" class="footer-menu-title">À propos</a>
                <a href="{{ path('app_AL_quisommenous') }}"  class="footer-menu-text">Qui sommes nous ?</a>
                <a href="{{ path('app_AL_informationlegal') }}" class="footer-menu-text">Informations légales</a>
                <a href="{{ path('app_AL_confidentialite') }}"  class="footer-menu-text"  >Politique de confidentialité</a>
                <a href="{{ path('app_AL_chartecookies') }}" class="footer-menu-text">Charte Cookies</a>
                <a href="{{ path('app_AL_plansite') }}" class="footer-menu-text">Plan du site</a>
                <a href="{{ path('app_AL_contact') }}"  class="footer-menu-text">Contact</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <a href="#" class="footer-menu-title">Recruteurs</a>
                <a href="{{ path('app_AL_societe') }}" class="footer-menu-text">La société</a>
                <a href="javascript:void(0);" onclick="showSweetAlert(this)"      class="footer-menu-text">S'inscrire</a>
                <a  href="javascript:void(0);" onclick="showSweetAlert(this)" class="footer-menu-text">Le réseau Direct Emploi</a>
                <a href="javascript:void(0);" onclick="showSweetAlert(this)" class="footer-menu-text">Les offres proposées</a>
                <a href="{{ path('app_AL_chartedurecrutement') }}" class="footer-menu-text">Charte du recrutement</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <a href="#" class="footer-menu-title">Candidats</a>
                <a href="{{ path('app_AL_register_candidat_e1') }}" class="footer-menu-text">S'inscrire</a>
                <a href="{{ path('app_AL_conseilsCV') }}"class="footer-menu-text">Conseils CV</a>
                <a href="{{ path('app_AL_conseilsmotivation') }}" class="footer-menu-text">Lettre de motivation</a>
                <a href="{{ path('app_AL_conseilsembauche') }}" class="footer-menu-text">Entretien d'embauche</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <p href="#" class="footer-menu-title">Emploi en alternance </p>
                <a href="{{ path('app_AL_Parcourirlesoffresdemploi_alternance') }}" class="footer-menu-text">Offres en alternance par métier</a>
                <a href="{{ path('app_AL_ParcourirlesoffresdemploiVille_alternance') }}" class="footer-menu-text">Offres en alternance par ville</a>
                <a href="{{ path('app_AL_entreprises') }}" class="footer-menu-text">Offres en alternance par entreprise</a>
                <a href="{{ path('app_AL_index_mot_cle_alternance') }}" class="footer-menu-text">Offres en alternance par mots clés</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <a href="#" class="footer-menu-title">Le réseau</a>
                <a href="https://www.directemploi.com/" class="footer-menu-text">Direct Emploi</a>
                <a href="https://www.directetudiant.com/DE/home" class="footer-menu-text">Direct Etudiant</a>
                <a href="https://www.carriere-btp.com/" class="footer-menu-text">Carrière BTP</a>
                <a href="https://www.carriere-distribution.com/" class="footer-menu-text">Carrière Distribution</a>
                <a href="https://www.carriere-industrie.com/" class="footer-menu-text">Carrière Industrie</a>
                <a href="https://www.carriere-informatique.com/" class="footer-menu-text">Carrière Informatique</a>
                <a href="https://www.carriere-medicale.com/" class="footer-menu-text">Carrière Médicale</a>
                <a href="https://www.carriere-restauration.com/" class="footer-menu-text">Carrière Restauration</a>
            </div>

        </div>
        <div class="social-media-links">
            <a href="#" ><i class="fa-brands fa-facebook-f fa-xl" ></i></a>
            <a href="#" ><i class="fa-brands fa-twitter fa-xl" ></i></a>
            <a href="#" ><i class="fa-brands fa-instagram fa-xl"></i></a>

        </div>
    </div>

    <div class="copyright-section">
        <p class="text-center copyright-text">© Direct Alternance 2000 - 2024</p>
    </div>
    <script>
        // Function to be executed when the <a> element is clicked
        function handleLinkClick(titleValue) {
            const url = `https://www.directalternance.com/AL/lists-offre/?contrat=&localisation=&localisation_field=&q=${titleValue}`;
            // Redirect to the constructed URL
            window.location.href = url;
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
    <script>
        function showSweetAlert(link) {
            const linkText = link.innerText || link.textContent;
            if (linkText==="Recruteurs")
            {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Alternance est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectAlternance/Front/images/logo-black.png')}}" alt="Direct Alternance Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/inscriptionRecruteur';
                });
            }else if (linkText==="Connexion")
            {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Alternance est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectAlternance/Front/images/logo-black.png')}}" alt="Direct Alternance Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/login';
                });
            }else if (linkText==="Création de compte")
            {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Alternance est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectAlternance/Front/images/logo-black.png')}}" alt="Direct Alternance Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/inscriptionRecruteur';
                });
            }
            else if (linkText === "S'inscrire") {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Alternance est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectAlternance/Front/images/logo-black.png') }}" alt="Direct Alternance Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/login';
                });
            } else if (linkText === "Le réseau Direct Emploi") {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Alternance est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectAlternance/Front/images/logo-black.png') }}" alt="Direct Alternance Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/lereseau';
                });
            } else if (linkText === "Les offres proposées") {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Alternance est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectAlternance/Front/images/logo-black.png') }}" alt="Direct Alternance Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/offresproposees';
                });
            }
        }
    </script>
</footer>