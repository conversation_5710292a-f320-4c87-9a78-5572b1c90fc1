{% extends 'DirectAlternance/base.html.twig' %}
{% set title_seo = "Recherche d'offres de stage, d'alternance, jobs étudiant sur Direct Alternance" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% set description_seo = "Recherche d'offre en alternance, contrat de professionnalisation, contrat d’apprentissage, stage en alternance, emploi en alternance sur Direct Alternance" %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block javascript %}
{% include 'carriereMedicale/json_ld/home.html.twig' %}
{% endblock %}
{% block body %}
    <div class="is-sticky">
    {% block head %}
        {% include 'DirectAlternance/header.html.twig' %}
    {% endblock %}

    <div class="spacer"></div>
    <div class="index-header d-flex justify-content-center align-items-center" id="">
        <h1 style="font-size: 1.25rem; line-height: 1.5;" class="text-center mb-0 text-white"><span style="color: #AACC4A;">Plus de {{ nb_offres}} </span>emplois en alternance sur toute la France </h1>
    </div>
    <div class="container">
        {% include 'DirectAlternance/accueil/recherche_elk_landingpage.html.twig' %}
        {% if path == '/AL/home/' %}
            {% set prefix = '/AL/lists-offre/' %}
        {% elseif path == "/theme/offres-stage" %}
            {% set prefix = '../lists-offre/stage-' %}
        {% elseif path == '/theme/offres-alternance' %}
            {% set prefix = '../lists-offre/alternance-' %}
        {% elseif path == "/theme/offre-emploi-jeune-diplome" %}
            {% set prefix = '../' %}
        {% elseif path == "/theme/offres-emploi-interim" %}
            {% set prefix = '../lists-offre/interim-' %}
        {% elseif path == "/theme/offres-emploi-cabinet-de-recrutement" %}
            {% set prefix = '../lists-offre/cdi-' %}
        {% else %}
            {% set prefix = '/AL/lists-offre/' %}
        {% endif %}
        <style>
            .single-logo
            {
                width: 60%;
                margin: auto;
                display: block;
            }
            .single-logo img
            {

            }
            .section-padding{
                padding:60px 0;
            }

            .owl-dots{
                text-align: center;
            }

            .owl-dot {
                display: inline-block;
                height: 15px !important;
                width: 15px !important;
                background-color: #222222 !important;
                opacity: 0.8;
                border-radius: 50%;
                margin: 0 5px;
            }

            .owl-dot.active {
                background-color: #AACC4A !important;
            }
        </style>
        <style>
            .offres-list ul {
                list-style: none;
                padding: 0;
                width: 23%;
            }

            .offres-list li {
                margin-bottom: 0.75em;
                line-height: 1.5;
            }

            .offres-list a {
                text-decoration: none;
                font-size: 0.8rem;
                color: #333;
                transition: color 0.3s ease;
            }

            .offres-list a:hover {
                color: #007bff;
                text-decoration: underline;
            }

            .offres-list i {
                margin-right: 6px;
                color: #AACC4A;
                font-size: 7px;
            }

            @media screen and (max-width: 768px) {
                .offres-list ul {
                    width: 48%;
                }
            }

            @media screen and (max-width: 480px) {
                .offres-list ul {
                    width: 100%;
                }
            }

            .titre-section {
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 1em;
                color: #222;
            }
        </style>
        <br>
        <section>
            <h6 class="text-center">Ils recrutent sur Direct Alternance</h6>
            <div class="brand-carousel section-padding owl-carousel">
                {% for boite in boites %}
                    <div class="single-logo">
                        <a href="{{ boite.url }}">
                            <img src="{{ asset("/uploads/" ~ boite.image) }}" alt="{{ boite.raison_sociale }}" class="img-fluid" style="width: 94px; height: 75px;" />
                        </a>
                    </div>
                {% endfor %}
            </div>
            <div class="text-center mt-4"><a href="{{ path('app_AL_entreprises') }}" class="btn button-inscription">Voir toutes les entreprises</a></div>

        </section>
        <section class="section-margin">
            <h6 class="text-center"><span class="yellow">Toutes </span>les offres en alternance</h6>

            <div class="menu-offres">
                <ul class="navbar-nav h-flex-responsive-nav">
                    <li class="nav-item"><a class="nav-link click-scroll" href="#section_1">Domaine d'activité</a></li>
                    <li class="nav-item"><a class="nav-link click-scroll" href="#section_2">Région</a></li>
                    <li class="nav-item"><a class="nav-link click-scroll" href="#section_3">Département</a></li>
                    <li class="nav-item"><a class="nav-link click-scroll" href="#section_4">Ville</a></li>
                </ul>

                <!-- Section 1 - Domaine -->
                <div id="section_1" class="container-fluid" style="display:block; padding: 2rem 0;">
                    <p class="titre-section" style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1.5rem;">
                        Toutes nos offres de stage / alternance classées par domaine d'activité
                    </p>

                    {% set domaines = listeOffres|sort((a, b) => a[1] <=> b[1]) %}
                    {% set total = domaines|length %}
                    {% set columns = 4 %}
                    {% set perCol = (total / columns)|round(0, 'ceil') %}

                    <div class="d-flex justify-content-between flex-wrap offres-list listing" style="gap: 30px;">
                        {% for col in 0..(columns - 1) %}
                            {% set columnItems = domaines|slice(col * perCol, perCol) %}
                            <ul style="list-style: none; padding: 0; width: 22%; font-size: 0.95rem; line-height: 1.6;">
                                {% for offre in columnItems %}
                                    <li style="margin-bottom: 8px;">
                                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #AACC4A; font-size: 7px; margin-right: 5px;"></i>
                                        <a href="{{ prefix ~ 'france-metiers-' ~ offre[0] ~ '-' ~ struct_home_search(offre[1]) }}" style="color: #333; text-decoration: none;">
                                            {{ offre[1]|replace({'/': '-'}) }}
                                        </a>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% endfor %}
                    </div>
                </div>


                <!-- Section 2 - Régions -->
                <div id="section_2" class="container-fluid" style="display:none;">
                    <p class="titre-section"><strong>Toutes nos offres en alternance classées par région</strong></p>

                    {% set regions = listeRegions|map(r => r[0])|sort %}
                    {% set total = regions|length %}
                    {% set columns = 4 %}
                    {% set perCol = (total / columns)|round(0, 'ceil') %}

                    <div class="d-flex justify-content-between flex-wrap offres-list" style="gap: 20px;">
                        {% for col in 0..(columns - 1) %}
                            {% set columnItems = regions|slice(col * perCol, perCol) %}
                            <ul>
                                {% for region in columnItems %}
                                    <li>
                                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #AACC4A; font-size: 7px;"></i>
                                        <a href="https://www.directalternance.com/AL/lists-offre/?contrat=6&q=&localisation={{ region }}&localisation_field=region">
                                            {{ region }}
                                        </a>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% endfor %}
                    </div>
                </div>

                <!-- Section 3 - Départements -->
                <div id="section_3" class="container-fluid" style="display:none;">
                    <p class="titre-section"><strong>Toutes nos offres en alternance classées par département</strong></p>


                    {% set departements = listeDepartements|sort((a, b) => a.libelle <=> b.libelle) %}
                    {% set total = departements|length %}
                    {% set columns = 4 %}
                    {% set perCol = (total / columns)|round(0, 'ceil') %}

                    <div class="d-flex justify-content-between flex-wrap offres-list" style="gap: 20px;">
                        {% for col in 0..(columns - 1) %}
                            {% set columnItems = departements|slice(col * perCol, perCol) %}
                            <ul>
                                {% for departement in columnItems %}
                                    <li>
                                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #44A2C6; font-size: 7px;"></i>
                                        <a href="https://www.directetudiant.com/DE/lists-offre/?contrat=&q=&localisation={{ departement }}&localisation_field=departement">
                                            {{ departement }}
                                        </a>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% endfor %}
                    </div>
                </div>
                <!-- Section 4 - Villes -->
                <div id="section_4" class="container-fluid" style="display:none;">
                    <p class="titre-section"><strong>Toutes nos offres en alternance classées par ville</strong></p>
                    {% set ville_redirects = {
                        'Alençon': 'ALENCON',
                        'Angoulême': 'ANGOULEME',
                        'Besançon': 'BESANCON',
                        'Béziers': 'BEZIERS',
                        'Compiègne': 'COMPIEGNE',
                        'Créteil': 'CRETEIL',
                        'Guéret': 'GUERET',
                        'La Rochelle': 'LA ROCHELLE',
                        'Le Havre': 'LE HAVRE',
                        'Le Mans': 'LE MANS',
                        'Lons-le-Saulnier': 'LONS-LE-SAUNIER',
                        'Mâcon': 'MACON',
                        'Nîmes': 'NIMES',
                        'Orléans': 'ORLEANS',
                        'Périgueux': 'PERIGUEUX',
                        'Saint-Lô': 'SAINT-LO'
                    } %}
                    {% set villes = codePostal|merge([{ libelle: "Vierzon" }])|sort((a, b) => a.libelle <=> b.libelle) %}
                    {% set total = villes|length %}
                    {% set columns = 4 %}
                    {% set perCol = (total / columns)|round(0, 'ceil') %}

                    <div class="d-flex justify-content-between flex-wrap offres-list" style="gap: 20px;">
                        {% for col in 0..(columns - 1) %}
                            {% set columnItems = villes|slice(col * perCol, perCol) %}
                            <ul>
                                {% for ville in columnItems %}
                                    {# lookup par libelle, fallback sur le même libellé #}
                                    {% set redir = ville_redirects[ville.libelle] ?? ville.libelle %}

                                    <li>
                                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #44A2C6; font-size: 7px;"></i>
                                        <a href="https://www.directalternance.com/AL/lists-offre/?contrat=&q=&localisation={{ redir }}&localisation_field=ville">
                                            {{ ville.libelle }}
                                        </a>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </section>

        <!-- Script d'activation dynamique des onglets -->
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                const navLinks = document.querySelectorAll(".menu-offres .nav-link");

                navLinks.forEach(link => {
                    link.addEventListener("click", function (e) {
                        e.preventDefault();

                        navLinks.forEach(l => l.classList.remove("active"));
                        this.classList.add("active");

                        document.querySelectorAll(".container-fluid").forEach(section => {
                            section.style.display = "none";
                        });

                        const target = this.getAttribute("href");
                        const section = document.querySelector(target);
                        if (section) section.style.display = "block";
                    });
                });
            });
        </script>


        <div class="row carousel-section mb-4">
            <div class="col-lg-4 col-md-12 col-sm-12 carousel-col mb-3">
                <div class="position-relative w-100">

                    <img src="{{ asset('DirectAlternance/Front/images/card_2.png')}}" class="d-block w-100" alt="image-btp">
                    <div class="centered-text-card w-100 h-100">
                        <div class="container h-100 py-4">
                            <div id="removeA" class="d-flex flex-column justify-content-between h-100">
                                <div>
                                    <div class="d-flex flex-row justify-content-around mb-4">
                                        <a href="{{ path('app_AL_conseilsCV') }}" title="LE CV">
                                            <i class="fa-solid fa-file-lines fa-2xl"></i>
                                        </a>

                                        <a href="{{ path('app_AL_conseilsmotivation') }}" title="LA LETTRE DE MOTIVATION">
                                            <i class="fa-solid fa-envelope fa-2xl"></i>
                                        </a>

                                        <a href="{{ path('app_AL_conseilsembauche') }}" title="L'ENTRETIEN D'EMBAUCHE">
                                            <i class="fa-solid fa-user-group fa-2xl"></i>
                                        </a>

                                    </div>
                                    <div style="height: 0.5px; width:100%; background-color:#fff; "></div>
                                </div>
                                <span class="text-center text-white" style="font-size: 18px;"><strong>Créez et déposez votre CV dans votre espace candidat.</strong></span>
                                {% if app.user  %}
                                    <div class="text-center"><a href="{{ path('app_DE_espaceCandidat') }}" style="color: #1E1E1E" class="btn button-white mx-auto"><strong>Profil</strong></a></div>
                                {% else %}
                                    <div class="text-center"><a href="{{ path('app_DE_register_candidat_e1') }}" style="color: #1E1E1E" class="btn button-white mx-auto"><strong>S'inscrire</strong></a></div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-12 col-sm-12 carousel-col mb-3">
                <div class="position-relative w-100">

                    <img src="{{ asset('DirectAlternance/Front/images/card_1.png')}}" class="d-block w-100" alt="image-btp">
                    <div class="centered-text-card w-100 h-100">
                        <div class="container h-100 py-4">
                            <div class="d-flex flex-column justify-content-between h-100">
                                <div></div>
                                <div>
                                    <a href="{{ path('app_AL_entreprises') }}"><span style="font-size: 18px;margin-bottom: 1rem;" class="text-center text-white w-100 d-block"><strong>Les entreprises qui recrutent<br> des alternants</strong></span></a>
                                    <a href="{{ path('app_AL_ecoles') }}"><span style="font-size: 18px;" class="text-center text-white d-block w-100"><strong>Les écoles en alternance</strong></span></a>
                                </div>
                                <div></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="col-lg-4 col-md-12 col-sm-12 carousel-col mb-3">
                <div class="position-relative w-100">

                    <img src="{{ asset('DirectAlternance/Front/images/card_3.png')}}" class="d-block w-100" alt="image-btp">
                    <div class="centered-text-card w-100 h-100">
                        <div class="container h-100 py-4">
                            <div class="d-flex flex-column justify-content-between h-100">
                                <div style="height:38px;">
                                </div>
                                <div>
                                    <a href="{{ path('app_AL_conseils') }}"><span style="font-size: 18px;" class="text-center text-white w-100 d-block mb-3 ">Guides et conseils <br>pour être recruté !</span></a>
                                </div>
                                <div class="text-center"><a href="{{ path('app_AL_conseils') }}" class="btn button-white mx-auto"><strong>En savoir plus</strong></a></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <section class="offres-section">
            <h2 class="text-center mb-5" style="font-size: 1.2rem;">Les offres du moment en alternance</h2>

            <div class="row ">
                <div class="col-lg-6 col-sm-12 my-3">
                    <div class="w-100 card-offre h-flex">
                        <div class="logo-card">
                            <img class="img-fluid"  style="width:70px" alt="logo College de Paris" src="{{ asset('/uploads/logos/' ~ ImgCompany1) }}" />
                        </div>
                        <div style="width:0.5px;height: 60px;background-color: #bdbdbd;"></div>
                        <div>
                            <ul>
                                {% for offre in Company1 %}
                                    <li class="middle-li">
                                        <i class="fa-solid fa-circle fa-2xs" style="color: #AACC4A;font-size: 7px"></i>
                                        <span><a href="https://www.directetudiant.com/DE/submitCandidat/{{ offre.id_offre }}" style="font-size: smaller;">{{ offre.intitule|length > 50 ? offre.intitule|slice(0, 50) ~ '...' : offre.intitule }}</a></span>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    <div class="w-100 card-offre middle-card  h-flex" >
                        <div class="logo-card">
                            <img class="img-fluid" style="width:70px" alt="logo iscod" src="{{ asset('/uploads/logos/' ~ ImgCompany2) }}"/>
                        </div>
                        <div style="width:0.5px;height: 60px;background-color: #bdbdbd;"></div>
                        <div>
                            <ul>
                                {% for offre in Company2 %}
                                    <li class="middle-li">
                                        <i class="fa-solid fa-circle fa-2xs" style="color: #AACC4A;font-size: 7px"></i>
                                        <span><a href="https://www.directetudiant.com/DE/submitCandidat/{{ offre.id_offre }}" style="font-size: smaller;">{{ offre.intitule|length > 50 ? offre.intitule|slice(0, 50) ~ '...' : offre.intitule }}</a></span>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    <div class="w-100 card-offre  h-flex" >
                        <div class="logo-card" >
                            <img class="img-fluid " style="width:70px"  alt="logo orano" src="{{ asset('/uploads/logos/' ~ ImgCompany3) }}" />
                        </div>
                        <div style="width:0.5px;height: 60px;background-color: #bdbdbd;"></div>
                        <div>
                            <ul>
                                {% for offre in Company3 %}
                                    <li class="middle-li">
                                        <i class="fa-solid fa-circle fa-2xs" style="color: #AACC4A;font-size: 7px"></i>
                                        <span><a href="https://www.directetudiant.com/DE/submitCandidat/{{ offre.id_offre }}" style="font-size: smaller;">{{ offre.intitule|length > 50 ? offre.intitule|slice(0, 50) ~ '...' : offre.intitule }}</a></span>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-sm-12 my-3">

                    <div class="w-100 card-offre h-flex">
                        <div class="logo-card">
                            <img class="img-fluid" style="width:70px" alt="logo Lidl" src="{{ asset('/uploads/logos/' ~ ImgCompany4) }}" />
                        </div>
                        <div style="width:0.5px;height: 60px;background-color: #bdbdbd;"></div>
                        <div>
                            <ul>
                                {% for offre in Company4 %}
                                    <li class="middle-li">
                                        <i class="fa-solid fa-circle fa-2xs" style="color: #AACC4A;font-size: 7px"></i>
                                        <span><a href="https://www.directetudiant.com/DE/submitCandidat/{{ offre.id_offre }}" style="font-size: smaller;">{{ offre.intitule|length > 50 ? offre.intitule|slice(0, 50) ~ '...' : offre.intitule }}</a></span>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    <div class="w-100 card-offre middle-card  h-flex" >
                        <div class="logo-card">
                            <img class="img-fluid"  style="width:70px" alt="logo Ascencia" src="{{ asset('/uploads/logos/' ~ ImgCompany5) }}" />
                        </div>
                        <div style="width:0.5px;height: 60px;background-color: #bdbdbd;"></div>
                        <div>
                            <ul>
                                {% for offre in Company5 %}
                                    <li class="middle-li">
                                        <i class="fa-solid fa-circle fa-2xs" style="color: #AACC4A;font-size: 7px"></i>
                                        <span><a href="https://www.directetudiant.com/DE/submitCandidat/{{ offre.id_offre }}" style="font-size: smaller;">{{ offre.intitule|length > 50 ? offre.intitule|slice(0, 50) ~ '...' : offre.intitule }}</a></span>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    <div class="w-100 card-offre  h-flex" >
                        <div class="logo-card" >
                            <img class="img-fluid " style="width:70px" alt="logo groupe barriere" src="{{ asset('/uploads/logos/' ~ ImgCompany6) }}" />
                        </div>
                        <div style="width:0.5px;height: 60px;background-color: #bdbdbd;"></div>
                        <div>
                            <ul>
                                {% for offre in Company6 %}
                                    <li class="middle-li">
                                        <i class="fa-solid fa-circle fa-2xs" style="color: #AACC4A;font-size: 7px"></i>
                                        <span><a href="https://www.directetudiant.com/DE/submitCandidat/{{ offre.id_offre }}" style="font-size: smaller;">{{ offre.intitule|length > 50 ? offre.intitule|slice(0, 50) ~ '...' : offre.intitule }}</a></span>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-4"><a href="{{ path('app_AL_candidat_search') }}" class="btn button-black">Voir toutes les offres</a></div>
            </div>
        </section>

        <div class="row carousel-section align-items-center">
            <div class="col-lg-6 col-md-12 col-sm-12 carousel-col mb-4">
                <div class="position-relative w-100">

                    <img src="{{ asset('DirectAlternance/Front/images/fiche-metier.png')}}"  class="d-block w-100 img-fluid"  alt="image-fiche-metier">
                    <div class="centered-text-card w-100">
                        <div class="container py-4">
                            <div class="d-flex flex-column justify-content-between">
                                <div></div>
                                <div>
                                    <a href="https://www.directemploi.com/conseil/listeMetiers"><span style="font-size: 18px;margin-bottom: 5rem;" class="text-center text-white w-100 d-block"><strong>Les fiches métiers de A à Z</strong></span></a>
                                    <a href="https://www.directemploi.com/conseil/listeDomaines"><span style="font-size: 18px;margin-bottom: 5rem;" class="text-center text-white w-100 d-block"><strong>Les domaines d'activités de A à Z</strong></span></a>

                                </div>
                                <div></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="col-lg-6">
                <section class="article-section-alternance">
                    <h1 style="font-size: 1.25rem; line-height: 1.5;" class="text-center mb-5" >Les actualités de l'alternance</h1>
                    <div class="row">
                        {% for article in articles|slice(0, 3) %}
                            <div class="col-lg-6 col-md-12 mb-3">
                                <div class="card">
                                    <div style=" border-radius: 1.25rem; height: 180px; background-image: url('{{ asset("/uploads/article/" ~ article.image_petite) }}');background-position: center;background-size: cover;">
                                    </div>
                                    <div class="card-body p-0 pt-2">
                                        <div class="d-flex flex-column justify-content-between h-100">
                                            <p class="title"><b>{{article.titre}}</b></p>

                                            <p class="">{{article.chapo|u.truncate(150, '...', false)}}</p>
                                            <a href="{{ "/AL/actualitesdetails/" ~ article.id ~ "/" ~ strtourl(article.titre) }}" style="color:#AACC4A">En savoir plus</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}

                    </div>
                    <div class="text-center mt-4"><a href="{{ path('app_AL_actualites') }}" class="btn button-inscription">Retrouver nos articles sur le thème de l'alternance</a></div>

                </section>
            </div>
        </div>


    </div>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const sections = document.querySelectorAll(".container-fluid");

            function hideAllSections() {
                sections.forEach(function (section) {
                    section.style.display = "none";
                });
            }

            function showSection(sectionId) {
                hideAllSections();
                const targetSection = document.getElementById(sectionId);
                if (targetSection) {
                    targetSection.style.display = "block";

                }
            }
            const links = document.querySelectorAll(".click-scroll");
            links.forEach(function (link) {
                link.addEventListener("click", function (event) {
                    event.preventDefault();
                    const sectionId = link.getAttribute("href").substring(1); // Enlève le dièse (#)
                    showSection(sectionId);
                });
            });
        });
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
    <script>
        $('.brand-carousel').owlCarousel({
            loop: true,
            margin: 10,
            autoplay: true,
            autoplayTimeout: 1500, // Augmentez cette valeur pour ralentir la vitesse
            slideBy:2,
            responsive: {
                0: {
                    items: 1
                },
                600: {
                    items: 3
                },
                1000: {
                    items: 8
                }
            }
        })

    </script>
    {% block footer %}
        {% include 'DirectAlternance/footer.html.twig' %}
    {% endblock %}


{% endblock %}