{% if path == '/DE/home/' %}
    {% set prefix = '/DE/lists-offre/' %}
{% elseif path == "/theme/offres-stage" %}
    {% set prefix = '../lists-offre/stage-' %}
{% elseif path == '/theme/offres-alternance' %}
    {% set prefix = '../lists-offre/alternance-' %}
{% elseif path == "/theme/offre-emploi-jeune-diplome" %}
    {% set prefix = '../' %}
{% elseif path == "/theme/offres-emploi-interim" %}
    {% set prefix = '../lists-offre/interim-' %}
{% elseif path == "/theme/offres-emploi-cabinet-de-recrutement" %}
    {% set prefix = '../lists-offre/cdi-' %}
{% else %}
    {% set prefix = '/DE/lists-offre/' %}
{% endif %}
<section class="section-margin">

    <h1 class="text-center" ><span class="yellow">Toutes</span> les offres de stage</h1>
    <div class="menu-offres">
        <ul class="navbar-nav h-flex-responsive-nav ">
            <li class="nav-item">
                <a class="nav-link click-scroll active" href="#section_1">Domaine d'activité</a>
            </li>
            <li class="nav-item">
                <a class="nav-link click-scroll" href="#section_2">Région</a>
            </li>

            <li class="nav-item">
                <a class="nav-link click-scroll" href="#section_3">Département</a>
            </li>

            <li class="nav-item">
                <a class="nav-link click-scroll" href="#section_4">Ville</a>
            </li>

        </ul>


        <!-- Métiers -->
        <div id="section_1" class="container-fluid" style="display:block;">
            <p class="my-3" style="font-size: 1.125rem;"><strong>Toutes nos offres de stage / alternance classées par domaine d'activité</strong></p>
            <div class="h-flex justify-content-between offres-list listing" id="liste_offres">
                <ul class="liste" style="list-style: none; padding: 0;">
                    {% for offre in listeOffres %}
                    {% set counter = ( counter | default(-1) ) + 1 %}
                    {% if counter == 7 or counter == 14 or counter == 22 %}
                </ul>
                <ul class="liste" style="list-style: none; padding: 0; margin: 0;">
                    {% endif %}
                    <li>
                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #44A2C6;font-size: 7px;"></i>
                        <a href="{{ prefix ~ 'france-metiers-' ~ offre[0] ~ '-' ~ struct_home_search(offre[1]) }}">
                            {{ offre[1]|replace({'/': '-'}) }}
                            ({{ offre[2] }}
                            {% if offre[2] == 1 or offre[2] == 0 %}
                            offre
                        {% else %}
                            offres
                            {% endif %})
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <div id="section_2" class="container-fluid" style="display:none;">
            <p class="my-3" style="font-size: 1,125rem;"><strong>Toutes nos offres de stage classées par région</strong></p>
            <div class="h-flex justify-content-between offres-list">
                <ul class="region-list" style="list-style: none; padding: 0; margin-right: 20px; float: left;">
                    {% set counter = 0 %}
                    {% for region in listeRegions %}
                    {% if counter != 0 and counter % 7 == 0 %}
                </ul>
                <ul class="region-list" style="list-style: none; padding: 0; margin-right: 20px; float: left;">
                    {% endif %}
                    <li>
                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #faaf40;font-size: 7px;"></i>
                        <a href="https://www.directalternance.com/AL/lists-offre/?contrat=6&q=&localisation={{ region }}&localisation_field=region">
                            {{ region }}
                        </a>
                    </li>
                    {% set counter = counter + 1 %}
                    {% endfor %}
                </ul>
            </div>
        </div>
        <div id="section_3" class="container-fluid" style="display:none;">
            <p class="my-3" style="font-size: 1,125rem;"><strong>Toutes nos offres de stage classées par département</strong></p>
            <div class="h-flex justify-content-between offres-list" style="flex-wrap: nowrap">
                <ul class="departement-list" style="list-style: none; padding: 0; margin-right: 20px; float: left; width: 25%;">
                    {% for departement in listeDepartements %}
                    {% set counter = ( counter | default(-1) ) + 1 %}
                    {% set array = [25, 51, 77, 103] %}
                    {% if counter != 0 and counter in array %}
                </ul>
                <ul class="departement-list" style="list-style: none; padding: 0; margin-right: 20px; float: left; width: 25%;">
                    {% endif %}
                    {% set cp = departement.CodePostal|raw %}
                    {% if cp|length == 1 %}
                        {% set cp = cp ~ "0" %}
                        {% set cp = cp|reverse %}
                    {% endif %}
                    <li>
                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #faaf40;font-size: 7px"></i>
                        <a href={{ prefix ~ struct_home_search(departement.libelle) ~ "-metiers-secteurs" }}>{{ departement.libelle ~ " (" ~ cp ~ ")" }}</a>
                    </li>
                    {% endfor %}
                </ul>

            </div>
        </div>
        <div id="section_4" class="container-fluid" style="display:none;">
            <p class="my-3" style="font-size: 1,125rem;"><strong>Toutes nos offres de stage classées par ville</strong></p>
            <div class="h-flex justify-content-between offres-list" style="flex-wrap: nowrap">
                <ul class="ville-list" style="list-style: none; padding: 0; margin-right: 20px; float: left; width: 25%;">
                    {% for ville in codePostal %}

                    {% set counter = ( counter | default(-1) ) + 1 %}
                    {% set array = [20, 40, 60, 80] %}
                    {% if counter != 0 and counter in array %}
                </ul>
                <ul class="ville-list" style="list-style: none; padding: 0; margin-right: 20px; float: left; width: 25%;">
                    {% endif %}
                    <li>
                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #faaf40;font-size: 7px"></i>
                        <a href={{ prefix ~ struct_home_search(ville.libelle) ~ "-(" ~ ville.codePostal ~ ")-metiers-secteurs" }}>{{ ville.libelle }}</a>
                    </li>
                    {% endfor %}
                    <li>
                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #faaf40;font-size: 7px"></i>
                        <a href={{ prefix ~ struct_home_search('Vierzon') ~ "-(18)-metiers-secteurs" }}>{{ "Vierzon" }}</a>
                    </li>
                </ul>

            </div>
        </div>

    </div>
</section>