{% extends 'DirectAlternance/base.html.twig' %}

{% set lettre = app.request.query.get('lettre') %}
{% set page = app.request.query.get('page') %}
{% if page %}
    {% set title_seo_part = " : Retrouvez les " %}
    {% if offresSelonEntreprise.totalItemCount > 0 %}
        {% set title_seo_part = title_seo_part ~ offresSelonEntreprise.totalItemCount ~ " postes à pourvoir chez " %}
    {% else %}
        {% set title_seo_part = title_seo_part ~ " postes à pourvoir chez " %}
    {% endif %}
    {% set title_seo = entreprise.nomEcole ~ title_seo_part ~ entreprise.nomEcole ~ "| Page : " ~ page %}

    {% set fullAddress = '' %}
    {% if adresse.adresse1 is defined %}{% set fullAddress = fullAddress ~ adresse.adresse1 %}{% endif %}
    {% if adresse.adresse2 is defined %}{% set fullAddress = fullAddress ~ ' - ' ~ adresse.adresse2 %}{% endif %}
    {% if adresse.codePostal is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.codePostal %}{% endif %}
    {% if adresse.nomVille is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.nomVille %}{% endif %}
    {% if offresSelonEntreprise.totalItemCount > 0 %}
        {% set description_seo = entreprise.nomEcole ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationEcole|raw|nl2br ~ "| Page : "~ page~"|"~ offresSelonEntreprise.totalItemCount ~ " postes" %}
    {% else %}
        {% set description_seo = entreprise.nomEcole ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationEcole|raw|nl2br ~ "| Page : "~ page %}
    {% endif %}

{% else %}
    {% set title_seo_part = " : Retrouvez les " %}
    {% if offresSelonEntreprise.totalItemCount > 0 %}
        {% set title_seo_part = title_seo_part ~ offresSelonEntreprise.totalItemCount ~ " postes à pourvoir chez " %}
    {% else %}
        {% set title_seo_part = title_seo_part ~ " postes à pourvoir chez " %}
    {% endif %}
    {% set title_seo = entreprise.nomEcole ~ title_seo_part ~ entreprise.nomEcole %}

    {% set fullAddress = '' %}
    {% if adresse.adresse1 is defined %}{% set fullAddress = fullAddress ~ adresse.adresse1 %}{% endif %}
    {% if adresse.adresse2 is defined %}{% set fullAddress = fullAddress ~ ' - ' ~ adresse.adresse2 %}{% endif %}
    {% if adresse.codePostal is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.codePostal %}{% endif %}
    {% if adresse.nomVille is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.nomVille %}{% endif %}
    {% if adresse.nomVille is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.nomVille %}{% endif %}
    {% if offresSelonEntreprise.totalItemCount > 0 %}
        {% set description_seo = entreprise.nomEcole ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationEcole|raw|nl2br ~ "| Page : "~ page~"|"~ offresSelonEntreprise.totalItemCount ~ " postes" %}
    {% else %}
        {% set description_seo = entreprise.nomEcole ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationEcole|raw|nl2br ~ "| Page : "~ page %}
    {% endif %}{% endif %}

{% block title %}{{ title_seo[:150] }}{% endblock %}
{% block title_og %}{{ title_seo[:150] }}{% endblock %}
{% block title_twitter %}{{ title_seo[:150] }}{% endblock %}
{% block meta_description %}{{ description_seo[:550] }}{% endblock %}
{% block meta_description_og %}{{ description_seo[:550] }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo[:550] }}{% endblock %}
{% block body %}
    <div class="is-sticky">
        {% block head %}
            {% include 'DirectAlternance/header.html.twig' %}
        {% endblock %}

        <section class="ajouter-cv-header" id="">
            <div class="container">
                {% if breadcrumb is defined %}
                    {% include '/_partials/_breadcrumb.html.twig'  %}
                {% endif %}
                <h1 class="text-center text-uppercase mb-3" style="font-size: 1.25rem; line-height: 1.4;"><span class="yellow">Emploi</span> {{entreprise.nomEcole}}</h1>
            </div>
        </section>


        <section class="la-une entreprise-profile">
            <div class="container">
                <div class="w-100 card-entreprise">
                    <div class="row">
                        <div class="col-lg-8">
                            <h6>{{entreprise.nomEcole}}</h6>
                            <p>{{fullAddress}}</p>
                            <a href="{{entreprise.siteInternet}}">{{entreprise.siteInternet}}</a>
                        </div>
                        <div class="col-lg-4">

                            <div class="w-100 vertical-center">
                                {% if entreprise.logo != null %}
                                    <div class="logo-entreprise ">
                                        <img src="{{ asset('/uploads/logos/' ~ entreprise.logo) }}" alt="{{entreprise.nomEcole}}">
                                    </div>
                                {% else %}
                                    <div class="logo-entreprise ">
                                        <img src="{{ asset('DirectAlternance/Front/images/logo noir.png') }}" alt="{{entreprise.nomEcole}}">
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="content">
            <div class="container">
                <div class="header-and-text">
                    <h6>Présentation de l'entreprise</h6>
                    <hr>
                    <p class="justified">{{entreprise.presentationEcole | raw}}</p>

                </div>
            </div>

        </section>
        {#
        <section class="entreprise-emploi">
            <div class="container">
                <div class="menu-offres">
                    <ul class="navbar-nav h-flex ">
                        <li class="nav-item">
                            <a class="nav-link click-scroll active" href="#" data-filter="all">Emploi</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link click-scroll" href="#" data-filter="Alternance">Alternance</a>
                        </li>
                    </ul>
                    <div class="container-fluid">
                        <div class="h-flex justify-content-between offres-list pt-5">
                            <style>
                                .offres-list {
                                    list-style: none;
                                    padding: 0;
                                    columns: 4;
                                    column-gap: 5em;
                                }

                                .offres-list li {
                                    break-inside: avoid;
                                    margin-bottom: 10px;
                                }
                            </style>

                            <ul class="offres-list" id="offres-list">
                                {% for offre in offresSelonEntreprise %}
                                    <li data-type-contrat="{{ offre.type_contrat }}">
                                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #13A3AA; font-size: 7px;"></i>
                                        <a href="{{ path('app_DE_submitCandidat', {id: offre.id_offre, slug: offre.intitule | raison }) }}">{{ offre.intitule }}</a>
                                        <div class="px-1">
                                            <p class="m-0 text-secondary">Date de publication : {{ offre.date_soumission | date('d/m/Y') }} </p>
                                            <p class="m-0 text-secondary">Type de contrat : {{ offre.type_contrat }}</p>
                                            <p class="m-0 text-secondary">
                                                Localisation : {{ offre.geo_ville }}{% if offre.departement %} - {{ offre.departement }}{% endif %}
                                            </p>
                                        </div>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div align="center" id="pagination_de">
                {{knp_pagination_render(offresSelonEntreprise, 'carriereBTP/directemploi/composants/paginationTemplate.html.twig')}}
            </div>
        </section>
        #}
        {% block footer %}
            {% include 'DirectAlternance/footer.html.twig' %}
        {% endblock %}
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var filterLinks = document.querySelectorAll('.filter-link');
            var offresList = document.getElementById('offres-list');

            filterLinks.forEach(function (link) {
                link.addEventListener('click', function (event) {
                    event.preventDefault();

                    var filterType = link.getAttribute('data-filter');

                    offresList.querySelectorAll('li').forEach(function (offre) {
                        offre.style.display = 'block';

                        if (filterType !== 'tous') {
                            if (offre.getAttribute('data-type-contrat') !== filterType) {
                                offre.style.display = 'none';
                            }
                        }
                    });
                });
            });
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.nav-link.click-scroll');
            const offersList = document.getElementById('offres-list').children;

            tabs.forEach(tab => {
                tab.addEventListener('click', function(event) {
                    event.preventDefault();

                    // Remove active class from all tabs
                    tabs.forEach(t => t.classList.remove('active'));
                    // Add active class to the clicked tab
                    this.classList.add('active');

                    const filter = this.getAttribute('data-filter');

                    // Show/hide offers based on the selected filter
                    Array.from(offersList).forEach(offer => {
                        const offerTitle = offer.querySelector('a').textContent.trim();
                        if (filter === 'all' || offer.getAttribute('data-type-contrat') === filter || offerTitle.includes(filter)) {
                            offer.style.display = 'block';
                        } else {
                            offer.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>

{% endblock %}