{% extends 'DirectAlternance/base.html.twig' %}
{% set lettre = app.request.query.get('lettre') %}

    {% set title_seo = "Offre d'alternance par Ville en France, " ~ dep[0].libelle ~ " | Direct Etudiant" %}
    {% set description_seo = "Consultez les Dernières Offre d'alternance par ville en " ~ dep[0].libelle ~ "sur Direct Etudiant. ✓ Postulez en ligne dès Maintenant ✓ Candidature Simple & Rapide !" %}

{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .alphabet-filter {
            text-align: center;
            padding: 10px;
            margin: 10px;
        }

        .alphabet-filter a {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border: 0.5px solid #ddd; /* Couleur de la bordure */
            border-radius: 4px; /* Coins arrondis */
            text-decoration: none; /* Supprime le soulignement des liens */
            color: #333; /* Couleur du texte */
            font-weight: bold; /* Rend le texte en gras */
            transition: background-color 0.3s, color 0.3s; /* Animation au survol */
        }

        .alphabet-filter a:hover {
            background-color: #AACC4A; /* Couleur de fond au survol */
            color: #fff; /* Couleur du texte au survol */
        }

    </style>
    <div class="is-sticky">
        {% block head %}
            {% include 'DirectAlternance/header.html.twig' %}
        {% endblock %}
        <section class="emploi-header d-flex justify-content-center align-items-center " id="">
            <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#AACC4A">Offre en alternance par ville </span> dans le département {{ dep[0].libelle }}</h1>
        </section>
        <br>
        <section class="section-embauche">
            <h6 class="text-center text-uppercase mb-5"><span style="color: #AACC4A;">Offre en alternance </span>{{ dep[0].libelle }}</h6>
            <p class="text-center" style="margin-bottom: 2rem;">Découvrez ci-dessous tout les Offre d'alternance disponibles dans différentes villes du département {{ dep[0].libelle }}.</p>
            <div class="container">
                <div class="row offres-list">
                    {% set taille_liste = dataVille|length %}
                    {% set taille_groupe = (taille_liste / 3)|round(0, 'ceil') %}

                    {% for i in 0..2 %}
                        <div class="col-lg-4 col-md-6">
                            {% for topville in dataVille|slice(i * taille_groupe, taille_groupe) %}
                                <div style="margin-bottom: 20px;">
                                    <ul style="list-style: none; padding: 0;">
                                        <li onclick="redirectTo('{{ topville.libelle | replace({"Offre d'alternance ": ''}) | escape('js') }}')">

                                            <a href="#" >Offre d'alternance {{ topville.libelle }}</a>
                                        </li>
                                    </ul>

                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                </div>
                {% if breadcrumb is defined %}
                    {% include '/_partials/_breadcrumb.html.twig'  %}
                {% endif %}

            </div>

            {% block footer %}
                {% include 'DirectAlternance/footer.html.twig' %}
            {% endblock %}
        </section>








        <!-- Include jQuery and Bootstrap JS from CDNJS -->
        <script>
            function redirectTo( value) {
                const encodedValue = encodeURIComponent(value);
                const url = `https://www.directalternance.com/AL/lists-offre/?contrat=6&localisation=${encodedValue}&localisation_field=&q=`;
                window.location.href = url;
            }

        </script>
        <script>
            document.getElementById('searchBar').addEventListener('input', function(e) {
                var searchValue = e.target.value.toLowerCase();
                var offres = document.querySelectorAll('.offres-list div');

                offres.forEach(function(offre) {
                    var villeText = offre.innerText.toLowerCase();
                    if (villeText.includes(searchValue)) {
                        offre.style.display = '';
                    } else {
                        offre.style.display = 'none';
                    }
                });
            });
        </script>

    </div>
{% endblock %}

