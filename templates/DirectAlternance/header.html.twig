


<style>
    .dropdown-emploi:hover .dropdown-menu {
        display: block;
    }

</style>
{% set current_path = app.request.attributes.get('_route') %}
<nav class="navbar navbar-expand-lg " style="position:fixed; z-index: 999;">
    <div class="container">
        <div class="v-flex-center w-100">
            <div class="h-flex-responsive align-items-center justify-content-around w-100">
                <button class="navbar-toggler mx-3" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="d-flex align-items-center mx-auto">
                    <a href="{{ path('app_AL_accueil') }}" class="navbar-brand mx-2"><img class="logo" src=""/></a>
                    <span class="tagline mx-2" style="font-weight: 600;">L’espace 100% alternance</span>
                </div>
            </div>


            <div class="collapse navbar-collapse w-100 mx-0-md mx-0-sm" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if current_path == 'app_AL_accueil' %}active{% endif %}" href="{{ path('app_AL_accueil') }}">Accueil</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if current_path == 'app_AL_entreprises' %}active{% endif %}" href="{{ path('app_AL_entreprises') }}">Entreprises</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if current_path == 'app_AL_actualites' %}active{% endif %}" href="{{ path('app_AL_actualites') }}">Actualités</a>
                    </li>
                    <div class="dropdown dropdown-conseil">
                        <li class="nav-item separator">
                            <a class="nav-link " href="{{ path('app_AL_conseils') }}">Conseils</a>
                        </li>
                        <div class="dropdown-content content-conseil"style="left: 0;">
                            <a href="{{ path('app_AL_conseilsCV') }}">Le CV</a>
                            <a href="{{ path('app_AL_conseilsmotivation') }}">La lettre de motivation</a>
                            <a href="{{ path('app_AL_conseilsembauche') }}">L'entretien d'embauche</a>
                            <a href="{{ path('app_AL_conseilsrechercheEmploi') }}">La recherche d'emploi</a>
                        </div>
                    </div>


                    {% if app.user is null %}
                        <div class="dropdown dropdown-recruteur">
                            <li class="nav-item">
                                <a class="nav-link" ><img class="nav-icon" src="{{ asset('DirectAlternance/Front/images/mdi-worker.svg')}}" />Candidats</a>
                            </li>
                            <div class="dropdown-content content-recruteur" style="left: 0;">
                                <a href="{{ path('app_AL_login') }}">Connexion</a>
                                <a href="{{ path('app_AL_register_candidat_e1') }}">Création de compte</a>
                            </div>

                        </div>
                        <div class="dropdown dropdown-candidat" style="float:right;">

                            <li class="nav-item">
                                <a class="nav-link" > <img class="nav-icon" src="{{ asset('DirectAlternance/Front/images/dashicons-businessman.svg')}}"/>Recruteurs</a>
                            </li>
                            <div class="dropdown-content content-candidat">
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Connexion</a>
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Création de compte</a>
                                <div class="px-3">
                                    <hr>
                                </div>
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Les offres proposées</a>
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Le réseau Direct Emploi</a>

                            </div>
                        </div>
                    {% else %}
                        <div class="dropdown dropdown-recruteur">
                            <li class="nav-item">
                                <a href="{{ path('app_AL_espaceCandidat') }}" class="nav-link  click-scroll" ><img class="nav-icon" alt="worker" src="{{ asset('DirectAlternance/Front/images/mdi-worker.svg')}}" />{{ app.user.nom }} {{ app.user.prenom }}</a>
                            </li>
                            <div class="dropdown-content content-recruteur" style="left: 0;float:right;">
                                <a href="{{ path('app_AL_espaceCandidat') }}">Mon Profil</a>
                                <a href="{{ path('app_AL_espaceCandidatprofilcv') }}">Mon CV</a>
                                <a href="{{ path('app_AL_espaceCandidatprofiloffre') }}">Mes offres</a>
                                <a href="{{ path('app_AL_espaceCandidatprofilmail') }}">Mes alertes mail</a>
                                {% for role in app.user.roles %}
                                    {% if role == "ROLE_ADMIN" %}
                                        <a href="{{ path('app_BTP_admin') }}">Espace Admin</a>
                                    {% endif %}
                                {% endfor %}
                                <div class="px-3">
                                    <hr class="py-0 my-1">
                                </div>
                                <a href="{{ path('app_logout') }}" class="text-danger">Se deconnecter</a>
                            </div>
                        </div>
                    {% endif %}
                </ul>

            </div>
        </div>
    </div>
</nav>
