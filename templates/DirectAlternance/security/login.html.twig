{% extends 'DirectAlternance/base.html.twig' %}
{% set title = "Se connecter " %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}

{% set description_seo = "Accéder à votre compte Direct Alternance est simple et sécurisé. Entrez vos identifiants pour découvrir un monde d'opportunités et gérer votre parcours professionnel en toute facilité." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <div class="is-sticky">

    {% block head %}
        {% include 'DirectAlternance/header.html.twig' %}
    {% endblock %}

    <section class="hero d-flex justify-content-center align-items-center" id="">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 col-12 title-col">
                    <div>
                        <div  class="hr-signup"></div>
                        <h1>Des <span style="color:#AACC4A;">milliers d’opportunités </span>vous attendent.</h1>
                    </div>
                </div>
                <div class="col-lg-6 col-12">
                    <div class="card rounded-lg">
                        <div class="card-header">
                            <div class="container container-form">
                                <h6 class="text-center">Se connecter à votre compte</h6>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="container container-form">
                                <form method="post">
                                    {% if error %}
                                        <div class="alert alert-danger">{{ error.messageKey|trans(error.messageData, 'security') }}</div>
                                    {% endif %}
                                    <div class="form-group">
                                        <label for="inputEmail">Email</label>
                                        <input type="email" name='email' value="{{ last_username }}" class="form-control" id="inputEmail" aria-describedby="emailHelp" placeholder="Email">
                                    </div>
                                    <div class="form-group">
                                        <label for="inputPassword">Mot de passe</label>
                                        <input type="password" name="password" class="form-control" id="inputPassword" placeholder="Password">
                                    </div>
                                    <div class="form-group">
                                        <a href="{{ path('BTP_login_mdp_forgotten') }}" class="text-secondary" style="font-size: 0.9375rem;">Mot de passe oublié ?</a>
                                    </div>
                                    <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">

                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary mb-2">Se connecter</button>
                                        <p class="mb-1 text-muted">Ou</p>
                                        <a href="{{ path('app_BTP_register_candidat_e1') }}" class="text-secondary" style="font-size: 0.9375rem; font-weight: 700;">S'inscrire</a>
                                    </div>
                                </form>

                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </section>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@10/dist/sweetalert2.min.css">
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const successMessage = '{{ app.session.flashBag.get("emailsend") | first }}';

            if (successMessage) {
                Swal.fire({
                    icon: 'success',
                    title: 'Un email vient de vous être envoyé',
                    showConfirmButton: false,
                    showCloseButton: true,  // Add this line to show the "Fermer" button
                });
            }
        });

    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const successMessage = '{{ app.session.flashBag.get("successupdatepassword") | first }}';
            if (successMessage) {
                Swal.fire({
                    icon: 'success',
                    title: 'Votre mot de passe a bien été modifié',
                    showConfirmButton: false,
                    showCloseButton: true,  // Add this line to show the "Fermer" button
                });
            }
        });

    </script>
    {% block footer %}
        {% include 'DirectAlternance/footer.html.twig' %}
    {% endblock %}
    </div>
{% endblock %}