{% extends 'DirectAlternance/base.html.twig' %}
{% set title = "Inscription - Troisième  Étape" %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}

{% set description_seo = "Finalisez votre inscription en créant ou en éditant une alerte mail. Cette fonctionnalité vous permet de rester informé des dernières offres d'emploi qui correspondent à vos critères. C'est un outil puissant pour s'assurer que vous ne manquez aucune opportunité dans le secteur du BTP et que vous êtes toujours à jour avec les offres les plus récentes et pertinentes.
" %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}

<div class="is-sticky">

    {% block head %}
    {% include 'DirectAlternance/header.html.twig' %}

{% endblock %}

    <section class="login-header d-flex justify-content-center align-items-center" id="">
        <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#AACC4A"> Création </span>compte candidat</h1>
    </section>
    <section class="login-form">
        {{ form_errors(InscriptionEtape3Form) }}
        {{ form_start(InscriptionEtape3Form) }}
        <div class="container">
            <form >
                <div id="msform">
                    <ul id="progressbar" class="d-flex flex-row justify-content-around">
                        <li class="was-active" id="account"><strong>Créer mon compte</strong></li>
                        <li class="was-active" id="personal"><strong>Définir mon profil</strong></li>
                        <li class="active" id="payment"><strong>Créer mon alerte mail</strong></li>
                    </ul>
                </div>
                <div class="info-section">
                    {{ form_row(InscriptionEtape3Form._token) }} <!-- Add the CSRF token here -->
                    <h6>Création / Édition d'une alerte mail</h6>
                    <hr>
                    <div class="row">
                        <div class="col-lg-12 col-sm-12">
                            <div class="form-group">
                                <label for="inputalerte">Nom de mon alerte</label>
                                {{ form_row(InscriptionEtape3Form.nom_alerte, {'label': false}) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="contratFormControl">Type de contrat *</label>
                                {{ form_row(InscriptionEtape3Form.contrat, {'label': false}) }}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="experienceFormControl">Experience</label>
                                {{ form_row(InscriptionEtape3Form.experience, {'label': false}) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="lang2FormControl">Domaine de votre métier * </label>
                                {{ form_row(InscriptionEtape3Form.domaine_activite, {'attr': {'class': 'domaine-activite-liste form-control'},'label': false}) }}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="niveau2FormControl">Région *</label>
                                {{ form_row(InscriptionEtape3Form.geo_liste_region, {'attr': {'class': 'geo_liste_region form-control'},'label': false}) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="lang2FormControl">Métier</label>
                                {{ form_row(InscriptionEtape3Form.metier_metier, {'attr': {'class': 'metier-metier-liste form-control'},'label': false}) }}

                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="niveau2FormControl">Département</label>
                                {{ form_row(InscriptionEtape3Form.geo_departement, {'attr': {'class': 'geo_departement form-control'},'label': false}) }}
                            </div>
                        </div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                        <label class="form-check-label" for="flexCheckDefault">
                            N'afficher que les offres ouvertes aux personnes en situation de handicap
                        </label>
                    </div>
                </div>
                <div class="text-center">
                    <button type="submit" class="btn mb-3 px-4 button-black">Enregistrer</button>
                </div>
                <div class="text-center">
                    <a href="{{ path('app_DE_logincandidat') }}" id="save-button"  class="btn mb-2 px-4 button-black">Passer</a>
                </div>
            </form>

        </div>

    </section>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var domaineActiviteListe = document.querySelector('.domaine-activite-liste');
            var metierMetierListe = document.querySelector('.metier-metier-liste');
            domaineActiviteListe.addEventListener('change', function () {
                var selectedDomaineActivite = this.value;
                // Simulate fetching data from the server
                fetch('/ajax/get-metiers/' + selectedDomaineActivite)
                    .then(response => response.json())
                    .then(data => {
                        // Clear the options
                        metierMetierListe.innerHTML = '';

                        // Update the options
                        data.forEach(function (metier) {
                            var option = document.createElement('option');
                            option.value = metier.id;
                            console.log(metier.id)
                            option.text = metier.libelle_metier;
                            metierMetierListe.add(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching metiers:', error);
                    });
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var geo_liste_region = document.querySelector('.geo_liste_region');
            var geo_departement = document.querySelector('.geo_departement');
            geo_liste_region.addEventListener('change', function () {
                var selectedgeo_liste_region = this.value;
                // Simulate fetching data from the server
                fetch('/ajax/get-departements/' + selectedgeo_liste_region)
                    .then(response => response.json())
                    .then(data => {
                        // Clear the options
                        geo_departement.innerHTML = '';

                        // Update the options
                        data.forEach(function (departement) {
                            var option = document.createElement('option');
                            option.value = departement.id;
                            console.log(departement)
                            option.text = departement.libelle;
                            geo_departement.add(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching metiers:', error);
                    });
            });
        });
    </script>
    {% block footer %}
        {% include 'DirectAlternance/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}