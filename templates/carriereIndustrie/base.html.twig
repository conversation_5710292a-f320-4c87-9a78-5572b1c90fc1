<!DOCTYPE html>
<html lang="fr">
    <head>
        <meta charset="UTF-8">
        <title>{% block title %}{% endblock %}</title>
        <link rel="icon" type="image/x-icon" sizes="16x16" href="{{ asset('carriereIndustrie/Front/images/logo-ind-16x16.png')}}">
        <link rel="icon" type="image/x-icon" sizes="32x32" href="{{ asset('carriereIndustrie/Front/images/logo-ind-32x32.png')}}">
        <link rel="icon" type="image/x-icon" href="{{ asset('carriereIndustrie/Front/images/logo-ind-32x32.png')}}">

        <meta property="og:title" content="{% block title_og %}{% endblock %}">
        <meta property="og:description" content="{% block meta_description_og %}{% endblock %}">
        <meta property="og:site_name" content="carriere-industrie.com">
        <meta property="og:locale" content="fr_FR">
        <meta property="og:type" content="website">
        <meta property="og:image" content="{{ asset('carriereIndustrie/Front/images/logoindustrie.png')}}">
        <meta name="twitter:card" content="summary">
        <meta name="twitter:site" content="@EmploiIndustrie">

        <meta name="google-site-verification" content="UYAQobM7Xol2yBYqQU6w7Bw7QmnY5Ip1i_5mgNtak2M">
        <meta name="twitter:title" content="{% block title_twitter %}{% endblock %}">
        <meta name="twitter:description" content="{% block meta_description_twitter %}{% endblock %}">
        <meta name="twitter:domain" content="www.carriere-industrie.com">
        <meta name="twitter:image" content="{{ asset('carriereIndustrie/Front/images/logoindustrie.png')}}">
        <meta name="robots" content="index, follow">
        <meta name="googlebot" content="index, follow">
        <meta name="description" content="{% block meta_description %} Ils recrutent sur Carriere Industrie · Nos offres d'emplois classées · Nos rubriques · Guides et conseils de l'emploi · Actualités de l'emploi et de la formation. {% endblock %} "/>
        {% set url = app.request.uri %}

        {% set urlRegex = '/^http:\/\//' %}
        {% set httpsUrl = url|replace([urlRegex], 'https://') %}

        {% if canonical is defined %}
            <meta property="og:url" content="{{ httpsUrl }}">
            <link rel="canonical" href="{{ httpsUrl }}" />
        {% else %}
            <meta property="og:url" content="{{ httpsUrl }}">
            <link rel="canonical" href="{{ httpsUrl }}" />
        {% endif %}
        <meta name="viewport" content="width=device-width, initial-scale=1.0">


        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
        <link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet">
        <link href="{{ asset('carriereIndustrie/Front/css/bootstrap-icons.css')}}" rel="stylesheet"  />
        <link href="{{ asset('carriereIndustrie/Front/css/bootstrap.min.css')}}" rel="stylesheet"  />
        <link href="{{ asset('carriereIndustrie/Front/css/magnific-popup.css')}}" rel="stylesheet"  />
        <link href="{{ asset('carriereIndustrie/Front/css/styles.css')}}" rel="stylesheet" type="text/css" />
        <link href="{{ asset('carriereIndustrie/Front/css/france-map.css')}}" rel="stylesheet" type="text/css" />
        {% if jsonLD is defined %}
            <script type="application/ld+json">
			{{ jsonLD|json_encode(constant('JSON_PRETTY_PRINT'))|raw }}
		</script>
        {% endif %}
        {% if breadcrumb is defined %}
            <script type="application/ld+json">
			{{   breadcrumbToJsonLD(breadcrumb) | raw }}
		</script>
        {% endif %}

        {% block stylesheets %}{% endblock %}


    </head>
    <body>
        {% block body %}{% endblock %}
        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-M3EPXFM62N"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', 'G-M3EPXFM62N');
        </script>
        <script  src="{{ asset('carriereIndustrie/Front/js/jquery.min.js')}}"></script>
        <script  src="{{ asset('carriereIndustrie/Front/js/bootstrap.min.js')}}"></script>
        <script  src="{{ asset('carriereIndustrie/Front/js/click-scroll.js')}}"></script>
        <script  src="{{ asset('carriereIndustrie/Front/js/jquery.magnific-popup.min.js')}}"></script>
        <script  src="{{ asset('carriereIndustrie/Front/js/magnific-popup-options.js')}}"></script>
        <script  src="{{ asset('carriereIndustrie/Front/js/custom.js')}}"></script>
    </body>
</html>
