<style>
    .dropdown-emploi:hover .dropdown-menu {
        display: block;
    }
</style>
{% set current_path = app.request.attributes.get('_route') %}
<nav class="navbar navbar-expand-lg "style="position:fixed; z-index: 999;">
    <div class="container">

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>


        <div class="collapse navbar-collapse w-100 ms-auto" id="navbarNav">
            <ul class="navbar-nav " style="margin-left:0; justify-content:space-around;align-items: center; width:100%;">
                <div class="h-flex-responsive">
                    <li class="nav-item">
                        <a class="nav-link {% if current_path == 'app_Ind_accueil' %}active{% endif %}" href="{{ path('app_Ind_accueil') }}">Accueil</a>
                    </li>

                    <div class="dropdown dropdown-conseil">
                        <li class="nav-item">
                            <a class="nav-link {% if current_path starts with 'app_Ind_candidat_search' %}active{% endif %} " href="{{ path('app_Ind_candidat_search') }}" >Emploi</a>
                        </li>
                    </div>

                    <li class="nav-item">
                        <a class="nav-link  {% if current_path == 'app_Ind_entreprises' %}active{% endif %}" href="{{ path('app_Ind_entreprises') }}">Entreprises</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if current_path == 'app_Ind_actualites' %}active{% endif %}" href="{{ path('app_Ind_actualites') }}">Actualités</a>
                    </li>
                    <div class="dropdown dropdown-conseil">
                        <li class="nav-item">
                            <a class="nav-link  {% if current_path == 'app_Ind_conseils' %}active{% endif %} " href="{{ path('app_Ind_conseils') }}">Conseils</a>
                        </li>
                        <div class="dropdown-content content-conseil"style="left: 0;">
                            <a href="{{ path('app_Ind_conseilsCV') }}">Le CV</a>
                            <a href="{{ path('app_Ind_conseilsmotivation') }}">La lettre de motivation</a>
                            <a href="{{ path('app_Ind_conseilsembauche') }}">L'entretien d'embauche</a>
                            <a href="{{ path('app_Ind_conseilsrechercheEmploi') }}">La recherche d'emploi</a>

                        </div>
                    </div>
                </div>
                <li class="nav-item m-0 p-0 pt-2 nav-logo-lg">
                    <a  href="{{ path('app_Ind_accueil') }}" class="navbar-brand nav-link m-0 p-0 logo-lg" style="text-decoration: none;margin-right: 200px !important;"><img class="logo" style="min-height:100px; width:100%;" src="{{ asset('carriereIndustrie/Front/images/logoindustrie.png') }}"/></a>
                </li>
                <div>
                    {% if app.user is null %}

                        <div class="dropdown dropdown-recruteur">
                            <li class="nav-item">
                                <a class="nav-link  click-scroll" href="{{ path('app_Ind_login') }}"><img class="nav-icon" alt="mdi-worker" src="{{ asset('carriereIndustrie/Front/images/mdi-worker.png')}}" />Candidats</a>
                            </li>
                            <div class="dropdown-content content-recruteur" style="left: 0;">
                                <a href="{{ path('app_Ind_login') }}">Connexion</a>
                                <a href="{{ path('app_Ind_register_candidat_e1') }}">Création de compte</a>
                            </div>
                        </div>
                        <div class="dropdown dropdown-candidat" style="float:right;">
                            <li class="nav-item">
                                <a class="nav-link click-scroll" href="javascript:void(0);" onclick="showSweetAlert(this)"> <!-- Pass the 'this' reference to the function -->
                                    <img class="nav-icon" alt="dashicons-businessman" src="{{ asset('carriereIndustrie/Front/images/dashicons-businessman.png')}}" />Recruteurs
                                </a>
                            </li>
                            <div class="dropdown-content content-candidat">
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Connexion</a>
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Création de compte</a>
                                <div class="px-3">
                                    <hr>
                                </div>
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Les offres proposées</a>
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Le réseau Direct Emploi</a>
                            </div>
                        </div>
                    {% else %}
                        <div class="dropdown dropdown-recruteur">
                            <li class="nav-item">
                                <a href="{{ path('app_Ind_espaceCandidat') }}" class="nav-link  click-scroll" ><img class="nav-icon" alt="worker" src="{{ asset('carriereIndustrie/Front/images/mdi-worker.png')}}" />{{ app.user.nom }} {{ app.user.prenom }}</a>
                            </li>
                            <div class="dropdown-content content-recruteur" style="left: 0;float:right;">
                                <a href="{{ path('app_Ind_espaceCandidat') }}">Mon Profil</a>
                                <a href="{{ path('app_Ind_espaceCandidatprofilcv') }}">Mon CV</a>
                                <a href="{{ path('app_Ind_espaceCandidatprofiloffre') }}">Mes offres</a>
                                <a href="{{ path('app_Ind_espaceCandidatprofilmail') }}">Mes alertes mail</a>
                                {% for role in app.user.roles %}
                                    {% if role == "ROLE_ADMIN" %}
                                        <a href="{{ path('app_Ind_admin') }}">Espace Admin</a>
                                    {% endif %}
                                {% endfor %}
                                <div class="px-3">
                                    <hr class="py-0 my-1">
                                </div>
                                <a href="{{ path('app_Ind_logout') }}" class="text-danger">Se deconnecter</a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </ul>
            <!--
                            <div class="d-lg-flex align-items-center d-none ms-auto">
                                <i class="navbar-icon bi-telephone-plus me-3"></i>
                                <a class="custom-btn btn" href="#section_5">
                                    ************
                                </a>
                            </div>
            -->
        </div>
    </div>
</nav>