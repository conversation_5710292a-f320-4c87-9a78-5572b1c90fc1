{% extends 'carriereIndustrie/base.html.twig' %}
{% block title %}Se connecter{% endblock %}

{% block body %}

<div class="is-sticky">
    {% block head %}
    {% include 'carriereIndustrie/header.html.twig' %}
{% endblock %}

    <section class="update-profil-header d-flex justify-content-center align-items-center" id="">
        <h1 class="text-center text-uppercase mb-0" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#A5312E"> Formulaire de mise à jour </span>des données personnelles</h1>
    </section>
    <section class="login-form">


        <div class="container">
            {{ form_start(EditCompte) }}
            {{ form_row(EditCompte._token) }}
                <div class="info-section">

                    <h6>Votre identifiant et mot de passe</h6>
                    <hr>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputEmail">Login</label>
                                {{ form_widget(EditCompte.login,{ 'attr': {'placeholder': 'Login', 'value': user.getLogin()} })}}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputEmail">Email</label>
                                {{ form_widget(EditCompte.email,{ 'attr': {'placeholder': 'Email', 'value': user.getEmail()} })}}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputPassword">Nouveau Mot de passe</label>
                                {{ form_widget(EditCompte.passCrypt.first,{ 'attr': {'placeholder': 'Nouveau mot de passe'} })}}
                                <small id="passwordHelp" class="form-text text-muted">Votre mot de passe doit contenir au moins 6 caractéres, 1 majuscule et 1 chiffre.</small>

                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputPasswordConfirm">Confirmation mot de passe</label>
                                {{ form_widget(EditCompte.passCrypt.second,{ 'attr': {'placeholder': 'Nouveau mot de passe'} })}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="info-section">

                    <h6>Votre état civil</h6>
                    <hr>
                    <div class="row">
                        <div class="col-lg-4 col-sm-12">
                            <div class="form-group">

                                {{ form_row(EditCompte.user_civilite_id_user_civilite ,{'value':  user.getUserCiviliteIdUserCivilite()}) }}
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-12">
                            <div class="form-group">
                                <label for="inputNom">Nom</label>
                                {{ form_widget(EditCompte.nom,{ 'attr': {'placeholder': 'Nom', 'value': user.nom()} })}}
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-12">
                            <div class="form-group">
                                <label for="inputPrenom">Prénom</label>
                                {{ form_widget(EditCompte.prenom,{ 'attr': {'placeholder': 'Prénom', 'value': user.prenom()} })}}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputAdresse">Adresse</label>
                                {{ form_widget(EditCompte.adresse1,{ 'attr': {'placeholder': 'Adresse', 'value': user_adresse.adresse1()} })}}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputComplement">Complément d'adresse</label>
                                {{ form_widget(EditCompte.complement_adresse,{ 'attr': {'placeholder': 'Complément d\'adresse', 'value': user_adresse.adresse2()} })}}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputPostal">Code Postal</label>
                                {{ form_widget(EditCompte.code_postal,{ 'attr': {'placeholder': 'Code postal', 'value': user_adresse.codePostal()} })}}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputVille">Ville</label>
                                {{ form_widget(EditCompte.ville,{ 'attr': {'placeholder': 'Ville', 'value': user_adresse.nomVille()} })}}
                            </div>
                        </div>
                    </div>
                    <div class="row">

                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="paysFormControl">Pays</label>
                                {{ form_widget(EditCompte.pays,{ 'separator':" ---------- " })}}
                            </div>
                        </div>
                    </div>

                    <div class="row">

                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputPassword">Telephone portable</label>
                                {{ form_widget(EditCompte.telephone,{ 'attr': {'placeholder': 'Téléphone Portable', 'value': user.telephone()} })}}
                            </div>
                        </div>
                    </div>


                </div>
                <div class="info-section">

                    <h6>Votre domaine d'activité et votre formation
                    </h6>
                    <hr>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="activiteFormControl">Domaine d'activité</label>
                                {{ form_widget(
                                    EditCompte.domaine_activite,
                                    {
                                        'attr': {'class': 'domaine-activite-liste form-control'},
                                        'label': false,
                                        'value': situation.offListeSecteurActiviteIdSecteurActivite()
                                    }
                                ) }}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="metierFormControl">Nom du métier</label>
                                {{ form_widget(EditCompte.metier_metier, {'attr': {'class': 'metier-metier-liste form-control'},'label': false,'value': situation.idMetier()}) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="formationFormControl">Niveau de formation</label>
                                {{ form_widget(EditCompte.formation_level, {
                                    'attr': {'class': 'formation-level-liste form-control'},
                                    'label': false,
                                    'value': situation.offListeTypeFormationIdTypeFormation()
                                }) }}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="metierFormControl">Établissement / Spécialité</label>
                                {{ form_widget(EditCompte.ec_ecole, {
                                    'attr': {'class': 'ec-ecole-liste form-control'},
                                    'label': false,
                                    'value': situation.getEcEcoleIdEcole()
                                }) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        {#
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputDiplome">Nom du diplôme</label>
                                {{ form_widget(EditCompte.nom_diplome, {'attr': {'placeholder': "Nom du diplome", 'value': (situation.nomDiplome() == '0' ? '' : situation.nomDiplome())}})}}
                            </div>
                        </div>
                        #}
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputDiplome">Année d'obtention du diplôme</label>
                                {{ form_widget(EditCompte.anneeObtentionDiplome, {'attr': {'placeholder': "Année d'obtention", 'value': (situation.anneeObtentionDiplome() == '0' ? '' : situation.anneeObtentionDiplome())}})}}                            </div>
                        </div>
                    </div>
                </div>
                <div class="info-section">

                    <h6>Votre Situation actuelle</h6>
                    <hr>
                    <div class="row">
                        {#
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputPostal">Poste actuel (ou dernier poste)</label>
                                {{ form_widget(EditCompte.poste_actuel,{ 'attr': {'placeholder': 'Intitulé du poste', 'value': (situation.posteActuel() == '0' ? '' : situation.posteActuel())} })}}
                            </div>
                        </div>
                        #}
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="activitéFormControl">Secteur d'activité du poste</label>
                                {{ form_widget(EditCompte.domaine_activite_situation, {
                                'attr': {'class': 'form-control'},
                                'label': false,
                                'required': false,
                                }) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="salaireFormControl">Salaire du poste</label>
                                {{ form_widget(EditCompte.remuneration)}}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="activitéFormControl">Activité</label>
                                {{ form_widget(EditCompte.activite)}}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="expFormControl">Nombre d'années d'expérience</label>
                                {{ form_widget(EditCompte.experience,{ 'attr': {'placeholder': 'Sélectionnez votre expérience'} })}}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="dispoFormControl">Disponibilité</label>
                                {{ form_widget(EditCompte.disponibilite,{ 'attr': {'placeholder': 'Sélectionnez votre disponibilité'} })}}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="mobilFormControl">Mobilité</label>
                                {{ form_widget(EditCompte.mobilite,{ 'attr': {'placeholder': 'Sélectionnez votre mobilité'} })}}

                            </div>
                        </div>
                    </div>
                </div>
                <div class="info-section">

                    <h6>Vos compétences</h6>
                    <hr>
                    <div class="row">
                        <div class="col-lg-12 col-sm-12">
                            <div class="form-group">
                                <label for="inputcompétences">Liste des compétences</label>
                                {{ form_widget(EditCompte.competence,{ 'attr': {'placeholder': 'Word, powerpoint, excel', 'style' : "width:882px", 'value': competences ? competences.getListeCompetences() : ''} })}}                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="lang1FormControl">Langue étrangère 1</label>
                                {{ form_widget(EditCompte.langue1,{ 'attr': {'placeholder': 'Sélectionnez la langue'} })}}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="niveau1FormControl">Niveau</label>
                                {{ form_widget(EditCompte.level1,{ 'attr': {'placeholder': 'Sélectionnez votre niveau'} })}}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="lang2FormControl">Langue étrangère 2</label>
                                {{ form_widget(EditCompte.langue2,{ 'attr': {'placeholder': 'Sélectionnez la langue'} })}}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="niveau2FormControl">Niveau</label>
                                {{ form_widget(EditCompte.level2,{ 'attr': {'placeholder': 'Sélectionnez votre niveau'} })}}
                            </div>
                        </div>
                    </div>

                    <div class="d-flex flex-row justify-content-start">
                        {{ form_label(EditCompte.permis, 'Permis B :', {'label_attr': {'class': 'col-form-label d-inline pt-0'}}) }}

                        <div class="form-check mx-2">
                            {{ form_widget(EditCompte.permis[0], {'attr': {'class': 'form-check-input'}}) }}
                            {{ form_label(EditCompte.permis[0], 'Non', {'label_attr': {'class': 'form-check-label'}}) }}
                        </div>
                        <div class="form-check mx-2">
                            {{ form_widget(EditCompte.permis[1], {'attr': {'class': 'form-check-input'}}) }}
                            {{ form_label(EditCompte.permis[1], 'Oui', {'label_attr': {'class': 'form-check-label'}}) }}
                        </div>
                    </div>

                </div>
            {% if user_veille|length %}

                <div class="info-section">

                    <h6>Votre recherche d'emploi</h6>
                    <hr>
                    <div class="row">
                        {#
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputPostal">Type de poste recherché</label>
                                {{ form_widget(EditCompte.poste_souhaite,{ 'attr': {'placeholder': 'Type de poste', 'value': user_veille|length ? user_veille.getTypeContratSerialized : ''} })}}
                            </div>
                        </div>
                        #}
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="expFormControl">Type de contrat souhaité</label>
                                {{ form_widget(EditCompte.contrat_souhaite, {
                                    'attr': {
                                        'placeholder': 'Sélectionnez un type de contrat'

                                    }
                                }) }}
                            </div>
                        </div>

                    </div>
                    <div class="row">

                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="dispoFormControl">Salaire souhaité</label>
                                {{ form_widget(EditCompte.remuneration_souhaitee,{ 'attr': {'placeholder': 'Sélectionnez votre salaire souhaité'} })}}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                <div class="info-section">

                    <h6>Paramètres de confidentialité</h6>
                    <hr>
                    <div class="d-flex flex-row flex-wrap justify-content-between mb-2">
                        {{ form_label(EditCompte.profil_visible, null, {'label_attr': {'class': 'col-form-label d-inline pt-0'}}) }}
                        <div class="d-flex flex-row">
                            <div class="form-check mx-2">
                                {{ form_widget(EditCompte.profil_visible[1], {'attr': {'class': 'form-check-input'}}) }}
                                {{ form_label(EditCompte.profil_visible[1], 'Oui', {'label_attr': {'class': 'form-check-label'}}) }}
                            </div>
                            <div class="form-check mx-2">
                                {{ form_widget(EditCompte.profil_visible[0], {'attr': {'class': 'form-check-input'}}) }}
                                {{ form_label(EditCompte.profil_visible[0], 'Non', {'label_attr': {'class': 'form-check-label'}}) }}
                            </div>
                        </div>
                    </div>
                    {% if user_cv|length %}
                        <div class="d-flex flex-row flex-wrap justify-content-between mb-2">
                            {{ form_label(EditCompte.profil_anonyme, null, {'label_attr': {'class': 'col-form-label d-inline pt-0'}}) }}
                            <div class="d-flex flex-row">
                                <div class="form-check mx-2">
                                    {{ form_widget(EditCompte.profil_anonyme[1], {'attr': {'class': 'form-check-input'}}) }}
                                    {{ form_label(EditCompte.profil_anonyme[1], 'Oui', {'label_attr': {'class': 'form-check-label'}}) }}
                                </div>
                                <div class="form-check mx-2">
                                    {{ form_widget(EditCompte.profil_anonyme[0], {'attr': {'class': 'form-check-input'}}) }}
                                    {{ form_label(EditCompte.profil_anonyme[0], 'Non', {'label_attr': {'class': 'form-check-label'}}) }}
                                </div>
                            </div>
                        </div>
                        <div class="d-flex flex-row flex-wrap justify-content-between mb-2">
                            {{ form_label(EditCompte.cv_visible, null, {'label_attr': {'class': 'col-form-label d-inline pt-0'}}) }}
                            <div class="d-flex flex-row">
                                <div class="form-check mx-2">
                                    {{ form_widget(EditCompte.cv_visible[1], {'attr': {'class': 'form-check-input'}}) }}
                                    {{ form_label(EditCompte.cv_visible[1], 'Oui', {'label_attr': {'class': 'form-check-label'}}) }}
                                </div>
                                <div class="form-check mx-2">
                                    {{ form_widget(EditCompte.cv_visible[0], {'attr': {'class': 'form-check-input'}}) }}
                                    {{ form_label(EditCompte.cv_visible[0], 'Non', {'label_attr': {'class': 'form-check-label'}}) }}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <div class="text-center">
                    <button type="submit" class="btn mb-2 px-4 button-black">Enregistrer</button>

                </div>
            {{ form_end(EditCompte, {'render_rest': false}) }}

        </div>
    </section>

    {% block footer %}
        {% include 'carriereIndustrie/footer.html.twig' %}
    {% endblock %}
    <script>
        var ecoleListe = document.querySelector('.ec-ecole-liste');
        var formationLevelListe = document.querySelector('.formation-level-liste');
        var ecoleIdValue = "{{ situation.getEcEcoleIdEcole() }}";
        var sessionFormationValue = "{{ situation.getEcSessionFormationIdSessionFormation()  }}";

        // Fonction pour mettre à jour la liste des écoles
        function updateEcoleList(selectedFormationLevel) {
            fetch('/ajax/get-ecoles/' + selectedFormationLevel)
                .then(response => response.json())
                .then(data => {
                    ecoleListe.innerHTML = ''; // Effacer les options existantes
                    console.log(selectedFormationLevel);

                    data.forEach(function (ecole) {
                        var option = document.createElement('option');
                        option.value = ecole.id;
                        option.text = ecole.nom_ecole;
                        ecoleListe.add(option);

                        // Set default selected option based on ecoleIdValue
                        if (ecole.id == ecoleIdValue) {
                            option.selected = true;
                        }
                    });
                })
                .catch(error => {
                    console.error('Une erreur s\'est produite lors de la récupération des écoles:', error);
                });
        }



            // Appeler la fonction pour mettre à jour la liste des écoles au chargement de la page
        updateEcoleList({{ situation.offListeTypeFormationIdTypeFormation() }});

        formationLevelListe.addEventListener('change', function () {
            var selectedFormationLevel = this.value;

            if (selectedFormationLevel !== 23) {
                updateEcoleList(selectedFormationLevel);
            }
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var domaineActiviteListe = document.querySelector('.domaine-activite-liste');
            var metierMetierListe = document.querySelector('.metier-metier-liste');
            var domaineActiviteId = {{ situation.offListeSecteurActiviteIdSecteurActivite() }};
            var metierid = {{ situation.idMetier() }};

            // Fonction pour mettre à jour la liste des métiers
            function updateMetierMetierListe(domaineId, selectedMetierId) {
                // Simulate fetching data from the server
                fetch('/ajax/get-metiers/' + domaineId)
                    .then(response => response.json())
                    .then(data => {
                        // Clear the options
                        metierMetierListe.innerHTML = '';

                        // Update the options
                        data.forEach(function (metier) {
                            var option = document.createElement('option');
                            option.value = metier.id;
                            option.text = metier.libelle_metier;
                            metierMetierListe.add(option);

                            // Pré-selectionne le métier si l'ID correspond
                            if(selectedMetierId && metier.id === selectedMetierId) {
                                option.selected = true;
                            }
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching metiers:', error);
                    });
            }

            // Pré-sélection du domaine d'activité et du métier lors du chargement de la page
            if(domaineActiviteId && metierid) {
                domaineActiviteListe.value = domaineActiviteId;
                updateMetierMetierListe(domaineActiviteId, metierid);
            }

            // Event listener pour la modification du domaine d'activité
            domaineActiviteListe.addEventListener('change', function () {
                updateMetierMetierListe(this.value, null);
            });
        });
    </script>
</div>
{% endblock %}