{% extends 'carriereIndustrie/base.html.twig' %}
{% set lettre = app.request.query.get('lettre') %}

    {% set title_seo = "Offres Emploi par Métier dans le Domaine Industrie | Carriere Industrie" %}
    {% set description_seo = "Consultez les Dernières Offres d'Emploi par métier dans le domaine Industrie sur Carriere Industrie. ✓ Postulez en ligne dès Maintenant ✓ Candidature Simple & Rapide !" %}

{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .alphabet-filter {
            text-align: center;
            padding: 10px;
            margin: 10px;
        }

        .alphabet-filter a {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border: 0.5px solid #ddd; /* Couleur de la bordure */
            border-radius: 4px; /* Coins arrondis */
            text-decoration: none; /* Supprime le soulignement des liens */
            color: #333; /* Couleur du texte */
            font-weight: bold; /* Rend le texte en gras */
            transition: background-color 0.3s, color 0.3s; /* Animation au survol */
        }

        .alphabet-filter a:hover {
            background-color: #A5312E; /* Couleur de fond au survol */
            color: #fff; /* Couleur du texte au survol */
        }

    </style>
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereIndustrie/header.html.twig' %}
        {% endblock %}
        <section class="emploi-header d-flex justify-content-center align-items-center " id="">
            <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#A5312E">Offres d’emploi</span> par métier dans le domaine Industrie</h1>
        </section>
        <br>
        <section class="section-embauche">
            <a href="#" onclick="redirectToBTP('BTP')"><h6 class="text-center text-uppercase mb-5"><span style="color: #A5312E;">Offres d'emploi  </span>Industrie</h6></a>
            <a href="{{ path('app_Ind_ParcourirlesoffresdemploiAllVille') }}"><p class="text-center mb-5" style="font-weight: bold;" ><span style="color: #A5312E;">Voir les villes </span>pour le domaine Industrie</p></a>
            <div class="search-container text-center mb-5">
                <input type="text" id="searchBar" placeholder="Rechercher..." style="width: 50%; padding: 10px; margin-top: 20px;">
            </div>
            <div class="container">
                <div class="row offres-list">
                    {% set taille_liste = metiers|length %}
                    {% set taille_groupe = (taille_liste / 3)|round(0, 'ceil') %}

                    {% for i in 0..2 %}
                        <div class="col-lg-4 col-md-6">
                            {% for metier in metiers|slice(i * taille_groupe, taille_groupe) %}
                                <div style="margin-bottom: 20px;">
                                    <ul style="list-style: none; padding: 0;">
                                        <li onclick="redirectTo('{{ metier | replace({"Offres d'emploi ": ''}) | escape('js') }}')">

                                            <a href="#" style="font-weight: bold;">{{ metier }}</a>
                                        </li>
                                    </ul>
                                    <p style="margin-top: 10px;">
                                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #A5312E;font-size: 7px;"></i>
                                        <a href="{{ path('app_Ind_ParcourirlesoffresdemploiMetierParVille', {'metier': (metier|replace({'Offres d\'emploi ': ''}))|url_encode}) }}" style="font-weight: normal; font-size: 14px;">Voir les villes pour le métier {{ metier | replace({"Offres d'emploi ": ''}) }}</a>

                                    </p>
                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        </section>




        <!-- Include jQuery and Bootstrap JS from CDNJS -->
        <script>
            function redirectTo(value) {
                // Encode the value to include it in the URL
                const url = `https://www.carriere-industrie.com/Ind/lists-offre/?contrat=&localisation=&localisation_field=&q=${value}`;
                // Redirect to the constructed URL
                window.location.href = url;
            }
            function redirectToBTP(value) {
                // Encode the value to include it in the URL
                const url = `https://www.carriere-industrie.com/Ind/lists-offre/?contrat=&localisation=&localisation_field=&q=${value}`;
                // Redirect to the constructed URL
                window.location.href = url;
            }
        </script>
        <script>
            document.getElementById('searchBar').addEventListener('input', function(e) {
                var searchValue = e.target.value.toLowerCase();
                var offres = document.querySelectorAll('.offres-list div');

                offres.forEach(function(offre) {
                    var villeText = offre.innerText.toLowerCase();
                    if (villeText.includes(searchValue)) {
                        offre.style.display = '';
                    } else {
                        offre.style.display = 'none';
                    }
                });
            });
        </script>
        {% block footer %}
            {% include 'carriereIndustrie/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}

