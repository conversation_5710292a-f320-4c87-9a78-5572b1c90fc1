{% extends 'carriereIndustrie/base.html.twig' %}
{% set lettre = app.request.query.get('lettre') %}

    {% set title_seo = "Offres Emploi par Métier dans le Domaine Industrie | Carriere Industrie" %}
    {% set description_seo = "Consultez les Dernières Offres d'Emploi par métier dans le domaine Industrie sur Carriere Industrie. ✓ Postulez en ligne dès Maintenant ✓ Candidature Simple & Rapide !" %}

{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .alphabet-filter {
            text-align: center;
            padding: 10px;
            margin: 10px;
        }

        .alphabet-filter a {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border: 0.5px solid #ddd; /* Couleur de la bordure */
            border-radius: 4px; /* Coins arrondis */
            text-decoration: none; /* Supprime le soulignement des liens */
            color: #333; /* Couleur du texte */
            font-weight: bold; /* Rend le texte en gras */
            transition: background-color 0.3s, color 0.3s; /* Animation au survol */
        }

        .alphabet-filter a:hover {
            background-color: #A5312E; /* Couleur de fond au survol */
            color: #fff; /* Couleur du texte au survol */
        }

    </style>
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereIndustrie/header.html.twig' %}
        {% endblock %}
        <section class="emploi-header d-flex justify-content-center align-items-center " id="">
            <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#A5312E">Offres d’emploi</span> par ville pour le métier {{ metier }}</h1>
        </section>
        <br>
        <section class="section-embauche">
            <h6 class="text-center text-uppercase mb-5"><span style="color: #A5312E;">Offres d'emploi </span>{{ metier }}</h6>
            <div class="search-container text-center mb-5">
                <input type="text" id="searchBar" placeholder="Rechercher..." style="width: 50%; padding: 10px; margin-top: 20px;">
            </div>
            <div class="container">
                <div class="row offres-list">
                    {% set taille_liste = offresEmploi|length %}
                    {% if taille_liste > 0 %}
                        {% set taille_groupe = (taille_liste / 3)|round(0, 'ceil') %}
                        {% for i in 0..2 %}
                            <div class="col-lg-4 col-md-6">
                                {% for offre in offresEmploi|slice(i * taille_groupe, taille_groupe) %}
                                    <div style="margin-bottom: 20px;">
                                        <ul style="list-style: none; padding: 0;">
                                            <li onclick="redirectTo('{{ offre.ville | escape('js') }}', '{{ offre.metier | escape('js') }}')">
                                                <a href="#" style="font-weight: bold;">Offre d'emploi {{ offre.metier }} {{ offre.ville }}</a>
                                            </li>
                                        </ul>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center">
                            <p>Pas d'offres disponible pour le moment.</p>
                            <a href="{{ path('app_Ind_candidat_search') }}" class="btn btn-primary">Voir toutes les offres</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </section>





        <!-- Include jQuery and Bootstrap JS from CDNJS -->
        <script>
            function redirectTo(localisation, value) {
                const encodedLocalisation = encodeURIComponent(localisation);
                const encodedValue = encodeURIComponent(value);

                const url = `https://www.carriere-industrie.com/Ind/lists-offre/?contrat=&localisation=${encodedLocalisation}&localisation_field=&q=${encodedValue}`;
                window.location.href = url;
            }

        </script>
        <script>
            document.getElementById('searchBar').addEventListener('input', function(e) {
                var searchValue = e.target.value.toLowerCase();
                var offres = document.querySelectorAll('.offres-list div');

                offres.forEach(function(offre) {
                    var villeText = offre.innerText.toLowerCase();
                    if (villeText.includes(searchValue)) {
                        offre.style.display = '';
                    } else {
                        offre.style.display = 'none';
                    }
                });
            });
        </script>
        {% block footer %}
            {% include 'carriereIndustrie/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}

