{% extends 'carriereIndustrie/base.html.twig' %}

{% if lettre %}
    {% set title_seo = "Moteur de recherche avancée d'offres un emploi en interim | Carriere BTP | pour les métiers qui commencent par la lettre '" ~ lettre ~ "'" %}
    {% set description_seo = "Vous recherchez une offre d'emploi BTP en interim ? Retrouvez les offres un emploi sur Carrière BTP. Recherchez toutes les annonces d'emploi : CDD, CDI, alternance, stage, intérim...| pour les métiers qui commencent par la lettre '" ~ lettre ~ "'" %}
{% else %}
    {% set title_seo = "Moteur de recherche avancée d'offres un emploi en interim | Carriere BTP | pour toutes les métiers" %}
    {% set description_seo = "Vous recherchez une offre d'emploi BTP en interim ? Retrouvez les offres un emploi sur Carrière BTP. Recherchez toutes les annonces d'emploi : CDD, CDI, alternance, stage, intérim... | pour toutes les métiers" %}
{% endif %}

{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}

{% block body %}
    <style>
        .alphabet-filter {
            text-align: center;
            padding: 10px;
            margin: 10px;
        }
        .alphabet-filter a {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border: 0.5px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
            font-weight: bold;
            transition: background-color 0.3s, color 0.3s;
        }
        .alphabet-filter a:hover {
            background-color: #A5312E;
            color: #fff;
        }
    </style>

    <div class="is-sticky">
        {% block head %}
            {% include 'carriereIndustrie/header.html.twig' %}
        {% endblock %}

        <section class="emploi-header d-flex justify-content-center align-items-center">
            <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#A5312E">Parcourir </span>les emplois</h1>
        </section>

        <br>

        <section class="section-embauche">
            <h6 class="text-center text-uppercase mb-5"><span style="color: #A5312E;">Annonce </span> par Métier</h6>
            <div class="container">
                <div class="alphabet-filter">
                    {% for lettre in range('A', 'Z') %}
                        <a href="{{ path('app_BTP_Parcourirlesoffresdemploi', {'lettre': lettre}) }}">{{ lettre }}</a>
                    {% endfor %}
                </div>

                <br><br>

                <div class="row offres-list">
                    {% for groupe in metiersParGroupe %}
                        <div class="col-lg-4 col-md-6">
                            <ul style="list-style: none; padding: 0;">
                                {% for metier in groupe %}
                                    <li onclick="redirectTo('{{ metier | replace({'Emploi en intérim ': ''}) | escape('js') }}')">
                                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #A5312E;font-size: 7px;"></i>
                                        <a href="#">{{ metier }}</a>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </section>

        <script>
            function redirectTo(value) {
                const url = `https://www.carriere-industrie.com/Ind/lists-offre/?contrat=4&localisation=&localisation_field=&q=${value}`;
                window.location.href = url;
            }
        </script>

        {% block footer %}
            {% include 'carriereIndustrie/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}