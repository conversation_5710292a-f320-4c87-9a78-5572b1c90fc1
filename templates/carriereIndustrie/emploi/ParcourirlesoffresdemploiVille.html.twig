{% extends 'carriereIndustrie/base.html.twig' %}
{% set lettre = app.request.query.get('lettre') %}

    {% set title_seo = "Offres Emploi par Ville en France | Carriere Industrie" %}
    {% set description_seo = "Consultez les Dernières Offres d'Emploi par ville en France sur Carriere Industrie. ✓ Postulez en ligne dès Maintenant ✓ Candidature Simple & Rapide !" %}

{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .alphabet-filter {
            text-align: center;
            padding: 10px;
            margin: 10px;
        }

        .alphabet-filter a {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border: 0.5px solid #ddd; /* Couleur de la bordure */
            border-radius: 4px; /* Coins arrondis */
            text-decoration: none; /* Supprime le soulignement des liens */
            color: #333; /* Couleur du texte */
            font-weight: bold; /* Rend le texte en gras */
            transition: background-color 0.3s, color 0.3s; /* Animation au survol */
        }

        .alphabet-filter a:hover {
            background-color: #A5312E; /* Couleur de fond au survol */
            color: #fff; /* Couleur du texte au survol */
        }

    </style>
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereIndustrie/header.html.twig' %}
        {% endblock %}
        <section class="emploi-header d-flex justify-content-center align-items-center " id="">
            <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#A5312E">Offres d’emploi</span> par ville en France</h1>
        </section>
        <br>
        <section class="section-embauche">
            <h6 class="text-center text-uppercase mb-5"><span style="color: #A5312E;">Villes </span>les plus recherchées</h6>
            <div class="container">
                <div class="row offres-list">
                    {% set taille_liste = topvilles|length %}
                    {% set taille_groupe = (taille_liste / 3)|round(0, 'ceil') %}

                    {% for i in 0..2 %}
                        <div class="col-lg-4 col-md-6">
                            {% for topville in topvilles|slice(i * taille_groupe, taille_groupe) %}
                                <div style="margin-bottom: 20px;">
                                    <ul style="list-style: none; padding: 0;">
                                        <li onclick="redirectTo('{{ topville | replace({"Offres d'emploi ": ''}) | escape('js') }}')">

                                            <a href="#" >Offres d'emploi {{ topville }}</a>
                                        </li>
                                    </ul>

                                </div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        </section>

        <section class="section-embauche">
            <h6 class="text-center text-uppercase mb-5"><span style="color: #A5312E;">Offres d'emploi </span>par localité</h6>
            <div class="container">
                <div class="row offres-list">
                    {% for id_region, regionInfo in departementsByRegions %}
                    <div class="col-lg-4 col-md-6">
                        <div style="margin-bottom: 20px;">
                            <a href="#" onclick="redirectTo('{{ regionInfo.libelle }}')"><h6 >Offres d'emploi <span style="color: #A5312E">{{ regionInfo.libelle }}</span> </h6></a>
                            <ul style="list-style: none; padding: 0;">
                                {% for departement in regionInfo.departements %}
                                    <li>
                                        <a href="{{ path('app_Ind_ParcourirlesoffresdemploiVilleParRegion', {'departementid': departement.getIdDepartement()}) }}">Voir les villes en {{ departement.libelle }}</a>
                                    </li>
                                {% endfor %}
                            </ul>
                            {% if not loop.last %}<hr>{% endif %} <!-- Ajoutez une séparation sauf pour la dernière région -->
                        </div>
                    </div>
                    {% if loop.index is divisible by(3) and not loop.last %}
                </div> <!-- Fin de la rangée courante -->
                <div class="row offres-list"> <!-- Commencez une nouvelle rangée -->
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </section>






        <!-- Include jQuery and Bootstrap JS from CDNJS -->
        <script>
            function redirectTo( value) {
                const encodedValue = encodeURIComponent(value);
                const url = `https://www.carriere-industrie.com/Ind/lists-offre/?contrat=&localisation=${encodedValue}&localisation_field=&q=`;
                window.location.href = url;
            }

        </script>
        <script>
            document.getElementById('searchBar').addEventListener('input', function(e) {
                var searchValue = e.target.value.toLowerCase();
                var offres = document.querySelectorAll('.offres-list div');

                offres.forEach(function(offre) {
                    var villeText = offre.innerText.toLowerCase();
                    if (villeText.includes(searchValue)) {
                        offre.style.display = '';
                    } else {
                        offre.style.display = 'none';
                    }
                });
            });
        </script>
        {% block footer %}
            {% include 'carriereIndustrie/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}

