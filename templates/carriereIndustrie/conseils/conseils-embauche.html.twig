{% extends 'carriereIndustrie/base.html.twig' %}
{% set title = "Nos conseils recrutement et emploi entretien d'embauche | Carriere BTP" %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}


{% set description_seo = "Maximisez vos chances de succès dans vos entretiens d'embauche dans le BTP avec Carriere BTP.
Nos conseils spécialisés couvrent tout, des techniques d'entretien spécifiques au BTP aux stratégies pour impressionner les recruteurs.
Apprenez comment mettre en avant vos compétences, gérer le stress de l'entretien, et naviguer dans les questions spécifiques au secteur.
Idéal pour les professionnels du BTP cherchant à se démarquer et sécuriser leur poste idéal." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}

{% block body %}
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereIndustrie/header.html.twig' %}
        {% endblock %}
        <section class="ajouter-cv-header" id="">
            <div class="container">
                <h1 class="text-center text-uppercase mb-3" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#A5312E">Conseils</span> L'entretien d'embauche</h1>
                <h6 class="text-center text-secondary">{{h1Title}}</h6>
                <hr>
                <p class="justified mb-0">{{intro.content|raw}}</p>
            </div>
        </section>
        <section class="conseils-cards">
            <div class="container">
                {% for info in infos %}
                <a href="{{ path('app_BTP_conseil_id', {'id': info.id}) }}">
                    <div class="card">
                        <div class="row">
                            <div class="col-lg-6 col-sm-12">
                                {% if info.getAdminImage() is defined and info.getAdminImage() != null %}
                                    {% set myImage = info.getAdminImage() %}
                                {% else %}
                                    {% set myImage = info.image %}
                                {% endif %}
                                <img class="img-fluid" src="{{myImage|replace({'\"': ''})}}" alt="{{info.titre}}"/>

                            </div>
                            <div class="col-lg-6">
                                <div class="vertical-center">
                                    <div>   <h6 class="mb-3">{{info.titre}}</h6>
                                        <p class="justified">{{info.description}}</p>
                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>
                </a>
                {% endfor %}
            </div>

        </section>
        <section class="section-embauche">
            <div class="container">
                <h6 class="text-uppercase text-center mb-4"><span style="color: #A5312E;">Un passeport </span>pour l'embauche</h6>
                <div class="row ">
                    <div class="col-lg-6 col-sm-12 mb-3">
                        <a href="{{ path('app_BTP_conseilsCV') }}">

                            <div class="w-100 info-button">
                                <p class="text-center button-title">Conseil CV</p>
                                <p class="text-center m-0">Tout savoir sur la conception d’un cv</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-6 col-sm-12 mb-3">
                        <a href="{{ path('app_BTP_conseilsmotivation') }}">

                            <div class="w-100 info-button">
                                <p class="text-center button-title">Conseils lettre de motivation</p>
                                <p class="text-center m-0">Les clés d'une lettre de motivation réussie</p>
                            </div>
                        </a>

                    </div>


                    <div class="col-lg-6 col-sm-12 mb-3">
                        <a href="{{ path('app_BTP_conseilsrechercheEmploi') }}">
                            <div class="w-100 info-button">
                                <p class="text-center button-title">Conseils recherche d’emploi</p>
                                <p class="text-center m-0">Comment faire une recherche d'emploi</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-6 col-sm-12  mb-3">

                        <a href="{{ path('app_BTP_conseilsembauche') }}">
                            <div class="w-100 info-button">
                                <p class="text-center button-title">Conseils entretien d’embauche</p>
                                <p class="text-center m-0">L'entretien : un face à face à préparer</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </section>
{% block footer %}
    {% include 'carriereIndustrie/footer.html.twig' %}
{% endblock %}
</div>
{% endblock %}