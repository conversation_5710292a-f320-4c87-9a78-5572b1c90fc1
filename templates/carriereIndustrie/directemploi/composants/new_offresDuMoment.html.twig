<div class="container offres_du_moment">
	{% if path == '/' %}
		{% set prefix = '/lists-offre/' %}
		<h4>Nos offres d'emplois classées</h4>
	{% elseif path == "/theme/offres-stage" %}
		{% set prefix = '../lists-offre/stage-' %}
		<h4>Nos offres de stages classées</h4>
	{% elseif path == '/theme/offres-alternance' %}
		{% set prefix = '../lists-offre/alternance-' %}
		<h4>Nos offres en alternance classées</h4>
	{% elseif path == "/theme/offre-emploi-jeune-diplome" %}
		{% set prefix = '../' %}
		<h4>Nos offres d'emplois jeune diplômé</h4>
	{% elseif path == "/theme/offres-emploi-interim" %}
		{% set prefix = '../lists-offre/interim-' %}
		<h4>Nos offres d'emplois en intérim</h4>
	{% elseif path == "/theme/offres-emploi-cabinet-de-recrutement" %}
		{% set prefix = '../lists-offre/cdi-' %}
		<h4>Nos offres d'emplois classées</h4>
	{% else %}
	{% set prefix = '/lists-offre/' %}
		<h4>Nos offres d'emplois classées</h4>
	{% endif %}
	<ul class="onglets_new">
		<li>
			<span class="onglet_active" id="onglet_secteur" onclick="showOffer('liste_offres', 'onglet_secteur')">Domaine d'activité</span>
		</li>
		<li>
			<span class="onglet_inactive" id="onglet_emploi" onclick="showOffer('liste_metiers', 'onglet_emploi')">Métier</span>
		</li>
		<li>
			<span class="onglet_inactive" id="onglet_region" onclick="showOffer('liste_regions', 'onglet_region')">Région</span>
		</li>
		<li>
			<span class="onglet_inactive" id="onglet_departement" onclick="showOffer('liste_departements', 'onglet_departement')">Département</span>
		</li>
		<li>
			<span class="onglet_inactive" id="onglet_ville" onclick="showOffer('liste_villes', 'onglet_ville')">Ville</span>
		</li>
	</ul>
	<div class="wrapper">
		<div class="listing" id="liste_offres">
			<ul class="liste">
				{% for offre in listeOffres %}
					{% set counter = ( counter | default(-1) ) + 1 %}
					{% if counter == 7 or counter == 14 or counter == 22 %}
					</ul>
					<ul class="liste">
					{% endif %}
					<li>
						<a href={{ prefix ~ "france-metiers-" ~ offre[0] ~ "-" ~ struct_home_search(offre[1]) }}>{{ offre[1]|replace({'/': "-"}) }}</a>

						{# <a href={{ "/candidatOffre/elk_search?mot_cle=" ~ stringtourl(offre[1]) ~ "&localisation=&id_type_contrat=" }}>{{ offre[1]|replace({'/': "-"}) }}</a> #}
					</li>
				{% endfor %}
			</ul>
		</div>
		<div class="listing" id="liste_metiers" style="display:none">
			<ul class="liste">
				{% for metier in listeMetiers %}
					{% set counter = ( counter | default(-1) ) + 1 %}
					{% if counter == 14 or counter == 28 or counter == 42 or counter == 57 %}
					</ul>
					<ul class="liste">
					{% endif %}
					<li>
						<a href={{ prefix ~ "france-metier-" ~ struct_home_search(metier.LibelleMetier) ~ "-secteurs" }}>{{ metier.LibelleMetier }}</a>
					</li>
				{% endfor %}
			</ul>
		</div>
		<div class="listing" id="liste_regions" style="display:none">
			<ul class="liste">
				{% for key, region in listeRegions %}
					{% set counter = ( counter | default(-1) ) + 1 %}
					{% if counter != 0 and counter % 7 == 0 %}
					</ul>
					<ul class="liste">
					{% endif %}
					<li>
					{% if region[1] != null %}
						<a href={{ prefix ~ struct_home_search(listeRegions[region[1]][0]) ~ "-metiers-secteurs" }}>{{ region[0] }}</a>
					{% else %}
						<a href={{ prefix ~ struct_home_search(region[0]) ~ "-metiers-secteurs" }}>{{ region[0] }}</a>
					{% endif %}
					</li>
				{% endfor %}
				<ul></div>
				<div class="listing" id="liste_departements" style="display:none">
					<ul class="liste">
						{% for departement in listeDepartements %}
							{% set counter = ( counter | default(-1) ) + 1 %}
							{% set array = [25, 51, 77, 103] %}
							{% if counter != 0 and counter in array %}
							</ul>
							<ul class="liste">
							{% endif %}
							{% set cp = departement.CodePostal|raw %}
							{% if cp|length == 1 %}
								{% set cp = cp ~ "0" %}
								{% set cp = cp|reverse %}
							{% endif %}
							<li>
								<a href={{ prefix ~ struct_home_search(departement.libelle) ~ "-metiers-secteurs" }}>{{ departement.libelle ~ " (" ~ cp ~ ")" }}</a>
							</li>
						{% endfor %}
					</ul>
				</div>
				<div class="listing" id="liste_villes" style="display:none">
					<ul class="liste">
						{% for ville in codePostal %}
						
							{% set counter = ( counter | default(-1) ) + 1 %}
							{% set array = [20, 40, 60, 80] %}
							{% if counter != 0 and counter in array %}
							</ul>
							<ul class="liste">
							{% endif %}

							<li>
								<a href={{ prefix ~ struct_home_search(ville.libelle) ~ "-(" ~ ville.codePostal ~ ")-metiers-secteurs" }}>{{ ville.libelle }}</a>
							</li>
						{% endfor %}
						
						<li>
							<a href={{ prefix ~ struct_home_search('Vierzon') ~ "-(18)-metiers-secteurs" }}>{{ "Vierzon" }}</a>
						</li>
					</ul>
				</div>
			</div>
		</div>


<div class="container_responsive offres_du_moment_responsive">
	{% if path == '/' %}
		{% set prefix = '/lists-offre/' %}
		<h4>Nos offres d'emplois classées</h4>
	{% elseif path == "/theme/offres-stage" %}
		{% set prefix = '../lists-offre/stage-' %}
		<h4>Nos offres de stages classées</h4>
	{% elseif path == '/theme/offres-alternance' %}
		{% set prefix = '../lists-offre/alternance-' %}
		<h4>Nos offres en alternance classées</h4>
	{% elseif path == "/theme/offre-emploi-jeune-diplome" %}
		{% set prefix = '../' %}
		<h4>Nos offres d'emplois jeune diplômé</h4>
	{% elseif path == "/theme/offres-emploi-interim" %}
		{% set prefix = '../lists-offre/interim-' %}
		<h4>Nos offres d'emplois en intérim</h4>
	{% else %}
	{% set prefix = '/lists-offre/' %}
		<h4>Nos offres d'emplois classées</h4>
	{% endif %}
	<ul class="onglets_new_responsive">
		<li>
			<span class="onglet_active_responsive" id="onglet_secteur_responsive" onclick="showOfferResponsive('liste_offres_responsive', 'onglet_secteur_responsive')">Domaine d'activité</span>
		</li>
		<li>
			<span class="onglet_inactive_responsive" id="onglet_emploi_responsive" onclick="showOfferResponsive('liste_metiers_responsive', 'onglet_emploi_responsive')">Métier</span>
		</li>
		<li>
			<span class="onglet_inactive_responsive" id="onglet_ville_responsive" onclick="showOfferResponsive('liste_villes_responsive', 'onglet_ville_responsive')">Ville</span>
		</li>
	</ul>
	<div class="wrapper_responsive">
		<div class="listing_responsive" id="liste_offres_responsive">
			<ul class="liste_responsive">
				{% for offre in listeOffres %}
					{% set counter = ( counter | default(-1) ) + 1 %}
					{% if counter == 7 or counter == 15 or counter == 23 %}
					</ul>
					<ul class="liste">
					{% endif %}
					<li>
						<a href={{ prefix ~ "france-metiers-" ~ offre[0] ~ "-" ~ struct_home_search(offre[1]) }}>{{ offre[1]|replace({'/': "-"}) }}</a>

						{# <a href={{ "/candidatOffre/elk_search?mot_cle=" ~ stringtourl(offre[1]) ~ "&localisation=&id_type_contrat=" }}>{{ offre[1]|replace({'/': "-"}) }}</a> #}
					</li>
				{% endfor %}
			</ul>
		</div>
		<div class="listing_responsive" id="liste_metiers_responsive" style="display:none">
			<ul class="liste_responsive">
				{% for metier in listeMetiers %}
					{% set counter = ( counter | default(-1) ) + 1 %}
					{% if counter == 19 or counter == 38 or counter == 57 %}
					</ul>
					<ul class="liste_responsive">
					{% endif %}
					<li>
						<a href={{ prefix ~ "france-metier-" ~ struct_home_search(metier.LibelleMetier) ~ "-secteurs" }}>{{ metier.LibelleMetier }}</a>
					</li>
				{% endfor %}
			</ul>
		</div>
		<div class="listing_responsive" id="liste_villes_responsive" style="display:none">
			<ul class="liste_responsive">
			{% for ville in codePostal %}
						
				{% set counter = ( counter | default(-1) ) + 1 %}
				{% set array = [33, 66, 120] %}
				{% if counter != 0 and counter in array %}
				</ul>
				<ul class="liste">
				{% endif %}

			<li>
				<a href={{ prefix ~ struct_home_search(ville.libelle) ~ "-(" ~ ville.codePostal ~ ")-metiers-secteurs" }}>{{ ville.libelle }}</a>
			</li>
			{% endfor %}
			</ul>
			</div>
		</div>
	</div>
