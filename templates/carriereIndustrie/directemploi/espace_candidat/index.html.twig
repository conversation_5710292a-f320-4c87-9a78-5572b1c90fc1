{% extends 'directemploi/layout.html.twig' %}
{% block stylesheets %}  <link rel="stylesheet" href="{{ asset('assets/css/espace_cand.min.css') }}"> {% endblock %}
{% block content %}
	<title>
		{% block title %}
			Bienvenue dans votre espace candidat - Direct Emploi
		{% endblock %}
	</title>

<style>
	
</style>

<div class="page_user">

	<h1>Mon compte</h1>

	<div id="fil_ariane">
		<a href="">Accueil</a>
		-
		<a href="">Espace Candidat</a>
	</div>

	<table class="mon_compte">

		<tr>
			<td class="left">

				
				<div class="item onglet_candidat blue_div">
    				<button class="ajax blue_button" id="profil">Mon profil</button>
				</div>

				<div class="item onglet_candidat blue_div">
    				<button class="ajax blue_button" id="cv">Mon CV</button>
				</div>

				</div>
				<div class="item onglet_candidat blue_div">
					<button class="ajax blue_button" id="offre">Mes offres</button>
				</div>

				<div class="item onglet_candidat blue_div">
					<button class="ajax blue_button" id="alertemail">Mes favoris</button>
				</div>

				<div style="text-align: center;">
					<a href="{{path('app_espace_candidat_delete')}}">Me désinscrire</a>
				</div>
						
			</td>
			<td class="right" id="right-right-cand">	
				<div id="espace_cand">
					<div class="div_render">
						<div class="cv_display">

						</div>
					</div>
				
				</div>
				{% if user_cv is defined and user_cv is not null %}
					{% set isVisible, isAnonym = user_cv.getIsVisible(), user_cv.getIsAnonym() %}
					{% if isVisible == 0 or isAnonym == 1 %}
						<div id="info-cv">
							<button class="read-more-info-icon modal-right">
								i
								<span class="hidden-info">
									{% if isVisible == 0 %}
										<strong>. CV invisible</strong><br><i>les recruteurs ne peuvent pas voir votre cv!<br>(modifiez le cv)</i>
									{% endif %}
									{% if isVisible == 0 and isAnonym == 1 %}
										<br><br><br>
									{% endif %}
									{% if isAnonym == 1 %}
										<strong>. CV anonyme</strong><br><i>les recruteurs ne peuvent pas savoir qui vous etes!<br>(modifiez le cv)</i>
									{% endif %}

								</span>
							</button>
						</div>
					{% endif %}
				{% endif %}
			</td>
			
		</tr>
	</table>
</div>

{# Bloc un passeport pour l'embauche #}
<div class="conseils_espace">
	<h2>
		Un passeport pour l'embauche
	</h2>

	<div class="conseils_contener">
		<table>
			<tr class="listaztrtop">
				<th></th>
				<th class="listazth"></th>
				<th></th>
				<th class="listazth"></th>
				<th></th>
			</tr>

			<tr>
				<th class="cadre_conseil">
					<a href="conseil/cv">
						<div class="title_conseil">Conseil&nbsp;CV</div>
						<div class="texte_conseil">Tout&nbsp;savoir&nbsp;sur&nbsp;la&nbsp;conception&nbsp;d'un&nbsp;CV</div>
					</a>
				</th>
				<th class="listazth"></th>
				<th class="cadre_conseil">
					<a href="conseil/lettreMotivation">
						<div class="title_conseil">Conseils&nbsp;lettre&nbsp;de&nbsp;motivation</div>
						<div class="texte_conseil">Les&nbsp;clés&nbsp;d'une&nbsp;lettre&nbsp;de&nbsp;motivation&nbsp;réussie</div>
					</a>
				</th>
				<th class="listazth"></th>
				<th class="cadre_conseil">
					<a href="listeMetier">
						<div class="title_conseil">Les&nbsp;métiers&nbsp;de&nbsp;A&nbsp;à&nbsp;Z</div>
						<div class="texte_conseil">Découvrez&nbsp;notre&nbsp;guide&nbsp;des&nbsp;métiers</div>
					</a>
				</th>
			</tr>

			<tr class="listaztr">
				<th></th>
				<th class="listazth"></th>
				<th></th>
				<th class="listazth"></th>
				<th></th>
			</tr>

			<tr>
				<th class="cadre_conseil">
					<a href="conseil/entretienEmbauche">
						<div class="title_conseil">Conseils&nbsp;entretien&nbsp;d'embauche</div>
						<div class="texte_conseil">L'entretien&nbsp;:&nbsp;un&nbsp;face&nbsp;à&nbsp;face&nbsp;à&nbsp;préparer</div>
					</a>
				</th>
				<th class="listazth"></th>
				<th class="cadre_conseil">
					<a href="conseil/rechercheEmploi">
						<div class="title_conseil">Conseils&nbsp;recherche&nbsp;d'emploi</div>
						<div class="texte_conseil">Comment&nbsp;effectuer&nbsp;une&nbsp;recherche&nbsp;d'emploi</div>
					</a>
				</th>
				<th class="listazth"></th>
				<th class="cadre_conseil">
					<a href="listeActivite">
						<div class="title_conseil">Les&nbsp;domaines&nbsp;de&nbsp;A&nbsp;à&nbsp;Z</div>
						<div class="texte_conseil">Découvrez&nbsp;notre&nbsp;guide&nbsp;des&nbsp;domaines</div>
					</a>
				</th>
			</tr>

			<tr class="listaztrtop">
				<th></th>
				<th class="listazth"></th>
				<th></th>
				<th class="listazth"></th>
				<th></th>
			</tr>
		</table>
	</div>




	<div class="conseils_contener_responsive">
		<table>
			<tr>
				<th class="cadre_conseil">
					<a href="conseil/cv">
						<div class="title_conseil">Conseil&nbsp;CV</div>
						<div class="texte_conseil">Tout&nbsp;savoir&nbsp;sur&nbsp;la&nbsp;conception&nbsp;d'un&nbsp;CV</div>
					</a>
				</th>
			</tr>
			<tr>
				<th class="cadre_conseil">
					<a href="conseil/lettreMotivation">
						<div class="title_conseil">Conseils&nbsp;lettre&nbsp;de&nbsp;motivation</div>
						<div class="texte_conseil">Les&nbsp;clés&nbsp;d'une&nbsp;lettre&nbsp;de&nbsp;motivation&nbsp;réussie</div>
					</a>
				</th>
			</tr>
			<tr>
				<th class="cadre_conseil">
					<a href="listeMetier">
						<div class="title_conseil">Les&nbsp;métiers&nbsp;de&nbsp;A&nbsp;à&nbsp;Z</div>
						<div class="texte_conseil">Découvrez&nbsp;notre&nbsp;guide&nbsp;des&nbsp;métiers</div>
					</a>
				</th>
			</tr>
			<tr>
				<th class="cadre_conseil">
					<a href="conseil/entretienEmbauche">
						<div class="title_conseil">Conseils&nbsp;entretien&nbsp;d'embauche</div>
						<div class="texte_conseil">L'entretien&nbsp;:&nbsp;un&nbsp;face&nbsp;à&nbsp;face&nbsp;à&nbsp;préparer</div>
					</a>
				</th>
			</tr>
			<tr>
				<th class="cadre_conseil">
					<a href="conseil/rechercheEmploi">
						<div class="title_conseil">Conseils&nbsp;recherche&nbsp;d'emploi</div>
						<div class="texte_conseil">Comment&nbsp;effectuer&nbsp;une&nbsp;recherche&nbsp;d'emploi</div>
					</a>
				</th>
			</tr>
			<tr>
				<th class="cadre_conseil">
					<a href="listeActivite">
						<div class="title_conseil">Les&nbsp;domaines&nbsp;de&nbsp;A&nbsp;à&nbsp;Z</div>
						<div class="texte_conseil">Découvrez&nbsp;notre&nbsp;guide&nbsp;des&nbsp;domaines</div>
					</a>
				</th>
			</tr>
		</table>
	</div>
</div>

<script>

	document.addEventListener("DOMContentLoaded", function() {
		
		var parent = document.querySelector('.cv_display');
		var child = document.getElementById("info-cv");
		var offset = parent.offsetTop - child.offsetTop;

		window.addEventListener("scroll", function() {
			var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
			var maxScroll = parent.scrollHeight - parent.clientHeight;

			if (scrollTop >= offset && scrollTop <= parent.scrollHeight - (maxScroll - offset - child.style.height))
				child.style.top = scrollTop - offset + "px";
		});
	});



	function loadContent(url) {
    	var req = new XMLHttpRequest();

		req.onreadystatechange = function() {
			if (req.readyState === 4 && req.status === 200) {
				document.querySelector('.cv_display').innerHTML = req.responseText;
			}
		};

		req.open("GET", url, true);
		req.send();
	}
	
	window.addEventListener("load", function() {
        loadContent("espaceCandidat/profil");
    });
	
	document.getElementById("profil").addEventListener("click", function() {
		loadContent("espaceCandidat/profil");
	});

	document.getElementById("cv").addEventListener("click", function() {
		loadContent("espaceCandidat/cv");
	});

	document.getElementById("offre").addEventListener("click", function() {
		loadContent("espaceCandidat/offre");
	});
	
	document.getElementById("alertemail").addEventListener("click", function() {
		loadContent("espaceCandidat/alertemail");
	});

</script>

{% endblock %}
