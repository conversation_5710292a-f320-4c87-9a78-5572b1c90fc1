<table>
	<tr>
		<td>
			<strong>Voir le dossier</strong>

			<select name="folders" id="folders-select" style='width:180px'>
				<option name="getIdRecruteurCvDossier" value="dossiers">Voir tous les Dossiers</option>
				<option name="getNomDossier" value="cv_non_classes">CV non classé.s</option>
			</select>
			?php echo  select_tag('id_dossier', objects_for_select($CvRecruteurCvDossiers,
															'getIdRecruteurCvDossier',
															'getNomDossier',
															$id_dossier,
															array('include_custom' => __('Voir tous les dossiers'))
															),
															array('onChange' => remote_function(array(
																	'update'	=> 'zoneListingCvs',
																	'url'		=> $module . '/' . $action,
																	'with'		=> "'" . $params . "id_dossier=' + $('id_dossier').value",
																	'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
															)),
															'style' => 'width:180px;')) ?>

			| <a href="espaceRecruteur/listFolder" title="Gérer mes dossiers">Gérer mes dossiers</a>
		</td>
		<td width="10px">

		</td>
		{% set params_date = params|replace({"order=note_up&": '', "order=note_down&": ''}) %}
		{% set params_ordre = params|replace({"date=recent&": '', "date=vieux&": ''}) %}
		<td>
			<strong>Note</strong>
				<a href="#" name="notes" id="notes" value="note_up" onclick=";return false;">1-5</a>
			<?php echo link_to_remote('1-5', array(
						'update'	=> 'zoneListingCvs',
						'url'		=> $module . '/' . $action,
						'with'		=> "'" . $params_ordre . "order=note_up'",
						'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
					)) ?>
			 |
			<?php echo link_to_remote('5-1', array(
						'update'	=> 'zoneListingCvs',
						'url'		=> $module . '/' . $action,
						'with'		=> "'" . $params_ordre . "order=note_down'",
						'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
					)) ?>
		</td>
		<td width="10px"></td>
		<td>
		<strong>Date</strong>
			<?php echo link_to_remote('Plus récent', array(
						'update'	=> 'zoneListingCvs',
						'url'		=> $module . '/' . $action,
						'with'		=> "'" . $params_date . "date=recent'",
						'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
					)) ?>
			 |
			<?php echo link_to_remote('Plus ancien', array(
						'update'	=> 'zoneListingCvs',
						'url'		=> $module . '/' . $action,
						'with'		=> "'" . $params_date . "date=vieux'",
						'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
					)) ?>
		</td>
	</tr>
</table>
</div>



<div id="liste_Cvs">


<div id="loadingListingCvs" style="display:none">
	<p>
	</p>
</div>

<?php if($CvCvs->getNbResults() > 0): ?>

<table width="100%" class="listing_offres" id="tableListingCvs" cellspacing="0" cellpadding="0">
		<tbody>
			<?php $key=0 ?>
			<?php foreach ($list as $id_group => $array): ?>
				<?php foreach ($array as $id => $Object) : ?>
					<?php if(!$id): ?>
					<tr>
						<td colspan="3">
							<h3 class="titre_dossier"><?php echo $Object->getNomDossier() ?></h3>
						</td>
					</tr>
					<?php else: ?>
					<tr><td height="10px" colspan="3"></TD></tr>
					<tr class="listing_offre <?php echo $key%2 == 0? 'even'  : 'odd' ?>">
						<td class="listing_offre" width="160px" valign="top">
							<?php echo $Object->getTitre() ?>
						</td>
						<td width="56px" valign="top">
							<div class="note_cv" id="note_cv_<?php echo $Object->getIdCv() ?>">
							<?php echo $Object->getNote($sf_user->getAttribute('id')) ?>
							</div>
						</td>
						<td valign="top">
							<div style="margin-top:8px;">

							<strong><?php echo __('Noter le CV') ?></strong>&nbsp;
							<?php echo  select_tag('note_'.$Object->getIdCv(), options_for_select(array('' => __('Notes'), '1' => '1 sur 5', '2' => '2 sur 5', '3' => '3 sur 5', '4' => '4 sur 5', '5' => '5 sur 5'),
													ceil($Object->getNote($sf_user->getAttribute('id')))),
													array('onChange' => remote_function(array(
															'url' => 'espaceRecruteur/updateNoteCv?id_cv='.$Object->getIdCv(),
															'update' => 'note_cv_'.$Object->getIdCv(),
															'with' => "'note=' + $('note_".$Object->getIdCv()."').value"
													)),
													'style' => 'width:75px;'))?>

							&nbsp;&nbsp;<strong><?php echo __('Classer le CV') ?></strong>&nbsp;
							<?php echo  select_tag('dossier_'.$Object->getIdCv(), objects_for_select($CvRecruteurCvDossiers,
																				'getIdRecruteurCvDossier',
																				'getNomDossier',
																				$Object->getIdDossier($sf_user->getAttribute('id'))),
																				array('onChange' => remote_function(array(
															'url' => 'espaceRecruteur/updateDossierCv?id_cv='.$Object->getIdCv(),
															'with' => "'id_dossier=' + $('dossier_".$Object->getIdCv()."').value",
															'complete' => remote_function(array(
																'update'	=> 'zoneListingCvs',
																'url'		=> $module . '/' . $action .'?page=' . $CvCvs->getPreviousPage(),
																'with'		=> "'" . $params . "'",
																'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
																))
															)),
															'style' => 'width:125px; margin-right:35px;'))?>
											<strong> <?php echo($Object->getCvRecruteurCvSauvegarde()->getDateSauvegarde()); ?></strong>
											<span id="note_cv"></span>
										   <?php
										   if ($Object->getEtat() == -1) {
										  		$lien_cv = "/cvtheque/voircv?id_cv_no_save=". $Object->getNomFichierCvStockage();
										   } else {
												$lien_cv = 'cvtheque/show?id_cv='. $Object->getIdCv();
										   }
										   ?>
                                 <?php echo link_to(__('Voir le CV'), $lien_cv, array('target' => '_blank')) ?>
                                 |
                                 <span class="rouge"><?php echo link_to_remote(__('Supprimer'), array(
                                        'update'	=> 'zoneListingCvs',
                                        'url'		=> $module . '/' . $action .'?id_del=' . $Object->getIdCv(),
                                        'with'		=> "'" . $params . "'",
                                        'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();",
                                        'confirm' => __('Êtes vous sûr ?')
                                    )) ?></span>
                            </div>

						</td>
					</tr>
					<?php endif; ?>
					<?php $key=$key+1?>
				<?php endforeach; ?>
			<?php endforeach; ?>

		</tbody></table>


		<!-- DEB PAGINATION --><?php if ($CvCvs->haveToPaginate()): ?>
							<div align="center" class="pagination"><table><tr><td><?php /*echo link_to_remote(image_tag('icones/resultset_first.png'), array(
									'update'	=> 'zoneListingCvs',
									'url'		=> $module . '/' . $action .'?page=' . $CvCvs->getFirstPage(),
									'with'		=> "'" . $params . "'",
									'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
								))*/ ?>
								<div class="pager_prec"><?php echo link_to_remote('Précédent', array(
									'update'	=> 'zoneListingCvs',
									'url'		=> $module . '/' . $action .'?page=' . $CvCvs->getPreviousPage(),
									'with'		=> "'" . $params . "'",
									'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
								)) ?></div>

								<?php if($CvCvs->getPage() >= 3
									AND $CvCvs->getNbResults()/$CvCvs->getMaxPerPage() >= 3): ?>

								<div class="bt_pager_chiffre"><?php echo link_to_remote(1, array(
									'update'	=> 'zoneListingCvs',
									'url'		=> $module . '/' . $action .'?page=' . $CvCvs->getFirstPage(),
									'with'		=> "'" . $params . "'",
									'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
								)) ?></div>

								<?php if($CvCvs->getPage() >= 4
									AND $CvCvs->getNbResults()/$CvCvs->getMaxPerPage() >= 5): ?>
								<div class="bt_pager_chiffre"><a href="#">...</a></div>
								<?php endif; ?>

								<?php endif; ?>

								<?php $links = $CvCvs->getLinks(3); foreach ($links as $page): ?>
									<div class="bt_pager_chiffre"><?php echo ($page == $CvCvs->getPage()) ? $page : link_to_remote($page, array(
										'update'	=> 'zoneListingCvs',
										'url'		=> $module . '/' . $action .'?page=' . $page,
										'with'		=> "'" . $params . "'",
										'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
									)) ?></div>

									<?php if ($page != $CvCvs->getCurrentMaxLink()): ?>
 								<?php endif; ?>
								<?php endforeach; ?>

								<?php if($CvCvs->getPage() <= ($CvCvs->getNbResults()/$CvCvs->getMaxPerPage()) - 1
									AND $CvCvs->getNbResults()/$CvCvs->getMaxPerPage() >= 3): ?>

								<?php if($CvCvs->getPage() <= ($CvCvs->getNbResults()/$CvCvs->getMaxPerPage()) - 2
									AND $CvCvs->getNbResults()/$CvCvs->getMaxPerPage() >= 5): ?>
								<div class="bt_pager_chiffre"><a href="#">...</a></div>
								<?php endif; ?>

								<div class="bt_pager_chiffre"><?php echo link_to_remote(ceil($CvCvs->getNbResults()/$CvCvs->getMaxPerPage()), array(
									'update'	=> 'zoneListingCvs',
									'url'		=> $module . '/' . $action .'?page=' . $CvCvs->getLastPage(),
									'with'		=> "'" . $params . "'",
									'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
								)) ?></div>

								<?php endif; ?>

								<div class="pager_suiv"><?php echo link_to_remote('Suivant', array(
									'update'	=> 'zoneListingCvs',
									'url'		=> $module . '/' . $action .'?page=' . $CvCvs->getNextPage(),
									'with'		=> "'" . $params . "'",
									'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
								)) ?></div>
								<?php /*echo link_to_remote(image_tag('icones/resultset_last.png'), array(
									'update'	=> 'zoneListingCvs',
									'url'		=> $module . '/' . $action .'?page=' . $CvCvs->getLastPage(),
									'with'		=> "'" . $params . "'",
									'before'	=> "$('tableListingCvs').hide(); $('loadingListingCvs').show();"
								))*/ ?>
				</TD></tr></table></div><?php endif; ?>
				 <!-- FIN PAGINATION -->

<?php else: ?>

<div id="tableListingCvs">

	<p>
		<em><?php echo __("Aucun CV sauvegardé") ?></em>
	</p>

</div>


<?php endif; ?>
</div>