{% extends 'directemploi/new_layout.html.twig' %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/new_responsive.min.css') }}">
{% endblock %}

{% set title_seo = "Les offres d'emploi et recrutement en France | Direct Emploi" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% set description_seo = "Accédez à nos " ~ nb_offres|number_format(0,'', ' ') ~ " annonces d'offres d'emploi des entreprises et cabinets d'experts qui recrutent et postulez en ligne facilement sur Direct Emploi." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}

{% block javascripts %}
    {% include 'json_ld/home.html.twig' %}
{% endblock %}

{% block content %}
    <section>
        {{ render(controller('App\\Controller\\MainController::recherche', {'path': app.request.pathinfo})) }}
    </section>
    <section>    
        <div class="container carrousel">
            <div style="display: flex; justify-content: space-between; margin-bottom: 20px; border-bottom: 1px solid black">
                <h4>Ils recrutent sur Direct Emploi</h4>
                <a href="/entreprises">> Voir toutes les entreprises</a>
            </div>
        {{ render(controller('App\\Controller\\MainController::carousel')) }}
        </div>
    </section>
    <section>    
        {{ render(controller('App\\Controller\\MainController::offres', {'path': app.request.pathinfo})) }}
    </section>
    <section> 
        <div class="container rubriques">
            <h4>Nos rubriques</h4>
            <div class="rubriques_cards_wrapper">
                <div class="row">
                    <div class="col">
                        <div class="card text-center" style="border-color: #4B93C5;" id="monde_etudiant">
                            <div class="card-body">
                                <h5 class="card-title">MONDE <br/>ÉTUDIANT</h5>
                            </div>
                            <div class="card-body"  style="text-align:center;font-size:16px;">
                                <a href="{{ path('app_theme_stage') }}"><color="black">•</color> Stage</a>
                                <a href="{{ path('app_theme_alternance') }}"><color="black">•</color> Alternance</a>
                                <a href="{{ path('app_theme_jeune_diplome') }}"><color="black">•</color> Jeunes diplômés</a>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card text-center" style="border-color: #4B93C5;" id="entreprises">
                            <div class="card-body">
                                <h5 class="card-title">ENTREPRISES ET LEURS OFFRES</h5>
                            </div>
                            <div class="card-body" style="text-align:center;font-size:16px;">
                                <a href="{{ path('app_entreprises') }}">• Entreprises (A à Z)</a>
                                <a href="{{ path('app_theme_recrutement') }}" >• Experts recrutement</span>
                                {# <a href="{{ path('app_zoom_index') }}">• Par domaine d'activité</a> #}
                                {# <span onclick="showOffer('liste_offres', 'onglet_secteur')">• Par domaine d'activité</a> #}
                                <a href="{{ path('app_theme_interim') }}"><color="black">• Groupes d'intérim</a>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card text-center" style="border-color: #4B93C5;" id="rubriques_thematiques">
                            <div class="card-body">
                                <h5 class="card-title">RUBRIQUES THÉMATIQUES</h5>
                            </div>
                            <div class="card-body"  style="text-align:center;font-size:16px;">
                                <a href="{{ path('app_theme_handicap') }}">• Handicap</a>
                                <a href="{{ path('app_pagesaisonniers') }}">• Saisonniers</a>
                                <a href="{{ path('app_pagecadre') }}">• Postes de direction</a>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card text-center" style="border-color: #4B93C5;" id="dossier_vente_v1">
                            <a href="https://www.directemploi.com/page/dossier-la-vente">
                                <div class="card-body">
                                    <h5 class="card-title" style="font-size:1.05em;padding-top:12px;">DOSSIER SPÉCIAL<br>MÉTIERS DE LA VENTE</h5>
                                </div>
                                <div class="card-body"  style="text-align:center;font-size:16px;">
                                    <div style="color:#ffff;">Conseils, témoignages ...</div>
                                    <div style="color:#ffff;">Plus de 50 métiers</div>
                                    <div style="color:#ffff;">à découvrir</div>
                                </div>
                            </a>
                        </div>
                    </div>
                    {#}
                    <div class="col">
                        {% for i in 0..nb_entr %}
                            {% if entreprises != [] %}
                            <div id={{ "entreprises_slide_" ~ i }} style={{"display:" ~ (i == 0 ? "block" : "none")}}>
                                {% set style = "background-image: url(" ~ (entreprises[i].getLogoFond() ? "/uploads/logos_fond/" ~ entreprises[i].getLogoFond() : images[i] ) ~ ");" %}
                                <a href={{ "/entreprise/" ~ entreprises[i].getIdEntreprise() ~ "/" ~ strtourl(entreprises[i].getRaisonSociale()) }}>
                                    <div class="card text-center" style="border-color: #ffc107;{{style}};" id="trouvez_formation">
                                        <div class="img">
                                            <img alt="entreprise" class="img-accueil-entreprise" src="{{ asset('/uploads/logos/' ~ entreprises[i].logo) }}">
                                        </div>
                                    </div>
                                </a>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                    {#}
                </div>
            </div>
        </div>
    </section>
    <section>
        <div class="container conseils">
            <h4>Guides et conseils de l'emploi</h4>
            <div class="text-center conseils_cards_wrapper">
                <a class="card" href="{{ path('app_conseil_cv') }}">
                    <img class="card-img" alt="pictogramme cv" src="{{ asset('/images/direct-emploi/picto_cv.jpg') }}" style="width: 75px;">
                    <span style="margin-top: 15px;">Le CV</span>
                </a>
                <a class="card" href="{{ path('app_conseil_lm') }}">
                    <img class="card-img" alt="pictogramme lettre motivation" src="{{ asset('/images/direct-emploi/picto_lm.jpg') }}" style="width: 75px;">
                    <span>La lettre de motivation</span>
                </a>
                <a class="card" href="{{ path('app_conseil_ee') }}">
                    <img class="card-img" alt="pictogramme lettre entretien" src="{{ asset('/images/direct-emploi/picto_entretien.jpg') }}" style="width: 75px;">
                    <span>L'entretien d'embauche</span>
                </a>
                <a class="card" href="{{ path('app_conseil_re') }}">
                    <img class="card-img" alt="pictogramme recherche" src="{{ asset('/images/direct-emploi/picto_recherche.jpg') }}" style="width: 75px;">
                    <span>La recherche d'emploi</span>
                </a>
                <a class="card" id="id_card_metiers" href="{{ path('app_conseil_listeMetiers') }}">
                    <img class="card-img" alt="pictogramme liste métier" src="{{ asset('/images/direct-emploi/picto_metiers.jpg') }}" style="width: 75px;">
                    <span>Les métiers</span>
                    <span>de A à Z</span>
                </a>
                <a class="card" id="id_card_domaines" href="{{ path('app_conseil_listeDomaines') }}">
                    <img class="card-img" alt="pictogramme domaine" src="{{ asset('/images/direct-emploi/picto_domaines.jpg') }}" style="width: 75px;">
                    <span>Les domaines</span>
                    <span>de A à Z</span>
                </a>
            </div>
        </div>
    </section>
    <section>
        <div class="container actualite">
            <div>
                <h4>Actualités de l'emploi et du recrutement</h4>
                <a href="{{ path('app_articles_list') }}">> Voir toutes les actualités</a>
            </div>
            <div class="actualite_cards_wrapper">
            <div class="row">
                {% for article in articles %}
                    <div class="col-3" style="padding-top:15px;">
                        <a href={{ "/article/" ~ article['id_article'] ~ "/" ~ strtourl(article['titre']) }} class="card card-zoom" style="height: 100%">
                            <img src={{ asset("/uploads/article/" ~ article['image_petite'] ) }} class="card-img-top" alt="{{article['image_petite']}}" style="height: 120px; width: 100%; align-self: center">
                            <div class="card-body">
                                <h5 class="card-title">{{article['titre']}}</h5>
                                <p class="card-text">{{article['chapo']|u.truncate(150, '...', false)}}</p>
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">
                                <!--
                                    <strong>Actualité :</strong> 
                                        {% if article['theme'] == 'Emploi' and article['type'] != 'Article' %}
                                            {{article['type']}}
                                        {% elseif article['type'] == 'Article' %}
                                            Dossier
                                        {% else %}
                                            {{article['theme']}}
                                        {% endif %}
                                -->
                                </small>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>
        </div>
    </section>
    <script language="javascript">
        var i = 0;
        var reloadDiv = function() {
            if (i > 0) {
                prev = document.getElementById("entreprises_slide_" + (i - 1));
                prev.style = "display:none";
            }
            if (i == 0) {
                prev = document.getElementById("entreprises_slide_" + '{{nb_entr}}');
                prev.style = "display:none";
            }
            entreprise = document.getElementById("entreprises_slide_" + i);
            entreprise.style = "display:block";
            if (i < '{{nb_entr}}')
                i++;
            else
                i = 0;
        }
		var intervalId = setInterval(reloadDiv, 2500);
    </script>
{% endblock %}
