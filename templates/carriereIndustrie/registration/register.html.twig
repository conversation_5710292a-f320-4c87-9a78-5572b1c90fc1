{% extends 'carriereIndustrie/base.html.twig' %}
{% set title = "Inscription - Première Étape" %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}

{% set description_seo = " Commencez votre aventure sur Carriere Industrie en fournissant des informations personnelles essentielles. Cette première étape vous permet de créer un profil détaillé, incluant votre niveau d'étude, les langues que vous parlez, et d'autres détails clés qui mettent en valeur votre parcours unique. C'est une occasion de présenter qui vous êtes et ce que vous apportez au monde professionnel du BTP." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}

    <div class="is-sticky">

        {% block head %}
            {% include 'carriereIndustrie/header.html.twig' %}
        {% endblock %}

        <section class="login-header d-flex justify-content-center align-items-center" id="">
            <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#A5312E"> Création </span>compte candidat</h1>
        </section>
        <section class="login-form">
            {{ form_errors(registrationForm) }}
            {{ form_start(registrationForm) }}
            <div id="first-form">

                <div class="container">
                    <div id="msform">
                        <ul id="progressbar" class="d-flex flex-row justify-content-around">
                            <li class="active" id="account"><strong>Créer mon compte</strong></li>
                            <li id="personal"><strong>Définir mon profil</strong></li>
                            <li id="payment"><strong>Créer mon alerte mail</strong></li>
                        </ul>

                    </div>
                    <div class="info-section">
                        <h6>Votre identifiant et mot de passe</h6>
                        <hr>

                        <div class="row">

                            {% for flash_error in app.flashes('verify_email_error') %}
                                <div class="alert alert-danger" role="alert">{{ flash_error }}</div>
                            {% endfor %}
                            <div class="col-lg-6 col-sm-12">
                                <div class="form-group">
                                    {{ form_row(registrationForm.login) }}
                                    {{ form_errors(registrationForm.login) }}
                                </div>
                                <div class="form-group">
                                    {{ form_row(registrationForm.email) }}
                                    {{ form_errors(registrationForm.email) }}
                                </div>

                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-sm-12">
                                <div class="form-group">
                                    {{ form_row(registrationForm.passCrypt.first) }}
                                </div>
                            </div>
                            <div class="col-lg-6 col-sm-12">
                                <div class="form-group">
                                    {{ form_row(registrationForm.passCrypt.second) }}
                                </div>
                            </div>
                        </div>
                        {{ form_row(registrationForm._token) }} <!-- Add the CSRF token here -->

                    </div>
                    <div class="info-section">

                        <h6>Votre état civil</h6>
                        <hr>
                        <div class="row">
                            <div class="col-lg-4 col-sm-12">
                                {{ form_row(registrationForm.nom) }}
                                {{ form_errors(registrationForm.nom) }}

                            </div>
                            <div class="col-lg-4 col-sm-12">
                                {{ form_row(registrationForm.prenom) }}
                                {{ form_errors(registrationForm.prenom) }}

                            </div>
                            <div class="col-lg-4 col-sm-12">
                                {{ form_row(registrationForm.user_civilite_id_user_civilite) }}
                                {{ form_errors(registrationForm.user_civilite_id_user_civilite) }}

                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-sm-12">
                                {{ form_row(registrationForm.telephone) }}
                            </div>
                            <div class="col-lg-6 col-sm-12">
                                {{ form_row(registrationForm.pays) }}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-6 col-sm-12">
                                {{ form_row(registrationForm.code_postal) }}
                                {{ form_errors(registrationForm.code_postal) }}
                            </div>
                            <div class="col-lg-6 col-sm-12">
                                {{ form_row(registrationForm.ville) }}
                            </div>
                        </div>

                    </div>
                    <div class="info-section">
                        <h6>Votre domaine d'activité et votre formation</h6>
                        <hr>
                        <div class="row">
                            <div class="col-lg-6 col-sm-12">
                                <span class="DomaineLabel">Domaine d'activité *	</span>
                                {{ form_row(registrationForm.domaine_activite, {'attr': {'class': 'domaine-activite-liste form-control'},'label': false}) }}
                            </div>
                            <div class="col-lg-6 col-sm-12" id="metier-section" style="display: none;">
                                <span class="metierLabel">Metier (Ou dernier métier exercé) *</span>
                                {{ form_row(registrationForm.metier_metier, {'attr': {'class': 'metier-metier-liste form-control'},'label': false}) }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-sm-12">
                                <span class="formationLabel">Niveau de formation *</span>
                                {{ form_row(registrationForm.formation_level, {'attr': {'class': 'formation-level-liste form-control'},'label': false}) }}
                            </div>
                            <div class="col-lg-6 col-sm-12" id="Etablissement-section" style="display: none;">
                                <div id="ecole-liste-container">
                                    <span class="ecole-listLabel">Etablissement / Spécialité *</span>
                                    {{ form_row(registrationForm.ec_ecole, {'attr': {'class': 'ec-ecole-liste form-control'},'label': false}) }}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="hidden-sm col-lg-6 col-sm-12">

                            </div>
                            <div class="col-lg-6 col-sm-12" id="annee-section" style="display: none;" >
                                <span class="anneeObtentionDiplomeLabel">Année d'obtention du diplôme *</span>
                                {{ form_row(registrationForm.anneeObtentionDiplome, {'attr': {'class': 'anneeObtentionDiplome form-control'},'label': false}) }}
                            </div>
                        </div>
                    </div>

                    <div class="info-section">
                        <h6>Je désire également</h6>
                        <hr>
                        <div class="row">
                            <div class="col-12">
                                <div class="bottom-login">
                                    <i class="fa-regular fa-file-lines" style="color: #A5312E; font-size:57px"></i>
                                    <div>
                                        <p><strong>Déposer gratuitement mon CV </strong> pour être contacté par plus de 5000 recruteurs</p>
                                        <div class="form-group">
                                            {{ form_row(registrationForm.cv_file) }}
                                            <small id="fileHelp" class="form-text text-muted">Taille max. de 1 Mo. Formats acceptés : doc, docx, pdf, rtf.</small>
                                        </div>
                                    </div>
                                    <div class="form-check" style="margin-left:auto;">
                                        <input class="form-check-input" type="checkbox" style="max-height: 24px;" value="" id="defaultCheck1">
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="bottom-login">
                                    <i class="fa-regular fa-newspaper" style="color: #A5312E; font-size:50px"></i>
                                    <div>
                                        <p><strong>M’abonner gratuitement à la newsletter de Direct Emploi et son réseau de sites spécialisés :</strong></p>
                                        <p>Préparation à l’entretien d’embauche, CV, conseils de négociation de salaire, fiches métiers, offres d’emploi ciblées, informations sur les formations, offres partenaires.</p>
                                    </div>
                                    <div class="form-check" style="margin-left:auto;">
                                        {{ form_row(registrationForm.newsletter) }}

                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="bottom-login">

                                    <i class="fa-regular fa-square-check" style="color: #A5312E; font-size:50px"></i>
                                    <div>
                                        <p><strong>J'accepte <a href="#"style="color:#A5312E;">les conditions générales</a> de Carrière Industrie, site du groupe Direct Emploi</strong> <span class="required"> * </span></p>
                                        <p>- Coordonnées transmises aux recruteurs, lecture du CV à des fins de recrutement</p>
                                        <p>- Envois potentiels d’offres d’emploi, stage, alternance, formations et communications partenaires</p>


                                    </div>
                                    <div class="form-check" style="margin-left:auto;">
                                        {{ form_row(registrationForm.csg) }}

                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <button id="validate-button"   class="btn mb-2 px-4 button-black">Valider et continuer</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{ form_end(registrationForm) }}

        </section>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <script>
            document.addEventListener("DOMContentLoaded", function () {
                var canSubmit = true;  // Flag to track form submission

                document.getElementById("validate-button").addEventListener("click", function (event) {
                    // Reset the flag on each form submission attempt
                    canSubmit = true;

                    // Validation du champ email
                    var email = document.getElementById("{{ registrationForm.email.vars.id }}").value;
                    if (!validateEmail(email)) {
                        showError('Veuillez entrer une adresse e-mail valide.');
                        canSubmit = false;
                    }

                    // Make a synchronous AJAX request to check if the email exists
                    var request = new XMLHttpRequest();
                    request.open('GET', `/ajax/VerifyEmail/${email}`, false);  // false makes the request synchronous
                    request.send();

                    if (request.status === 200) {
                        // Parse the JSON response
                        var data = JSON.parse(request.responseText);
                        console.log(data);
                        if (data.exists) {
                            // Email exists, show error message
                            showError('Cette adresse e-mail est déjà utilisée.');
                            canSubmit = false;
                        }
                    } else {
                        console.error('Error:', request.status);
                    }
                    // Validation des champs de mot de passe
                    var password1 = document.getElementById("{{ registrationForm.passCrypt.first.vars.id }}").value;
                    var password2 = document.getElementById("{{ registrationForm.passCrypt.second.vars.id }}").value;

                    var validationResult = validatePassword(password1, password2);

                    if (validationResult) {
                        // Display the validation error message
                        showError(validationResult);
                        canSubmit = false;
                    }

                    // Validation du champ nom
                    var nom = document.getElementById("{{ registrationForm.nom.vars.id }}").value;
                    if (nom.trim() === "") {
                        showError('Veuillez entrer votre nom.');
                        canSubmit = false;
                    }

                    // Validation du champ prénom
                    var prenom = document.getElementById("{{ registrationForm.prenom.vars.id }}").value;
                    if (prenom.trim() === "") {
                        showError('Veuillez entrer votre prénom.');
                        canSubmit = false;
                    }

                    // Validation du champ civilité
                    var civilite = document.getElementById("{{ registrationForm.user_civilite_id_user_civilite.vars.id }}").value;
                    if (civilite.trim() === "") {
                        showError('Veuillez sélectionner votre civilité.');
                        canSubmit = false;
                    }

                    // Validation du champ téléphone
                    var telephone = document.getElementById("{{ registrationForm.telephone.vars.id }}").value;
                    if (telephone.trim() === "") {
                        showError('Veuillez entrer votre numéro de téléphone.');
                        canSubmit = false;
                    }

                    // Validation du champ pays
                    var pays = document.getElementById("{{ registrationForm.pays.vars.id }}").value;
                    if (pays.trim() === "") {
                        showError('Veuillez sélectionner votre pays.');
                        canSubmit = false;
                    }
                    var login = document.getElementById("{{ registrationForm.login.vars.id }}").value;
                    if (!validateLogin(login)) {
                        showError('Veuillez entrer un login valide (sans espaces et au moins 6 caractères).');
                        canSubmit = false;
                    }
                    // Validation du champ code postal
                    var codePostal = document.getElementById("{{ registrationForm.code_postal.vars.id }}").value;
                    if (codePostal.trim() === "" || !/^\d+$/.test(codePostal) || codePostal.length !== 5) {
                        showError('Veuillez entrer un code postal valide composé de 5 chiffres.');
                        canSubmit = false;
                    }

                    // Submit the form if all validations passed
                    if (!canSubmit) {
                        event.preventDefault();  // Prevent form submission
                    }
                });

                // Function to display error message
                function showError(message) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur de saisie',
                        text: message,
                    });
                }

                // Fonction de validation d'email
                function validateEmail(email) {
                    // Utilisez une expression régulière pour la validation de l'email
                    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(email);
                }

                // Fonction de validation de mot de passe
                function validatePassword(password1, password2) {
                    // Validation that the password length is at least 8 characters
                    if (password1.length < 8) {
                        return "Le mot de passe doit contenir au moins 8 caractères.";
                    }

                    // Validation that both passwords are identical
                    if (password1 !== password2) {
                        return "Les mots de passe ne correspondent pas.";
                    }

                    // If all validations pass, return an empty string (no error)
                    return "";
                }

                function validateLogin(login) {
                    // Utilisez une expression régulière pour la validation du login
                    // Elle doit contenir au moins 6 caractères (lettres et chiffres) et ne pas contenir d'espaces
                    var loginRegex = /^[a-zA-Z0-9]{6,}$/;
                    return loginRegex.test(login);
                }

            });
        </script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                var domaineActiviteListe = document.querySelector('.domaine-activite-liste');
                var metierMetierListe = document.querySelector('.metier-metier-liste');

                domaineActiviteListe.addEventListener('change', function () {
                    var selectedDomaineActivite = this.value;
                    document.getElementById("metier-section").style.display = "block";
                    console.log(selectedDomaineActivite);
                    const url = `https://www.carriere-industrie.com/Ind/ajax/get-metiers/${selectedDomaineActivite}`;

                    // Simulate fetching data from the server
                    fetch(url)
                        .then(response => response.json())
                        .then(data => {
                            // Clear the options
                            metierMetierListe.innerHTML = '';

                            // Update the options
                            data.forEach(function (metier) {
                                var option = document.createElement('option');
                                option.value = metier.id;
                                console.log(metier.id)
                                option.text = metier.libelle_metier;
                                metierMetierListe.add(option);
                            });
                        })
                        .catch(error => {
                            console.error('Error fetching metiers:', error);
                        });
                });
            });
        </script>

        <script>
            function initializeFormBehavior() {
                var formationLevelListe = document.querySelector('.formation-level-liste');
                var ecoleListe = document.querySelector('.ec-ecole-liste');
                var anneeObtentionDiplome = document.querySelector('.anneeObtentionDiplome');
                var ec_ecole_listLabel = document.querySelector('.ec-ecole-listLabel');
                var anneeObtentionDiplomeLabel = document.querySelector('.anneeObtentionDiplomeLabel');

                formationLevelListe.addEventListener('change', function () {
                    var selectedFormationLevel = this.value;

                    console.log(selectedFormationLevel)
                    if (selectedFormationLevel === "23") {

                    } else {
                        document.getElementById("Etablissement-section").style.display = "block";
                        document.getElementById("annee-section").style.display = "block";


                    }

                    if (selectedFormationLevel !== 23) {
                        const url = `https://www.carriere-industrie.com/Ind/ajax/get-ecoles/${selectedFormationLevel}`;

                        fetch(url)
                            .then(response => response.json())
                            .then(data => {
                                ecoleListe.innerHTML = ''; // Effacer les options existantes

                                data.forEach(function (ecole) {
                                    var option = document.createElement('option');
                                    option.value = ecole.id;
                                    option.text = ecole.nom_ecole;
                                    ecoleListe.add(option);
                                });
                            })
                            .catch(error => {
                                console.error('Une erreur s\'est produite lors de la récupération des écoles:', error);
                            });
                    }
                });
            }

            document.addEventListener('DOMContentLoaded', initializeFormBehavior);
        </script>



        {% block footer %}
            {% include 'carriereIndustrie/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}