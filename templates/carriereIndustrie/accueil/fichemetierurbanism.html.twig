{% extends 'carriereBTP/base.html.twig' %}


{% set lettre = app.request.query.get('lettre') %}
{% set page = app.request.query.get('page') %}

{% if lettre and page %}
    {% set title_seo = "Trouvez tous les métiers (IMMOBILIER URBANISME) qui recrutent | Carriere BTP| pour les metiers qui commencent par la lettre '" ~ lettre ~ "'. Page : " ~ page %}
    {% set description_seo = "Quels sont les métiers (IMMOBILIER URBANISME) qui commencent par la lettre '" ~ lettre ~ "' et qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines… <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Data analyst, Ebéniste, Facteur… dans la page :"~ page  %}
{% elseif lettre and page is null %}
    {% set title_seo = "Trouvez tous les métiers (IMMOBILIER URBANISME) qui recrutent | Carriere BTP| pour les metiers qui commencent par la lettre '" ~ lettre ~ "'"  %}
    {% set description_seo = "Quels sont les métiers (IMMOBILIER URBANISME) qui commencent par la lettre '" ~ lettre ~ "' et qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines… Acheteur, Barmaid, Câbleur, Data analyst, Ebéniste, Facteur… "  %}
{% elseif lettre is null and page %}
    {% set title_seo = "Trouvez tous les métiers (IMMOBILIER URBANISME) qui recrutent | Carriere BTP| pour la page'" ~ page ~ "'"  %}
    {% set description_seo = "Quels sont les métiers (IMMOBILIER URBANISME) pour la page '" ~ page ~ "' et qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines… Acheteur, Barmaid, Câbleur, Data analyst, Ebéniste, Facteur… "  %}
{% else %}
    {% set title_seo = "Trouvez tous les métiers (IMMOBILIER URBANISME) BTP qui recrutent | Carriere BTP" %}
    {% set description_seo = "Quels sont les métiers (IMMOBILIER URBANISME) qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines… Acheteur, Barmaid, Câbleur, Data analyst, Ebéniste, Facteur…"  %}


{% endif %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .alphabet-filter {
            text-align: center;
            padding: 10px;
            margin: 10px;
        }

        .alphabet-filter a {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border: 0.5px solid #ddd; /* Couleur de la bordure */
            border-radius: 4px; /* Coins arrondis */
            text-decoration: none; /* Supprime le soulignement des liens */
            color: #333; /* Couleur du texte */
            font-weight: bold; /* Rend le texte en gras */
            transition: background-color 0.3s, color 0.3s; /* Animation au survol */
        }

        .alphabet-filter a:hover {
            background-color: #faaf40; /* Couleur de fond au survol */
            color: #fff; /* Couleur du texte au survol */
        }

    </style>
<div class="is-sticky">
    {% block head %}
    {% include 'carriereBTP/header.html.twig' %}
{% endblock %}

    <section class="urbanism-header d-flex flex-column align-items-center" id="">

        <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.4;"> LES OFFRE D’EMPLOI<br> PAR METIER<br><br> <span style="color:#FAAF40">Architecture / Urbanisme / Immobilier</span></h1>
    </section>

    <section class="section-embauche env">
        <div class="alphabet-filter">
            {% for lettre in range('A', 'Z') %}
                <a href="{{ path('app_BTP_urbanism', {'lettre': lettre}) }}">{{ lettre }}</a>
            {% endfor %}
        </div>

        <div class="container">
            <div class="row">
                {% for metier in metiersParPage %}
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <a href="{{ '/BTP/lists-offre/?q=' ~ metier }}" target="_blank">
                            <div class="w-100 info-button text-center">
                                <h6>{{ metier }}</h6>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            <!-- Pagination Personnalisée -->
            <div class="pagination-center text-center">
                <div class="pagination">
                    <!-- Page précédente -->
                    <a href="{{ page > 1 ? path('app_BTP_fichemetierBTP', {'lettre': lettre, 'page': page - 1}) : '#' }}" {% if page == 1 %}class="disabled"{% endif %}>&laquo;</a>

                    <!-- Numéros de page -->
                    {% for i in 1..nbTotalPages %}
                        <a href="{{ path('app_BTP_fichemetierBTP', {'lettre': lettre, 'page': i}) }}" class="{{ page == i ? 'active' : '' }}">{{ i }}</a>
                    {% endfor %}

                    <!-- Page suivante -->
                    <a href="{{ page < nbTotalPages ? path('app_BTP_fichemetierBTP', {'lettre': lettre, 'page': page + 1}) : '#' }}" {% if page == nbTotalPages %}class="disabled"{% endif %}>&raquo;</a>
                </div>
            </div>
        </div>
    </section>
    <section class="carousel-fiche-metier">
        <div id="carouselExampleControls" class="carousel slide" data-bs-ride="carousel" data-bs-carousel-init>
            <div class="carousel-inner">
                <div class="carousel-item ">
                    <img   src="{{ asset('carriereBTP/Front/images/urbanisme-header.jpg')}}" class="d-block w-100" alt="Wild Landscape"/>
                    <div class="carousel-overlay">
                        <h5 class="text-white text-uppercase">Fiches Metiers</h5>
                        <p class="text-white text-uppercase">Immobilier Urbanisme</p>
                    </div>
                </div>
                <div class="carousel-item">
                    <img src="{{ asset('carriereBTP/Front/images/btp-header.jpg')}}"  class="d-block w-100" alt="Camera"/>
                    <div class="carousel-overlay">
                        <h5 class="text-white text-uppercase">Fiches Metiers</h5>
                        <p class="text-white text-uppercase">BTP</p>
                    </div>
                </div>
                <div class="carousel-item active">
                    <img  src="{{ asset('carriereBTP/Front/images/env-slider.jpg')}}" class="d-block w-100" alt="Exotic Fruits"/>
                    <div class="carousel-overlay">
                        <h5 class="text-white text-uppercase">Fiches Metiers</h5>
                        <p class="text-white text-uppercase">ÉNERGIE / ENVIRONNEMENT</p>
                    </div>
                </div>
            </div>
            <button aria-label="Previous" class="carousel-control-prev" type="button" data-bs-target="#carouselExampleControls" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="false"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button aria-label="Next" class="carousel-control-next" type="button" data-bs-target="#carouselExampleControls" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="false"></span>
                <span class="visually-hidden">Next</span>
            </button>
        </div>
    </section>
    {% block footer %}
        {% include 'carriereBTP/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}