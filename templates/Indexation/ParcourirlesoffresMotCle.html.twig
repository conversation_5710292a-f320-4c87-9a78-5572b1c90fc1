{% extends 'Indexation/base.html.twig' %}
{% set lettre = app.request.query.get('lettre') %}

    {% set title_seo = "Offres Emploi par Métier | Direct Emploi" %}
    {% set description_seo = "Consultez les Dernières Offres d'Emploi par métier sur Direct Emploi. ✓ Postulez en ligne dès Maintenant ✓ Candidature Simple & Rapide !" %}

{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
        {% block head %}
            {% include 'Indexation/header.html.twig' %}
        {% endblock %}
<section class="emploi-header d-flex justify-content-center align-items-center " id="">
    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#44A2C6">Toutes les offres</span> d’emploi par mots clés les plus recherchés </h1>
</section>
<br>
<section>
    <div class="container">


        <div class="header-and-text">
            <h6 >Liste des mots clés les plus recherchés</h6>
            <hr>
            <div class="search-container text-center mb-5">
                <input type="text" id="searchBar" placeholder="Rechercher..." style="width: 50%; padding: 10px; margin-top: 20px;">
            </div>
            <div class="container">
                <div class="row offres-list">
                    {% set taille_liste = keywords|length %}
                    {% set taille_groupe = (taille_liste / 4)|round(0, 'floor') %}
                    {% set elements_restants = taille_liste % 4 %}
                    {% set debut = 0 %}

                    {% for i in 0..3 %}
                        {% set ajustement = i < elements_restants ? 1 : 0 %}
                        {% set fin = debut + taille_groupe + ajustement %}

                        <div class="col-lg-3 col-md-4">
                            {% for metier in keywords|slice(debut, taille_groupe + ajustement) %}
                                <div >
                                    <ul style="list-style: none; padding: 0;">
                                        <li onclick="redirectTo('{{ metier}}')">
                                            <a href="#" >{{ metier }}</a>
                                        </li>
                                    </ul>
                                </div>
                            {% endfor %}
                        </div>
                        {% set debut = fin %}
                    {% endfor %}
                </div>
            </div>
        </div>
        {% if breadcrumb is defined %}
            {% include '/_partials/_breadcrumb.html.twig'  %}
        {% endif %}
    </div>


    {% block footer %}
        {% include 'footerpages/footer.html.twig' %}
    {% endblock %}

</section>

    <!-- Include jQuery and Bootstrap JS from CDNJS -->
    <script>
        function redirectTo(value) {
            // Encode the value to include it in the URL
            const url = `https://www.directemploi.com/lists-offre/?contrat=&localisation=&localisation_field=&q=${value}`;
            // Redirect to the constructed URL
            window.location.href = url;
        }
        function normalizeString(str) {
            return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "").replace(/[^a-zA-Z0-9 ]/g, "").toLowerCase();
        }
        function redirectToDomaine(value) {
            const normalizedValue = normalizeString(value);

            const sectors = {
                "Achat": 1, // Example ID
                "Administratif / Secrétariat": 30,
                "Agriculture / Agronomie / Agroalimentaire": 29 ,
                "Architecture / Art / Design":46 ,
                "Automobile / Equipementier":2,
                "Autres": 31,
                "Banque / Assurance": 3,
                "BTP / Construction / Génie Civil":4 ,
                "Chimie / Matériaux / Plasturgie":5 ,
                "Coiffure / Esthétique / lnstitut de beauté":47 ,
                "Commerces et Grande distribution":16 ,
                "Commercial / Vente":6 ,
                "Défense / Armée":49 ,
                "Dirigeant / Poste de direction":8 ,
                "Droit / Fiscalité": 9,
                "E-commerce / lnternet / Multimédia":10 ,
                "Electronique / Èlectricité": 11,
                "Energie / Environnement": 12,
                "Enseignement": 40,
                "Etudes / Statistiques / Economie":32 ,
                "Evenementjel": 44,
                "Export lnternational": 13,
                "Finance / Audit / Comptabilité":14 ,
                "Fonction Publique et Collectivité locale": 15,
                "Hotellerie / Restauration":18 ,
                "lndustrie": 39 ,
                "lnformatique / systeme d'information":19 ,
                "Langues / Linguistique":33 ,
                "Logistique / Transport":20 ,
                "Marketing / Communication / Publicité": 21,
                "Mode / Photographie / Textile":48 ,
                "Petite enfance / Social / Aide a la personne":38 ,
                "Presse / Audiovisuel / Médias": 45,
                "Production / Mécanique / Maintenance":22 ,
                "Qualité / Sécurité":23 ,
                "Recherche & Développement":24 ,
                "RH / Formation":25 ,
                "Santé / Paramédical / Biologie / Pharmacie": 26,
                "Télécommunication et Réseaux": 27 ,
                "Tourisme / Loisirs / Sport / Culture":28 ,
            };
            let foundSector = Object.keys(sectors).find(sector =>
                normalizeString(sector).includes(normalizedValue)
            );

            console.log(foundSector)
            if (foundSector) {
                // Si trouvé, utiliser l'ID du secteur pour construire l'URL
                const sectorID = sectors[foundSector];
                window.location.href = `https://www.directemploi.com/emploi-index/domaine/${value}/${sectorID}`;
                //window.location.href = `http://127.0.0.1:8000/emploi-index/domaine/${value}/${sectorID}`;
            } else {
                window.location.href = `https://www.directemploi.com/lists-offre`;
            }
            const urlDomaine = `https://www.directemploi.com/emploi-index/domaine/${value}`;
            // Redirect to the constructed URL
            //  window.location.href = urlDomaine;
        }
        function redirectToBTP(value) {
            // Encode the value to include it in the URL
            const url = `https://www.directemploi.com/lists-offre/?contrat=&localisation=&localisation_field=&q=${value}`;
            // Redirect to the constructed URL
            window.location.href = url;
        }
    </script>
    <script>
        document.getElementById('searchBar').addEventListener('input', function(e) {
            var searchValue = e.target.value.toLowerCase();
            var offres = document.querySelectorAll('.offres-list div');

            offres.forEach(function(offre) {
                var villeText = offre.innerText.toLowerCase();
                if (villeText.includes(searchValue)) {
                    offre.style.display = '';
                } else {
                    offre.style.display = 'none';
                }
            });
        });
    </script>

    </div>
{% endblock %}
