{% extends 'directemploi/layout.html.twig' %}
		
{% block stylesheets %}
<link rel="stylesheet" href="{{ asset('assets/css/main_article.min.css') }}">
{% endblock %}

{% block content %}
<div class="site">

<div id="DE_container_rech_cadre" style="background-image: url(/images/direct-emploi/page_saisonnier/bannière.jpg)">
    <div class="box_rech home_stage clearfix" id="DE_box_rech_cadre">
        <div class="inside">
            <h2 class="h3_title" id="DE_h3_title_cadre">ESPACE SAISONNIER</h2>
        </div>
    </div>
</div>

<div class='cadre'>
    
    <fieldset style="background-color: #89BAEB; width: 42%;margin-top:27px;">
        <legend>Les postes proposés</legend>
           <ul style="width:100%;margin-left:5%">
                <div id = 'liste_metiers_cadre' style="width:100%">
                    {% for metier in metiers %}
                        <div style="padding: 3px"></div>
                        <div class = 'metier_cadre'>
                            <li id = 'li_cadre'>
                                <a href={{ '/lists-offre/?q=' ~ (strtourl(metier.LibelleMetier)) ~ '&localisation=&localisation_field=&id_type_contrat=' }} style="font-size:17px;">{{ metier.LibelleMetier }}</a>
                                <br/><br/>
                            </li>
                        </div>
                    {% endfor %}
                <div style="padding: 2px"></div>
            </div>
        </ul>
    </fieldset>
                    
    <div style="margin-left: 50%; margin-top: -74%;">
        <h2 style="margin-top:17px;margin-bottom:10px;"> Zoom métier </h2>
        {% set count = 0 %}
        {% for article in zooms %}
            <a href={{ "/article/" ~ article.idArticle ~ "/" ~ strtourl(article.titre) }}>
                <div class="image_dossier1_cadre">
                    <div class="td img">
                        <img src={{ "/images/direct-emploi/page_saisonnier/" ~ article.imagePetite }} alt={{ article.titre }}/>
                    </div>
                    <div class="box_text_dossier_cadre">
                        <h5 style='margin-top:0px;'>
                            <a href={{ "/article/" ~ article.idArticle ~ "/" ~ strtourl(article.titre) }} title={{article.titre}}> {{article.titre}} </a>
                        </h5>
                        <div class="chapodossier_cadre"> {{ article.chapo }} </div>
                        <p class="suspensions_cadre" style='margin:0% 0%'> ... </p>
                    </div>
                </div>
            </a>
            {% if count < 1 %}
                <h2 style="margin-top:17px;margin-bottom:10px;"></h2>
            {% endif %}
            {% set count = count + 1 %}
        {% endfor %}
        </table>
    </div>
    
    <div class="Carriere_container_metier" style="margin-top:8%">
		<div class="pave_fiche_metier">
		    <a class="lien_cadre">
			    <div id="DE_container_cadre_boite" style="background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), url(/images/direct-emploi/page_saisonnier/pexels-photo-2252618.jpeg);text-align:center;background-size: cover;background-repeat: no-repeat;">
                    <div class="accueil_teinte_noir_fiche_metier box_fiche_metier">
                        <div  style="margin-top:60%; height:50px; width:325px; background:#89BAEB;">
						    <p id="titre_bandeau_bas" style="margin-top:3%">Agriculture- Récolte</p>
					    </div>
                    </div>
                </div>
		    </a>
        </div>

        <div class="espace_Carriere_container_metier"></div>
		<a class="lien_cadre">
			<div id="DE_container_cadre_boite" style="background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), url(/images/direct-emploi/page_saisonnier/pexels-photo-3801649.jpg);text-align:center;background-size: cover;background-repeat: no-repeat;">
                <div class="accueil_teinte_noir_fiche_metier box_fiche_metier">
                    <div style="margin-top:60%; height:50px; width:325px; background:#89BAEB;">
    					<p id="titre_bandeau_bas" style="margin-top:3%">Hôtellerie - Restauration</p>
		            </div>
                </div>
            </div>
		</a>
        
        <div class="espace_Carriere_container_metier"></div>
		<a class="lien_cadre">
    		<div id="DE_container_cadre_boite" style="background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), url(/images/direct-emploi/page_saisonnier/pexels-photo-1268855.jpeg);text-align:center;background-size: cover;background-repeat: no-repeat;">
                <div class="accueil_teinte_noir_fiche_metier box_fiche_metier">
                    <div style="margin-top:60%; height:50px; width:325px; background:#89BAEB;">
    					<p id="titre_bandeau_bas" style="margin-top:3%">Animation- Loisirs</p>
					</div>
                </div>
            </div>
		</a>
    </div>
</article>

{% endblock %}
