{% extends 'directemploi/new_layout.html.twig' %}




{% set title = "Articles et liste des métiers emplois <PERSON>sonniers" %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}

{% set description_seo = "Découvrez notre rubrique dédiée aux saisonniers ! Chaque année, vous êtes des milliers à trouver des opportunités professionnelles uniques en travaillant dans des secteurs en constante évolution au rythme des saisons. Que ce soit pour les passionnés de l’hôtellerie et de la restauration recherchant l'effervescence estivale, le soleil et la mer, les adeptes de sports d'hiver attirés par les stations enneigées, ou encore ceux qui trouvent leur vocation dans l'agriculture saisonnière, ces métiers offrent une expérience enrichissante et souvent mémorable." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}





{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/new_page_saisonnier.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_offres_stage_alternance.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_responsive.min.css') }}">
{% endblock %}

{% block content %}
<section>
    <div class="container bandeau_page_saisonnier">
        <div class="cadre_titre_page_saisonnier">
            <div class="cadre_wrapper_page_saisonnier">
                <div class="titre_page_saisonnier">
                    <h1 style="font-size: 1.75rem; color:white">ESPACE SAISONNIER</h1>
                </div>
            </div>
        </div>
        <div class="article_offres-stage">
            <p>
                Découvrez notre rubrique dédiée <strong>aux saisonniers</strong> ! Chaque année, vous êtes des milliers à trouver des opportunités 
                professionnelles uniques en travaillant dans des secteurs en constante évolution au rythme des saisons. Que ce soit pour <strong>les passionnés de 
                l’hôtellerie et de la restauration</strong> recherchant l'effervescence estivale, le soleil et la mer, <strong>les adeptes de sports d'hiver</strong> attirés 
                par les stations enneigées, ou encore ceux qui trouvent leur vocation dans <strong>l'agriculture saisonnière</strong>, ces métiers offrent une expérience 
                enrichissante et souvent mémorable. <br><br>Nous avons sélectionné pour vous <strong>20 métiers distincts</strong>. Pour chaque métier, vous avez accès 
                directement aux offres d’emploi et vous pouvez postuler directement. Également, à travers <strong>des témoignages et des conseils d'experts</strong>, nous 
                vous donnons toutes les chances de trouver votre job saisonnier pour la prochaine saison !
            </p>
        </div>
    </div>
    <div class="container milieu_page_saisonnier">
        <fieldset class="postes_page_saisonnier">
            <!--legend class="float-none" style="width: 44%">Les postes proposés</legend-->
            <div class="liste_metier">
            {% for metier in metiers %}
                <li>
                    <a href={{ '/lists-offre/?q=' ~ (strtourl(metier.LibelleMetier)) ~ '&localisation=&localisation_field=&id_type_contrat=' }} style="font-size:17px;">{{ metier.LibelleMetier }}</a>
                </li>
            {% endfor %}
            </div>
        </fieldset>
        <div class="cards_container_page_saisonnier">
            <a href="{{ path('app_candidat_search_avancee_result', {'id_secteur': domaines[0].offListeSecteurActiviteIdSecteurActivite,'lien': domaines[0].lien }) }}" class="cards_page_saisonnier" id="agriculture_récolte">
                <h4>Agriculture - Récolte</h4>
            </a>
            <a href="{{ path('app_candidat_search_avancee_result', {'id_secteur': domaines[1].offListeSecteurActiviteIdSecteurActivite,'lien': domaines[1].lien }) }}" class="cards_page_saisonnier" id="hotellerie_restauration">
                <h4>Hotellerie - Restauration</h4>
            </a>
            <a href="{{ path('app_candidat_search_avancee_result', {'id_secteur': domaines[2].offListeSecteurActiviteIdSecteurActivite,'lien': domaines[2].lien }) }}" class="cards_page_saisonnier" id="animation_loisirs">
                <h4>Animation - Loisirs</h4>
            </a>
        </div>
    </div>
    <div class="container actualité_page_saisonnier">
        <h4>L'actualité des saisonniers</h4>
        <div class="actualité_page_saisonnier_cards_wrapper">
            <div class="row" style="justify-content: space-between">
            {% for article in articles %}
                <div class="col">
                        <a href={{ "/article/" ~ article['id_article'] ~ "/" ~ strtourl(article['titre']) }} class="card card-zoom" style="height: 100%">
                            <img src={{ asset("/uploads/article/" ~ article['image_petite'] ) }} class="card-img-top" alt="{{article['image_petite']}}" style="height: 150px; width: 100%; align-self: center">
                            <div class="card-body">
                                <h5 class="card-title">{{article['titre']}}</h5>
                                <p class="card-text">{{article['chapo']|u.truncate(150, '...', false)}}</p>
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">
                                    <!--<strong>Actualité :</strong>
                                        {% if article['theme'] == 'Emploi' and article['type'] != 'Article' %}
                                            {{article['type']}}
                                        {% elseif article['type'] == 'Article' %}
                                            Dossier
                                        {% else %}
                                            {{article['theme']}}
                                        {% endif %} 
                               -->
                               </small>
                            </div>
                        </a>
                    </div>
            {% endfor %}
            </div>
        </div>
    </div>
</section>
{% endblock %}