{#% extends 'directemploi/new_layout.html.twig' %#}
<style>
    /*the container must be positioned relative:*/
    .autocomplete {
        position: relative;
        display: inline-block;
    }




    input[type=text]:focus
    {
        background:transparent;
    }



    .autocomplete-items {
        position: absolute;
        border: 1px solid #d4d4d4;
        border-bottom: none;
        border-top: none;
        z-index: 99 !important;
        background-color: red;
        /*position the autocomplete items to be the same width as the container:*/
        top: 100%;
        left: 0;
        right: 0;
    }

    .autocomplete-items div {
        padding: 10px;
        cursor: pointer;
        background-color: #fff;
        border-bottom: 1px solid #d4d4d4;
    }

    /*when hovering an item:*/
    .autocomplete-items div:hover {
        background-color: #e9e9e9;
    }

    /*when navigating through the items using the arrow keys:*/
    .autocomplete-active {
        background-color: DodgerBlue !important;
        color: #ffffff;
    }
</style>
<form action="{{ path('app_DE_candidat_search') }}" method="get">
    <div class="row align-items-end">
        <div class="col-lg-3">
            <div class="form-group m-0">
                <label for="contratFormControl">Type de contrat</label>
                {{ form_widget(searchForm.contrat, {'attr': { 'class':"form-select", 'name':"contrat", 'id':"contrat"}}) }}
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group m-0">
                <label for="inputposte">Poste recherché</label>
                <div class="autocomplete" style="width:300px;">
                    <input id="poste_input" type="text" name="q" class="form-control" placeholder="Poste recherché" value="{{ app.request.query.get('q', '') }}">
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group m-0">
                <label for="inputlocalisation">Localisation</label>
                <div class="autocomplete" style="width:300px;">
                    <input id="select_box" type="text" name="localisation" class="form-control" placeholder="Région, département, ville" value="{{ app.request.query.get('localisation', '') }}">
                </div>
                {{ form_widget(searchForm.localisation_field) }}
            </div>
        </div>
        <div class="col-lg-3">
            <div class="text-center">
                <button aria-label="Recherche" type="submit" class="btn w-100 px-4 button-black">Recherche</button>
            </div>
        </div>
    </div>
</form>
<script>
    // Function to fetch job names (metiers)
    async function fetchMetiers(key) {
        try {
            let response = await fetch(`/DE/api/metier-search?key=${encodeURIComponent(key)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            let data = await response.json();
            return data;
        } catch (error) {
            console.error('Fetching metiers failed:', error);
            return [];
        }
    }

    // Autocomplete function for job names (metiers)
    function autocompleteMetier(inp) {
        var currentFocus;
        inp.addEventListener("input", async function (e) {
            var a, b, i, val = this.value.trim();
            closeAllLists();
            if (!val) {
                return false;
            }
            currentFocus = -1;

            let metiers = await fetchMetiers(val);

            a = document.createElement("DIV");
            a.setAttribute("id", this.id + "autocomplete-list");
            a.setAttribute("class", "autocomplete-items");
            this.parentNode.appendChild(a);

            for (i = 0; i < metiers.length; i++) {
                let name = metiers[i].libelle_metier;

                if (normalizeString(name).includes(normalizeString(val))) {
                    b = document.createElement("DIV");
                    b.innerHTML = "<strong>" + name.substr(0, val.length) + "</strong>";
                    b.innerHTML += name.substr(val.length);
                    b.innerHTML += "<input type='hidden' value='" + name + "'>";
                    b.addEventListener("click", function (e) {
                        inp.value = this.getElementsByTagName("input")[0].value;
                        closeAllLists();
                    });
                    a.appendChild(b);
                }
            }
        });

        inp.addEventListener("keydown", function (e) {
            var x = document.getElementById(this.id + "autocomplete-list");
            if (x) x = x.getElementsByTagName("div");
            if (e.keyCode == 40) { // Arrow down
                currentFocus++;
                addActive(x);
            } else if (e.keyCode == 38) { // Arrow up
                currentFocus--;
                addActive(x);
            } else if (e.keyCode == 13) { // Enter
                e.preventDefault();
                if (currentFocus > -1 && x) {
                    x[currentFocus].click();
                }
            }
        });

        function addActive(x) {
            if (!x) return false;
            removeActive(x);
            if (currentFocus >= x.length) currentFocus = 0;
            if (currentFocus < 0) currentFocus = (x.length - 1);
            x[currentFocus].classList.add("autocomplete-active");
        }

        function removeActive(x) {
            for (var i = 0; i < x.length; i++) {
                x[i].classList.remove("autocomplete-active");
            }
        }

        function closeAllLists(elmnt) {
            var x = document.getElementsByClassName("autocomplete-items");
            for (var i = 0; i < x.length; i++) {
                if (elmnt != x[i] && elmnt != inp) {
                    x[i].parentNode.removeChild(x[i]);
                }
            }
        }

        document.addEventListener("click", function (e) {
            closeAllLists(e.target);
        });
    }

    // Initialize autocomplete for job names
    autocompleteMetier(document.getElementById("poste_input"));
</script>

<script>
    // Function to fetch locations
    async function fetchLocations(key) {
        try {
            let response = await fetch(`/DE/api/location-search?key=${encodeURIComponent(key)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            let data = await response.json();
            return data;
        } catch (error) {
            console.error('Fetching locations failed:', error);
            return [];
        }
    }

    // Autocomplete function for locations
    function autocompleteLocation(inp) {
        var currentFocus;
        inp.addEventListener("input", async function (e) {
            var a, b, i, val = this.value.trim();
            closeAllLists();
            if (!val) {
                return false;
            }
            currentFocus = -1;

            let locations = await fetchLocations(val);

            a = document.createElement("DIV");
            a.setAttribute("id", this.id + "autocomplete-list");
            a.setAttribute("class", "autocomplete-items");
            this.parentNode.appendChild(a);

            for (i = 0; i < locations.length; i++) {
                let name = locations[i].libelle;
                let type = locations[i].type;
                let codePostal = locations[i].code_postal || '';

                let displayText = codePostal ? `${name} (${codePostal})` : name;

                // On rend la recherche possible aussi sur le code postal
                if (
                    normalizeString(name).includes(normalizeString(val)) ||
                    normalizeString(codePostal).includes(normalizeString(val))
                ) {
                    b = document.createElement("DIV");

                    // Met en gras la partie tapée
                    let matchStart = normalizeString(displayText).indexOf(normalizeString(val));
                    if (matchStart !== -1) {
                        b.innerHTML =
                            displayText.substr(0, matchStart) +
                            "<strong>" +
                            displayText.substr(matchStart, val.length) +
                            "</strong>" +
                            displayText.substr(matchStart + val.length);
                    } else {
                        b.innerHTML = displayText;
                    }

                    b.innerHTML += `<input type='hidden' value='${name}' data-type='${type}' data-codepostal='${codePostal}'>`;

                    b.addEventListener("click", function (e) {
                        // Affiche le libellé avec le code postal dans le champ
                        inp.value = this.getElementsByTagName("input")[0].value + (codePostal ? ` (${codePostal})` : '');
                        let selectedType = this.getElementsByTagName("input")[0].getAttribute('data-type');
                        document.querySelector('[name="localisation_field"]').value = selectedType;
                        closeAllLists();
                    });

                    a.appendChild(b);
                }
            }

        });
        function normalizeString(str) {
            return str
                .normalize("NFD")
                .replace(/[\u0300-\u036f]/g, "")
                .replace(/-/g, " ")
                .toLowerCase();
        }

        inp.addEventListener("keydown", function (e) {
            var x = document.getElementById(this.id + "autocomplete-list");
            if (x) x = x.getElementsByTagName("div");
            if (e.keyCode == 40) { // Arrow down
                currentFocus++;
                addActive(x);
            } else if (e.keyCode == 38) { // Arrow up
                currentFocus--;
                addActive(x);
            } else if (e.keyCode == 13) { // Enter
                e.preventDefault();
                if (currentFocus > -1 && x) {
                    x[currentFocus].click();
                }
            }
        });

        function addActive(x) {
            if (!x) return false;
            removeActive(x);
            if (currentFocus >= x.length) currentFocus = 0;
            if (currentFocus < 0) currentFocus = (x.length - 1);
            x[currentFocus].classList.add("autocomplete-active");
        }

        function removeActive(x) {
            for (var i = 0; i < x.length; i++) {
                x[i].classList.remove("autocomplete-active");
            }
        }

        function closeAllLists(elmnt) {
            var x = document.getElementsByClassName("autocomplete-items");
            for (var i = 0; i < x.length; i++) {
                if (elmnt != x[i] && elmnt != inp) {
                    x[i].parentNode.removeChild(x[i]);
                }
            }
        }

        document.addEventListener("click", function (e) {
            closeAllLists(e.target);
        });
    }

    // Initialize autocomplete for locations
    autocompleteLocation(document.getElementById("select_box"));

    // Detect location type when the input changes
    document.getElementById("select_box").addEventListener("input", function () {
        detectLocationType(this.value);
    });

    async function detectLocationType(locationInput) {
        let input = locationInput.trim().toLowerCase();
        let locations = await fetchLocations(input);
        let locationField = document.querySelector('[name="localisation_field"]');

        let found = locations.find(loc => loc.libelle.toLowerCase() === input);

        if (found) {
            locationField.value = found.type; // Set the type (ville, département, région)
        } else {
            locationField.value = ""; // Clear if no match
        }

        updateFormAction();
    }

    function updateFormAction() {
        let form = document.querySelector("form");
        let locationType = document.querySelector('[name="localisation_field"]').value;

        let searchParams = new URLSearchParams(new FormData(form));
        if (locationType) {
            searchParams.set("localisation_field", locationType);
        }
        searchParams.set("page", "1");

        form.action = form.action.split("?")[0] + "?" + searchParams.toString();
    }
</script>

