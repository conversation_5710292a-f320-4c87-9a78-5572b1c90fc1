{% if path == '/DE/home/' %}
    {% set prefix = '/DE/lists-offre/' %}
{% elseif path == "/theme/offres-stage" %}
    {% set prefix = '../lists-offre/stage-' %}
{% elseif path == '/theme/offres-alternance' %}
    {% set prefix = '../lists-offre/alternance-' %}
{% elseif path == "/theme/offre-emploi-jeune-diplome" %}
    {% set prefix = '../' %}
{% elseif path == "/theme/offres-emploi-interim" %}
    {% set prefix = '../lists-offre/interim-' %}
{% elseif path == "/theme/offres-emploi-cabinet-de-recrutement" %}
    {% set prefix = '../lists-offre/cdi-' %}
{% else %}
    {% set prefix = '/DE/lists-offre/' %}
{% endif %}
<section class="section-margin">

    <h1 class="text-center" ><span class="yellow">Toutes</span> les offres de stage</h1>
    <div class="menu-offres">
        <ul class="navbar-nav h-flex-responsive-nav ">
            <li class="nav-item">
                <a class="nav-link click-scroll active" href="#section_1">Domaine d'activité</a>
            </li>
            <li class="nav-item">
                <a class="nav-link click-scroll" href="#section_2">Région</a>
            </li>

            <li class="nav-item">
                <a class="nav-link click-scroll" href="#section_3">Département</a>
            </li>

            <li class="nav-item">
                <a class="nav-link click-scroll" href="#section_4">Ville</a>
            </li>

        </ul>


        <!-- Métiers -->
        <div id="section_1" class="container-fluid" style="display:block;">
            <p class="my-3" style="font-size: 1.125rem;"><strong>Toutes nos offres de stage classées par domaine d'activité</strong></p>
            <div class="h-flex justify-content-between offres-list">
                <ul style="list-style: none; padding: 0;">
                    {% set counterMetiers = 0 %}
                    {% for metier in listeOffres %}
                    {% if counterMetiers > 0 and counterMetiers % 15 == 0 %}
                </ul>
                <ul style="list-style: none; padding: 0;">
                    {% endif %}
                    <li>
                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #faaf40;font-size: 7px;"></i>
                        <a href="{{ prefix ~ 'france-metier-' ~ struct_home_search(metier.LibelleMetier) ~ '-secteurs' }}">{{ metier.LibelleMetier }}</a>
                    </li>
                    {% set counterMetiers = counterMetiers + 1 %}
                    {% endfor %}
                </ul>
            </div>
        </div>

        <div id="section_2" class="container-fluid" style="display:none;">
            <p class="my-3" style="font-size: 1,125rem;"><strong>Toutes nos offres de stage classées par région</strong></p>
            <div class="h-flex justify-content-between offres-list">
                <ul class="region-list" style="list-style: none; padding: 0; margin-right: 20px; float: left;">
                    {% set counter = 0 %}
                    {% for region in listeRegions %}
                    {% if counter != 0 and counter % 7 == 0 %}
                </ul>
                <ul class="region-list" style="list-style: none; padding: 0; margin-right: 20px; float: left;">
                    {% endif %}
                    <li>
                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #faaf40;font-size: 7px;"></i>
                        <a href="https://www.directetudiant.com/DE/lists-offre/?contrat=&q=&localisation={{ region }}&localisation_field=region">
                            {{ region }}
                        </a>
                    </li>
                    {% set counter = counter + 1 %}
                    {% endfor %}
                </ul>
            </div>
        </div>
        <div id="section_3" class="container-fluid" style="display:none;">
            <p class="my-3" style="font-size: 1,125rem;"><strong>Toutes nos offres de stage classées par département</strong></p>
            <div class="h-flex justify-content-between offres-list" style="flex-wrap: nowrap">
                <ul class="departement-list" style="list-style: none; padding: 0; margin-right: 20px; float: left; width: 25%;">
                    {% for departement in listeDepartements %}
                    {% set counter = ( counter | default(-1) ) + 1 %}
                    {% set array = [25, 51, 77, 103] %}
                    {% if counter != 0 and counter in array %}
                </ul>
                <ul class="departement-list" style="list-style: none; padding: 0; margin-right: 20px; float: left; width: 25%;">
                    {% endif %}
                    {% set cp = departement.CodePostal|raw %}
                    {% if cp|length == 1 %}
                        {% set cp = cp ~ "0" %}
                        {% set cp = cp|reverse %}
                    {% endif %}
                    <li>
                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #faaf40;font-size: 7px"></i>
                        <a href="https://www.directetudiant.com/DE/lists-offre/?contrat=&q=&localisation={{ departement.libelle }}&localisation_field=departement">
                            {{ departement.libelle }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>

            </div>
        </div>
        <div id="section_4" class="container-fluid" style="display:none;">
            <p class="my-3" style="font-size: 1.125rem;"><strong>Toutes nos offres de stage classées par ville</strong></p>

            <div class="d-flex flex-wrap" style="gap: 20px;">
                {% set itemsPerCol = 20 %}
                {% set total = codePostal|length %}
                {% set numCols = (total / itemsPerCol)|round(0, 'ceil') %}

                {% for col in 0..(numCols - 1) %}
                    <ul class="ville-list" style="list-style: none; padding: 0; margin-right: 20px; width: 22%;">
                        {% for i in 0..(itemsPerCol - 1) %}
                            {% set index = col * itemsPerCol + i %}
                            {% if codePostal[index] is defined %}
                                {% set ville = codePostal[index] %}
                                <li>
                                    <i class="fa-solid fa-chevron-right fa-2xs" style="color: #faaf40; font-size: 7px;"></i>
                                    <a href="https://www.directetudiant.com/DE/lists-offre/?contrat=&q=&localisation={{ ville.libelle }}&localisation_field=ville">
                                        {{ ville.libelle }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}
                    </ul>
                {% endfor %}
            </div>
        </div>


    </div>
</section>