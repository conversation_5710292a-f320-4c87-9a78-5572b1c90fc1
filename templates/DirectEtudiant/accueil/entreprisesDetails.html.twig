{% extends 'DirectEtudiant/base.html.twig' %}
{% set lettre = app.request.query.get('lettre') %}
{% set page = app.request.query.get('page') %}
{% if page %}
    {% set title_seo_part = " : Retrouvez les " %}
    {% if totalCountWithStatusOne > 0 %}
        {% set title_seo_part = title_seo_part ~ totalCountWithStatusOne ~ " postes à pourvoir chez " %}
    {% else %}
        {% set title_seo_part = title_seo_part ~ " postes à pourvoir chez " %}
    {% endif %}
    {% set title_seo = entreprise.raisonSociale ~ title_seo_part ~ entreprise.raisonSociale ~ "| Page : " ~ page %}

    {% set fullAddress = '' %}
    {% if adresse.adresse1 is defined %}{% set fullAddress = fullAddress ~ adresse.adresse1 %}{% endif %}
    {% if adresse.adresse2 is defined %}{% set fullAddress = fullAddress ~ ' - ' ~ adresse.adresse2 %}{% endif %}
    {% if adresse.codePostal is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.codePostal %}{% endif %}
    {% if adresse.nomVille is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.nomVille %}{% endif %}
    {% if totalCountWithStatusOne > 0 %}
        {% set description_seo = entreprise.raisonSociale ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationSociete|raw|nl2br ~ "| Page : "~ page~"|"~ totalCountWithStatusOne ~ " postes" %}
    {% else %}
        {% set description_seo = entreprise.raisonSociale ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationSociete|raw|nl2br ~ "| Page : "~ page %}
    {% endif %}

{% else %}
    {% set title_seo_part = " : Retrouvez les " %}
    {% if totalCountWithStatusOne > 0 %}
        {% set title_seo_part = title_seo_part ~ totalCountWithStatusOne ~ " postes à pourvoir chez " %}
    {% else %}
        {% set title_seo_part = title_seo_part ~ " postes à pourvoir chez " %}
    {% endif %}
    {% set title_seo = entreprise.raisonSociale ~ title_seo_part ~ entreprise.raisonSociale %}

    {% set fullAddress = '' %}
    {% if adresse.adresse1 is defined %}{% set fullAddress = fullAddress ~ adresse.adresse1 %}{% endif %}
    {% if adresse.adresse2 is defined %}{% set fullAddress = fullAddress ~ ' - ' ~ adresse.adresse2 %}{% endif %}
    {% if adresse.codePostal is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.codePostal %}{% endif %}
    {% if adresse.nomVille is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.nomVille %}{% endif %}
    {% if totalCountWithStatusOne > 0 %}
        {% set description_seo = entreprise.raisonSociale ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationSociete|raw|nl2br ~ "| Page : "~ page~"|"~ totalCountWithStatusOne ~ " postes" %}
    {% else %}
        {% set description_seo = entreprise.raisonSociale ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationSociete|raw|nl2br ~ "| Page : "~ page %}
    {% endif %}
{% endif %}

{% block title %}{{ title_seo[:150] }}{% endblock %}
{% block title_og %}{{ title_seo[:150] }}{% endblock %}
{% block title_twitter %}{{ title_seo[:150] }}{% endblock %}
{% block meta_description %}{{ description_seo[:550] }}{% endblock %}
{% block meta_description_og %}{{ description_seo[:550] }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo[:550] }}{% endblock %}
{% block body %}
<div class="is-sticky">
    {% block head %}
    {% include 'DirectEtudiant/header.html.twig' %}
{% endblock %}
    <style>
        .offres-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .offres-grid .offre-item {
            border: 1px solid #ddd;
            padding: 1rem;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            height: 100%; /* Ensure all cards take full height of their container */
            display: flex;
            flex-direction: column;
            justify-content: space-between; /* Distribute space evenly */
        }

        .offres-grid .offre-item a {
            color: #13A3AA;
            text-decoration: none;
        }

        .offres-grid .offre-item a:hover {
            text-decoration: underline;
        }

        .offres-grid .offre-item .text-secondary {
            color: #6c757d;
        }

        .offres-grid .offre-item .fa-chevron-right {
            margin-right: 5px;
        }

        /* Ensure all cards have the same height */
        .offres-grid .col-md-3 {
            display: flex;
        }

        .offres-grid .col-md-3 > .offre-item {
            flex: 1; /* Make cards stretch to fill the height */
        }

        @media (max-width: 768px) {
            .offres-grid .col-md-3 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .offres-grid .col-md-3 {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }
    </style>
    <section class="ajouter-cv-header" id="">
        <div class="container">
            {% if breadcrumb is defined %}
                {% include '/_partials/_breadcrumb.html.twig'  %}
            {% endif %}
            <h1 class="text-center text-uppercase mb-3" style="font-size: 1.25rem; line-height: 1.4;"><span class="yellow">Emploi</span> {{entreprise.raisonSociale}} ({{ totalCountWithStatusOne }} offres en ligne)</h1>
        </div>
    </section>
    <section class="contrat-travail">
        <div class="container">



    <section class="la-une entreprise-profile">
        <div class="container">
            <div class="w-100 card-entreprise">
                <div class="row">
                    <div class="col-lg-8">
                        <h6>{{entreprise.raisonSociale}}</h6>
                        <p>{{fullAddress}}</p>
                        <a href="{{entreprise.siteWeb}}">{{entreprise.siteWeb}}</a>
                    </div>
                    <div class="col-lg-4">

                        <div class="w-100 vertical-center">
                            {% if entreprise.logo != null %}
                                <div class="logo-entreprise ">
                                    <img src="{{ asset('/uploads/logos/' ~ entreprise.logo) }}" alt="{{entreprise.raisonSociale}}">
                                </div>
                            {% else %}
                                <div class="logo-entreprise ">
                                    <img src="{{ asset('carriereDE/Front/images/logo noir.png') }}" alt="{{entreprise.raisonSociale}}">
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="content">
        <div class="container">
            <div class="header-and-text">
                <h6>Présentation de l'entreprise</h6>
                <hr>
                <p class="justified">{{entreprise.presentationSociete|raw}}</p>

            </div>
        </div>

    </section>
    {% set stageCount = offres|filter(offres => offres.type_contrat == 'Stage')|length %}
    {% set alternanceCount = offres|filter(offres => offres.type_contrat == 'Alternance')|length %}

    <section class="entreprise-emploi">
        <div class="container">
            <div class="menu-offres">
                <ul class="navbar-nav h-flex">
                    {% if stageCount > 0 %}
                        <li class="nav-item">
                            <a class="nav-link click-scroll" href="#" data-filter="stage">Stage</a>
                        </li>
                    {% endif %}
                    {% if alternanceCount > 0 %}
                        <li class="nav-item">
                            <a class="nav-link click-scroll" href="#" data-filter="alternance">Alternance</a>
                        </li>
                    {% endif %}

                </ul>
            </div>

            <div class="container-fluid">
                <div class="h-flex justify-content-between offres-grid pt-5">
                    <div class="row" id="offres-grid">
                        {% for offre in offres %}
                            {% if offre.type_contrat == 'Alternance' or offre.type_contrat == 'Stage' %}
                                <div class="col-md-3 col-sm-6 mb-4" data-type-contrat="{{ offre.type_contrat }}">
                                    <div class="offre-item">
                                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #13A3AA; font-size: 7px;"></i>
                                        <a href="{{ path('app_DE_submitCandidat', { id: offre['id_offre'] }) }}">{{ offre["intitule"]|raw }}</a>
                                        <div class="px-1">
                                            <p class="m-0 text-secondary">Date de publication : {{ offre.date_soumission | date('d/m/Y') }}</p>
                                            <p class="m-0 text-secondary">Type de contrat : {{ offre.type_contrat }}</p>
                                            <p class="m-0 text-secondary">
                                                Localisation : {{ offre.geo_ville }}{% if offre.departement %} - {{ offre.departement }}{% endif %}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>

                </div>
            </div>


        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const tabs = document.querySelectorAll('.nav-link.click-scroll');
                const offersGrid = document.getElementById('offres-grid').children;

                tabs.forEach(tab => {
                    tab.addEventListener('click', function(event) {
                        event.preventDefault();

                        // Retirer la classe 'active' de tous les onglets
                        tabs.forEach(t => t.classList.remove('active'));

                        // Ajouter la classe 'active' à l'onglet cliqué
                        this.classList.add('active');

                        const filter = this.getAttribute('data-filter');

                        // Afficher/Masquer les offres selon l'onglet sélectionné
                        Array.from(offersGrid).forEach(offer => {
                            const typeContrat = offer.getAttribute('data-type-contrat');

                            if (filter === 'emploi' && ['CDI', 'CDD', 'Interim', 'Freelance'].includes(typeContrat)) {
                                offer.style.display = 'block';
                            } else if (filter === 'alternance' && typeContrat === 'Alternance') {
                                offer.style.display = 'block';
                            } else if (filter === 'stage' && typeContrat === 'Stage') {
                                offer.style.display = 'block';
                            } else {
                                offer.style.display = 'none';
                            }
                        });
                    });
                });

                // Scroll down button functionality
                const scrollDownButton = document.getElementById('scroll-down-button');
                scrollDownButton.addEventListener('click', function() {
                    window.scrollTo({
                        top: document.body.scrollHeight,
                        behavior: 'smooth'
                    });
                });
            });
        </script>
    </section>
    {% block footer %}
        {% include 'DirectEtudiant/footer.html.twig' %}
    {% endblock %}
</div>



{% endblock %}
