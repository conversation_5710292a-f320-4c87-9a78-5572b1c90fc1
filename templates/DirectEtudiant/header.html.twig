<style>
    .dropdown-emploi:hover .dropdown-menu {
        display: block;
    }
</style>
{% set current_path = app.request.attributes.get('_route') %}
<nav class="navbar navbar-expand-lg "style="position:fixed; z-index: 999;">
    <div class="container">
        <div class="v-flex-center w-100">
            <div class="d-flex flex-row align-items-center ">

                <button class="navbar-toggler mx-3" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <a href="{{ path('app_DE_accueil') }}"class="navbar-brand mx-auto mx-lg-0"><img class="logo" src=""/></a>
                <!--
                            <div class="d-flex align-items-center d-lg-none">
                                <i class="navbar-icon bi-telephone-plus me-3"></i>
                                <a class="custom-btn btn" href="#section_5">
                                    ************
                                </a>
                            </div>
                -->
            </div>
            <div class="collapse navbar-collapse ms-auto" id="navbarNav">
                <ul class="navbar-nav ">
                    <li class="nav-item">
                        <a class="nav-link {% if current_path == 'app_DE_accueil' %}active{% endif %}" href="{{ path('app_DE_accueil') }}">Accueil</a>
                    </li>
                    {#
                    <li class="nav-item">
                        <a class="nav-link {% if current_path starts with 'app_DE_candidat_search' %}active{% endif %} " href="{{ path('app_DE_candidat_search') }}">Emploi</a>
                    </li>
                    #}
                    <li class="nav-item">
                        <a class="nav-link {% if current_path == 'app_DE_entreprises' %}active{% endif %}" href="{{ path('app_DE_entreprises') }}">Entreprises</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {% if current_path == 'app_DE_actualites' %}active{% endif %}" href="{{ path('app_DE_actualites') }}">Actualités</a>
                    </li>
                    <div class="dropdown dropdown-conseil">
                        <li class="nav-item separator">
                            <a class="nav-link " href="{{ path('app_DE_conseils') }}">Conseils</a>
                        </li>
                        <div class="dropdown-content content-conseil"style="left: 0;">
                            <a href="{{ path('app_DE_conseilsCV') }}">Le CV</a>
                            <a href="{{ path('app_DE_conseilsmotivation') }}">La lettre de motivation</a>
                            <a href="{{ path('app_DE_conseilsembauche') }}">L'entretien d'embauche</a>
                            <a href="{{ path('app_DE_conseilsrechercheEmploi') }}">La recherche d'emploi</a>
                        </div>
                    </div>

                    {% if app.user is null %}
                        <div class="dropdown dropdown-recruteur">
                            <li class="nav-item">
                                <a class="nav-link" ><img class="nav-icon" src="{{ asset('DirectEtudiant/Front/images/mdi-worker.svg')}}" />Candidats</a>
                            </li>
                            <div class="dropdown-content content-recruteur" style="left: 0;">
                                <a href="{{ path('app_DE_login') }}">Connexion</a>
                                <a href="{{ path('app_DE_register_candidat_e1') }}">Création de compte</a>
                            </div>

                        </div>
                        <div class="dropdown dropdown-candidat" style="float:right;">

                            <li class="nav-item">
                                <a class="nav-link" > <img class="nav-icon" src="{{ asset('DirectEtudiant/Front/images/dashicons-businessman.svg')}}"/>Recruteurs</a>
                            </li>
                            <div class="dropdown-content content-candidat">
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Connexion</a>
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Création de compte</a>
                                <div class="px-3">
                                    <hr>
                                </div>
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Les offres proposées</a>
                                <a href="javascript:void(0);" onclick="showSweetAlert(this)">Le réseau Direct Emploi</a>

                            </div>
                        </div>
                    {% else %}
                        <div class="dropdown dropdown-recruteur">
                            <li class="nav-item">
                                <a href="{{ path('app_DE_espaceCandidat') }}" class="nav-link  click-scroll" ><img class="nav-icon" alt="worker" src="{{ asset('DirectEtudiant/Front/images/mdi-worker.svg')}}" />{{ app.user.nom }} {{ app.user.prenom }}</a>
                            </li>
                            <div class="dropdown-content content-recruteur" style="left: 0;float:right;">
                                <a href="{{ path('app_DE_espaceCandidat') }}">Mon Profil</a>
                                <a href="{{ path('app_DE_espaceCandidatprofilcv') }}">Mon CV</a>
                                <a href="{{ path('app_DE_espaceCandidatprofiloffre') }}">Mes offres</a>
                                <a href="{{ path('app_DE_espaceCandidatprofilmail') }}">Mes alertes mail</a>
                                {% for role in app.user.roles %}
                                    {% if role == "ROLE_ADMIN" %}
                                        <a href="{{ path('app_BTP_admin') }}">Espace Admin</a>
                                    {% endif %}
                                {% endfor %}
                                <div class="px-3">
                                    <hr class="py-0 my-1">
                                </div>
                                <a href="{{ path('app_logout') }}" class="text-danger">Se deconnecter</a>
                            </div>
                        </div>
                    {% endif %}
                </ul>
            </div>
        </div>
</nav>
