{% extends 'DirectEtudiant/base.html.twig' %}
{% set title = offreDetails[0].intitule|raw ~ " - " ~  offreDetails[0].reference ~ " - " ~ offreDetails['libelleContrat']|raw %}
{% if offreDetails[0].geoVille is defined and offreDetails[0].geoVille != null %}{% set title = title ~ " " ~ offreDetails[0].geoVille %}
{% elseif departement is defined and departement != null %}{% set title = title ~ " " ~ departement.libelle %}
{% elseif region.libelle is defined and region.libelle != null %}{% set title = title ~ " " ~ region.libelle %}{% endif %}
{% set title = title[:100] %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}

{% set description_string = "retrouver tous les details de notre offre " ~ offreDetails[0].intitule|raw ~ " " ~ offreDetails[0].geoVille|raw ~ " " ~ offreDetails[0].reference|raw ~ " sur Direct Etudiant" %}
{% block meta_description %}{{ description_string }}{% endblock %}
{% block meta_description_og %}{{ description_string }}{% endblock %}
{% block meta_description_twitter %}{{ description_string }}{% endblock %}

{% block body %}

<div class="is-sticky">
    {% block head %}
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

        {% include 'DirectEtudiant/header.html.twig' %}
{% endblock %}
<style>
    .offre-form-header {
        position: relative;
        /* autres styles nécessaires */
    }

    .btn-passer-au-formulaire {
        position: absolute;
        bottom: 0;
        right: 0;
        padding: 10px 20px;
        background-color: #13A3AA; /* Choisissez la couleur du bouton */
        color: white; /* Couleur du texte */
        border: none;
        border-radius: 5px;
        text-decoration: none;
        font-weight: bold;
    }

    .btn-passer-au-formulaire:hover {
        background-color: #d98c35; /* Couleur au survol */
    }

</style>
    <section class="offre-form-header d-flex justify-content-center align-items-center" id="">
        <h1 class="text-center text-uppercase mb-0" style="font-size: 1.25rem; line-height: 1.5;">
            <span>DESCRIPTIF DE L'OFFRE</span>
        </h1>
        <!-- Bouton ajouté ici -->
        <a href="#offre-form" class="btn-passer-au-formulaire">
            Passez au formulaire
        </a>
    </section>


    <section class="login-form">

        <div class="container">
            <div class="row">
                <div class="col-lg-8">
            <div class="card p-5 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
                <h6 class="mb-3">Informations clés</h6>
                <ul>
                    <li class="mb-2">
                        {% set contrat = offreDetails[0].OffListeTypeContratIdTypeContrat %}
                        {# {% if contrat is defined and contrat == 1 or contrat == 2 or contrat == 3 %} #}
                            {% if (offreDetails[0].OffListeTypeContratIdTypeContrat == 1) or offreDetails[0].OffListeTypeContratIdTypeContrat == 2 %}
                                <b>Offre d'emploi :</b>
                            {% elseif (offreDetails[0].OffListeTypeContratIdTypeContrat == 3) %}
                                <b>Offre en freelance :</b>
                            {% elseif (offreDetails[0].OffListeTypeContratIdTypeContrat == 4) %}
                                <b>Offre en interim :</b>
                            {% elseif (offreDetails[0].OffListeTypeContratIdTypeContrat == 6) %}
                                <b>Offre en alternance :</b>
                            {% elseif (offreDetails[0].OffListeTypeContratIdTypeContrat == 7 or offreDetails[0].OffListeTypeContratIdTypeContrat == 8) %}
                                <b>Offre de stage</b>
                            {% else %}
                                <b>Offre d'emploi :</b>
                            {% endif %}
                        {# {% endif %} #}
                        <span>
                            {% if offreDetails[0].intitule is defined and offreDetails[0].intitule != null %}
                                {{ offreDetails[0].intitule|raw }}
                            {% endif %}
                        </span>
                    </li>

                    {% if offreDetails['raison_sociale'] is defined and offreDetails['raison_sociale'] != null %}
                    <li class="mb-2"><b>Société</b><span> : {{offreDetails['raison_sociale']}}</span> </li>
                    {% endif %}

                    {% if offreDetails[0].dateSoumission is defined and offreDetails[0].dateSoumission != null %}
                    <li class="mb-2"><b>Date de publication</b> : {{offreDetails[0].dateSoumission|date('d/m/Y')}} </li>
                    {% endif %}

                    {% if offreDetails[0].reference is defined and offreDetails[0].reference != null %}
                    <li class="mb-2"><b>Reference de l'offre</b> : {{ offreDetails[0].reference }} </li>
                    {% endif %}

                    {% if offreDetails['libelleSecteur'] is defined and offreDetails['libelleSecteur'] != null %}
                    <li class="mb-2"><b>Secteur d'activité</b> : {{ offreDetails['libelleSecteur']}} </li>
                    {% endif %}

                    {% if region.libelle is defined and region.libelle != null %}
                    <li class="mb-2"><b>Localisation</b> :  <a href="{{ path('app_candidat_emploi_region', {region: region.idRegion }) }}">{{ region.libelle }}</a>
                        :
                        {% if departement is defined and departement != null %}
                        <a href="{{ path('app_candidat_emploi_departement', {departement: departement.idDepartement }) }}">{{ departement.libelle }}</a>
                        {% endif %}
                        {% if offreDetails[0].geoVille is defined and offreDetails[0].geoVille != null %}
                                -
                            {{offreDetails[0].geoVille}}
                        {% endif %}
                    </li>
                    {% endif %}


                    {% if offreDetails['libelleContrat'] is defined and offreDetails['libelleContrat'] != null %}
                    <li class="mb-2"><b>Type de contrat </b>: <a href="{{ path('app_candidat_emploi_contrat', {contrat: offreDetails['id_type_contrat']}) }}">{{offreDetails['libelleContrat']}}</a> </li>
                    {% endif %}


                    {% if (dateDebut is defined and dateDebut != null and dateDebut != '0000-00-00') %}
                    <li class="mb-2"><b>Debut du contrat</b> : {{ dateDebut }} </li>
                    {% endif %}


                    {% if (offreDetails[0].DureeContrat is defined and offreDetails[0].DureeContrat != null) %}
                    <li class="mb-2"><b>Durée du contrat</b> : {{ offreDetails[0].DureeContrat }} </li>
                    {% endif %}


                    {% if offreDetails['libelleRemuneration'] is defined and offreDetails['libelleRemuneration'] != null %}
                    <li class="mb-2"><b>Salaire </b> : {{ offreDetails['libelleRemuneration']}} </li>
                    {% endif %}

                    {% if offreDetails['libelleExperience'] is defined and offreDetails['libelleExperience'] != null %}
                        <li class="mb-2"><b>Expérience requise </b> : {{ offreDetails['libelleExperience'] }}</li>
                    {% endif %}

                    {% if offreDetails['libelleFormation'] is defined and offreDetails['libelleFormation'] != null %}
                        <li class="mb-2"><b>Formation requise </b> : {{ offreDetails['libelleFormation'] }}</li>
                    {% endif %}




                </ul>
            </div>
            </div>
                <div class="col-lg-4">
                    <div class="info_entre" style="text-align: center;">
                        <div class="img img_logo">
                            <div class="int">
                                <img src="{{ asset('/uploads/logos/' ~ offreDetails['logo']) }}" alt="{{offreDetails['logo']}}" style="width: 100%; height: 100%">
                            </div>
                        </div>
                        <div class="voir_toutes_offres">
                            <a href="{{ path('app_DE_entreprisesDetails', {id: offreDetails['id_entreprise'], slug: offreDetails['raison_sociale'] | raison }) }}"  title="Découvrir l'entreprise">Découvrir
                                <span class="ordi">l'entreprise,</span>
                                toutes les offres
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card p-5 mt-3" style="background-color:#F9F9F9; border-radius:20px;">


                {% if (offreDetails[0].isAnonym == 0 and offreDetails[0].nomSociete == null) %}
                <h6 class="mb-2">L'entreprise</h6>
                <hr>
                    <div>
                {{offreDetails['presentation_societe']|raw|nl2br}}
                    </div>
                {% endif %}
                <h6 class="mb-2 mt-3">Description de l'offre</h6>
                <hr>
                <div>
                    {{offreDetails[0].mission|raw|nl2br}}
                </div>
                {% if offreDetails[0].profil != '' %}
                <h6 class="mb-2 mt-3">Profil recherché</h6>
                <hr>
                    <div>
                        {{offreDetails[0].profil|raw|nl2br}}
                    </div>
                {% endif %}




            </div>
            <hr>
            <section id="offre-form">
            <section class="offre-form-header d-flex justify-content-center align-items-center" id="">
                <h5 class="text-center text-uppercase mb-0">Formulaire de réponse à l’offre</h5>
            </section>

                {{ form_start(form) }}
                <div class="info-section mb-0">
                    {{ form_widget(form.id_offre, {'attr': {'value': offreDetails[0].idOffre }}) }}
                    <div class="row">
                        <div class="col-lg-4 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.nom, "Nom") }}
                                <span class="required-asterisk">*</span>
                                {{ form_widget(form.nom, {'attr': {'class': 'form-control', 'placeholder': 'Nom', 'required': 'required'}}) }}
                                {{ form_errors(form.nom) }}
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.prenom, "Prénom") }}
                                {% if form.prenom.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.prenom, {'attr': {'class': 'form-control', 'placeholder': 'Prénom'}}) }}
                                {{ form_errors(form.prenom) }}
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.user_civilite_id_user_civilite, "Civilité") }}
                                {% if form.user_civilite_id_user_civilite.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.user_civilite_id_user_civilite, {'attr': {'class': 'form-select form-control'}}) }}
                                {{ form_errors(form.user_civilite_id_user_civilite) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">

                        <div class="col-lg-4 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.email, "Email") }}
                                {% if form.email.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.email, {'attr': {'class': 'form-control', 'placeholder': 'Email'}}) }}
                                {{ form_errors(form.email) }}
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.telephone, "Téléphone") }}
                                {% if form.telephone.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.telephone, {'attr': {'class': 'form-control', 'placeholder': 'Téléphone'}}) }}
                                {{ form_errors(form.telephone) }}
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.code_postal, "Code postal") }}
                                {% if form.code_postal.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.code_postal, {'attr': {'class': 'form-control', 'placeholder': 'Code postal'}}) }}
                                {{ form_errors(form.code_postal) }}
                            </div>
                        </div>
                    </div>

                </div>


                <div class="info-section mb-0">
                    <hr>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.domaine_activite, "Domaine d'expertise") }}
                                {% if form.domaine_activite.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.domaine_activite, {'attr': {'class': 'form-select form-control'}}) }}
                                {{ form_errors(form.domaine_activite) }}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.liste_formation, "Niveau de formation") }}
                                {% if form.liste_formation.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.liste_formation, {'attr': {'class': 'form-select form-control'}}) }}
                                {{ form_errors(form.liste_formation) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.metier_metier, "Métier") }}
                                {% if form.metier_metier.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.metier_metier, {'attr': {'class': 'form-select form-control'}}) }}
                                {{ form_errors(form.metier_metier) }}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.promo, "Année d'obtention du diplôme") }}
                                {% if form.promo.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.promo, {'attr': {'class': 'form-control', 'placeholder': 'Année'}}) }}
                                {{ form_errors(form.promo) }}
                            </div>
                        </div>

                    </div>

                </div>
                <div class="info-section mb-0">
                    <hr>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group" >
                                {{ form_label(form.cv, "Ajouter votre CV") }}
                                {% if form.cv.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.cv, {'attr': {'class': 'form-control', 'placeholder': 'CV'}}) }}
                                {{ form_errors(form.cv) }}
                                <p class="text-muted">Taille maximale du fichier : 1Mo</p>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group" style="margin-bottom:0;">
                                {{ form_label(form.lm, "Lettre de motivation") }}
                                {% if form.lm.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.lm, {'attr': {'class': 'form-control', 'placeholder': 'Lettre de motivation'}}) }}
                                {{ form_errors(form.lm) }}
                                <p class="text-muted">Taille maximale du fichier : 1Mo</p>
                            </div>

                        </div>

                    </div>
                </div>
                <div class="info-section mb-0">

                    <hr>
                    <div class="row">

                        <div class="col-12">
                            <div class="bottom-login">

                                {# <i class="fa-regular fa-square-check" class="blue" style=" font-size:42px"></i> #}
                                <div>
                                    <p><strong>J'accepte <a href="https://www.directemploi.com/page/mentions-legales"class="">les conditions générales</a> de Direct Etudiant</strong></p>
                                    <p>- Coordonnées transmises aux recruteurs, lecture du CV à des fins de recrutement</p>
                                    <p>- Envois potentiels d’offres d’emploi, stage, alternance, formations et communications partenaires</p>


                                </div>
                                <div class="form-check" style="margin-left:auto;">
                                    {{ form_widget(form.cgu, {'attr': {'class': 'form-check-input', 'style': 'max-height: 24px;'}}) }}
                                    <span class="required-asterisk">*</span>

                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="text-center">
                    <button type="submit" style="background-color: #ffca2c" class="btn mb-2 px-4 button-inscription">Postuler mantenant</button>
                </div>
                {{ form_end(form) }}
            <div class="card p-5 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
                <h6 class="mb-3">Attention</h6>
                <ul>
                    <li class="mb-2">Nous vous conseillons de désactiver les bloqueurs de publicité de type AdBlock pour assurer la bonne validation du formulaire.</li>
                    <li>Si vous ne parvenez pas à postuler, utilisez notre <a href="#" class="yellow">formulaire simplifié.</a></li>

                </ul>
            </div>

            </section>
        </div>
    </section>

    {% block footer %}
        {% include 'DirectEtudiant/footer.html.twig' %}
    {% endblock %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const domaineSelect = document.querySelector('#submit_candidature_domaine_activite'); // Adjust the ID as necessary
            const metierSelect = document.querySelector('#submit_candidature_metier_metier'); // Adjust the ID as necessary

            domaineSelect.addEventListener('change', function() {
                const selectedId = this.value;
                const url = `https://www.directetudiant.com/DE/ajax/get-metiers/${selectedId}`;

                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        metierSelect.innerHTML = ''; // Clear existing options
                        data.forEach(metier => {
                            const option = new Option(metier.libelle_metier, metier.id);
                            metierSelect.add(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching métiers:', error);
                    });
            });
        });

    </script>
</div>

{% endblock %}