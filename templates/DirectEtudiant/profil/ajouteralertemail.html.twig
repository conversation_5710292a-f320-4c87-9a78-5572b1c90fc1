{% extends 'DirectEtudiant/base.html.twig' %}
{% block title %}Ajouter Alert Mail{% endblock %}

{% block body %}

<div class="is-sticky">
    {% block head %}
    {% include 'DirectEtudiant/header.html.twig' %}
{% endblock %}

    <section class="login-header d-flex justify-content-center align-items-center" id="">
        <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#13A3AA"> ESPACE </span>CANDIDAT</h1>
    </section>
    <section class="login-form">


        <div class="container">
            {{ form_errors(InscriptionEtape3Form) }}
            {{ form_start(InscriptionEtape3Form) }}
            <div class="info-section">
                {{ form_row(InscriptionEtape3Form._token) }} <!-- Add the CSRF token here -->
                <h6>Création / Édition d'une alerte mail</h6>
                <hr>
                <div class="row">
                    <div class="col-lg-12 col-sm-12">
                        <div class="form-group">
                            <label for="inputalerte">Nom de mon alerte</label>
                            {{ form_row(InscriptionEtape3Form.nom_alerte, {'label': false}) }}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label for="contratFormControl">Type de contrat *</label>
                            {{ form_row(InscriptionEtape3Form.contrat, {'label': false}) }}
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label for="experienceFormControl">Experience</label>
                            {{ form_row(InscriptionEtape3Form.experience, {'label': false}) }}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label for="lang2FormControl">Domaine de votre métier * </label>
                            {{ form_row(InscriptionEtape3Form.domaine_activite, {'attr': {'class': 'domaine-activite-liste form-control'},'label': false}) }}
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label for="niveau2FormControl">Région *</label>
                            {{ form_row(InscriptionEtape3Form.geo_liste_region, {'attr': {'class': 'geo_liste_region form-control'},'label': false}) }}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label for="lang2FormControl">Métier</label>
                            {{ form_row(InscriptionEtape3Form.metier_metier, {'attr': {'class': 'metier-metier-liste form-control'},'label': false}) }}

                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label for="niveau2FormControl">Département</label>
                            {{ form_row(InscriptionEtape3Form.geo_departement, {'attr': {'class': 'geo_departement form-control'},'label': false}) }}
                        </div>
                    </div>
                </div>
                <div class="form-check">
                    {{ form_label(InscriptionEtape3Form.handi, 'Handi :', {'label_attr': {'class': 'col-form-label d-inline pt-0'}}) }}

                    <div class="form-check mx-2">
                        {{ form_widget(InscriptionEtape3Form.handi[0], {'attr': {'class': 'form-check-input'}}) }}
                        {{ form_label(InscriptionEtape3Form.handi[0], 'Non', {'label_attr': {'class': 'form-check-label'}}) }}
                    </div>
                    <div class="form-check mx-2">
                        {{ form_widget(InscriptionEtape3Form.handi[1], {'attr': {'class': 'form-check-input'}}) }}
                        {{ form_label(InscriptionEtape3Form.handi[1], 'Oui', {'label_attr': {'class': 'form-check-label'}}) }}
                    </div>
                </div>
            </div>
            <div class="text-center">
                <button type="submit" class="btn mb-3 px-4 button-black">Enregistrer</button>
            </div>
            <div class="text-center">
                <a href="{{ path('app_DE_espaceCandidatprofilmail') }}" type="button" class="btn btn-outline-secondary btn-outline-grey px-4">Retour</a>
            </div>
            </form>

        </div>

    </section>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var domaineActiviteListe = document.querySelector('.domaine-activite-liste');
            var metierMetierListe = document.querySelector('.metier-metier-liste');
            domaineActiviteListe.addEventListener('change', function () {
                var selectedDomaineActivite = this.value;
                // Simulate fetching data from the server
                fetch('/ajax/get-metiers/' + selectedDomaineActivite)
                    .then(response => response.json())
                    .then(data => {
                        // Clear the options
                        metierMetierListe.innerHTML = '';

                        // Update the options
                        data.forEach(function (metier) {
                            var option = document.createElement('option');
                            option.value = metier.id;
                            console.log(metier.id)
                            option.text = metier.libelle_metier;
                            metierMetierListe.add(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching metiers:', error);
                    });
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var geo_liste_region = document.querySelector('.geo_liste_region');
            var geo_departement = document.querySelector('.geo_departement');
            geo_liste_region.addEventListener('change', function () {
                var selectedgeo_liste_region = this.value;
                // Simulate fetching data from the server
                fetch('/ajax/get-departements/' + selectedgeo_liste_region)
                    .then(response => response.json())
                    .then(data => {
                        // Clear the options
                        geo_departement.innerHTML = '';

                        // Update the options
                        data.forEach(function (departement) {
                            var option = document.createElement('option');
                            option.value = departement.id;
                            console.log(departement)
                            option.text = departement.libelle;
                            geo_departement.add(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching metiers:', error);
                    });
            });
        });
    </script>
    {% block footer %}
        {% include 'DirectEtudiant/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}