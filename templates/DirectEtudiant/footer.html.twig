
<footer class="site-footer">
    <div class="text-center image-footer">
        <img class="img-fluid" alt="logo noir" src="{{ asset('DirectEtudiant/Front/images/logo-black.png') }}"/>
    </div>
    <div class="container">
        <div class="footer-all-menus">

            <div class="d-flex flex-column align-items-left footer-menu">
                <p  class="footer-menu-title">À propos</p>
                <a href="{{ path('app_DE_quisommenous') }}"  class="footer-menu-text">Qui sommes nous ?</a>
                <a href="{{ path('app_DE_informationlegal') }}" class="footer-menu-text">Informations légales</a>
                <a href="{{ path('app_DE_confidentialite') }}"  class="footer-menu-text"  >Politique de confidentialité</a>
                <a href="{{ path('app_DE_chartecookies') }}" class="footer-menu-text">Charte Cookies</a>
                <a href="{{ path('app_DE_plansite') }}" class="footer-menu-text">Plan du site</a>
                <a href="{{ path('app_DE_contact') }}"  class="footer-menu-text">Contact</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <p  class="footer-menu-title">Recruteurs</p>
                <a href="{{ path('app_DE_societe') }}" class="footer-menu-text">La société</a>
                <a href="javascript:void(0);" onclick="showSweetAlert(this)"      class="footer-menu-text">S'inscrire</a>
                <a  href="javascript:void(0);" onclick="showSweetAlert(this)" class="footer-menu-text">Le réseau Direct Emploi</a>
                <a href="javascript:void(0);" onclick="showSweetAlert(this)" class="footer-menu-text">Les offres proposées</a>
                <a href="{{ path('app_DE_chartedurecrutement') }}" class="footer-menu-text">Charte du recrutement</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <p  class="footer-menu-title">Candidats</p>
                <a href="{{ path('app_DE_register_candidat_e1') }}" class="footer-menu-text">S'inscrire</a>
                <a href="{{ path('app_DE_conseilsCV') }}"class="footer-menu-text">Conseils CV</a>
                <a href="{{ path('app_DE_conseilsmotivation') }}" class="footer-menu-text">Lettre de motivation</a>
                <a href="{{ path('app_DE_conseilsembauche') }}" class="footer-menu-text">Entretien d'embauche</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <p href="#" class="footer-menu-title">Stage</p>
                <a href="{{ path('app_DE_Parcourirlesoffresdemploi_stage') }}" class="footer-menu-text">Offres de stage par métier</a>
                <a href="{{ path('app_DE_ParcourirlesoffresdemploiVille') }}" class="footer-menu-text">Offres de stage par ville</a>
                <a href="{{ path('app_DE_entreprises') }}" class="footer-menu-text">Offres de stage par entreprise</a>
                <a href="{{ path('app_DE_index_mot_cle') }}" class="footer-menu-text">Offres de stage par mots clés</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <p href="#" class="footer-menu-title">Emploi en alternance </p>
                <a href="{{ path('app_DE_Parcourirlesoffresdemploi_alternance') }}" class="footer-menu-text">Offres en alternance par métier</a>
                <a href="{{ path('app_DE_ParcourirlesoffresdemploiVille_alternance') }}" class="footer-menu-text">Offres en alternance par ville</a>
                <a href="{{ path('app_DE_entreprises') }}" class="footer-menu-text">Offres en alternance par entreprise</a>
                <a href="{{ path('app_DE_index_mot_cle_alternance') }}" class="footer-menu-text">Offres en alternance par mots clés</a>
            </div>
            {#
            <div class="d-flex flex-column align-items-left footer-menu">
                <p href="#"  class="footer-menu-title">Domaines les + demandés</p>
                <a href="#" title="Santé / Paramédical / Biologie / Pharmacie" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Biologie</a>
                <a href="#" title="Santé / Paramédical / Biologie / Pharmacie" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Médical</a>
                <a href="#" title="Santé / Paramédical / Biologie / Pharmacie" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Médico-social</a>
                <a href="#" title="Santé / Paramédical / Biologie / Pharmacie" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Génie Paramédical</a>
                <a href="#" title="Santé / Paramédical / Biologie / Pharmacie" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Pharmacie</a>
                <a href="#" title="Santé / Paramédical / Biologie / Pharmacie" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Santé</a>
            </div>
            <div class="d-flex flex-column align-items-left footer-menu">
                <p  class="footer-menu-title">Métiers les + demandés</p>
                <a href="#" title="Emploi Aide à la personne" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Aide à la personne</a>
                <a href="#" title="Emploi Auxiliaire de vie" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Auxiliaire de vie</a>
                <a href="#" title="Emploi Infirmier" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Infirmier</a>
                <a href="#" title="Emploi Kinésithérapeute" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Kinésithérapeute</a>
                <a href="#" title="Emploi Médecin" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Médecin</a>
                <a href="#" title="Emploi Pharmacien" onclick="handleLinkClick(this.getAttribute('title'))" class="footer-menu-text">Emploi Pharmacien</a>
            </div>
            #}
        </div>
       
    </div>

    <div class="copyright-section">
        <p class="text-center copyright-text">© Direct Etudiant 2013 - 2024</p>
    </div>
    <script>
        // Function to be executed when the <a> element is clicked
        function handleLinkClick(titleValue) {
            const url = `https://www.directetudiant.com/DE/lists-offre/?contrat=&localisation=&localisation_field=&q=${titleValue}`;
            // Redirect to the constructed URL
            window.location.href = url;
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
    <script>
        function showSweetAlert(link) {
            const linkText = link.innerText || link.textContent;
            if (linkText==="Recruteurs")
            {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Etudiant est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectEtudiant/Front/images/logo-black.png')}}" alt="Direct Etudiant Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/inscriptionRecruteur';
                });
            }else if (linkText==="Connexion")
            {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Etudiant est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectEtudiant/Front/images/logo-black.png')}}" alt="Direct Etudiant Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/login';
                });
            }else if (linkText==="Création de compte")
            {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Etudiant est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectEtudiant/Front/images/logo-black.png')}}" alt="Direct Etudiant Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png")}}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/inscriptionRecruteur';
                });
            }
            else if (linkText === "S'inscrire") {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Etudiant est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectEtudiant/Front/images/logo-black.png') }}" alt="Direct Etudiant Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/login';
                });
            } else if (linkText === "Le réseau Direct Emploi") {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Etudiant est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectEtudiant/Front/images/logo-black.png') }}" alt="Direct Etudiant Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/lereseau';
                });
            } else if (linkText === "Les offres proposées") {
                Swal.fire({
                    icon: 'success',
                    title: 'Direct Etudiant est un site du réseau Direct Emploi',
                    html: '<p>Vous allez être redirigé vers l\'espace recruteur de Direct Emploi<p><br>' +
                        '<img src="{{ asset('DirectEtudiant/Front/images/logo-black.png') }}" alt="Direct Etudiant Logo" style="max-width: 100px; margin-right: 10px;">un site de réseau<img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="Direct Emploi Logo" style="max-width: 100px; margin-left: 10px;">',
                    timer: 3000,
                    showConfirmButton: false,
                }).then(() => {
                    window.location.href = 'https://www.directemploi.com/offresproposees';
                });
            }
        }
    </script>
</footer>