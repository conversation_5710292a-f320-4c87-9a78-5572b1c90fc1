{% extends 'Indexation/base.html.twig' %}
{% set lettre = app.request.query.get('lettre') %}

    {% set title_seo = "Offres Emploi par Métier | Direct Emploi" %}
    {% set description_seo = "Consultez les Dernières Offres d'Emploi par métier sur Direct Emploi. ✓ Postulez en ligne dès Maintenant ✓ Candidature Simple & Rapide !" %}

{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    {% block head %}
        {% include 'Indexation/header.html.twig' %}
    {% endblock %}
<section class="emploi-header d-flex justify-content-center align-items-center " id="">
    <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#13A3AA">Offres en alternance </span>par ville pour le métier {{ metier }}</h1>
</section>
<br>
<section class="section-embauche">
    <h6 class="text-center text-uppercase mb-5"><span style="color: #13A3AA;">Offres en alternance </span>{{ metier }}</h6>
    <div class="search-container text-center mb-5">
        <input type="text" id="searchBar" placeholder="Rechercher..." style="width: 50%; padding: 10px; margin-top: 20px;">
    </div>
    <div class="container">
        <div class="row offres-list">
            {% set taille_liste = offresEmploi|length %}
            {% if taille_liste > 0 %}
                {% set taille_groupe = (taille_liste / 3)|round(0, 'ceil') %}
                {% for i in 0..2 %}
                    <div class="col-lg-4 col-md-6">
                        {% for offre in offresEmploi|slice(i * taille_groupe, taille_groupe) %}
                            <div style="margin-bottom: 20px;">
                                <ul style="list-style: none; padding: 0;">
                                    <li onclick="redirectTo('{{ offre.ville | escape('js') }}', '{{ offre.metier | escape('js') }}')">
                                        <a href="#" style="font-weight: bold;">Offre d'emploi {{ offre.metier }} {{ offre.ville }}</a>
                                    </li>
                                </ul>
                            </div>
                        {% endfor %}
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center">
                    <p>Pas d'offres disponible pour le moment.</p>
                    <a href="{{ path('app_BTP_candidat_search') }}" class="btn btn-primary">Voir toutes les offres</a>
                </div>
            {% endif %}
        </div>
        {% if breadcrumb is defined %}
            {% include '/_partials/_breadcrumb.html.twig'  %}
        {% endif %}
    </div>


</section>

    {% block footer %}
        {% include 'footerpages/footer.html.twig' %}
    {% endblock %}
{% endblock %}