{% extends 'DirectEtudiant/base.html.twig' %}
{% set lettre = app.request.query.get('lettre') %}

    {% set title_seo = "Offre de stage par Métier | Direct Etudiant" %}
    {% set description_seo = "Consultez les Dernières Offre de stage par métier sur Direct Etudiant. ✓ Postulez en ligne dès Maintenant ✓ Candidature Simple & Rapide !" %}

{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{description_seo}}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <div class="is-sticky">
    {% block head %}
        {% include 'DirectEtudiant/header.html.twig' %}
    {% endblock %}
    <section class="emploi-header d-flex justify-content-center align-items-center " id="">
        <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#13A3AA">offres de stage</span> par métier dans le domaine {{ domainename }}</h1>
 </span> </h1>
    </section>
    <br>
    <section>
        <div class="container">
            <div class="header-and-text">
                <h6 >Offre de stage {{ domainename }}</h6>

                <hr>
                <div class="container">
                    <div class="row offres-list">
                        {% set taille_liste = listeMetiers|length %}
                        {% set taille_groupe = (taille_liste / 4)|round(0, 'floor') %}
                        {% set elements_restants = taille_liste % 4 %}
                        {% set debut = 0 %}

                        {% for i in 0..3 %}
                            {% set ajustement = i < elements_restants ? 1 : 0 %}
                            {% set fin = debut + taille_groupe + ajustement %}

                            <div class="col-lg-3 col-md-4">
                                {% for metier in listeMetiers|slice(debut, taille_groupe + ajustement) %}
                                    <div >
                                        <ul style="list-style: none; padding: 0;">
                                            <li onclick="redirectTo('{{ metier.getLibelleMetier()}}')">
                                                <a href="#" style="font-weight: bold;">Offre de stage {{ metier.getLibelleMetier() }}</a>
                                            </li>
                                        </ul>
                                    </div>
                                {% endfor %}
                            </div>
                            {% set debut = fin %}
                        {% endfor %}
                    </div>
                </div>
            </div>

            {% if breadcrumb is defined %}
                {% include '/_partials/_breadcrumb.html.twig'  %}
            {% endif %}
        </div>

    </section>


    {% block footer %}
        {% include 'DirectEtudiant/footer.html.twig' %}
    {% endblock %}

    <!-- Include jQuery and Bootstrap JS from CDNJS -->
    <script>
        function redirectTo(value) {
            // Encode the value to include it in the URL
            const url = `https://www.directetudiant.com/DE/lists-offre/?contrat=8&localisation=&localisation_field=&q=${value}`;
            // Redirect to the constructed URL
            window.location.href = url;
        }  function redirectToDomaine(value) {
            // Encode the value to include it in the URL
            // Redirect to the constructed URL
            //window.location.href = `https://www.directetudiant.com/DE/emploi-index/metier-ville/${value}`;
            //window.location.href = `http://127.0.0.1:8000/emploi-index/metier-ville/${value}`;
        }

    </script>
    <script>
        document.getElementById('searchBar').addEventListener('input', function(e) {
            var searchValue = e.target.value.toLowerCase();
            var offres = document.querySelectorAll('.offres-list div');

            offres.forEach(function(offre) {
                var villeText = offre.innerText.toLowerCase();
                if (villeText.includes(searchValue)) {
                    offre.style.display = '';
                } else {
                    offre.style.display = 'none';
                }
            });
        });
    </script>

    </div>
{% endblock %}
