{% extends 'directemploi/new_layout.html.twig' %}

{% block title %}
    {% if app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
        Jeunes diplômés - Trouvez votre premier emploi
    {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
        Intérim - Des missions pour les intérimaires
    {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
        Handicap
    {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
        Cabinet de recrutement - Des emplois en CDI
    {% endif %}
{% endblock %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/new_page_emploi.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_responsive.min.css') }}">
{% endblock %}

{% block content %}
<section>
    {{ render(controller('App\\Controller\\MainController::recherche', {'path': app.request.pathinfo})) }}
</section>
<section>      
    <div class="container" style="width: 940px; margin-top: 10px">
        <div class="row">
            <div class="col">
                {% if breadcrumb is defined %}
                    {% include '/_partials/_breadcrumb.html.twig'  %}
                {% endif %}
            </div>

        </div>
    </div>
</section>
<section>       
    <div class="container page_emploi_box_milieu">


        <div class="page_emploi_pub_milieu_wrapper">
            {% for i in 0..nb_entr %}
                {% if entreprises != [] %}
                    <div class="page_emploi_pub_milieu" id={{ "entreprises_slide_" ~ i }} style={{"display:" ~ (i == 0 ? "block" : "none")}}>
                        {% set style = "background-image: url(" ~ (entreprises[i].getLogoFond() ? "/uploads/logos_fond/" ~ entreprises[i].getLogoFond() : images[i] ) ~ ")" %}
                        <a href={{ "/entreprise/" ~ entreprises[i].getIdEntreprise() ~ "/" ~ strtourl(entreprises[i].getRaisonSociale()) }}>
                            <div class="page_emploi_card text-center" style="{{style}};" id="trouvez_formation">
                                <div class="page_emploi_img">
                                    <img class="page_emploi_img-accueil-entreprise" src="{{ asset('/uploads/logos/' ~ entreprises[i].logo) }}" alt="logo entreprise {{ entreprises[i].getIdEntreprise() }}">
                                </div>
                            </div>
                        </a>
                    </div>
                {% endif %}
            {% endfor %}
        </div>

        <div class="page_emploi_texte_milieu">
            <h5>
                <strong>
                {% if app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
                    Jeunes diplômés - Trouvez votre premier emploi
                {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
                    Intérim - Des missions pour les intérimaires
                {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
                    Handicap
                {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
                    Cabinet de recrutement - Des emplois en CDI
                {% endif %}
                </strong>
            </h5>
            <p>
                {% if app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
                    Direct Emploi propose de nombreuses opportunités pour les <strong>débutants, les jeunes diplômés</strong> qui sortent chaque année de lycées, universités,
                    grandes écoles, centres de formation, écoles spécialisées… Retrouvez dans notre rubrique « jeunes diplômés » des offres d’emploi de 
                    <strong>tous métiers</strong> et dans <strong>tous les domaines d’activité</strong>, mais spécifiquement proposés pour les 
                    jeunes diplômés.<br><br>Sur Direct Emploi, de nombreuses entreprises proposent des <strong>opportunités de carrière</strong> pour les 
                    jeunes diplômés qui sortent d’écoles avec une expérience de stage qui leur a permis de découvrir le monde professionnel, et choisir ainsi 
                    la filière et le métier qui leur correspond le mieux. Nous proposons également des offres d’emploi pour des personnes <strong>sans 
                    diplôme</strong> qui souhaite tout simplement travailler dès la sortie du système scolaire ou même après quelques années de formations diverses. 
                {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
                    Direct Emploi propose de nombreux emploi pour ceux qui ont choisi l’intérim comme mode de travail. Ces missions d’intérim représentent la moitié des 
                    opportunités de carrière, soit plus de <strong>50 000 offres en ligne</strong>. En effet, de nombreux groupes d’intérim nous font confiance et diffusent leurs
                     annonces dans notre rubrique d’<strong>offres en intérim</strong>. Les <strong>offres d’emploi en intérim</strong> concernent essentiellement des missions
                      dans les domaines de l’industrie, de la logistique, du transport, du Bâtiment mais aussi de la restauration, de la distribution et du tertiaire. <br><br>Les 
                      annonces en intérim sur Direct Emploi sont disponibles sur <strong>toute la France</strong>, mais les régions Ile de France, Hauts de France, Grand est, 
                      Auvergne Rhône Alpes et Bretagne représentent les bassins les plus importants. N’oubliez pas que toutes nos agences clientes consultent 
                      <strong>la cvthèque</strong> de Direct Emploi afin de vous contacter. Alors n’oubliez pas de vous inscrire. 
                {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
                    Direct Emploi propose de nombreuses annonces d’emploi pour les personnes en <strong>situation de handicap</strong>. Retrouvez dans cette rubrique des offres
                     d’emploi dans les <strong>tous les métiers</strong> et dans <strong>tous les domaines d’activité et sur toute la France</strong>. Les entreprises sont 
                     sensibilisées aux problématiques de la <strong>diversité au travail</strong> et cherchent à recruter des travailleurs handicapés. Autant en <strong>CDI, CDD,
                      alternance</strong>, ou <strong>contrats de professionnalisation</strong>... les opportunités ne cessent de se multiplier.<br><br>Quel que soit votre 
                      handicap, qu’il soit moteur, sensoriel, mental, etc. Si vous avez les compétences, vous trouverez une offre qui vous correspond sur Direct Emploi. Alors 
                      n’hésitez pas <strong>à répondre aux annonces</strong> en envoyant en quelques clics votre cv et lettre de motivation. 
                {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
                    De nombreux cabinets de recrutement sont présents sur Direct Emploi. Ils proposent des emplois dans <strong>tous les secteurs d’activité</strong>. En effet, 
                    vous pouvez retrouver sur Direct Emploi, des <strong>cabinets de recrutement</strong> à la fois <strong>généralistes</strong> et proposant des postes dans tous 
                    les domaines et métiers mais aussi des <strong>cabinets spécialisés</strong>, qui eux, ont choisi de cibler leur expertise souvent sur un ou deux métiers très 
                    demandés ou un domaine d’activité particulier. <br><br>Vous pouvez retrouver dans la rubrique <strong>Cabinet de recrutement</strong> des offres pour des 
                    exclusivement des <strong>missions en CDI</strong> et plutôt à l’attention de <strong>cadres</strong> qui ont quelques années d’expérience mais également de 
                    profils <strong>middle management</strong> et de <strong>direction</strong>. Alors n’hésitez pas à répondre à leurs offres d’emploi et transmettez en quelques 
                    clics votre cv et lettre de motivation. Nous vous invitons également à rejoindre notre <strong>cvthèque</strong> afin d’être contacté directement par ces 
                    experts du recrutement.
                {% endif %}
            </p>
        </div>
    </div>
</section>
<section>   
    {{ render(controller('App\\Controller\\MainController::offres', {'path': app.request.pathinfo})) }}
</section>
<section>       
    <div class="container actualite page_emploi_actualite">
        {% if app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
        <div>
            <h4>Les actualités pour les jeunes diplômés</h4>
            <a class="lien_s_actualite" href="/article/list" title="&gt; Voir toutes les actualités"> Voir toutes les actualités</a>
        </div>
        {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
        <div>
            <h4> Les actualités de l’emploi en intérim</h4>
            <a class="lien_s_actualite" href="/article-theme/interim" title="&gt; Voir toutes les actualités"> Voir toutes les actualités</a>
        </div>
        {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
        <div>
            <h4>Les actualités de l’emploi et du handicap</h4>
            <a class="lien_s_actualite" href="/article-theme/handicap" title="&gt; Voir toutes les actualités"> Voir toutes les actualités</a>
        </div>
        {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
        <div>
            <h4>Les actualités des cabinets de recrutement</h4>
            <a class="lien_s_actualite" href="/article-theme/cabinet-recrutement" title="&gt; Voir toutes les actualités"> Voir toutes les actualités</a>
        </div>
        {% endif %}
        <div class="page_emploi_actualite_cards_wrapper">
            <div class="row" style="justify-content: space-between">
            {% for article in articles %}
                 <div class="col">
                        <a href={{ "/article/" ~ article['id_article'] ~ "/" ~ strtourl(article['titre']) }} class="card card-zoom" style="height: 100%">
                            <img src={{ asset("/uploads/article/" ~ article['image_petite'] ) }} class="card-img-top" alt="{{article['image_petite']}}" style="height: 150px; width: 100%; align-self: center">
                            <div class="card-body">
                                <h5 class="card-title">{{article['titre']}}</h5>
                                <p class="card-text">{{article['chapo']|u.truncate(150, '...', false)}}</p>
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">
                                    <!--<strong>Actualité :</strong>
                                        {% if article['theme'] == 'Emploi' and article['type'] != 'Article' %}
                                            {{article['type']}}
                                        {% elseif article['type'] == 'Article' %}
                                            Dossier
                                        {% else %}
                                            {{article['theme']}}
                                        {% endif %}
                                 --></small>
                            </div>
                        </a>
                    </div>
            {% endfor %}
            </div>
        </div>
    </div>
</section>

<script language="javascript">
    var i = 0;
    var reloadDiv = function() {
        if (i > 0) {
            prev = document.getElementById("entreprises_slide_" + (i - 1));
            prev.style = "display:none";
        }
        if (i == 0) {
            prev = document.getElementById("entreprises_slide_" + '{{nb_entr}}');
            prev.style = "display:none";
        }
        entreprise = document.getElementById("entreprises_slide_" + i);
        entreprise.style = "display:block";
        if (i < '{{nb_entr}}')
            i++;
        else
            i = 0;
    }
    var intervalId = setInterval(reloadDiv, 2500);
</script>
{% endblock %}
