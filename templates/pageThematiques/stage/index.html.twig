{% extends 'directemploi/new_layout.html.twig' %}

{% block title %}
    {% if app.request.pathinfo == '/theme/offres-stage' %}
        Direct emploi, toutes nos offres en contrat de Stage
    {% elseif app.request.pathinfo == '/theme/offres-alternance' %}
        Direct emploi, toutes nos offres en contrat d'alternance
    {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
        Intérim - Des missions pour les intérimaires
    {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
        Cabinet de recrutement - Des emplois en CDI
    {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
        Handicap - Des emplois pour les personnes en situation de handicap
    {% elseif app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
        Jeunes diplômés - Trouvez votre premier emploi
    {% endif %}
{% endblock %}

{% if app.request.pathinfo == '/theme/offres-stage' %}
    {% set description_seo = "Explorez nos opportunités de stage et développez vos compétences professionnelles." %}
{% elseif app.request.pathinfo == '/theme/offres-alternance' %}
    {% set description_seo = "Découvrez nos offres d'alternance pour démarrer votre carrière avec une expérience pratique." %}
{% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
    {% set description_seo = "Trouvez des emplois en intérim pour une flexibilité dans votre carrière professionnelle." %}
{% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
    {% set description_seo = "Explorez les opportunités d'emploi permanentes avec notre cabinet de recrutement de talent." %}
{% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
    {% set description_seo = "Découvrez nos offres d'emploi inclusives pour les personnes en situation de handicap." %}
{% elseif app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
    {% set description_seo = "Trouvez des opportunités d'emploi passionnantes pour les jeunes diplômés." %}
{% endif %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}


{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/new_offres_stage_alternance.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_responsive.min.css') }}">
    <link href="https://fonts.googleapis.com/css?family=Montserrat" rel="stylesheet">

	<link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/bootstrap.min.css') }}">
	<link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/bootstrap-icons.min.css') }}">
	<link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/magnific-popup.min.css') }}">
	<link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">

    <style>
        body, h1, h2, h3, h4, h5, h6, p, a {
            font-family: 'Montserrat', sans-serif !important;
        }

        .container.carrousel {
            width: 100%;
        }

        .box_milieu_offres-stage {
            width: 100% !important;
        }

        .actualite_stages {
            width: 100% !important;
        }

        .actualite_stages h5 {
            font-size: 1.2rem;
        }
        .card-conseil{
            background-color: rgba(255,255,255,0.1);
            width: 100%;
            padding: 1rem;
            border-radius: 25px;
            margin-bottom: 1rem;

        }
        .card-conseil:hover{
        cursor: pointer;

        }


        .card-conseil h3{
            font-size: 22px;
        }
        .card-conseil .icon{
            color: #F0C6B6;
            font-size: 48px;
            margin-bottom: 1rem;
        }
        .card-conseil p{
            font-size: 12.6px;
            margin-bottom: 1rem;
        }

        .card-link{
            color:#fff;font-size: 12.6px;text-align: end;
        }
        .card-link i{
            color:#fff;font-size: 12.6px;text-align: end;
            transition: color 0.3s;
        }


        .card-link:hover i{
            color:#F0C6B6;
        }
        .card-link:hover{
            color:#F0C6B6;
        }
        .card {
            border-radius: 15px!important;
            height: 100%!important;
            margin-bottom: 1rem!important;
            position: relative!important;
            display: flex!important;
            flex-direction: column!important;
            min-width: 0!important;
            word-wrap: break-word!important;
            background-color: #fff!important;
            background-clip: border-box!important;
            border: 1px solid rgba(0, 0, 0, .125)!important;
            box-shadow: none!important;
        }
        .card p {
            font-size: 13px;
        }
        .article-section .card-body h3{
            font-size: 16px;
            font-weight: 600;
            line-height: 1.6;
            color: #4ba8c5;

        }

        .article-section .button-inscription{
            width: 260px;
            background-color: #44A2C6;
        }

        .article-section .button-inscription:hover
        {

            background-color: #5ab1d9;
        }

        .article-section .card-link:hover{
            color: #5ab1d9;
        }

        .article-section .card-link{
            color:#44A2C6;font-size: 12.6px;text-align: end;
        }

        .article-section .card-link i{
            color:#44A2C6;font-size: 12.6px;text-align: end;
        }

    </style>
{% endblock %}

{% block content %}
<section>

    {{ render(controller('App\\Controller\\MainController::recherche', {'path': app.request.pathinfo})) }}
</section>
<section>    
    <div class="container carrousel">
        {% if breadcrumb is defined %}
            {% include '/_partials/_breadcrumb.html.twig'  %}
        {% endif %}
        <h2 class="text-center">
            {% if app.request.pathinfo == '/theme/offres-stage' %}
                Ils recrutent des stagiaires sur Direct Emploi
            {% elseif app.request.pathinfo == '/theme/offres-alternance' %}
                Ils recrutent des alternants sur Direct Emploi
            {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
                Ils recrutent en intérim sur Direct Emploi
            {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
                Les cabinets de recrutement sur Direct Emploi
            {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
                Ils recrutent des personnes en situation de handicap sur Direct Emploi
            {% elseif app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
                Ils recrutent des jeunes diplômés sur Direct Emploi
            {% endif %}
        </h2>
        {{ render(controller('App\\Controller\\MainController::carousel')) }}
        <div class="article_offres-stage" style="padding: 1rem; background-color: rgba(0,0,0,0.05)">
            <h2 style="font-size: 18px;">
                {% if app.request.pathinfo == '/theme/offres-stage' %}
                    Vous cherchez un stage ?
                {% elseif app.request.pathinfo == '/theme/offres-alternance' %}
                    Vous cherchez un stage en alternance ?
                {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
                    Vous cherchez un emploi en intérim ?
                {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
                    Vous cherchez un emploi en CDI ?
                {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
                    Vous cherchez un emploi en situation de handicap ?
                {% elseif app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
                    Vous êtes jeune diplômé et vous cherchez un emploi ?
                {% endif %}
            </h2>
            <p style="padding: 0;">
            {% if app.request.pathinfo == '/theme/offres-stage' %}
                Découvrez notre rubrique dédiée exclusivement <strong>à la recherche de stage</strong>.
                En effet, au de-là des offres d’emploi et en alternance, Direct Emploi souhaite apporter aux étudiants en recherche 
                d’un stage ses services : un moteur de recherche proposant de nombreuses annonces de stage mais aussi des conseils pour 
                trouver et réaliser son stage dans les meilleures conditions.<br><br>Que vous soyez lycéen, universitaire ou futur diplômé de grandes 
                écoles, écoles de commerce ou d’ingénieurs, vous trouverez des stages à tous niveaux de diplôme, CAP, bac pro, BTS, DUT, licence, 
                master 1 ou 2 ou MBA de grandes écoles. Il s'agit de stage pour les <strong>1ères années de formation</strong> mais aussi de 
                <strong>fin de cursus</strong>, le fameux stage de fin d’études. Des stages pour de nombreux métiers et dans de nombreux domaines 
                d’activités sont proposés sur Direct Emploi et sur toute la France. <br><br>Direct Emploi vous accompagne pour la recherche de votre 
                stage en vous apportant des conseils et des informations pratiques notamment la réalisation de votre CV, de votre lettre de motivation 
                et votre entretien d’embauche. Vous pouvez également retrouver ces offres de stage sur le site partenaire 
                <strong><a href="https://www.directetudiant.com/" style="color:black;">Direct Etudiant</a></strong>.
            {% elseif app.request.pathinfo == '/theme/offres-alternance' %}
                Si Direct Emploi est un moteur de recherche pour trouver plus particulièrement un emploi, vous pouvez 
                retrouver spécifiquement dans cette rubrique <strong>« Alternance »</strong>, exclusivement 
                <strong>des offres de stage en alternance</strong>. En postulant à ces offres, vous pouvez réaliser une expérience en 
                formation alternée à la fois en entreprise et à l’école. Comme vous le savez, c’est l’entreprise qui finance votre formation !
                <br><br>Du diplôme CAP en passant par le bac pro, le bac technique, le BTS, le DUT, la licence, le master 1 ou 2 d’universités, 
                les MBA d’écoles de commerce, d’ingénieurs, les grandes écoles mais aussi les formations spécialisées, vous pouvez trouver sur 
                Direct Emploi <strong>le stage en alternance de vos rêves</strong> et sur toute la France. Direct Emploi propose à la fois 
                <strong>des contrats de professionnalisation</strong> mais aussi des <strong>contrats d’apprentissage</strong>. 
                <br><br>Site emploi généraliste, <strong>Direct Emploi</strong> propose des offres en alternance également pour tous 
                les métiers et dans tous les domaines d’activité. Au-delà de son moteur d’offres, <strong>Direct Emploi</strong> accompagne 
                les étudiant qui recherche une alternance en apportant des conseils pratiques notamment pour la réalisation du cv, de la lettre 
                de motivation, l’entretien d’embauche et toutes les informations nécessaires pour ce contrat fortement proposé par les entreprises.
            {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
                Direct Emploi propose de nombreux emploi pour ceux qui ont choisi l’intérim comme mode de travail. Ces missions d’intérim représentent la moitié des 
                opportunités de carrière, soit plus de <strong>50 000 offres en ligne</strong>. En effet, de nombreux groupes d’intérim nous font confiance et diffusent leurs
                annonces dans notre rubrique d’<strong>offres en intérim</strong>. Les <strong>offres d’emploi en intérim</strong> concernent essentiellement des missions
                dans les domaines de l’industrie, de la logistique, du transport, du Bâtiment mais aussi de la restauration, de la distribution et du tertiaire. <br><br>Les 
                annonces en intérim sur Direct Emploi sont disponibles sur <strong>toute la France</strong>, mais les régions Ile de France, Hauts de France, Grand est, 
                Auvergne Rhône Alpes et Bretagne représentent les bassins les plus importants. N’oubliez pas que toutes nos agences clientes consultent 
                <strong>la cvthèque</strong> de Direct Emploi afin de vous contacter. Alors n’oubliez pas de vous inscrire. 
            {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
                De nombreux cabinets de recrutement sont présents sur Direct Emploi. Ils proposent des emplois dans <strong>tous les secteurs d’activité</strong>. En effet, 
                vous pouvez retrouver sur Direct Emploi, des <strong>cabinets de recrutement</strong> à la fois <strong>généralistes</strong> et proposant des postes dans tous 
                les domaines et métiers mais aussi des <strong>cabinets spécialisés</strong>, qui eux, ont choisi de cibler leur expertise souvent sur un ou deux métiers très 
                demandés ou un domaine d’activité particulier. <br><br>Vous pouvez retrouver dans la rubrique <strong>Cabinet de recrutement</strong> des offres pour des 
                exclusivement des <strong>missions en CDI</strong> et plutôt à l’attention de <strong>cadres</strong> qui ont quelques années d’expérience mais également de 
                profils <strong>middle management</strong> et de <strong>direction</strong>. Alors n’hésitez pas à répondre à leurs offres d’emploi et transmettez en quelques 
                clics votre cv et lettre de motivation. Nous vous invitons également à rejoindre notre <strong>cvthèque</strong> afin d’être contacté directement par ces 
                experts du recrutement.
            {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
                Direct Emploi propose de nombreuses annonces d’emploi pour les personnes en <strong>situation de handicap</strong>. Retrouvez dans cette rubrique des offres
                d’emploi dans les <strong>tous les métiers</strong> et dans <strong>tous les domaines d’activité et sur toute la France</strong>. Les entreprises sont 
                sensibilisées aux problématiques de la <strong>diversité au travail</strong> et cherchent à recruter des travailleurs handicapés. Autant en <strong>CDI, CDD,
                alternance</strong>, ou <strong>contrats de professionnalisation</strong>... les opportunités ne cessent de se multiplier.<br><br>Quel que soit votre 
                handicap, qu’il soit moteur, sensoriel, mental, etc. Si vous avez les compétences, vous trouverez une offre qui vous correspond sur Direct Emploi. Alors 
                n’hésitez pas <strong>à répondre aux annonces</strong> en envoyant en quelques clics votre cv et lettre de motivation. 
            {% elseif app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
                Direct Emploi propose de nombreuses opportunités pour les <strong>débutants, les jeunes diplômés</strong> qui sortent chaque année de lycées, universités,
                grandes écoles, centres de formation, écoles spécialisées… Retrouvez dans notre rubrique « jeunes diplômés » des offres d’emploi de 
                <strong>tous métiers</strong> et dans <strong>tous les domaines d’activité</strong>, mais spécifiquement proposés pour les 
                jeunes diplômés.<br><br>Sur Direct Emploi, de nombreuses entreprises proposent des <strong>opportunités de carrière</strong> pour les 
                jeunes diplômés qui sortent d’écoles avec une expérience de stage qui leur a permis de découvrir le monde professionnel, et choisir ainsi 
                la filière et le métier qui leur correspond le mieux. Nous proposons également des offres d’emploi pour des personnes <strong>sans 
                diplôme</strong> qui souhaite tout simplement travailler dès la sortie du système scolaire ou même après quelques années de formations diverses. 
            {% endif %}
            </p>
        </div>
    </div>
</section>
<section>
    <div class="container">
        {% if '/theme/offres-alternance' in app.request.pathinfo %}
            {{ render(controller('App\\Controller\\MainController::offresAlternance', {'path': app.request.pathinfo})) }}
        {% elseif '/theme/offres-stage' in app.request.pathinfo %}
            {{ render(controller('App\\Controller\\MainController::offresStage', {'path': app.request.pathinfo})) }}
        {% elseif 'theme/offres-emploi-interim' in app.request.pathinfo %}
            {{ render(controller('App\\Controller\\MainController::offresInterim', {'path': app.request.pathinfo})) }}
        {% endif %}
    </div>

</section>
<section>        
    <div class=" box_milieu_offres-stage">
        {# <div class="page_thematique_pub_milieu_wrapper">
        {% for i in 0..nb_entr %}
            {% if entreprises != [] %}
                <div class="page_thematique_pub_milieu" id={{ "entreprises_slide_" ~ i }} style={{"display:" ~ (i == 0 ? "block" : "none")}}>
                    {% set style = "background-image: url(" ~ (entreprises[i].getLogoFond() ? "/uploads/logos_fond/" ~ entreprises[i].getLogoFond() : images[i] ) ~ ")" %}
                    <a href={{ "/entreprise/" ~ entreprises[i].getIdEntreprise() ~ "/" ~ strtourl(entreprises[i].getRaisonSociale()) }}>
                        <div class="page_thematique_card text-center" style="{{style}};" id="trouvez_formation">
                            <div class="page_thematique_img">
                                <img class="page_thematique_img-accueil-entreprise" src="{{ asset('/uploads/logos/' ~ entreprises[i].logo) }}" alt="logo {{ entreprises[i].getIdEntreprise() }}">
                            </div>
                        </div>
                    </a>
                </div>
            {% endif %}
        {% endfor %}
        </div> #}
        {# <div class="box_conseil_offres-stage">
            {% if (app.request.pathinfo == '/theme/offres-alternance') or (app.request.pathinfo == '/theme/offres-stage') %}
            <p><strong>CANDIDATS: TROUVEZ VOTRE FUTUR STAGE / ALTERNANCE SUR DIRECT EMPLOI !</strong></p>
            {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
            <p><strong>CANDIDATS: TROUVEZ VOTRE FUTUR EMPLOI EN INTÉRIM SUR DIRECT EMPLOI !</strong></p>
            {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
            <p><strong>CANDIDATS: TROUVEZ VOTRE FUTUR EMPLOI EN CDI SUR DIRECT EMPLOI !</strong></p>
            {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
            <p><strong>CANDIDATS: TROUVEZ VOTRE FUTUR EMPLOI EN SITUATION DE HANDICAP SUR DIRECT EMPLOI !</strong></p>
            {% elseif app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
            <p><strong>CANDIDATS: TROUVEZ VOTRE PREMIER EMPLOI SUR DIRECT EMPLOI !</strong></p>
            {% endif %}
            <div class="container box_conseil_content">
                <div class="container box_conseil_milieu">
                    <span><strong>Créez et déposez votre CV dans votre espace candidat.</strong></span>
                    <a href="{{ path('app_register_candidat_e1') }}" type="button" class="btn btn-warning" style="border: 1px white solid; color: white; font-size: small">
                        <strong>Je m'inscris</strong><img src="{{ asset('/images/direct-emploi/inscription_newb.png') }}" alt="logo pour inscription" style="width: 20%">
                    </a>
                    {% if (app.request.pathinfo == '/theme/offres-alternance') or (app.request.pathinfo == '/theme/offres-stage') %}
                    <span><strong>Recevez des offres de stage de plus de 3000 recruteurs.</strong></span>
                    {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
                    <span><strong>Recevez des offres d'emploi en intérim de plus de 3000 recruteurs.</strong></span>
                    {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
                    <span><strong>Recevez des offres d'emploi en CDI de plus de 3000 recruteurs.</strong></span>
                    {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
                    <span><strong>Recevez des offres d'emploi en situation de handicap de plus de 3000 recruteurs.</strong></span>
                    {% elseif app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
                    <span><strong>Recevez des offres d'emploi de plus de 3000 recruteurs.</strong></span>
                    {% endif %}
                </div>
                <div class="box_conseil_title">
                    <h6>CONSEILS</h6>
                </div>
                <div class="box_conseils_links">
                    <a href="{{ path('app_conseil_cv') }}" role="button" class="btn btn-warning"><strong>Le CV</strong></a>
                    <a href="{{ path('app_conseil_lm') }}" role="button" class="btn btn-warning"><strong>La lettre de motivation</strong></a>
                    <a href="{{ path('app_conseil_ee') }}" role="button" class="btn btn-warning"><strong>L'entretien d'embauche</strong></a>
                </div>
            </div>
        </div> #}

            <div class="w-100  img-container" style="background-color: #357F9C; padding: 1rem 0;">

                <!-- <img src="images/card_2.png" class="d-block w-100" alt="image-btp">-->
                <div class="w-100">
                    <div class="container d-flex flex-column justify-content-between py-4">
                        <div class="">
                            {# <h6 class="text-white text-center " style="font-size:18px;">CANDIDATS : TROUVEZ VOTRE FUTUR EMPLOI SUR CARRIERE MÉDICALE !</h6> #}
                            {# <p class="text-center text-white">Recevez des offres d'emploi de plus de 3000 recruteurs.</p> #}
                            {% if (app.request.pathinfo == '/theme/offres-alternance') or (app.request.pathinfo == '/theme/offres-stage') %}
                            <h2 class="text-white text-center " style="">Recevez des offres de stage de plus de 3000 recruteurs.</h2>
                            {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
                            <h2 class="text-white text-center " style="">Recevez des offres d'emploi en intérim de plus de 3000 recruteurs.</h2>
                            {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
                            <h2 class="text-white text-center " style="">Recevez des offres d'emploi en CDI de plus de 3000 recruteurs.</h2>
                            {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
                            <h2 class="text-white text-center " style="">Recevez des offres d'emploi en situation de handicap de plus de 3000 recruteurs.</h2>
                            {% elseif app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
                            <h2 class="text-white text-center " style="">Recevez des offres d'emploi de plus de 3000 recruteurs.</h2>
                            {% endif %}
                            <div style="height: 0.5px; width:100%; background-color:#fff; "></div>
                        </div>

                        <div class="mt-3">
                            <h3 class="text-center text-white mb-3" style="font-size:18px;">Conseils</h3>
                            <div class="row" style="display: flex; justify-content: space-around; align-items: center;">


                                <div class="col-lg-4">
                                    <a href="{{ path('app_Medicale_conseilsCV') }}" class="">
                                        <div class="card-conseil">
                                            <i class="fa-solid fa-file icon"></i>
                                            <h3 class="text-white">Le cv</h3>
                                            <p class="text-white justified">Le CV est essentiel pour décrocher un emploi, un stage ou une alternance. Dès vos études, il devient la vitrine de votre parcours. Pour chaque candidature, actualisez-le régulièrement et mettez en avant les informations clés attendues par les recruteurs.</p>
                                            <div class="text-end"> <a href="{{ path('app_conseil_cv') }}" class="card-link" >En savoir plus <i class="fa-solid fa-arrow-right" style="line-height: 1.3"></i></a></div>

                                        </div>
                                        </a>
                                </div>

                                <div class="col-lg-4"><a href="{{ path('app_Medicale_conseilsmotivation') }}" class="">
                                        <div class="card-conseil">
                                            <i class="fa-solid fa-envelope icon"></i>
                                            <h3 class="text-white">La lettre de motivation</h3>
                                            <p class="text-white justified">La lettre de motivation complète le CV et reste essentielle. Elle permet d'ajouter des arguments pour convaincre les recruteurs. Présentez-vous brièvement, expliquez vos motivations pour ce poste et mettez en avant vos atouts pour l’entreprise.</p>
                                            <div class="text-end"> <a href="{{ path('app_conseil_cv') }}" class="card-link" >En savoir plus <i class="fa-solid fa-arrow-right" style="line-height: 1.3"></i></a></div>

                                        </div>
                                        </a></div>

                                <div class="col-lg-4"><a href="{{ path('app_Medicale_conseilsembauche') }}" class="">
                                        <div class="card-conseil">
                                            <i class="fa-solid fa-briefcase icon"></i>
                                            <h3 class="text-white">L’entretien d’embauche</h3>
                                            <p class="text-white justified">L’entretien d’embauche, souvent redouté, devient plus serein avec une bonne préparation. L’improvisation étant risquée, maîtrisez cet exercice codifié en respectant ses règles et en mettant en avant vos compétences pour maximiser vos chances de succès.</p>
                                            <div class="text-end"> <a href="{{ path('app_conseil_cv') }}" class="card-link" >En savoir plus <i class="fa-solid fa-arrow-right" style="line-height: 1.3"></i></a></div>

                                        </div>
                                        </a></div>

                            </div>

                        </div>
                        <div class="text-center carousel-col-button"style="margin-top:60px;">
                            <p class="text-white">Créez et déposez votre CV dans votre espace candidat.</p>
                            <a href="{{ path('app_Medicale_register_candidat_e1') }}" class="btn button-white mx-auto w-75 " style="font-size: 16px;">
                                <strong>S'inscrire</strong>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</section>
<section>    
    {# <div class="container box_offres_emplois">
        <h4>
            {% if app.request.pathinfo == '/theme/offres-stage' %}
                Les offres de stages du moment
            {% elseif app.request.pathinfo == '/theme/offres-alternance' %}
                Les offres en alternance du moment
            {% endif %}
        </h4>
        <div class="box_entreprises_wrapper">
            <div class="box_entreprises_offres">
            {% for entreprise in entreprises_stages_alternances[0:3] %}
                <div class="box_entreprises">
                    <div class="entreprises_logo">
                        <img src="{{ asset("/uploads/logos/" ~ entreprise.logo) }}" alt="{{entreprise.logo}}">
                    </div>
                {% if loop.index % 2 == 0 %}
                    <div class="entreprises_offres jaune">
                        <h5><strong>{{entreprise.RaisonSociale}}</strong></h5>
                        <ul>
                        {% for key, offres in entreprises_offres %}
                            {% if key == entreprise.idEntreprise %}
                                {% for offre in offres %}
                                    <li><a href="{{ path('app_candidat_search_details', {id: offre.idOffre}) }}">{{offre.intitule|u.truncate(20, '...', false) }}</a></li>
                                {% endfor %}
                            {% endif %}
                        {% endfor %}
                        </ul>
                        <a target="_blank"  href="{{ path('app_entreprises_details', {id: entreprise.idEntreprise, slug: entreprise.raisonSociale}) }}">Voir toutes les offres</a>
                    </div>
                {% else %}
                    <div class="entreprises_offres bleu">
                        <h5><strong>{{entreprise.RaisonSociale}}</strong></h5>
                        <ul>
                        {% for key, offres in entreprises_offres %}
                            {% if key == entreprise.idEntreprise %}
                                {% for offre in offres %}
                                    <li><a href="{{ path('app_candidat_search_details', {id: offre.idOffre}) }}">{{offre.intitule|u.truncate(20, '...', false) }}</a></li>
                                {% endfor %}
                            {% endif %}
                        {% endfor %}
                        </ul>
                        <a target="_blank" href="{{ path('app_entreprises_details', {id: entreprise.idEntreprise, slug: entreprise.raisonSociale}) }}">Voir toutes les offres</a>
                    </div>
                {% endif %}
                </div>
            {% endfor %}
            </div>
            <div class="box_entreprises_offres">
            {% for entreprise in entreprises_stages_alternances[4:] %}
                <div class="box_entreprises">
                    <div class="entreprises_logo">
                        <img src="{{ asset("/uploads/logos/" ~ entreprise.logo) }}" alt="{{entreprise.logo}}">
                    </div>
                {% if loop.index % 2 == 0 %}
                    <div class="entreprises_offres bleu">
                        <h5><strong>{{entreprise.RaisonSociale}}</strong></h5>
                        <ul>
                        {% for key, offres in entreprises_offres %}
                            {% if key == entreprise.idEntreprise %}
                                {% for offre in offres %}
                                    <li><a href="{{ path('app_candidat_search_details', {id: offre.idOffre}) }}">{{offre.intitule|u.truncate(20, '...', false) }}</a></li>
                                {% endfor %}
                            {% endif %}
                        {% endfor %}
                        </ul>
                        <a target="_blank" href="{{ path('app_entreprises_details', {id: entreprise.idEntreprise, slug: entreprise.raisonSociale}) }}">Voir toutes les offres</a>
                    </div>
                {% else %}
                    <div class="entreprises_offres jaune">
                        <h5><strong>{{entreprise.RaisonSociale}}</strong></h5>
                        <ul>
                        {% for key, offres in entreprises_offres %}
                            {% if key == entreprise.idEntreprise %}
                                {% for offre in offres %}
                                    <li><a href="{{ path('app_candidat_search_details', {id: offre.idOffre}) }}">{{offre.intitule|u.truncate(20, '...', false) }}</a></li>
                                {% endfor %}
                            {% endif %}
                        {% endfor %}
                        </ul>
                        <a target="_blank"  href="{{ path('app_entreprises_details', {id: entreprise.idEntreprise, slug: entreprise.raisonSociale}) }}">Voir toutes les offres</a>
                    </div>
                {% endif %}
                </div>
            {% endfor %}
            </div>
        </div>
    </div> #}

{#        <div class="actualite_stages_cards_wrapper">#}
{#            <div class="row">#}
{#            {% for article in articles %}#}
{#                <div class="col">#}
{#                        <a href="{{ "/article/" ~ article['id_article'] ~ "/" ~ strtourl(article['titre']) }}" class="card card-zoom" style="height: 100%">#}
{#                            <img src="{{ asset("/uploads/article/" ~ article['image_petite'] ) }}" class="card-img-top" alt="{{article['image_petite']}}" style="width: 100%; align-self: center; height: 14em;">#}
{#                            <div class="card-body">#}
{#                                <h5 class="card-title">{{article['titre']}}</h5>#}
{#                                <p class="card-text">{{article['chapo']|u.truncate(150, '...', false)}}</p>#}
{#                            </div>#}
{#                            <div class="card-footer">#}
{#                                <small class="text-muted">#}
{#                                    <!--<strong>Actualité :</strong>#}
{#                                        {% if article['theme'] == 'Emploi' and article['type'] != 'Article' %}#}
{#                                            {{article['type']}}#}
{#                                        {% elseif article['type'] == 'Article' %}#}
{#                                            Dossier#}
{#                                        {% else %}#}
{#                                            {{article['theme']}}#}
{#                                        {% endif %}#}
{#                                 -->#}
{#                                </small>#}
{#                            </div>#}
{#                        </a>#}
{#                    </div>#}
{#            {% endfor %}#}
{#            </div>#}
{#        </div>#}

        <section class="article-section container">
            {% if app.request.pathinfo == '/theme/offres-stage' %}
                    <h2 class="text-center" style=" margin: auto auto 10px; width: 80%">Les actualités sur le thème du stage </h2>
{#                    <a class="lien_s_actualite" href="/article-theme/stage" title="&gt; Voir toutes les actualités"> Voir toutes les actualités</a>#}
            {% elseif app.request.pathinfo == '/theme/offres-alternance' %}
                    <h2 class="text-center" style=" margin: auto auto 10px; width: 80%">Les actualités de l’alternance et de l’apprentissage </h2>
{#                    <a class="lien_s_actualite" href="/article-theme/alternance" title="&gt; Voir toutes les actualités"> Voir toutes les actualités</a>#}
            {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
                    <h2 class="text-center" style=" margin: auto auto 10px; width: 80%">Les actualités sur le thème de l’intérim </h2>
{#                    <a class="lien_s_actualite" href="/article-theme/interim" title="&gt; Voir toutes les actualités"> Voir toutes les actualités</a>#}

            {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
                    <h2 class="text-center" style=" margin: auto auto 10px; width: 80%">Les actualités sur le thème du recrutement </h2>
{#                    <a class="lien_s_actualite" href="/article-theme/cabinet-recrutement" title="&gt; Voir toutes les actualités"> Voir toutes les actualités</a>#}
            {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
                    <h2 class="text-center" style=" margin: auto auto 10px; width: 80%">Les actualités sur le thème du handicap </h2>
{#                    <a class="lien_s_actualite" href="/article-theme/handicap" title="&gt; Voir toutes les actualités"> Voir toutes les actualités</a>#}
            {% elseif app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
                    <h2 class="text-center" style=" margin: auto auto 10px; width: 80%">Les actualités sur le thème du jeune diplômé </h2>
{#                    <a class="lien_s_actualite" href="/article/list" title="&gt; Voir toutes les actualités"> Voir toutes les actualités</a>#}

            {% endif %}
            <div class="row">
                {% for article in articles %}

                    <div class="col-lg-4 col-md-12 mb-3">
                        <a href="{{ "/article/" ~ article['id_article'] ~ "/" ~ strtourl(article['titre']) }}">
                            <div class="card ">
                                <img class="card-img-top mb-3" alt="{{ article['image_petite'] }}" style="height: 230px;border-radius: 1.25rem; width: 100%;object-fit: cover; object-position: center; align-self: center" src={{ asset("/uploads/article/" ~ article['image_petite'] ) }} >

                                <div class="card-body p-0 pt-2">
                                    <div class="d-flex flex-column justify-content-between h-100">
                                        <h3 >{{article['titre']}}</h3>
                                        <p style="line-height: 1.8">{{article['chapo']|u.truncate(150, '...', false)}}</p>
                                        <a href="{{ "/article/" ~ article['id_article'] ~ "/" ~ strtourl(article['titre']) }}" class="card-link">En savoir plus <i class="fa-solid fa-arrow-right" style="line-height: 1.3"></i></a>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>
            {% if app.request.pathinfo == '/theme/offres-stage' %}
                <div class="text-center mt-4">
                    <a class="btn button-inscription" href="/article-theme/stage" title="&gt; Voir toutes les actualités" style="color: white;">Voir toutes les actualités</a>
                </div>
            {% elseif app.request.pathinfo == '/theme/offres-alternance' %}
                <div class="text-center mt-4">
                    <a class="btn button-inscription" href="/article-theme/alternance" title="&gt; Voir toutes les actualités" style="color: white;">Voir toutes les actualités</a>
                </div>
            {% elseif app.request.pathinfo == '/theme/offres-emploi-interim' %}
                <div class="text-center mt-4">
                    <a class="btn button-inscription" href="/article-theme/interim" title="&gt; Voir toutes les actualités" style="color: white;">Voir toutes les actualités</a>
                </div>

            {% elseif app.request.pathinfo == '/theme/offres-emploi-cabinet-de-recrutement' %}
                <div class="text-center mt-4">
                    <a class="btn button-inscription" href="/article-theme/cabinet-recrutement" title="&gt; Voir toutes les actualités" style="color: white;">Voir toutes les actualités</a>
                </div>
            {% elseif app.request.pathinfo == '/theme/offres-emploi-handicap' %}
                <div class="text-center mt-4">
                    <a class="btn button-inscription" href="/article-theme/handicap" title="&gt; Voir toutes les actualités" style="color: white;">Voir toutes les actualités</a>
                </div>
            {% elseif app.request.pathinfo == '/theme/offres-emploi-jeune-diplome' %}
                <div class="text-center mt-4">
                    <a class="btn button-inscription" href="/article/list" title="&gt; Voir toutes les actualités" style="color: white;">Voir toutes les actualités</a>
                </div>

            {% endif %}
        </section>
</section>
<script language="javascript">
    var i = 0;
    var reloadDiv = function() {
        if (i > 0) {
            prev = document.getElementById("entreprises_slide_" + (i - 1));
            prev.style = "display:none";
        }
        if (i == 0) {
            prev = document.getElementById("entreprises_slide_" + '{{nb_entr}}');
            prev.style = "display:none";
        }
        entreprise = document.getElementById("entreprises_slide_" + i);
        entreprise.style = "display:block";
        if (i < '{{nb_entr}}')
            i++;
        else
            i = 0;
    }
    var intervalId = setInterval(reloadDiv, 2500);
</script>
{% endblock %}
