{% extends 'directemploi/layout.html.twig' %}

{% set title_seo = "Recruteurs : Créez votre compte et postez vos annonces sur Direct Emploi" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% set description_seo = "Postez vos offres d'emploi c'est simple sur Direct Emploi : Créez votre compte recruteur en quelques minutes, accédez simplement à nos services Emploi" %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}

{% block content %}
	<div class="site">
		<div class="container">

			{% if breadcrumb is defined %}
				{% include '/_partials/_breadcrumb.html.twig'  %}
			{% endif %}


			{% if app.request.pathinfo == '/espaceRecruteur/gestionCompte' %}
				<h1>Modifier mon compte recruteur</h1>
			{% else %}
				<h1>Créer un compte recruteur</h1>
			{% endif %}
			<div class="block_promo">
				<div class="content">Notre offre est accessible uniquement aux entreprises certifiés. Nous contrôlons l'identité de chaque société. Pour l'<strong>inscription</strong>
					et la
					<strong>diffusion d'offres</strong>, l'utilisation d'un email professionnel (ex :
					<b><EMAIL></b>) et non une adresse email générique (ex :
					<b><EMAIL></b>), est obligatoire.</div>
			</div>

			<div class="obli">
				<p>Les champs marqués d'une astérisque
					<span class="ob">
				*</span>
					sont obligatoires</p>
			</div>

			{{ form_start(inscriptionRecruteur) }}

			<div class="form_box">
				<h3>Votre compte</h3>
				<table class="formulaire">

					<tr>
						<td class="label">{{ form_label(inscriptionRecruteur.login, "Login")}}<span class="ob">
						*</span>
						</td>
						<td>{{ form_widget(inscriptionRecruteur.login,{ 'attr': {'placeholder': 'Login'} })}}</td>
					</tr>

					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.passCrypt.first, "Mot de passe")}}<span class="ob">
						*</span>
						</td>
						<td>{{ form_widget(inscriptionRecruteur.passCrypt.first, { 'attr': {'placeholder': 'Mot de passe'} })}}</td>
					</tr>

					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.passCrypt.second,"Confirmation Mot de passe")}}<span class="ob">
						*</span>
						</td>
						<td>{{ form_widget(inscriptionRecruteur.passCrypt.second,{ 'attr': {'placeholder': 'Confirmation mot de passe'} })}}</td>
					</tr>
				</table>

			</div>
			<div class="form_box">
				<h3>Votre identité</h3>
				<table class="formulaire">
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.civilite,"Civilité")}}<span class="ob">
						*</span>
						</td>
						<td>{{ form_widget(inscriptionRecruteur.civilite,{ 'attr': {'placeholder': 'Login'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.nom,"Nom")}}<span class="ob">
						*</span>
						</td>
						<td>{{ form_widget(inscriptionRecruteur.nom,{ 'attr': {'placeholder': 'Nom'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.prenom,"Prénom")}}<span class="ob">
						*</span>
						</td>
						<td>{{ form_widget(inscriptionRecruteur.prenom,{ 'attr': {'placeholder': 'Prénom'} })}}</td>
					</tr>
				</table>
			</div>

			<div class="form_box">
				<h3>Vos coordonnées</h3>
				<table class="formulaire">
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.email,"Email")}}<span class="ob">
						*</span>
						</td>
						<td>{{ form_widget(inscriptionRecruteur.email,{ 'attr': {'placeholder': 'Email'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.telephone,"Numéro de téléphone")}}<span class="ob">
						*</span>
						</td>
						<td>{{ form_widget(inscriptionRecruteur.telephone,{ 'attr': {'placeholder': 'Numéro de téléphone'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.telephone_portable,"Téléphone portable")}}
						</td>
						<td>{{ form_widget(inscriptionRecruteur.telephone_portable,{ 'attr': {'placeholder': 'Téléphone Portable'} })}}</td>
					</tr>
				</table>
			</div>

			<div class="form_box">
				<h3>Votre entreprise</h3>
				<table class="formulaire">
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.raison_sociale,"Raison sociale")}}<span class="ob">
						*</span>
						</td>
						<td>{{ form_widget(inscriptionRecruteur.raison_sociale,{ 'attr': {'placeholder': 'Raison sociale'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.numero_siret,"Numéro de Siret")}}</td>
						<td>{{ form_widget(inscriptionRecruteur.numero_siret,{ 'attr': {'placeholder': 'Numéro de Siret'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.site_internet,"Site internet")}}</td>
						<td>{{ form_widget(inscriptionRecruteur.site_internet,{ 'attr': {'placeholder': 'Site internet'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.presentation_entreprise,"Présentation de l'entreprise")}}<span class="ob">
						*</span>
						</td>
						<td>{{ form_widget(inscriptionRecruteur.presentation_entreprise,{ 'attr': {'placeholder': 'Présentation de l\'entreprise'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.secteur_activite, "Secteur d'activité de l'entreprise")}}<span class="ob"> *</span></td>
						<td>{{ form_widget(inscriptionRecruteur.secteur_activite,{ 'attr': {'placeholder': "Secteur d'activité de l'entreprise"} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.adresse,"Adresse")}}<span class="ob"> *</span></td>
						<td>{{ form_widget(inscriptionRecruteur.adresse,{ 'attr': {'placeholder': 'Adresse'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.complement_adresse, "Complement d'adresse") }}</td>
						<td>{{ form_widget(inscriptionRecruteur.complement_adresse,{ 'attr': {'placeholder': 'Complément d\'adresse'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.code_postal,"Code Postal")}}<span class="ob"> *</span></td>
						<td>{{ form_widget(inscriptionRecruteur.code_postal,{ 'attr': {'placeholder': 'Code postal'} })}}</td>
					</tr>
					<tr>
						<td class='label'>{{ form_label(inscriptionRecruteur.ville, "Ville")}}<span class="ob"> *</span></td>
						<td>{{ form_widget(inscriptionRecruteur.ville,{ 'attr': {'placeholder': 'Ville'} })}}</td>
					</tr>

				</table>
			</div>


			<div id="err_zone"></div>

			<div class="act">
				<button type="submit" class="btn" id="btn_submit" onclick="checkEndForm(event);">Valider et continuer</button>
			</div>

			{{ form_end(inscriptionRecruteur) }}

			<div class='smaller'>
				<br/>Les informations recueillies à partir de ce formulaire font l’objet d’un traitement informatique destiné à <strong>Direct Emploi – Direct Performance</strong>pour une gestion de<strong>comptes et clientèle</strong>.
				Le destinataire de ces données est notre<strong>service commercial</strong>.
				Conformément à la loi « informatique et libertés » du 6 janvier 1978 modifiée, vous disposez d’un droit d’accès et de rectification aux informations qui vous concernent.   Vous pouvez accéder aux informations vous concernant en vous adressant à :<a href="mailto:<EMAIL>"><EMAIL></a>.
				Vous pouvez également, pour des motifs légitimes, vous opposer au traitement des données vous concernant.</div>

			<div class="box_alaune ordi" style="margin-top:20px;">
				<h2>Notre réseau</h2>
				<div class="int clearfix">
					<div class="item">
						<img alt="Notre reseau" src="/images/bandeau_reseau2.png">
						<img alt="Apec" src="/images/carre_apec.png">
						<img alt="Pole Emploi" src="/images/carre_poleemploi.png">
					</div>
				</div>
			</div>
		</div>
	</div>


	<script type="text/javascript">

		const hideElement = function(Elem2Hide) {
			Elem2Hide.hidden = true;
		}

		const putError = function(text) {
			var err_div = document.getElementById('err_zone');
			err_div.innerHTML = ('<p><font color="red">' + text + '</p>');
			if (text != "")
				event.preventDefault();
		}

		const checkEndForm = function(e) {
			let selected_count = 0;
			let secteurs = document.getElementById("inscription_recruteur_secteur_activite").options;
			for (let i in secteurs)
				if (secteurs[i].selected == true)
					selected_count++;
			selected_count > 3 ? putError("selectionnez 3 secteurs maximum") : putError("");
		}

		window.onload = () => {
			var whoami = window.location.href.split("/espaceRecruteur/");
			{% if user is defined %}
			document.querySelector("#inscription_recruteur_login").value = '{{ user.getLogin() }}'
			document.querySelector("#inscription_recruteur_civilite").value = '{{ user.getUserCiviliteIdUserCivilite() }}'
			document.querySelector("#inscription_recruteur_nom").value = '{{ user.getNom() }}'
			document.querySelector("#inscription_recruteur_prenom").value = '{{ user.getPrenom() }}'
			document.querySelector("#inscription_recruteur_email").value = '{{ user.getEmail() }}'
			document.querySelector("#inscription_recruteur_telephone").value = '{{ user.getTelephone() }}'
			document.querySelector("#inscription_recruteur_telephone_portable").value = '{{ user.getMobile() }}'
			document.querySelector("#inscription_recruteur_raison_sociale").value = '{{ user_entr.getRaisonSociale() }}'
			document.querySelector("#inscription_recruteur_numero_siret").value = '{{ user_entr.getSiret() }}'
			document.querySelector("#inscription_recruteur_site_internet").value = '{{ user_entr.getSiteWeb() }}'
			document.querySelector("#inscription_recruteur_presentation_entreprise").value = '{{ user_entr.getPresentationSociete() }}'

			preloadSelectiveFields('{{ user_sect }}', 'inscription_recruteur_secteur_activite')

			document.querySelector("#inscription_recruteur_adresse").value = '{{ user_geo.getAdresse1 }}'
			document.querySelector("#inscription_recruteur_complement_adresse").value = '{{ user_geo.getAdresse2 }}'
			document.querySelector("#inscription_recruteur_code_postal").value = '{{ user_geo.getCodePostal }}'
			document.querySelector("#inscription_recruteur_ville").value = '{{ user_geo.getNomVille }}'
			{% endif %}
			document.getElementById("inscription_recruteur_passCrypt_first").required = ""
			document.getElementById("inscription_recruteur_passCrypt_second").required = ""
		}

		function preloadSelectiveFields(data, divname) {
			data = data.split("_");
			for (let i in document.querySelector("#" + divname).options) {
				if (data.includes(document.querySelector("#" + divname).options[i].value))
					document.querySelector("#" + divname).options[i].selected = true;
			}
		}

	</script>

{% endblock %}
