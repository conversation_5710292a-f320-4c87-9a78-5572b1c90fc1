{% extends 'directemploi/layout.html.twig' %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/responsive.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/direct_emploi.min.css') }}">
	<link rel="stylesheet" href="{{ asset('assets/css/new_main_DE.min.css') }}">
{% endblock %}

{% block javascripts %}
	<script src="{{ asset('assets/js/new_script.min.js') }}" defer></script>
	<script src="{{ asset('assets/js/bootstrap.bundle.min.js')}}" defer></script>
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.13.1/jquery-ui.min.js"></script>
{% endblock %}

{% block content %}
    <div class="page_sectoriel">
        <div id="search_container" style="background-image: url(/images/all/{% if zoom[0].image is defined%}{{zoom[0].image}}{% else %}'default.jpg'{% endif %}); height: 300px;">
            <div class="box_rech home_stage clearfix margin_zoom_sectoriel" id="DE_box_rech">
                <div class="inside">
                    <form class="form_emploi" method="get" action="{{ path('app_candidat_search') }}">
                        <h2 class="h3_title" id="DE_h3_title">Trouver un emploi, un contrat en alternance, un stage parmi nos offres
                            <span class='countOffre'></span>
                        </h2>
                        <div class="decoration_compte bar"></div>
                        <div class="select_type_contrat">
                            <select name="id_type_contrat" id="id_type_contrat" onchange="changer_color()" style="color: rgb(117, 117, 117);">
                                <option value="">Type de contrat</option>
                                <option value="2">CDI</option>
                                <option value="1">CDD</option>
                                <option value="4">Interim</option>
                                <option value="3">FreeLance / Indépendant</option>
                                <option value="6">Alternance</option>
                                <option value="8">Stage</option>
                            </select>
                            <div class="select_type_contrat_arrow"></div>
                        </div>
                        <div class="text_localisation">
                            <input type="text" name="localisation" id="localisation" value="" autocomplete="off" class="form_input" placeholder="Localisation">
                            <div id="localisation_auto_complete" class="auto_complete" style="display: none;"></div>  
                        </div>
                        <div class="text_mot_cle">
                            <br>
                            <input type="text" name="mot_cle" id="mot_cle" value="" class="form_input" placeholder="Poste recherché" style="float: left;">
                        </div>
                        <div class="buton_recherche">
                            <input type="submit" id="DE_form_valid">
                        </div>
                        <a href="{{ path('app_candidat_search_avancee') }}" class="form_valid">
                            <div>
                                <img class="bouton_recherche_avancee" src="{{ asset('/images/direct-emploi/LogoLoupe.png') }}">
                            </div>
                        </a>
                    </form>
                </div>
                <br>
            </div>
        </div>
        <div class="form_after" id="search_box_form_after" style="height: 20em;"></div>
        
        
        {# {% include 'entreprises/bannierePublicite.html.twig' %} #}
        <div class="clearfix"></div>
        <div class="clearfix"></div>
        <h3>Les actualités de l'emploi {{title}} <a href="" class="lien_s2">> Voir toutes les actualités</a></h3>
        {% for actu in liste_actus %}
            <div class="box_actus">
                <div class="table list" id="tableListingArticles">
                    <div class="row">
                            <div class="td td_img td_img_sectoriel"> 
                            {% if actu.imagePetite %}
                                <img class="image_article" src="{{ asset('/images/uploads/article/' ~ actu.imagePetite) }}" alt="{{actu.imagePetite}}" style="max-width: none;">
							{% else %}
								<img class="image_article" src="{{ asset('/images/uploads/article/' ~ actu.imageMini) }}" alt="{{actu.imageMini}}" style="max-width: none;">
                            {% endif %}
                            </div>
                        <div class="td texte">
                            <div class="intitule">
                                <h4><a>{{actu.titre}}</a></h4>
                            </div>
                            <div class="text">
                                {{actu.chapo}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
    <script>
        var divs = document.getElementsByClassName('text');

        for(i=0; i <= divs.length; i++) {
            divs[i].innerHTML = divs[i].innerHTML.substring(0,300) + ' ...';
        }
    </script>
{% endblock %}
