{% extends 'directemploi/new_layout.html.twig' %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/new_zoom_sectoriel.min.css') }}">
{% endblock %}

{% block javascripts %}
	<script src="{{ asset('assets/js/new_script.min.js') }}" defer></script>
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.13.1/jquery-ui.min.js"></script>
{% endblock %}

{% block content %}
<section>
    <div class="container zoom_sectoriel">
        <div style="display: flex; justify-content: space-between; margin-bottom: 20px; border-bottom: 1px solid black">
            <h4>{{zoom[0].titrePage}}</h4>
        </div>
        <div class="box_gris">{{zoom[0].textePage|raw}}</div>
        </div>
    </div>
    <div id="search_container" style="background: center url(/images/all/{% if zoom[0].image is defined%}{{zoom[0].image}}{% else %}'default.jpg'{% endif %}); height: 300px;">
        <div class="recherche_zoom"></div>
        <div class="form_wrapper">
            <h4>Trouver un emploi, un contrat en alternance, un stage parmi nos offres</h4>
            <form class="form_emploi" method="get" action="">
                <div class="form_content">
                    <div class="row mt-2 mb-2 p-0">
                        <div class="col-3">
                            <select class="form-select" name="id_type_contrat" id="id_type_contrat" onchange="changer_color()">
                                <option value="">Contrat</option>
                                <option value="2">CDI</option>
                                <option value="1">CDD</option>
                                <option value="4">Interim</option>
                                <option value="3">FreeLance / Indépendant</option>
                                <option value="6">Alternance</option>
                                <option value="8">Stage</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <input type="text" name="q" id="mot_cle" value="" class="form-control" placeholder="Métier, domaine, mots clés">
                        </div>
                    </div>
                    <div class="row mt-2 mb-2 p-0">
                        <div class="col-6">
                            <input type="text" name="localisation" id="localisation" value="" class="form-control" placeholder="Région, département, ville">
                        </div>
                        <div class="col-3" style="margin-left: 40px">
                            <button type="submit" class="btn btn-warning"><strong>Rechercher</strong></button>
                        </div>
                    </div>
                </div>
                <div class="vr" style="width: 2px; background-color: black; opacity: 1; margin: 11px; margin-right: 50px;"></div>
                <div class="form_advanced">
                    <a href="{{ path('app_candidat_search_avancee') }}" class="btn btn-warning">
                        <span class="material-symbols-outlined">search</span><strong>Recherche avancée</strong> 
                    </a>
                </div>
            </form>
        </div>
    </div>
    <div class="container offres_du_moment">
    <h4>Toutes nos offres {{zoom['libelle_activite']}} classées par métier (H/F)</h4>
        <div class="listing" id="liste_metiers" style="justify-content: center">
            <ul class="liste">
                {% set counter = 0 %}
                {% if zoom['libelle_activite'] == "Petite enfance / Social / Aide à la personne" %}
                    {% for metier in metiers %}
                        {% if counter != 0 and counter % 8 == 0 %}
                            </ul>
                            <ul class="liste">
                        {% endif %}
                        <li>
                            <a href={{ "lists-offre/france-metier-" ~ struct_home_search(metier.LibelleMetier) ~ "-secteurs" }}>{{ metier.LibelleMetier }}</a>
                        </li>
                    {% set counter = counter + 1 %}
                    {% endfor %}
                {% elseif zoom['libelle_activite'] == "Hôtellerie / Restauration / Cuisine" %}
                    {% for metier in metiers %}
                        {% if counter != 0 and counter % 11 == 0 %}
                            </ul>
                            <ul class="liste">
                        {% endif %}
                        <li>
                            <a href={{ "lists-offre/france-metier-" ~ struct_home_search(metier.LibelleMetier) ~ "-secteurs" }}>{{ metier.LibelleMetier }}</a>
                        </li>
                    {% set counter = counter + 1 %}
                    {% endfor %}
                {% elseif zoom['libelle_activite'] == "BTP / Construction / Génie civil" %}
                    {% for metier in metiers %}
                        {% if counter != 0 and counter % 13 == 0 %}
                            </ul>
                            <ul class="liste">
                        {% endif %}
                        <li>
                            <a href={{ "lists-offre/france-metier-" ~ struct_home_search(metier.LibelleMetier) ~ "-secteurs" }}>{{ metier.LibelleMetier }}</a>
                        </li>
                        {% set counter = counter + 1 %}
                    {% endfor %}
                {% elseif zoom['libelle_activite'] == "Artisanat, petit commerce" %}
                    {% for metier in metiers %}
                        {% if counter != 0 and counter % 10 == 0 %}
                            </ul>
                            <ul class="liste">
                        {% endif %}
                        <li>
                            <a href={{ "lists-offre/france-metier-" ~ struct_home_search(metier.LibelleMetier) ~ "-secteurs" }}>{{ metier.LibelleMetier }}</a>
                        </li>
                        {% set counter = counter + 1 %}
                    {% endfor %}
                {% elseif zoom['libelle_activite'] == "Banque / Assurance / Trading" %}
                    {% for metier in metiers %}
                        {% if counter != 0 and counter == 12 or counter == 24 or counter == 36 or counter == 50 or counter == 62 %}
                            </ul>
                            <ul class="liste">
                        {% endif %}
                        <li>
                            <a href={{ "lists-offre/france-metier-" ~ struct_home_search(metier.LibelleMetier) ~ "-secteurs" }}>{{ metier.LibelleMetier }}</a>
                        </li>
                        {% set counter = counter + 1 %}
                    {% endfor %}
                {% elseif zoom['libelle_activite'] == "Agriculture / Animaux / Nature" %}
                    {% for metier in metiers %}
                        {% if counter != 0 and counter % 15 == 0 %}
                            </ul>
                            <ul class="liste">
                        {% endif %}
                        <li>
                            <a href={{ "lists-offre/france-metier-" ~ struct_home_search(metier.LibelleMetier) ~ "-secteurs" }}>{{ metier.LibelleMetier }}</a>
                        </li>
                        {% set counter = counter + 1 %}
                    {% endfor %}
                {% elseif zoom['libelle_activite'] == "Digital / Internet" %}
                    {% for metier in metiers %}
                        {% if counter != 0 and counter == 11 or counter == 23 or counter == 34 or counter == 45 %}
                            </ul>
                            <ul class="liste">
                        {% endif %}
                        <li>
                            <a href={{ "lists-offre/france-metier-" ~ struct_home_search(metier.LibelleMetier) ~ "-secteurs" }}>{{ metier.LibelleMetier }}</a>
                        </li>
                        {% set counter = counter + 1 %}
                    {% endfor %}
                {% elseif zoom['libelle_activite'] == "Santé / Paramédical / Biologie" %}
                    {% for metier in metiers %}
                        {% if counter != 0 and counter % 12 == 0 %}
                            </ul>
                            <ul class="liste">
                        {% endif %}
                        <li>
                            <a href={{ "lists-offre/france-metier-" ~ struct_home_search(metier.LibelleMetier) ~ "-secteurs" }}>{{ metier.LibelleMetier }}</a>
                        </li>
                        {% set counter = counter + 1 %}
                    {% endfor %}
                {% endif %}
            </ul>
        </div>
    </div>
    <div class="container actualite">
            <div>
                <h4>Actualités de l'emploi et de la formation</h4>
                <a href="{{ path('app_articles_list') }}">> Voir toutes les actualités</a>
            </div>
            <div class="actualite_cards_wrapper">
            <div class="row">
                {% for article in liste_actus %}
                    <div class="col-3">
                        <a href={{ "/article/" ~ article['id_article'] ~ "/" ~ strtourl(article['titre']) }} class="card card-zoom" style="height: 100%">
                            <img src={{ asset("/uploads/article/" ~ article['image_petite'] ) }} class="card-img-top" alt="{{article['image_petite']}}" style="height: 150px; width: 100%; align-self: center">
                            <div class="card-body">
                                <h5 class="card-title">{{article['titre']}}</h5>
                                <p class="card-text">{{article['chapo']|u.truncate(150, '...', false)}}</p>
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">
                                    <!--<strong>Actualité :</strong> 
                                        {% if article['theme'] == 'Emploi' and article['type'] != 'Article' %}
                                            {{article['type']}}
                                        {% elseif article['type'] == 'Article' %}
                                            Dossier
                                        {% else %}
                                            {{article['theme']}}
                                        {% endif %}
                                 -->
                                </small>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>
        </div>
</section>
{% endblock %}