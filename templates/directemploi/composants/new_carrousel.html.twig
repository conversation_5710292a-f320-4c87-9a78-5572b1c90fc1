<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.2.1/assets/owl.carousel.min.css">

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.2.1/owl.carousel.min.js"></script>
<style>
    .brand-carousel .box {
        padding: 10px; /* Add some spacing around images */
        align-items: center;
        justify-content: center;
        width: 70%;
        margin: auto;
        display: block;
        margin-top: 40px;
    }
    .brand-carousel .box img {
        width: 100%; /* Prend toute la largeur de la box */
        height: 120px; /* ← Augmente cette valeur pour des images plus grandes */
        object-fit: contain;
        background: white;
        border-radius: 10px;
        box-sizing: border-box;
        transition: transform 0.3s ease;
    }


    .brand-carousel .box img:hover {
        transform: scale(1.1); /* Slight zoom on hover */
    }
    .owl-stage{
        height: 180px;
    }
    .owl-dots{
        margin-top: 20px;
        margin-bottom: 40px;
    }
</style>

<script>
    $(document).ready(function() {
        var boites = [
            {% for boite in boites %}
            {
                url: "{{ boite.url }}",
                image: "{{ asset('/uploads/' ~ boite.image) }}"
            },
            {% endfor %}
        ];

        function adjustCarousel() {
            var itemsToShow = boites.length;

            // Déterminer le nombre d'éléments à afficher selon la largeur de l'écran
            if (window.innerWidth < 600) {
                itemsToShow = Math.ceil(boites.length / 2);
            } else if (window.innerWidth < 1000) {
                itemsToShow = Math.ceil(boites.length / 2);
            } else if (window.innerWidth < 1300) {
                itemsToShow = Math.ceil(boites.length / 2);
            }

            // Trancher le tableau boites pour obtenir les éléments à afficher
            var boitesToShow = boites.slice(0, itemsToShow);

            // Retirer les éléments existants
            $('.brand-carousel').trigger('destroy.owl.carousel').html('');

            // Ajouter les nouveaux éléments
            boitesToShow.forEach(function(boite) {
                $('.brand-carousel').append('<div class="box"><a target="_blank" href="' + boite.url + '"><img src="' + boite.image + '" alt="' + boite.image + '"></a></div>');
            });

            // Initialiser le carrousel
            $('.brand-carousel').owlCarousel({
                loop: true,
                margin: 10,
                autoplay: true,
                autoplayTimeout: 2000,
                responsive: {
                    0: {
                        items: 2
                    },
                    600: {
                        items: 3
                    },
                    1000: {
                        items: 5
                    },
                    1300: {
                        items: 6
                    }
                }
            });
        }

        adjustCarousel();

        $(window).resize(function() {
            adjustCarousel();
        });
    });
</script>

<section>
    <div class="brand-carousel owl-carousel"></div>
    <div class="text-center mt-4">
        <a href="{{ path('app_candidat_search') }}" class="btn button-inscription">Voir toutes les offres</a>
    </div>
</section>
