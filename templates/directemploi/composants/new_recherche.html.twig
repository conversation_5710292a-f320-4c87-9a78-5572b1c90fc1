<link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Source+Sans+3:ital,wght@0,200..900;1,200..900&display=swap" rel="stylesheet">

<style>
    /* Styles de base pour le conteneur d'autocomplétion */
    .autocomplete {
        position: relative;
        display: inline-block;
        width: 100%;
    }

    /* Style pour le champ de saisie */
    .autocomplete input {
        width: 100%;
        background-color: #fff;
        font-size: 16px;
        border-radius: 10px;
    }

    input[type=text]:focus {
        background: transparent;
        outline: none;
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Style pour la liste d'autocomplétion */
    .autocomplete-items {
        position: absolute;
        border: 1px solid #d4d4d4;
        border-bottom: none;
        border-top: none;
        z-index: 99999 !important; /* Z-index très élevé pour éviter tout conflit */
        background-color: #fff;
        left: 0;
        right: 0;
        top: 100%;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 0 0 4px 4px;
        /* Assurer que la liste reste visible même si le parent a overflow:hidden */
        overflow: visible !important;
    }

    /* Style pour chaque élément de la liste */
    .autocomplete-items div {
        padding: 12px 15px;
        cursor: pointer;
        background-color: #fff;
        border-bottom: 1px solid #e9e9e9;
        text-align: left;
        font-size: 14px;
        color: #333;
        transition: background-color 0.2s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Style pour le survol d'un élément */
    .autocomplete-items div:hover {
        background-color: #f1f8ff;
    }

    /* Style pour l'élément actif lors de la navigation au clavier */
    .autocomplete-active {
        background-color: #1e90ff !important;
        color: #fff !important;
    }

    /* Effet de mise en évidence du texte correspondant à la recherche */
    .autocomplete-items div strong {
        color: #44A2C6;
        font-weight: 600;
    }

    .autocomplete-active strong {
        color: #fff !important;
    }

    /* Assurer que les conteneurs parents ne masquent pas la liste */
    .form-item {
        position: relative;
        overflow: visible !important;
    }

    .job-form {
        overflow: visible !important;
    }

    .job-search-card {
        overflow: visible !important;
    }

    /* Style pour les petits écrans */
    @media (max-width: 768px) {
        .autocomplete-items {
            position: fixed;
            top: auto;
            left: 10px;
            right: 10px;
            width: calc(100% - 20px);
            max-height: 50vh;
        }
    }
</style>

<section class="index-header" style="overflow: visible; background-image: url({{ asset('directEmploi_new/Front/images/bg_de_home.png') }} )!important; background-position: 50% 85%!important;" id="">

    {% if path == '/theme/offres-stage' %}
        <h1 style="font-size: 1.25rem; line-height: 1.5;" class="text-center mb-4 text-white">Trouvez votre Stage parmi nos <span class="second-color">{{nb_offres|number_format(0,'', ' ')}} offres de stage</span></h1>
    {% elseif path == '/theme/offres-alternance' %}
        <h1 style="font-size: 1.25rem; line-height: 1.5;" class="text-center mb-4 text-white">Trouvez votre Alternance parmi nos <br/> <span class="second-color">{{nb_offres|number_format(0,'', ' ')}} offres en alternance</span></h1>
    {% elseif path == '/theme/offres-emploi-interim' %}
        <h1 style="font-size: 1.25rem; line-height: 1.5;" class="text-center mb-4 text-white">Trouvez votre Interim parmi nos <br/><span class="second-color">{{nb_offres|number_format(0,'', ' ')}} offres d'intérim</span></h1>
    {% elseif path == '/theme/offres-emploi-cabinet-de-recrutement' %}
        <h1 style="font-size: 1.25rem; line-height: 1.5;" class="text-center mb-4 text-white">Trouvez votre CDI parmi nos <br/><span class="second-color">{{nb_offres|number_format(0,'', ' ')}} offres d'emploi</span></h1>
    {% else %}
        <h1 style="font-size: 1.25rem; line-height: 1.5;" class="text-center mb-4 text-white">Trouvez votre {{intitule}}  parmi nos<br/> <span class="second-color">{{listOffres.getTotalItemCount|number_format(0,'', ' ')}} offres d'emploi</span></h1>
    {% endif %}
    

        <div class="form-padding">
            <form autocomplete="off" method="get" action="{{ path('app_candidat_search') }}">
            <div class="job-search-card mb-4" style="width: 80%; margin: auto">
                <div class="job-form w-100">
                    <div class="form-item">
                        <label for="name"><span class="entypo-user"></span></label>
                        <select placeholder="Name" autofocus style="width:100%;" name="contrat" id="contrat" onchange="changer_color()">
                            {% if intitule_long == 'OFFRES DE STAGE' %}
                                <option value="8">Stage</option>
                            {% elseif intitule_long == "OFFRES D'ALTERNANCE" %}
                                <option value="6">Alternance</option>
                            {% elseif intitule_long == 'OFFRES EN INTERIM' %}
                                <option value="4">Interim</option>
                            {% elseif intitule_long == 'OFFRES EN CABINET DE RECRUTEMENT' %}
                                <option value="2">CDI</option>
                            {% else %}
                                <option value="">Contrat</option>
                                <option value="2">CDI</option>
                                <option value="1">CDD</option>
                                <option value="4">Interim</option>
                                <option value="3">FreeLance / Indépendant</option>
                                <option value="6">Alternance</option>
                                <option value="8">Stage</option>
                            {% endif %}
                            </select>
                    </div>


                    <div class="form-item middle-form" style="overflow: visible !important;">
                        <div class="autocomplete" style="">
                            <label for="poste"><span class="entypo-user"></span></label>
                            <input type="text" name="q" id="mot_cle" placeholder="Métier, domaine, mots clés" >
                        </div>
                    </div>
                    <div class="form-item middle-form" style="overflow: visible !important;">
                        <label for="localisation"><span class="entypo-user"></span></label>
                        <div class="autocomplete" style="">
                            <input id="select_box" type="text" name="localisation" class = "form-control"  placeholder="Région, département, ville">
                        </div>
                        <input type="hidden" name="localisation_field" id="localisation_field" >
                    </div>
                    <div class="text-center title-col job-button p-0"  >
                        <button type="submit" class="btn px-3 py-3 button-black"><i class="fa-solid fa-magnifying-glass" style="color: #ffffff; margin-right: 5px;"></i>Recherche</button>
                    </div>
                </div>

            </div>
            </form>

        </div>

    </section>
<script>
    // ✅ Fonction globale accessible partout
    function normalizeString(str) {
        return str
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/-/g, " ")
            .toLowerCase();
    }

    // =============== MÉTIERS ===============
    async function fetchMetiers(key) {
        try {
            const response = await fetch(`/api/metier-search?key=${encodeURIComponent(key)}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('Fetching metiers failed:', error);
            return [];
        }
    }

    function autocompleteMetier(inp) {
        let currentFocus;
        inp.addEventListener("input", async function () {
            let val = this.value.trim();
            closeAllLists();
            if (!val) return;

            currentFocus = -1;
            let metiers = await fetchMetiers(val);

            let a = document.createElement("DIV");
            a.setAttribute("id", this.id + "autocomplete-list");
            a.setAttribute("class", "autocomplete-items");
            this.parentNode.appendChild(a);

            for (let i = 0; i < metiers.length; i++) {
                let name = metiers[i].libelle_metier;
                if (normalizeString(name).includes(normalizeString(val))) {
                    let b = document.createElement("DIV");
                    b.innerHTML = "<strong>" + name.substr(0, val.length) + "</strong>";
                    b.innerHTML += name.substr(val.length);
                    b.innerHTML += `<input type='hidden' value='${name}'>`;
                    b.addEventListener("click", function () {
                        inp.value = this.getElementsByTagName("input")[0].value;
                        closeAllLists();
                    });
                    a.appendChild(b);
                }
            }
        });

        inp.addEventListener("keydown", function (e) {
            let x = document.getElementById(this.id + "autocomplete-list");
            if (x) x = x.getElementsByTagName("div");
            if (e.keyCode === 40) { currentFocus++; addActive(x); }
            else if (e.keyCode === 38) { currentFocus--; addActive(x); }
            else if (e.keyCode === 13) {
                e.preventDefault();
                if (currentFocus > -1 && x) x[currentFocus].click();
            }
        });

        function addActive(x) {
            if (!x) return;
            removeActive(x);
            if (currentFocus >= x.length) currentFocus = 0;
            if (currentFocus < 0) currentFocus = x.length - 1;
            x[currentFocus].classList.add("autocomplete-active");
        }

        function removeActive(x) {
            for (let i = 0; i < x.length; i++) {
                x[i].classList.remove("autocomplete-active");
            }
        }

        function closeAllLists(elmnt) {
            let x = document.getElementsByClassName("autocomplete-items");
            for (let i = 0; i < x.length; i++) {
                if (elmnt !== x[i] && elmnt !== inp) {
                    x[i].parentNode.removeChild(x[i]);
                }
            }
        }

        document.addEventListener("click", function (e) {
            closeAllLists(e.target);
        });
    }

    // =============== LOCALISATIONS ===============
    async function fetchLocations(key) {
        try {
            const response = await fetch(`/api/location-search?key=${encodeURIComponent(key)}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('Fetching locations failed:', error);
            return [];
        }
    }

    function autocompleteLocation(inp) {
        let currentFocus;
        inp.addEventListener("input", async function () {
            let val = this.value.trim();
            closeAllLists();
            if (!val) return;
            currentFocus = -1;

            let locations = await fetchLocations(val);

            let a = document.createElement("DIV");
            a.setAttribute("id", this.id + "autocomplete-list");
            a.setAttribute("class", "autocomplete-items");
            this.parentNode.appendChild(a);

            for (let i = 0; i < locations.length; i++) {
                let name = locations[i].libelle;
                let type = locations[i].type;
                let codeInsee = locations[i].code_insee || '';

                // On génère ce qui sera affiché dans la liste
                let displayText = name;
                if (type === 'departement' && codeInsee) {
                    displayText = `${name} - ${codeInsee}`;
                }

                // Recherche sur libellé ou code INSEE
                if (
                    normalizeString(name).includes(normalizeString(val)) ||
                    normalizeString(codeInsee).includes(normalizeString(val))
                ) {
                    let b = document.createElement("DIV");

                    // Mise en gras de la correspondance
                    let matchStart = normalizeString(displayText).indexOf(normalizeString(val));
                    if (matchStart !== -1) {
                        b.innerHTML =
                            displayText.substring(0, matchStart) +
                            "<strong>" +
                            displayText.substring(matchStart, matchStart + val.length) +
                            "</strong>" +
                            displayText.substring(matchStart + val.length);
                    } else {
                        b.innerHTML = displayText;
                    }

                    b.innerHTML += `<input type='hidden' value='${name}' data-type='${type}' data-codeinsee='${codeInsee}'>`;

                    b.addEventListener("click", function () {
                        // Affiche le libellé dans l’input avec le code INSEE si c’est un département
                        inp.value = name + (type === 'departement' && codeInsee ? ` - ${codeInsee}` : '');
                        document.querySelector('[name="localisation_field"]').value = type;
                        closeAllLists();
                    });

                    a.appendChild(b);
                }
            }
        });

        inp.addEventListener("keydown", function (e) {
            let x = document.getElementById(this.id + "autocomplete-list");
            if (x) x = x.getElementsByTagName("div");
            if (e.keyCode === 40) { currentFocus++; addActive(x); }
            else if (e.keyCode === 38) { currentFocus--; addActive(x); }
            else if (e.keyCode === 13) {
                e.preventDefault();
                if (currentFocus > -1 && x) x[currentFocus].click();
            }
        });

        function addActive(x) {
            if (!x) return;
            removeActive(x);
            if (currentFocus >= x.length) currentFocus = 0;
            if (currentFocus < 0) currentFocus = x.length - 1;
            x[currentFocus].classList.add("autocomplete-active");
        }

        function removeActive(x) {
            for (let i = 0; i < x.length; i++) {
                x[i].classList.remove("autocomplete-active");
            }
        }

        function closeAllLists(elmnt) {
            let x = document.getElementsByClassName("autocomplete-items");
            for (let i = 0; i < x.length; i++) {
                if (elmnt !== x[i] && elmnt !== inp) {
                    x[i].parentNode.removeChild(x[i]);
                }
            }
        }

        document.addEventListener("click", function (e) {
            closeAllLists(e.target);
        });
    }


    async function detectLocationType(locationInput) {
        let input = locationInput.trim().toLowerCase();
        let locations = await fetchLocations(input);
        let locationField = document.querySelector('[name="localisation_field"]');
        let found = locations.find(loc => loc.libelle.toLowerCase() === input);
        locationField.value = found ? found.type : "";
        updateFormAction();
    }

    function updateFormAction() {
        let form = document.querySelector("form");
        let locationType = document.querySelector('[name="localisation_field"]').value;
        let searchParams = new URLSearchParams(new FormData(form));
        if (locationType) searchParams.set("localisation_field", locationType);
        searchParams.set("page", "1");
        form.action = form.action.split("?")[0] + "?" + searchParams.toString();
    }

    // ✅ Initialisation des autocomplétions
    autocompleteMetier(document.getElementById("mot_cle"));
    autocompleteLocation(document.getElementById("select_box"));
    document.getElementById("select_box").addEventListener("input", function () {
        detectLocationType(this.value);
    });
</script>


