{% if pageCount > 1 %}
    <div class="pagination-center text-center">
        <div class="pagination">
            <!-- Double flèche pour revenir à la première page -->
            {% if previous is defined %}
            <a href="{{ path(route, query|merge({(pageParameterName): 1})) }}">&laquo;&laquo;</a>
            {% else %}
            <a href="#">&laquo;&laquo;</a>
            {% endif %}
            
            <!-- Page précédente -->
            {% if previous is defined %}
            <a href="{{ path(route, query|merge({(pageParameterName): previous})) }}">&laquo;</a>
            {% else %}
            <a href="#">&laquo;</a>
            {% endif %}
            
            <!-- Numéros de page -->
            {% for page in pagesInRange %}
            <a href="{{ path(route, query|merge({(pageParameterName): page})) }}" class="{{ page == current ? 'active' }}">{{ page }}</a>
            {% endfor %}
            
            <!-- Page suivante -->
            {% if next is defined %}
            <a href="{{ path(route, query|merge({(pageParameterName): next})) }}">&raquo;</a>
            {% else %}
            <a href="#">&raquo;</a>
            {% endif %}
            
            <!-- Double flèche pour aller directement à la dernière page -->
            {% if next is defined %}
            <a href="{{ path(route, query|merge({(pageParameterName): pageCount})) }}">&raquo;&raquo;</a>
            {% else %}
            <a href="#">&raquo;&raquo;</a>
            {% endif %}
        </div>
    </div>
    
{% endif %}