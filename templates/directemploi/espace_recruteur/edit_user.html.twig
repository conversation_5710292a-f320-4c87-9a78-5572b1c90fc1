{% extends 'directemploi/new_layout.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="{{ asset('assets/css/direct_emploi.css') }}">
{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1 class="mb-4">Éditer les coordonnées</h1>

    {{ form_start(form) }}
        <div class="form-group mb-3">
            {{ form_label(form.email, 'Email', {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(form.email, {'attr': {'class': 'form-control'}}) }}
            {{ form_errors(form.email) }}
        </div>
        {# <div class="form-group mb-3">
            {{ form_label(form.password_crypt, 'Mot de passe', {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(form.password_crypt, {'attr': {'class': 'form-control'}}) }}
            {{ form_errors(form.password_crypt) }}
        </div> #}
        <div class="form-group mb-3">
            {{ form_label(form.nom, 'Nom', {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(form.nom, {'attr': {'class': 'form-control'}}) }}
            {{ form_errors(form.nom) }}
        </div>
        <div class="form-group mb-3">
            {{ form_label(form.prenom, 'Prénom', {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(form.prenom, {'attr': {'class': 'form-control'}}) }}
            {{ form_errors(form.prenom) }}
        </div>
        <div class="form-group mb-3">
            {{ form_label(form.telephone, 'Téléphone', {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(form.telephone, {'attr': {'class': 'form-control'}}) }}
            {{ form_errors(form.telephone) }}
        </div>

        <button type="submit" class="btn btn-primary">Enregistrer</button>
        <a href="{{ path('app_espace_recruteur') }}" class="btn btn-secondary">Retour</a>
    {{ form_end(form) }}
</div>
{% endblock %}
