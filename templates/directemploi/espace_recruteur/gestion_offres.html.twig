
<style>
	.row{
		display:flex;
	}
	#rechercheOffres hr{
		margin-bottom: 0;
	}
	#rechercheOffres .formulaire{
		background-color:rgba(0,0,0,0.03);
		margin-top: 0;
	}
	#rechercheOffres .formulaire .form-control{
		width: 100px;
	}
	#rechercheOffres .formulaire .form-select{
		width: 100px;
	}
</style>

<section>
	<div class="de-div call-to-action">
		<div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
			<div style="flex: 1 1 100%; margin-bottom: 1rem;">
				<h4 style="margin: 0;">
					<strong>Bienvenue dans la rubrique gestion des offres !</strong>
				</h4>
			</div>
			<p style="margin: 0; flex: 1;margin-top: 40px">
				Vous avez actuellement <span class="blue">{{ user_stats.getNbOffresEmploi() }} offres d'emploi</span>, <span class="blue">{{ user_stats.getNbOffresStage() }} offres de stage</span> et <span class="blue">{{ user_stats.getNbOffresAlternance() }} offres en alternance</span> publiées sur le site.
			</p>
		</div>
	</div>
</section>

{#  <section>
	<h4 >Rechercher une offre d'emploi / stage / alternance</h4>
	<hr>
	<div class="filter-div">
		<div class="row align-items-end">
			<div class="col-lg-2">
				<div class="form-group mb-2">
					<label for="inputTags">Mot clé</label>
					<input type="text" class="" id="inputTags" aria-describedby="tagsHelp" placeholder="CDI, CDD...">
				</div>
			</div>
			<div class="col-lg-3">
				<div class="form-group mb-2">
					<label for="contratFormControl">Contrat</label>
					<select class="form-select " id="contratFormControl">
						<option>Type de contrat</option>
					</select>
				</div>
			</div>
			<div class="col-lg-2">
				<div class="form-group mb-2">
					<label for="contratFormControl">Etat de l'offre</label>
					<select class="form-select form-control" id="contratFormControl">
						<option>Tous</option>

					</select>
				</div>
			</div>
			<div class="col-lg-3">
				<div class="form-group mb-2">
					<label for="contratFormControl">Trier par</label>
					<select class="form-select form-control" id="contratFormControl">
						<option>Type de contrat</option>
					</select>
				</div>
			</div>
			<div class="col-lg-2">
				<div class="text-center mb-2 ">
					<button type="submit" class="btn w-100 px-4 button-inscription">Valider</button>
				</div>
			</div>
		</div>
	</div>
</section>#}


<div id="rechercheOffres">
	<h4 style="color: #f6ca03;border: 0"><strong>Rechercher une offre d'emploi / stage / alternance</strong></h4>
	<hr>
	<div id="moteur">
		<table width="100%" cellspacing="0" cellpadding="0" border="0" class="formulaire">
			<tr valign="top">
				{{ form_start(RechercheOffre) }}
				<td align="left" valign="middle"> Mot clé {{ form_widget(RechercheOffre.mot_cle,{ 'attr': {'placeholder': '', 'id': 'mc','class':'form-control'} }) }} </td>
				<td align="left" valign="middle"> Contrat {{ form_widget(RechercheOffre.type_offre,{ 'attr': {'placeholder': '','class':'form-select'} }) }} </td>
				<td align="left" valign="middle"> Etat de l'offre {{ form_widget(RechercheOffre.etat_offre,{ 'attr': {'placeholder': '','class':'form-select'} }) }} </td>
				<td align="left" valign="middle"> Trier par : {{ form_widget(RechercheOffre.tri_offre,{ 'attr': {'placeholder': '','class':'form-select'} }) }} </td>
				<td align="center" colspan="3" valign="middle">
					<div class="act">
						<button class="ajax btn submit button-inscription" id="offreSearch">Valider</button>
					</div>
				</td>
				{{ form_end(RechercheOffre) }}
			</tr>
		</table>
	</div>
	<div id="loading" style="display:none;">
		<p align="center">
			<img src="/images/loading.gif">
			Recherche en cours &hellip;
		</p>
	</div>
</div>


{% include 'directemploi/espace_recruteur/off_diffu_tmp.html.twig' %}
<table>
	<tr>
		<td colspan="4" id="error_date_cv_download"> Vous pouvez télécharger vos Cvs dans la limite des 3 derniers mois</td></tr>
	<tr>
		<td style="width:45%">
			<h4>Liste des offres</h4>
			<div class="header-line"></div>
		</td>


		<td style="width:10%;" valign="middle"> à partir du :</td>
		<td valign="middle" style="width:10%;">
			<input type="date" id="date" class="form-control" min="{{ 'now' | dateThreeMonth }}" max="{{ 'now'|date("Y-m-d") }}">
		</td>
		<td align="center">
			<button id="downloadCv" style="margin-left: 30px;" class="ajax btn submit sub_jaune sub_large blue " title="Télécharger les Cvs">Télécharger les Cvs</button>
		</td>
</table>

<iframe id="download" style="display:none;"></iframe>

{% if mssg is defined %} <div style="text-align:center" > <span id="mssg" style="border: 1px solid #4F805D;padding: 5px;color: white;background-color: #4F805D;"> {{ mssg }} </span></div> {% endif %}


{#
<div id="rechercheOffres">
	<h4>Rechercher une offre d'emploi / stage / alternance</h4>
	<div id="moteur">
		<table width="100%" cellspacing="0" cellpadding="0" border="0" class="formulaire">
			<tr valign="top">
            	{{ form_start(RechercheOffre) }}
                    <td align="center" valign="middle"> Mot clé {{ form_widget(RechercheOffre.mot_cle,{ 'attr': {'placeholder': '', 'id': 'mc'} }) }} </td>
                    <td align="center" valign="middle"> Contrat {{ form_widget(RechercheOffre.type_offre,{ 'attr': {'placeholder': ''} }) }} </td>
					<td align="center" valign="middle"> Etat de l'offre {{ form_widget(RechercheOffre.etat_offre,{ 'attr': {'placeholder': ''} }) }} </td>
                    <td align="center" valign="middle"> Trier par : {{ form_widget(RechercheOffre.tri_offre,{ 'attr': {'placeholder': ''} }) }} </td>
					<td align="center" colspan="3" valign="middle">
					<div class="act">
						<button class="ajax btn submit sub_jaune" id="offreSearch">Valider</button>
					</div>
					</td>
                {{ form_end(RechercheOffre) }}
			</tr>
		</table>
	</div>
	<div id="loading" style="display:none;">
		<p align="center">
		    <img src="/images/loading.gif">
			Recherche en cours &hellip;
		</p>
	</div>
</div>


				{% include 'directemploi/espace_recruteur/off_diffu_tmp.html.twig' %}
<table>
    <tr>
    	<td colspan="4" id="error_date_cv_download"> Vous pouvez télécharger vos Cvs dans la limite des 3 derniers mois</td></tr>
	<tr>
	<td style="width:45%">
		<h2>Liste des offres</h2>
	</td>
	<td>
		<button id="downloadCv" class="ajax btn submit sub_jaune sub_large" title="Télécharger les Cvs">Télécharger les Cvs</button>
	</td>

    <td style="width:10%;"> &nbsp; à partir du :</td>
	<td>
        &nbsp;<input type="date" id="date" min="{{ 'now' | dateThreeMonth }}" max="{{ 'now'|date("Y-m-d") }}">
    </td>

</table>

<iframe id="download" style="display:none;"></iframe>

{# {% if mssg is defined %} <div style="text-align:center" > <span id="mssg" style="border: 1px solid #4F805D;padding: 5px;color: white;background-color: #4F805D;"> {{ mssg }} </span></div> {% endif %} #}


<div id="ajax-reslits" class="my-3">
	{% include '/directemploi/espace_recruteur/recherche_offre.html.twig' %}
</div>


<script language="javascript">

	window.addEventListener('load',
			function() {
				setTimeout(displyMssg, 7000);
			}, false);


	function displyMssg() {
		document.getElementById('mssg').style.display = "none";
	}



	let date = document.querySelector("#date");
	let download = document.getElementById("download_button");
	let today = new Date();

	const goToDiffusion = function() {
		let selected = document.getElementById("offres_dispo").value;
		if (selected != "#")
			location.href = "/espaceRecruteur/diffuser/?id_off_co=" + selected;
	}

	const sendDownloadForm = function(event, value) {
		var year = value.toLocaleString("default", { year: "numeric" });
		var month = value.toLocaleString("default", { month: "2-digit" });
		var day = value.toLocaleString("default", { day: "2-digit" });

		var formattedDate = day + "-" + month + "-" + year;
		var datas = {
			"dateDownload" : formattedDate,
		};
		$.ajax({
			type: "POST",
			url: "espaceRecruteur",
			data: datas,
		}).done(function (data) {
			//download files
		});
		event.preventDefault();
	};

	window.addEventListener("load", function(event) {
		error = document.getElementById("error_date_cv_download");
		hideElement(error);
	});
</script>

<script>
	$(document).on('click', 'button.ajax', function () {
		that = $(this);
		var app_route = $(this).attr("id");
		$.ajax({
			url: 'espaceRecruteur/' + app_route,
			type: "POST",
			dataType: "json",
			data: {
				"onclick": "true",
				"date": document.getElementById("date").value,
				"mc": document.getElementById("recherche_offre_mot_cle").value,
				"type_offre": document.getElementById("recherche_offre_type_offre").value,
				"tri_offre": document.getElementById("recherche_offre_tri_offre").value,
				"etat_offre": document.getElementById("recherche_offre_etat_offre").value
			},
			async: true,
			success: function (data) {

				if(app_route  == 'downloadCv'){
					if(Object.entries(data).length !== 0){
						location.href = data;
					} else {
						alert('Aucun Cv reçu pour la période sélectionnée');
					}
				} else {
					document.getElementById("ajax-reslits").innerHTML = data.output;
				}

			}
		});
		return false;
	});

</script>
