
<style>
	.row {
		display: flex;
	}
	.row {
		--bs-gutter-x: 3%;
		--bs-gutter-y: 0;
		display: flex;
		flex-wrap: wrap;
		margin-top: calc(-1* var(--bs-gutter-y));
		margin-right: calc(-.5* var(--bs-gutter-x));
		margin-left: calc(-.5* var(--bs-gutter-x));
	}
	@media (min-width: 992px) {

		.col-lg-6 {
			flex: 0 0 auto;
			width: 47%;
		}
	}
</style>
<div id="recruteur" style="">
	<div id="msg_onglet_1">
		<h4 style="padding-bottom: 30px">
			<strong>Bienvenue dans la Cvthèque de Direct Emploi !</strong>
		</h4>

		<p>
			Avec plus de <strong style="color: #44A2C6">800 000 inscrits</strong>, découvrez la nouvelle version de notre cvthèque avec ses dernières optimisations.
			Au-delà des critères de sélection correspondant aux informations transmises par les candidats, nous vous invitons à utiliser la recherche par mots clés dans le CV, notamment pour indiquer le métier recherché.
			<br><br>
			Plus de <strong style="color: #44A2C6">35 000 profils</strong> s’inscrivent ou corrigent leurs informations professionnelles chaque mois, alors n’hésitez pas à revenir consulter de nouveaux CV !
		</p>
	</div>

	<div class="cvtheque-banner v-flex justify-content-center align-items-center my-5">
		{% if canSeeCvtheque %}
			<p class="p_count" style="padding-top: 0;">Il y a <span class="span_count">{{ count }} CV</span> disponibles</p>

			<a class="btn button-inscription" href="{{ path('app_cvtheque_page_search') }}">Accéder au moteur de recherche</a>
		{% else %}
			<p class="p_count" style="padding-top: 35px;">Il y a <span class="span_count">{{ count }} CV</span> disponibles</p>

			<p>
				<button class="button-inscription" style="border:none;"><a href="{{ path('app_contact')}}" style="color:white;"><strong>CONTACTEZ NOTRE SERVICE COMMERCIALE </br>POUR ACCEDER A NOS SERVICES </br> 06 80 17 52 34</strong></a></button></p>
		{% endif %}

	</div>

<script>
	document.addEventListener('DOMContentLoaded', function() {
    if (window.location.href.indexOf('show_cvt=active') > -1) {
        console.log('scroll to top');
        setTimeout(function() {
            window.scrollTo(0, 0);
        }, 100);  
    }
});
</script>

	{# <div class="row cvtheque-card-row justify-content-around">
		<div class="col-lg-6 mb-5">
			<a href="{{ path('app_all_my_profils') }}">

				<div class="de-div v-flex align-items-center">
					<img class="img-fluid mb-2" src="/images/direct-emploi/cvt/Frame_55.png" alt="image rubrique profils favoris">
					<a href="{{ path('app_all_my_profils') }}" class="medium">Profil & Favoris</a>
				</div>
			</a>
		</div>
		<div class="col-lg-6 mb-5">
			<a href="{{ path('app_notice_cvt') }}">

				<div class="de-div v-flex align-items-center">
					<img class="img-fluid mb-2" src="/images/direct-emploi/cvt/Frame_56.png" alt="image rubrique mode d'emploi">
					<a href="{{ path('app_notice_cvt') }}" class="medium">Mode & emploi</a>
				</div>
			</a>
		</div>
		<div class="col-lg-6 mb-5">
			<a href="{{ path('app_potentiel_cvt') }}">

				<div class="de-div v-flex align-items-center">
					<img class="img-fluid mb-2" src="/images/direct-emploi/cvt/Frame_57.png" alt="image rubrique potentiels">
					<a href="{{ path('app_potentiel_cvt') }}" class="medium">Potentiels & nouveautés</a>
				</div>
			</a>
		</div>
		<div class="col-lg-6 mb-5">
			<a href="{{ path('app_save_search_cvt') }}">

				<div class="de-div v-flex align-items-center">
					<img class="img-fluid mb-2" src="/images/direct-emploi/cvt/Frame_58.png" alt="image rubrique alerte mail">
					<a href="{{ path('app_save_search_cvt') }}" class="medium">Alerte & mail</a>
				</div>
			</a>
		</div>
	</div> #}

	{#	<div class="grid_all_unique">#}
	{#    	<div class='grid_unique_a'>#}
	{#			<a href="{{ path('app_all_my_profils') }}">#}
	{#			<img src="/images/direct-emploi/cvt/Frame_55.png" alt="image rubrique profils favoris">#}
	{#			<br>Profils &amp; favoris</a>#}
	{#		</div>#}
	{#		<div class='grid_unique_b'>#}
	{#			<a href="{{ path('app_notice_cvt') }}">#}
	{#			<img src="/images/direct-emploi/cvt/Frame_56.png" alt="image rubrique mode d'emploi">#}
	{#			<br>Mode &amp; d'emploi </a>#}
	{#		</div>#}
	{#		<div class='grid_unique_c'>#}
	{#			<a href="{{ path('app_potentiel_cvt') }}">#}
	{#			<img src="/images/direct-emploi/cvt/Frame_57.png" alt="image rubrique potentiels">#}
	{#			<br>Potentiels &amp; nouveautés</a>#}
	{#		</div>#}
	{#		<div class='grid_unique_d'>#}
	{#			<a href="{{ path('app_save_search_cvt') }}">#}
	{#			<img src="/images/direct-emploi/cvt/Frame_58.png" alt="image rubrique alerte mail">#}
	{#			<br>Alerte &amp; mail</a>#}
	{#		</div>#}
	{#	</div>#}


</div>
