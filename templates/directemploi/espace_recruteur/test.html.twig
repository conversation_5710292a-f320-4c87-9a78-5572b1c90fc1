{% extends 'directemploi/new_layout.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="{{ asset('assets/css/direct_emploi.css') }}">



    <style>
        .paginations ul {
            list-style: none;
            display: flex;
            justify-content: center; /* Garantit que les éléments de paginations sont centrés */
            padding-left: 0; /* Enlève le padding par défaut des listes */
        }

        .paginations li {
            margin: 0 5px; /* Espace entre les boutons de paginations */
        }

        .paginations li a {
            text-decoration: none;
            padding: 8px 12px; /* Ajustez au besoin pour la taille des liens */
            display: block; /* Assure que le padding est appliqué correctement */
            border-radius: 5px; /* Bordures arrondies pour les liens (optionnel) */
        }

        .paginations li.active a {
            background-color: #007bff; /* Fond bleu pour la page active */
            color: white; /* Texte blanc pour la page active */
            font-weight: bold;
        }


    </style>


{% endblock %}

{% block content %}

    <section>

    </section>

    <div class="container">
        <div class="h-flex-between">
            <h4>Liste des candidats</h4>
            <form method="get" action="{{ path('app_espace_recruteur_candidatures_download') }}">
                <label for="start_date">Date de début :</label>
                <input type="date" id="start_date" name="start_date" required>

                <label for="end_date">Date de fin :</label>
                <input type="date" id="end_date" name="end_date" required>

                <button type="submit">Télécharger la liste des candidatures</button>
            </form>
            {# <button class="blue" style="background: none;border:none;" data-bs-toggle="offcanvas" data-bs-target="#offcanvasExample" aria-controls="offcanvasExample">Voir les candidatures intéressantes</button> #}
        </div>
        <hr>

        <div class="container-table100">
            <div class="wrap-table100">
                <div class="table">
                    <div class="row-table header">
                        <div class="cell">
                            Intérêt
                        </div>
                        <div class="cell">
                            Nom
                        </div>
                        <div class="cell">
                            Prénom
                        </div>
                        <div class="cell">
                            Email
                        </div>
                        <div class="cell">
                            Offre
                        </div>
                        <div class="cell">
                            Date de
                            candidature
                        </div>
                        <div class="cell">
                            Actions
                        </div>
                    </div>
                    {% for candidate in candidates %}
                        {% set cvPath = 'uploads_test/temp/' ~ candidate.cv %}
                        {% if not fileExists(cvPath) %}
                            {% set cvPath = 'uploads_test/cv_cand_save/' ~ candidate.cv %}
                        {% endif %}

                        <div class="row-table">
                            <div class="cell" data-title="Intérêt">
                                {% if candidate.tri_recruteur == 1 %}
                                    <span class="interessant">Intéressante</span>
                                {% elseif candidate.tri_recruteur == 2 %}
                                    <span class="non-interessant">Non intéressante</span>
                                {% else %}
                                    <span class="en-attente">En attente</span>
                                {% endif %}


                            </div>
                            <div class="cell" data-title="Nom">
                                {{ candidate.nom }}
                            </div>
                            <div class="cell" data-title="Prénom">
                                {{ candidate.prenom }}
                            </div>
                            <div class="cell" data-title="Email">
                                {{ candidate.email }}
                            </div>
                            <div class="cell" data-title="Offre">
                                {{ candidate.intitule }}
                            </div>
                            <div class="cell" data-title="Date de
                                    candidature">
                                {{ candidate.date_candidature|date('d/m/Y') }}
                            </div>
                            <div class="cell" data-title="Actions">
                                <a href="" data-bs-toggle="modal" data-bs-target="#cvModal{{ loop.index }}"><i class="fa-solid fa-eye" style="color: #999999;"></i></a>
                                <a href="{{ asset(cvPath) }}" download><i class="fa-solid fa-download" style="color: #44a2c6;"></i></a>
                                {# {% if candidate.tri_recruteur != 1 %} #}
                                {# <a href="#" class="btn btn-primary mark-interesting-btn" data-candidature-id="{{ candidate.id }}">Intéressante</a> #}
                                <a href="" class="mark-interesting-btn" data-candidature-id="{{ candidate.id }}"><i class="fa-solid fa-thumbs-up" style="color: #4ec644;"></i></a>
                                {# {% endif %} #}
                                {# {% if candidate.tri_recruteur == 1 %} #}
                                {# <a href="#" class="btn btn-danger mark-uninteresting-btn" data-candidature-id="{{ candidate.id }}">Non intéressante</a> #}
                                <a href="" class="mark-uninteresting-btn" data-candidature-id="{{ candidate.id }}"><i class="fa-solid fa-thumbs-down" style="color: #f33232;"></i></a>
                                {# {% endif %} #}
                                {% if candidate.view > 0 %}
                                  {#  <img class="img-fluid"  src="/images/direct-emploi/cv_validate.png" alt="img_cv_vu">#}
                                    <span class="green">Déjà vu</span>
                                {% endif %}
                                <div class="modal fade" id="cvModal{{ loop.index }}" tabindex="-1" aria-labelledby="cvModal{{ loop.index }}Label" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="cvModal{{ loop.index }}Label">CV de {{ candidate.nom }} {{ candidate.prenom }}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                {% set cvPath = 'uploads_test/temp/' ~ candidate.cv %}
                                                {% if not fileExists(cvPath) %}
                                                    {% set cvPath = 'uploads_test/cv_cand_save/' ~ candidate.cv %}
                                                {% endif %}
                                                {% if candidate.cv|split('.')|last == 'pdf' %}
                                                    <!-- Inclure le PDF dans un iframe -->
                                                    <iframe src="{{ asset(cvPath) }}" width="100%" height="600" frameborder="0"></iframe>
                                                {% elseif candidate.cv|split('.')|last == 'docx' or candidate.cv|split('.')|last == 'doc' %}
                                                    <!-- Inclure le document Word dans un iframe -->
                                                    <iframe src="https://docs.google.com/gview?url={{ asset(cvPath) }}&embedded=true" width="100%" height="600" frameborder="0"></iframe>
                                                {% else %}
                                                    <img src="{{ asset(cvPath) }}" alt="CV de {{ candidate.nom }} {{ candidate.prenom }}" class="img-fluid">
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    {% endfor %}

                </div>
            </div>
        </div>
        <div class="d-flex justify-content-center">
            <div class="paginations">
                {% if totalPages > 1 %}
                    <ul class="paginations-list">
                        <!-- Lien vers la première page et la page précédente -->
                        {% if currentPage > 1 %}
                            <li><a href="{{ path('app_espace_recruteur_candidatures', {'page': 1}) }}">&laquo;&laquo;</a></li>
                            <li><a href="{{ path('app_espace_recruteur_candidatures', {'page': currentPage - 1}) }}">&laquo;</a></li>
                        {% endif %}

                        <!-- Affichage des pages avec gestion des ellipses -->
                        {% set startPage = max(currentPage - 5, 1) %}
                        {% set endPage = min(startPage + 9, totalPages) %}

                        {% if startPage > 1 %}
                            <li><a href="#">...</a></li>
                        {% endif %}

                        {% for i in range(startPage, endPage) %}
                            <li class="{% if i == currentPage %}active{% endif %}">
                                <a href="{{ path('app_espace_recruteur_candidatures', {'page': i}) }}">{{ i }}</a>
                            </li>
                        {% endfor %}

                        {% if endPage < totalPages %}
                            <li><a href="#">...</a></li>
                        {% endif %}

                        <!-- Lien pour la page suivante et la dernière page -->
                        {% if currentPage < totalPages %}
                            <li><a href="{{ path('app_espace_recruteur_candidatures', {'page': currentPage + 1}) }}">&raquo;</a></li>
                            <li><a href="{{ path('app_espace_recruteur_candidatures', {'page': totalPages}) }}">&raquo;&raquo;</a></li>
                        {% endif %}
                    </ul>
                {% endif %}
            </div>
        </div>



    </div>
    <br><br>
    <br><br>
    <div class="offcanvas offcanvas-start custom-offcanvas w-50" tabindex="-1" id="offcanvasExample" aria-labelledby="offcanvasExampleLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="offcanvasExampleLabel">Retrouvez les candidatures intéressante</h5>
            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Fermer"></button>
        </div>
        <div class="offcanvas-body">
            <!-- Contenu de votre tableau -->
            <table class="table">
                <thead>
                <tr>
                    <th>Nom</th>
                    <th>Prénom</th>
                    <th>Email</th>
                    <th>Offre</th>
                    <th>Date de candidature</th>
                    <th>Actions</th>
                    <!-- Ajoutez plus de colonnes si nécessaire -->
                </tr>
                </thead>
                {# on affiche uniquement les candidatures qui ont un tri_recruteur = 1 #}
                <tbody>
                {% for candidate in candidates %}
                    {% set cvPath = 'uploads_test/temp/' ~ candidate.cv %}
                    {% if not fileExists(cvPath) %}
                        {% set cvPath = 'uploads_test/cv_cand_save/' ~ candidate.cv %}
                    {% endif %}
                    {% if candidate.tri_recruteur == 1 %}
                        <tr>
                            <td>{{ candidate.nom }}</td>
                            <td>{{ candidate.prenom }}</td>
                            <td>{{ candidate.email }}</td>
                            <td>{{ candidate.intitule }}</td>
                            <td>{{ candidate.date_candidature|date('d/m/Y') }}</td>
                            <td>
                                <!-- Bouton pour ouvrir la modal du CV -->
                                {# <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#cvModal{{ loop.index }}">
                      Voir le CV
                    </button> #}
                                <a href="#" class="" data-bs-toggle="modal" data-bs-target="#cvModal{{ loop.index }}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {# <a href="{{ asset(cvPath) }}" class="btn btn-success" download>Télécharger CV</a> #}
                                <a href="{{ asset(cvPath) }}" class="" download>
                                    <i class="fas fa-download"></i>
                                </a>
                                {# {% if candidate.tri_recruteur != 1 %} #}
                                {# <a href="#" class="btn btn-primary mark-interesting-btn" data-candidature-id="{{ candidate.id }}">Intéressante</a> #}
                                {# {% endif %} #}
                                {# {% if candidate.tri_recruteur == 1 %} #}
                                {# <a href="#" class="btn btn-danger mark-uninteresting-btn" data-candidature-id="{{ candidate.id }}">Non intéressante</a> #}
                                <a href="#" class="mark-uninteresting-btn" data-candidature-id="{{ candidate.id }}">
                                    <i class="fas fa-thumbs-down"></i>
                                </a>
                                {# {% endif %} #}
                                <!-- Modal pour afficher le CV -->
                                <div class="modal fade" id="cvModal{{ loop.index }}" tabindex="-1" aria-labelledby="cvModal{{ loop.index }}Label" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="cvModal{{ loop.index }}Label">CV de {{ candidate.nom }} {{ candidate.prenom }}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                {% set cvPath = 'uploads_test/temp/' ~ candidate.cv %}
                                                {% if not fileExists(cvPath) %}
                                                    {% set cvPath = 'uploads_test/cv_cand_save/' ~ candidate.cv %}
                                                {% endif %}
                                                {% if candidate.cv|split('.')|last == 'pdf' %}
                                                    <!-- Inclure le PDF dans un iframe -->
                                                    <iframe src="{{ asset(cvPath) }}" width="100%" height="600" frameborder="0"></iframe>
                                                {% elseif candidate.cv|split('.')|last == 'docx' or candidate.cv|split('.')|last == 'doc' %}
                                                    <!-- Inclure le document Word dans un iframe -->
                                                    <iframe src="https://docs.google.com/gview?url={{ asset(cvPath) }}&embedded=true" width="100%" height="600" frameborder="0"></iframe>
                                                {% else %}
                                                    <img src="{{ asset(cvPath) }}" alt="CV de {{ candidate.nom }} {{ candidate.prenom }}" class="img-fluid">
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <!-- Ajoutez d'autres colonnes selon vos besoins -->
                        </tr>
                    {% endif %}
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>

        document.addEventListener("DOMContentLoaded", function () {
            console.log("DOM entièrement chargé et script démarré.");



            var candidatesJson = {{ candidatesJson|raw }};
            console.log("Données JSON des candidats:", candidatesJson);

            var viewButtons = document.querySelectorAll('.fa-eye');
            viewButtons.forEach(function (button) {
                button.addEventListener('click', function (event) {
                    var candidatureId = button.closest('tr').querySelector('.mark-interesting-btn').dataset.candidatureId;


                    // Envoyer une requête AJAX
                    fetch('{{ path('app_mark_viewed', {'id': 0}) }}'.replace('0', candidatureId), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({})
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Mettre à jour l'interface utilisateur en fonction de la réponse
                                // if (data.isViewed) {
                                //     // Mettre à jour le nombre de vue dans le tableau
                                //     button.closest('tr').querySelector('.views').textContent = data.views;
                                // }

                                // si le candidat a été vu on affiche l'image img_cv_vu dans un td a la fin du tableau
                                if (data.isViewed) {
                                    var removeTd = button.closest('tr').querySelector('td:last-child');
                                    if (removeTd) {
                                        removeTd.remove();
                                    }
                                    var td = document.createElement('td');
                                    td.style.maxWidth = '10px';
                                    var img = document.createElement('img');
                                    img.src = '/images/direct-emploi/cv_validate.png';
                                    img.alt = 'img_cv_vu';
                                    img.classList.add('img-fluid');
                                    td.appendChild(img);
                                    button.closest('tr').appendChild(td);
                                }
                                console.log(data.message);

                            } else {
                                console.error(data.message);
                            }
                        })
                        .catch(error => console.error(error));
                });
            });

            // <div class="modal-backdrop fade show"></div>
            var modalBackdrop = document.querySelector('.modal-backdrop');
            if (modalBackdrop) {
                modalBackdrop.remove();
            }


            // si dans le tableau candidatesJson il y a des candidats qui ont un tri_recruteur = 1
            // alors on affiche le bouton "Non intéressante" pour ces candidats
            candidatesJson.forEach(function (candidate) {
                if (candidate.tri_recruteur === 1) {
                    var markInterestingButton = document.querySelector('.mark-interesting-btn[data-candidature-id="' + candidate.id + '"]');
                    if (markInterestingButton) {
                        markInterestingButton.style.display = 'none';
                    }

                    var markUninterestingButton = document.querySelector('.mark-uninteresting-btn[data-candidature-id="' + candidate.id + '"]');
                    if (markUninterestingButton) {
                        markUninterestingButton.style.display = 'inline-block';
                    }
                }
            });

            // si dans le tableau candidatesJson il y a des candidats qui ont un tri_recruteur = 2
            // alors on affiche le bouton "Intéressante" pour ces candidats
            candidatesJson.forEach(function (candidate) {
                if (candidate.tri_recruteur === 2) {
                    var markInterestingButton = document.querySelector('.mark-interesting-btn[data-candidature-id="' + candidate.id + '"]');
                    if (markInterestingButton) {
                        markInterestingButton.style.display = 'inline-block';
                    }

                    var markUninterestingButton = document.querySelector('.mark-uninteresting-btn[data-candidature-id="' + candidate.id + '"]');
                    if (markUninterestingButton) {
                        markUninterestingButton.style.display = 'none';
                    }
                }
            });

            var markInterestingButtons = document.querySelectorAll('.mark-interesting-btn');
            var markUninterestingButtons = document.querySelectorAll('.mark-uninteresting-btn');
            console.log("Configuration des boutons d'intérêt et de désintérêt.");

            var markInterestingBadge = document.querySelector('.badge.bg-primary');
            var markUninterestingBadge = document.querySelector('.badge.bg-danger');

            markInterestingButtons.forEach(function (button) {
                button.addEventListener('click', function (event) {

                    event.preventDefault();
                    var candidatureId = button.dataset.candidatureId;
                    console.log("Bouton d'intérêt cliqué:", button);
                    console.log("ID du candidat:", candidatureId);

                    console.log("Préparation de la requête AJAX pour l'intérêt avec l'ID:", candidatureId);

                    // Envoyer une requête AJAX
                    fetch('{{ path('app_mark_interested', {'id': 0}) }}'.replace('0', candidatureId), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({})
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                console.log(data);
                                // Mettre à jour l'interface utilisateur en fonction de la réponse
                                if (data.isInterested) {
                                    // Cacher le bouton "Intéressante"
                                    button.style.display = 'none';

                                    // Afficher le bouton "Non intéressante"
                                    var uninterestingButton = button.parentElement.querySelector('.mark-uninteresting-btn');
                                    if (uninterestingButton) {
                                        uninterestingButton.style.display = 'inline-block';
                                    }

                                    // Afficher le badge "Intéressante"
                                    button.closest('tr').querySelector('.badge').classList.remove('bg-secondary');
                                    button.closest('tr').querySelector('.badge').classList.remove('bg-danger');
                                    button.closest('tr').querySelector('.badge').classList.add('bg-primary');
                                    button.closest('tr').querySelector('.badge').textContent = 'Intéressante';

                                    // on met a jour le tableau offcanvas si on a cliqué sur le bouton "Intéressante" d'un candidat dans le tableau principal on alimente le tableau offcanvas
                                    var markInterestingButton = document.querySelector('.mark-interesting-btn[data-candidature-id="' + candidatureId + '"]');
                                    if (markInterestingButton) {
                                        markInterestingButton.style.display = 'none';
                                    }

                                    var markUninterestingButton = document.querySelector('.mark-uninteresting-btn[data-candidature-id="' + candidatureId + '"]');
                                    if (markUninterestingButton) {
                                        markUninterestingButton.style.display = 'inline-block';
                                    }

                                    var tableBody = document.querySelector('.offcanvas table tbody');
                                    var tr = document.createElement('tr');
                                    // on recupere le chemin du cv public/temp/ ou cv_cand_save/
                                    function fileExists(url) {
                                        var http = new XMLHttpRequest();
                                        http.open('HEAD', url, false);
                                        http.send();
                                        return http.status != 404;
                                    }

                                    // Utilisation de la fonction fileExists
                                    var cvPath = '/uploads_test/temp/' + data.cv;
                                    if (!fileExists(cvPath)) {
                                        cvPath = '/uploads_test/cv_cand_save/' + data.cv;
                                    }
                                    tr.innerHTML = `
                                    <td>${button.closest('tr').querySelector('td:nth-child(2)').textContent}</td>
                                    <td>${button.closest('tr').querySelector('td:nth-child(3)').textContent}</td>
                                    <td>${button.closest('tr').querySelector('td:nth-child(4)').textContent}</td>
                                    <td>${button.closest('tr').querySelector('td:nth-child(5)').textContent}</td>
                                    <td>${button.closest('tr').querySelector('td:nth-child(6)').textContent}</td>
                                    <td>
                                        <!-- Bouton pour ouvrir la modal du CV -->
                                        <a href="#" class="" data-bs-toggle="modal" data-bs-target="#cvModal">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <!-- Bouton pour télécharger le CV -->
                                        <a href="${cvPath}" class="" download>
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <!-- Bouton pour marquer la candidature comme non intéressante -->
                                        <a href="#" class="mark-uninteresting-btn" data-candidature-id="${data.id}">
                                            <i class="fas fa-thumbs-down"></i>
                                        </a>
                                        <!-- Modal pour afficher le CV -->
                                        <div class="modal fade" id="cvModal" tabindex="-1" aria-labelledby="cvModalLabel" aria-hidden="true">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="cvModalLabel">CV de ${button.closest('tr').querySelector('td:nth-child(2)').textContent} ${button.closest('tr').querySelector('td:nth-child(3)').textContent}</h5>

                                                    </div>
                                                    <div class="modal-body">
                                                        ${data.cv.split('.').pop() === 'pdf' ? `<iframe src="${cvPath}" width="100%" height="600" frameborder="0"></iframe>` : `<img src="${cvPath}" alt="CV de ${button.closest('tr').querySelector('td:nth-child(2)').textContent} ${button.closest('tr').querySelector('td:nth-child(3)').textContent}" class="img-fluid">`}
                                                        </div>

                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                `;
                                    tableBody.appendChild(tr);


                                }
                                console.log(data.message);
                            } else {
                                console.error(data.message);
                            }
                        })
                        .catch(error => console.error(error));
                });
            });

            markUninterestingButtons.forEach(function (button) {
                button.addEventListener('click', function (event) {
                    event.preventDefault();
                    var candidatureId = button.dataset.candidatureId;
                    console.log("Bouton de désintérêt cliqué:", button);
                    console.log("ID du candidat:", candidatureId);

                    console.log("Préparation de la requête AJAX pour le désintérêt avec l'ID:", candidatureId);
                    // Envoyer une requête AJAX
                    fetch('{{ path('app_mark_uninterested', {'id': 0}) }}'.replace('0', candidatureId), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({})
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                console.log(data);
                                // Mettre à jour l'interface utilisateur en fonction de la réponse
                                if (data.isUninterested) {
                                    // Cacher le bouton "Non intéressante"
                                    button.style.display = 'none';

                                    var interestingButton = button.parentElement.querySelector('.mark-interesting-btn');
                                    if (interestingButton) {
                                        interestingButton.style.display = 'inline-block';
                                    }

                                    // Afficher le badge "Non intéressante"
                                    button.closest('tr').querySelector('.badge').classList.remove('bg-secondary');
                                    button.closest('tr').querySelector('.badge').classList.remove('bg-primary');
                                    button.closest('tr').querySelector('.badge').classList.add('bg-danger');
                                    button.closest('tr').querySelector('.badge').textContent = 'Non intéressante';

                                    // on veut supprimer la ligne du tableau offcanvas
                                    var markUninterestingButton = document.querySelector('.mark-uninteresting-btn[data-candidature-id="' + candidatureId + '"]');
                                    if (markUninterestingButton) {
                                        markUninterestingButton.style.display = 'none';
                                    }

                                    var markInterestingButton = document.querySelector('.mark-interesting-btn[data-candidature-id="' + candidatureId + '"]');
                                    if (markInterestingButton) {
                                        markInterestingButton.style.display = 'inline-block';
                                    }

                                    // on met a jour le tableau offcanvas si on a cliqué sur le bouton "Non intéressante" d'un candidat dans le tableau principal on supprime la ligne du tableau offcanvas
                                    var tableBody = document.querySelector('.offcanvas table tbody');
                                    var tr = document.querySelector('.offcanvas table tbody tr');
                                    if (tr) {
                                        tr.remove();
                                    }
                                }
                                console.log(data.message);
                            } else {
                                console.error(data.message);
                            }
                        })
                        .catch(error => console.error(error));
                });
            });

            // Dans mon offcanvas, si je clique sur le bouton "Non intéressante" d'un candidat il doit disparaitre du tableau offcanvas
            var offcanvasMarkUninterestingButtons = document.querySelectorAll('.offcanvas .mark-uninteresting-btn');

            offcanvasMarkUninterestingButtons.forEach(function (button) {
                button.addEventListener('click', function (event) {
                    event.preventDefault();
                    var candidatureId = button.dataset.candidatureId;

                    // Envoyer une requête AJAX
                    fetch('{{ path('app_mark_uninterested', {'id': 0}) }}'.replace('0', candidatureId), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({})
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                console.log(data);
                                // Mettre à jour l'interface utilisateur en fonction de la réponse
                                if (data.isUninterested) {
                                    // Cacher le bouton "Non intéressante"
                                    button.style.display = 'none';

                                    // Supprimer la ligne du tableau
                                    button.closest('tr').remove();

                                    // on met a jour le badge de la liste principale
                                    var markUninterestingButton = document.querySelector('.mark-uninteresting-btn[data-candidature-id="' + candidatureId + '"]');
                                    if (markUninterestingButton) {
                                        markUninterestingButton.style.display = 'none';
                                    }

                                    var markInterestingButton = document.querySelector('.mark-interesting-btn[data-candidature-id="' + candidatureId + '"]');
                                    if (markInterestingButton) {
                                        markInterestingButton.style.display = 'inline-block';
                                    }

                                    // Afficher le badge "Non intéressante"
                                    markUninterestingBadge.classList.remove('bg-secondary');
                                    markUninterestingBadge.classList.remove('bg-danger');
                                    markUninterestingBadge.classList.add('bg-danger');
                                    markUninterestingBadge.textContent = 'Non intéressante';

                                    // on met a jour le badge de la liste principale
                                    var badge = markUninterestingButton.closest('tr').querySelector('.badge');
                                    if (badge) {
                                        badge.classList.remove('bg-secondary');
                                        badge.classList.remove('bg-danger');
                                        badge.classList.add('bg-danger');
                                        badge.textContent = 'Non intéressante';
                                    }

                                }
                                console.log(data.message);
                            } else {
                                console.error(data.message);
                            }
                        })
                        .catch(error => console.error(error));
                });
            });
        });
    </script>
{% endblock %}