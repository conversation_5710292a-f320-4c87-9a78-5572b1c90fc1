{% extends 'directemploi/new_layout.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="{{ asset('assets/css/direct_emploi.css') }}">
{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1 class="mb-4">Éditer l'entreprise</h1>

    {{ form_start(form) }}
        <div class="form-group mb-3">
            {{ form_label(form.raison_sociale, 'Raison sociale', {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(form.raison_sociale, {'attr': {'class': 'form-control'}}) }}
            {{ form_errors(form.raison_sociale) }}
        </div>
        <div class="form-group mb-3">
            {{ form_label(form.siret, 'SIRET', {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(form.siret, {'attr': {'class': 'form-control'}}) }}
            {{ form_errors(form.siret) }}
        </div>
        <div class="form-group mb-3">
            {{ form_label(form.telephone, 'Téléphone', {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(form.telephone, {'attr': {'class': 'form-control'}}) }}
            {{ form_errors(form.telephone) }}
        </div>
        <div class="form-group mb-3">
            {{ form_label(form.site_web, 'Site web', {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(form.site_web, {'attr': {'class': 'form-control'}}) }}
            {{ form_errors(form.site_web) }}
        </div>
        <div class="form-group mb-3">
            {{ form_label(form.presentation_societe, 'Présentation de la société', {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(form.presentation_societe, {'attr': {'class': 'form-control'}}) }}
            {{ form_errors(form.presentation_societe) }}
        </div>

        <button type="submit" class="btn btn-primary">Enregistrer</button>
        <a href="{{ path('app_espace_recruteur') }}" class="btn btn-secondary">Retour</a>
    {{ form_end(form) }}
</div>
{% endblock %}
