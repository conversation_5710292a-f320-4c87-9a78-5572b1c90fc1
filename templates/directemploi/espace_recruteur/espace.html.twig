{% extends 'directemploi/layout.html.twig' %}

{% block title %}Votre espace recruteur | Direct Emploi{% endblock %}

{% block stylesheets %}
	<link rel="stylesheet" href="{{ asset('assets/css/espace_recrut.min.css') }}">
	<link rel="stylesheet" href="{{ asset('assets/css/espace_recrut_cvt.min.css') }}">
	<link rel="stylesheet" href="{{ asset('assets/css/direct_emploi.css') }}">
{% endblock %}

{% block content %}
	<div class="container">

		<h2>Mon compte</h2>

		<div id="fil_ariane" class="my-3">
			<a href="/" title="Accueil">Accueil</a> -
			<a href="/espaceRecruteur" title="Recruteur">Recruteur</a>
		</div>

		{% set page = app.request.get('page') %}


		{% if cvtready %}
			{% set cvtactive = "active" %}
			{% set mon_compte = "" %}
			{% set diffu = "" %}
		{% elseif diffu %}
			{% set cvtactive = "" %}
			{% set mon_compte = "" %}
			{% set diffu = "active" %}
		{% elseif mon_compte %}
			{% set mon_compte = "active" %}
			{% set cvtactive = "" %}
			{% set diffu = "" %}
		{% elseif proposal %}
			{% set proposal = "active" %}
			{% set mon_compte = "" %}
			{% set cvtactive = "" %}
			{% set diffu = "" %}
		{% else %}
			{% set mon_compte = "active" %}
			{% set cvtactive = "" %}
			{% set diffu = "" %}

		{% endif %}
		<style>
			.nav-wrapper {
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				padding: 10px;
			}

			.onglets {
				display: flex;
				flex-direction: row;
				gap: 10px;
				flex-wrap: wrap;
			}

			.onglets a {
				text-decoration: none;
				padding: 10px;
				background-color: #f0f0f0;
				border-radius: 5px;
				transition: background-color 0.3s;
			}
			.onglets a.active {
				background: #44A2C6;
				color: white;
				border:none;
			}
			.onglets a:hover {
				background-color: #44A2C6AA;
				color: white;
				border: none;
			}

			@media (max-width: 600px) {
				.onglets {
					flex-direction: column;
					width: 100%;
				}
				.onglets a {
					width: 100%;
					text-align: left;
					margin-top: 5px;
				}
			}

		</style>
		<div class="nav-wrapper" style="border-bottom: 1px solid #bdbdbd;">
			<div class="onglets">
				<a id="onglet_mon_compte" class="{{mon_compte}}" onclick="showOffer('mon_compte', 'onglet_mon_compte');">Mon compte</a>
				<a id="onglet_offres" class="{{diffu}}" onclick="showOffer('gestion_offres', 'onglet_offres')">Gestion des offres</a>
				<a id="onglet_candidatures" class="" onclick="showOffer('gestion_candidatures', 'onglet_candidatures');">Gestion des candidatures</a>
				<a id="onglet_CVthèque" class="{{ cvtactive }}" onclick="showOffer('CVthèque', 'onglet_CVthèque');">CVthèque</a>
				{# <a id="onglet_proposal" class="{{ proposal }}" onclick="showOffer('proposal', 'onglet_proposal');">Offres proposées</a> #}
			</div>
		</div>

		{#		<hr style="height: 0.5px;background: #bdbdbd;">#}

		{#
		<div class="row">
            <div class="col-lg-3">
                <div class="settings-card">
                    <a href="#" class="blue">
                        <i class="fa-solid fa-arrow-left"></i>
                        Retourner vers le profil
                    </a>
                    <h6>Mon compte</h6>
                    <a id="onglet_mon_compte" onclick="showOffer('mon_compte', 'onglet_mon_compte');" href="" class="menu-link {{mon_compte}}">Mon compte</a>
                    <a id="onglet_offres"  href="" onclick="showOffer('gestion_offres', 'onglet_offres')" class="menu-link {{diffu}}">Gestion des offres</a>
                    <a href="" class="menu-link">Gestion des candidatures</a>
                    <a href="" class="menu-link">CVthèque</a>
                    <a href="" class="menu-link">Offres proposées</a>
                </div>
            </div>

            <div id="mon_compte" class="col-lg-9">
                <div id="tab_onglet_m1">
                    {% include '/directemploi/espace_recruteur/mon_compte.html.twig' %}
                </div>
            </div>

            <div id="gestion_offres" class="col-lg-9">
                <div id="tab_onglet_m2">

                    {% include '/directemploi/espace_recruteur/gestion_offres.html.twig' %}
                </div>
            </div>
            <div id="gestion_candidatures" class="col-lg-9">
                <div id="tab_onglet_m3">

                    {% include '/directemploi/espace_recruteur/candidatures_gestion.html.twig' %}
                </div>
            </div>
            <div id="CVthèque" class="col-lg-9">
                <div id="tab_onglet_m3">

                    {% include '/directemploi/espace_recruteur/cvtheque.html.twig' %}
                </div>
            </div>
            <div id="proposal" class="col-lg-9">
                <div id="tab_onglet_m4">

                    {% include '/directemploi/espace_recruteur/proposal.html.twig' %}
                </div>
            </div>
        </div>#}


		<div id="mon_compte" class="contener" style="padding:0px">
			<div class="field2" id="tab_onglet_m1" style="display:block;">
				{% include '/directemploi/espace_recruteur/mon_compte.html.twig' %}
			</div>
		</div>

		<div id="gestion_offres" class="contener" style="padding:0px">
			<div class="field2" id="tab_onglet_m2" style="display:block;">
				{% include '/directemploi/espace_recruteur/gestion_offres.html.twig' %}
			</div>
		</div>

		<div id="gestion_candidatures" class="contener" style="padding:0px">
			<div class="field2" id="tab_onglet_m3" style="display:block;">
				{% include '/directemploi/espace_recruteur/candidatures_gestion.html.twig' %}
			</div>
		</div>

		<div id="CVthèque" class="contener" style="padding:0px">
			<div class="field2" id="tab_onglet_m3" style="display:block;padding:27px">
				{% include '/directemploi/espace_recruteur/cvtheque.html.twig' %}
			</div>
		</div>

		{# <div id="proposal" class="contener" style="padding:0px">
			<div class="field2" id="tab_onglet_m4" style="display:block;padding:27px">
				{% include '/directemploi/espace_recruteur/proposal.html.twig' %}
			</div>
		</div> #}
	</div>


	<script type="text/javascript">

		window.onload = () => {
			var active = document.getElementsByClassName('active')[0].id;
			if (active == "onglet_mon_compte") {
				showOffer('mon_compte', 'onglet_mon_compte')
				console.log("mon_compte")
			}
			if (active == "onglet_offres") {
				showOffer('gestion_offres', 'onglet_offres')
				console.log("gestion_offres")
			}
			if (active == "onglet_candidatures") {
				showOffer('gestion_candidatures', 'onglet_candidatures')
				console.log("gestion_candidatures")
			}
			if (active == "onglet_CVthèque") {
				showOffer('CVthèque', 'onglet_CVthèque')
				// window.scrollTo(0, 0);
				console.log("CVthèque")
			}
			if (active == "onglet_proposal") {
				showOffer('proposal', 'onglet_proposal')
				console.log("proposal")
			}

			window.scrollTo(0, 0);
		}
	</script>

{% endblock %}
