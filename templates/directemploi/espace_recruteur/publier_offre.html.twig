{% extends 'directemploi/layout.html.twig' %}

{% block title %} Créer votre offre | Direct Emploi {% endblock %}

{% block stylesheets %}
	<style>
		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
			min-height: 100vh;
			padding: 20px;
		}

		.form-container {
			max-width: 900px;
			margin: 0 auto;
			background: white;
			border-radius: 20px;
			box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
			overflow: hidden;
		}

		.form-header {
			background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
			color: white;
			padding: 30px;
			text-align: center;
		}

		.form-header h1 {
			font-size: 2.5rem;
			font-weight: 300;
			margin-bottom: 10px;
		}

		#fil_ariane {
			background: #f8f9fa;
			padding: 15px 30px;
			border-bottom: 1px solid #e9ecef;
		}

		#fil_ariane a {
			color: #6c757d;
			text-decoration: none;
			font-size: 0.9rem;
		}

		#fil_ariane a:hover {
			color: #3498db;
		}

		.form_box {
			padding: 40px;
			border-bottom: 1px solid #e9ecef;
		}

		.form_box:last-of-type {
			border-bottom: none;
		}

		.form_box h2 {
			font-size: 1.5rem;
			color: #2c3e50;
			margin-bottom: 30px;
			padding-bottom: 10px;
			border-bottom: 3px solid #3498db;
			display: inline-block;
		}

		.formulaire {
			width: 100%;
			border-collapse: separate;
			border-spacing: 0;
		}

		.formulaire tr {
			display: grid;
			grid-template-columns: 1fr;
			gap: 8px;
			margin-bottom: 25px;
			align-items: start;
		}

		.formulaire td.label {
			font-weight: 600;
			color: #2c3e50;
			font-size: 0.95rem;
			margin-bottom: 0;
		}

		.formulaire td:not(.label) {
			width: 100%;
		}

		.formulaire input[type="text"],
		.formulaire input[type="email"],
		.formulaire input[type="url"],
		.formulaire input[type="date"],
		.formulaire select,
		.formulaire textarea {
			width: 100%;
			padding: 12px 16px;
			border: 2px solid #e9ecef;
			border-radius: 10px;
			font-size: 1rem;
			transition: all 0.3s ease;
			background: #fff;
			font-family: inherit;
		}

		.formulaire input:focus,
		.formulaire select:focus,
		.formulaire textarea:focus {
			outline: none;
			border-color: #3498db;
			box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
		}

		.formulaire select {
			cursor: pointer;
			appearance: none;
			background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
			background-position: right 12px center;
			background-repeat: no-repeat;
			background-size: 16px;
			padding-right: 40px;
		}

		.formulaire textarea {
			min-height: 120px;
			resize: vertical;
		}

		.ob {
			color: #e74c3c;
			margin-left: 3px;
			font-weight: bold;
		}

		/* Styles spéciaux pour les champs radio et checkbox */
		.handicap-section,
		.anonymat-section {
			background: #f8f9ff;
			border-left: 4px solid #3498db;
			padding: 20px;
			margin: 20px 0;
			border-radius: 0 10px 10px 0;
		}

		.radio-group,
		.checkbox-group {
			display: flex;
			gap: 20px;
			margin-top: 10px;
			align-items: center;
		}

		.radio-group input[type="radio"],
		.checkbox-group input[type="checkbox"] {
			width: 18px;
			height: 18px;
			accent-color: #3498db;
			margin-right: 8px;
		}

		.radio-group label,
		.checkbox-group label {
			font-weight: normal;
			cursor: pointer;
			margin: 0;
		}

		/* Grille pour les champs en ligne */
		.form-row {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: 20px;
			align-items: start;
		}

		.form-row .formulaire tr {
			margin-bottom: 0;
		}

		/* Section de soumission */
		.act {
			padding: 30px 40px;
			background: #f8f9fa;
			text-align: center;
			margin: 0;
		}

		.btn {
			background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
			color: white;
			padding: 15px 40px;
			border: none;
			border-radius: 50px;
			font-size: 1.1rem;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
		}

		.btn:hover {
			transform: translateY(-2px);
			box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
		}

		/* Responsive */
		@media (max-width: 768px) {
			.form-container {
				margin: 10px;
				border-radius: 15px;
			}

			.form-header {
				padding: 20px;
			}

			.form-header h1 {
				font-size: 2rem;
			}

			.form_box {
				padding: 25px;
			}

			.form-row {
				grid-template-columns: 1fr;
				gap: 0;
			}

			.radio-group,
			.checkbox-group {
				flex-direction: column;
				align-items: flex-start;
				gap: 10px;
			}
		}

		/* Styles pour les messages d'erreur Symfony */
		.form-error {
			color: #e74c3c;
			font-size: 0.85rem;
			margin-top: 5px;
			display: block;
		}

		.has-error input,
		.has-error select,
		.has-error textarea {
			border-color: #e74c3c;
		}

		/* Animation d'apparition */
		.form_box {
			opacity: 0;
			transform: translateY(20px);
			animation: slideInUp 0.6s ease forwards;
		}

		.form_box:nth-child(2) { animation-delay: 0.1s; }
		.form_box:nth-child(3) { animation-delay: 0.2s; }
		.form_box:nth-child(4) { animation-delay: 0.3s; }

		@keyframes slideInUp {
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}
	</style>
{% endblock %}

{% block content %}

	<div class="form-container">
		<div class="form-header">
			<h1>Déposer une offre</h1>
		</div>

		<div id="fil_ariane">
			<a href="{{ path('directemploi') }}" title="Accueil">Accueil</a> -
			<a href="{{ path('app_espace_recruteur') }}" title="Recruteur">Recruteur</a>
		</div>

		{{ form_start(DiffusionOffre) }}
		<!-- Section 1: Informations sur le poste -->
		<div class="form_box" id="top_page">
			<h2>1 - Informations sur le poste</h2>
			<table class="formulaire">
				{% if app.user and app.user.userIdentifier == '<EMAIL>' %}
					<tr>
						<td class='label'>{{ form_label(DiffusionOffre.entreprise,"Entreprise") }} <span class="ob">*</span></td>
						<td>
							{{ form_widget(DiffusionOffre.entreprise,{ 'attr': {'placeholder': 'Entreprise'} }) }}
							{{ form_errors(DiffusionOffre.entreprise) }}
						</td>
					</tr>
				{% endif %}

				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.titre_offre,"Titre de l'offre") }} <span class="ob">*</span></td>
					<td>
						{{ form_widget(DiffusionOffre.titre_offre,{ 'attr': {'placeholder': 'Titre de l\'offre'} }) }}
						{{ form_errors(DiffusionOffre.titre_offre) }}
					</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.reference_offre,"Référence") }} <span class="ob">*</span></td>
					<td>
						{{ form_widget(DiffusionOffre.reference_offre,{ 'attr': {'placeholder': 'Référence'} }) }}
						{{ form_errors(DiffusionOffre.reference_offre) }}
					</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.type_contrat_offre,"Type de contrat") }} <span class="ob">*</span></td>
					<td>
						{{ form_widget(DiffusionOffre.type_contrat_offre,{ 'attr': {'placeholder': 'Sélectionnez votre type de contrat'} }) }}
						{{ form_errors(DiffusionOffre.type_contrat_offre) }}
					</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.duree_contrat_offre,"Durée du contrat") }}</td>
					<td>
						{{ form_widget(DiffusionOffre.duree_contrat_offre,{ 'attr': {'placeholder': 'Durée du contrat'} }) }}
						{{ form_errors(DiffusionOffre.duree_contrat_offre) }}
					</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.date_debut_contrat_offre,"Date de début") }}</td>
					<td>
						{{ form_widget(DiffusionOffre.date_debut_contrat_offre,{ 'attr': {'placeholder': 'Date de début'} }) }}
						{{ form_errors(DiffusionOffre.date_debut_contrat_offre) }}
					</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.fourchette_remuneration_offre,"Rémunération") }}</td>
					<td>
						{{ form_widget(DiffusionOffre.fourchette_remuneration_offre,{ 'attr': {'placeholder': 'Sélectionnez la rémunération'} }) }}
						{{ form_errors(DiffusionOffre.fourchette_remuneration_offre) }}
					</td>
				</tr>
			</table>

			<!-- Section spéciale pour handicap et anonymat -->
			<div class="handicap-section">
				<div class='label'><b>Poste accessible aux personnes<br>en situation de handicap</b><span class="ob"> *</span></div>
				<div class="radio-group">
					{{ form_widget(DiffusionOffre.handicap_offre[0]) }} {{ form_label(DiffusionOffre.handicap_offre[0]) }}
					{{ form_widget(DiffusionOffre.handicap_offre[1]) }} {{ form_label(DiffusionOffre.handicap_offre[1]) }}
				</div>
				{{ form_errors(DiffusionOffre.handicap_offre) }}
			</div>

			<div class="anonymat-section">
				<div class='label'><b>Anonymat</b></div>
				<div class="checkbox-group">
					{{ form_widget(DiffusionOffre.anonymat_offre[0]) }} {{ form_label(DiffusionOffre.anonymat_offre[0]) }}
				</div>
				{{ form_errors(DiffusionOffre.anonymat_offre) }}
			</div>

			<table class="formulaire">
				<tr id="rep_mail">
					<td class='label'>{{ form_label(DiffusionOffre.reponse_mail_offre,"Réponse par mail") }}</td>
					<td id="reponse_mail">
						{{ form_widget(DiffusionOffre.reponse_mail_offre,{ 'attr': {'placeholder': 'Votre email'} }) }}
						{{ form_errors(DiffusionOffre.reponse_mail_offre) }}
					</td>
				</tr>

				<tr id="rep_site">
					<td class='label'>{{ form_label(DiffusionOffre.reponse_site_offre,"Réponse sur le site") }}</td>
					<td id="reponse_site">
						{{ form_widget(DiffusionOffre.reponse_site_offre, { 'attr': {'placeholder': 'URL du site'} }) }}
						{{ form_errors(DiffusionOffre.reponse_site_offre) }}
						<div id="res_site" value="{{ DiffusionOffre.reponse_site_offre.vars.value }}"></div>
					</td>
				</tr>
			</table>
		</div>

		<!-- Section 2: Fonction / secteur et localisation géographique -->
		<div class="form_box">
			<h2>2 - Fonction / secteur et localisation géographique</h2>
			<table class="formulaire">
				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.secteur_activite, "Fonction / Domaine du poste à pourvoir")}} <span class="ob"> *</span></td>
					<td>
						{{ form_widget(DiffusionOffre.secteur_activite,{ 'attr': {'placeholder': "Fonction / Domaine du poste à pourvoir"} })}}
						{{ form_errors(DiffusionOffre.secteur_activite) }}
					</td>
				</tr>
{#
				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.region_offre, "Région")}} <span class="ob"> *</span></td>
					<td>
						{{ form_widget(DiffusionOffre.region_offre,{ 'attr': {'placeholder': "Région"} })}}
						{{ form_errors(DiffusionOffre.region_offre) }}
					</td>
				</tr>
				<tr id="dept">
					<td class='label'>{{ form_label(DiffusionOffre.dept_offre, "Département")}} </td>
					<td>
						{{ form_widget(DiffusionOffre.dept_offre,{ 'attr': {'placeholder': "Département"} })}}
						{{ form_errors(DiffusionOffre.dept_offre) }}
					</td>
				</tr>
				#}
				<tr id="villes">
					<td class='label'>{{ form_label(DiffusionOffre.villes_offre, "Ville")}} <span class="ob">*</span></td>
					<td>
						{{ form_widget(DiffusionOffre.villes_offre,{ 'attr': {'placeholder': "Ville"} })}}
						{{ form_errors(DiffusionOffre.villes_offre) }}
					</td>
				</tr>
				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.code_postal,"Code postal") }} <span class="ob">*</span></td>
					<td>
						{{ form_widget(DiffusionOffre.code_postal,{ 'attr': {'placeholder': 'Code postal'} }) }}
						{{ form_errors(DiffusionOffre.code_postal) }}
					</td>
				</tr>
			</table>
		</div>

		<!-- Section 3: Profil du poste et descriptif -->
		<div class="form_box">
			<h2>3 - Profil du poste et descriptif</h2>
			<table class="formulaire">
				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.descriptif_offre, "Descriptif de l'offre")}} <span class="ob"> *</span></td>
					<td>
						{{ form_widget(DiffusionOffre.descriptif_offre,{ 'attr': {'placeholder': "Descriptif de l'offre"} })}}
						{{ form_errors(DiffusionOffre.descriptif_offre) }}
					</td>
				</tr>
				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.type_formation_offre, "Type de formation")}} <span class="ob"> *</span></td>
					<td>
						{{ form_widget(DiffusionOffre.type_formation_offre,{ 'attr': {'placeholder': "Type de formation"} })}}
						{{ form_errors(DiffusionOffre.type_formation_offre) }}
					</td>
				</tr>
				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.exp_requise_offre, "Expérience requise")}} <span class="ob"> *</span></td>
					<td>
						{{ form_widget(DiffusionOffre.exp_requise_offre,{ 'attr': {'placeholder': "Expérience requise"} })}}
						{{ form_errors(DiffusionOffre.exp_requise_offre) }}
					</td>
				</tr>
				<tr>
					<td class='label'>{{ form_label(DiffusionOffre.profil_offre, "Profil")}} </td>
					<td>
						{{ form_widget(DiffusionOffre.profil_offre,{ 'attr': {'placeholder': "Profil"} })}}
						{{ form_errors(DiffusionOffre.profil_offre) }}
					</td>
				</tr>
			</table>
		</div>

		<div class="act">
			<button type="submit" class="btn" id="btn_submit">Valider et continuer</button>
		</div>

		{{ form_end(DiffusionOffre) }}
	</div>

	<script language="javascript">
		window.onload = () => {
			let domaine = document.querySelector("#diffusion_offre_secteur_activite");
			let region = document.querySelector("#diffusion_offre_region_offre");

			if (region) {
				region.addEventListener("change", function () {
					let form = this.closest("form");
					let data = this.name + "=" + this.value;

					fetch(form.action, {
						method: form.getAttribute("method"),
						body: data,
						headers: {
							"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
						}
					}).then(response => response.text()).then(html => {
						let content = document.createElement("html");
						content.innerHTML = html;
						let nouveauSelect = content.querySelector("#diffusion_offre_dept_offre");
						if (nouveauSelect) {
							document.querySelector("#diffusion_offre_dept_offre").replaceWith(nouveauSelect);
						}
					});
				});
			}

			if (domaine) {
				domaine.addEventListener("change", function () {
					let form = this.closest("form");
					let data = this.name + "=" + this.value;

					fetch(form.action, {
						method: form.getAttribute("method"),
						body: data,
						headers: {
							"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
						}
					}).then(response => response.text()).then(html => {
						let content = document.createElement("html");
						content.innerHTML = html;
						let nouveauSelect = content.querySelector("#diffusion_offre_sous_secteur_offre");
						if (nouveauSelect) {
							document.querySelector("#diffusion_offre_sous_secteur_offre").replaceWith(nouveauSelect);
						}
					});
				});
			}

			// Animation au scroll pour les sections
			const sections = document.querySelectorAll('.form_box');
			const observer = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						entry.target.style.opacity = '1';
						entry.target.style.transform = 'translateY(0)';
					}
				});
			}, { threshold: 0.1 });

			sections.forEach(section => {
				observer.observe(section);
			});
		}
	</script>

{% endblock %}