{# <h2>Candidatures intéressantes</h2>

{% if message_candidatures_interessantes == null %}

<div class="listing_candidature_interessant">
	<table>

		<thead>
			<tr>
				<td> Nom Candidat </td>
				<td> Prénom </td>
				<td> Date de postulation </td>
				<td> Nom de l'offre </td>
			</tr>
		</thead>

		<tbody>

			{% set compteur_modal = 0 %}
			{% set compteur_couleur = 0 %}
			{% for id_candidature, infos_candidature in infos_candidatures_interessantes %}
				{% if compteur_couleur % 2 == 0 %}
					<tr class='couleur1'>
				{% else %}
					<tr class='couleur2'>
                {% endif %}
				<td>
					{% set modal = "modal" ~ compteur_modal %}
					<a href="#" value="{{ modal }}" class="js-modal-open" onclick="openModal(event, '{{ modal }}', '{{ infos_candidature.cv_candidature }}')"> {{ infos_candidature.nom_candidat }} </a>
					<aside value="{{ modal }}" class="modal js-modal-close" id="{{ modal }}" aria-hidden="true" role="dialog" aria-modal="false" aria-labelledby="titlemodal" style="display: none;">
						<div class="modal_wrapper">
							<div class="bouton_fermer_modal">
								<a href="#" value="{{ modal }}" class="js-modal-close" onclick="closeModal(event, '{{ modal }}')">fermer la fenetre</a>
							</div>
							<h1 id="titlemodal"> CV du candidat </h1>
							{% if infos_candidature.cv_candidature == null %}
								<p>Désolé mais l'aperçu du CV n'est pas disponible</p>
							{% else %}
								{% if ".doc" in infos_candidature.cv_candidature %}
									<iframe id="{{ "iframe_" ~ modal }}" name="{{ infos_candidature.cv_candidature }}"></iframe>
								{% else %}
									<iframe src={{ infos_candidature.cv_candidature }}></iframe>
								{% endif %}
							{% endif %}
						</div>
					</aside>
				<td> {{ infos_candidature.prenom_candidat }} </td>
				<td> {{ (infos_candidature.date_candidature)|date('Y-m-d') }} </td>
				<td> {{ infos_candidature.nom_offre }} </td>
				
			    {% set compteur_couleur = compteur_couleur + 1 %}
			{% endfor %}
		</tbody>
	</table>
</div>

{% else %}

    <div class="message_candidature">
        {{ message_candidatures_interessantes }}
    </div>

{% endif %} #}
