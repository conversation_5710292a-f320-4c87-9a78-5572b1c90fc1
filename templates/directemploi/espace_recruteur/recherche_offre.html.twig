{% block stylesheets %}

	<style>
		#edit-offres .submit a, .submit input, a.submit, input[type=submit]{
			border:none!important;
			font-size: 1rem;
			font-weight: 500;
		}
		#edit-offres .submit a:hover, .submit input:hover, a.submit:hover, input[type=submit]:hover {
			background-color: #44A2C6AA ;
			background: #44A2C6AA ;
		}
		.pagination {
			width: 100%;
			height: 100%;
			display: flex;
			float: none;
		}

		.pagination:hover {
			background-color: #44A2C6;
		}
		.pagination a {
			color: black;
			float: left;
			padding: 8px 16px;
			text-decoration: none;
		}

		.pagination a.active {
			background-color: #44A2C6;
			color: white;
			border-radius: 5px;
		}

		.pagination a:hover:not(.active) {
			background-color: #ddd;
			border-radius: 5px;
		}

		a {
			color: #4ba8c5;
		}
	</style>

{% endblock %}

<div id="zoneListingOffres">

	{% if tri_offres|length > 0 %}

		{% for key, tri_offre in tri_offres %}
			<div class="de-div offres-list-recruteur">
				<div class="h-flex-between">
					<div class="h-flex align-items-center">
						<a href={{ "candidatOffre/" ~ tri_offre.id_offre }} class="blue">{{ tri_offre.intitule|raw }}</a>
						{% if tri_offre.statut == 1 %}
							<span class="dot  mx-2 green-background"></span>
						{% else %}
							<span class="dot  mx-2 red-background"></span>
						{% endif %}
					</div>

					<div class="form-check" style="margin-left:auto;">
						{% if tri_offres|length > 1 %}
							<input class="form-check-input" id="select_{{ tri_offre.id_offre }}" type="checkbox" style="max-height: 24px;" value="{{ tri_offre.id_offre }}">
						{% endif %}
					</div>

				</div>
				<div class="h-flex-between align-items-center">
					<div class="v-flex">
						<span><span class="medium">Type offre :</span> {{ tri_offre.type_offre }} - <span class="medium">Ref :</span> {{ tri_offre.reference|raw }}</span>
						<span class="gray">Crée le {{ tri_offre.date_creation|date('d/m/Y') }}</span>
						{% if (tri_offre.date_creation != tri_offre.date_soumission and tri_offre.statut == 1) %}
							<span class="gray">Mise à jour le {{ tri_offre.date_soumission|date('d/m/Y') }}</span>
						{% endif %}
						{#  {% if (tri_offre.nb_candids > 0) %}
				&nbsp;
				{% set txt_lien = tri_offre.nb_candids ~ " candidature" %}
				{% if (tri_offre.nb_candids > 1) %}
					{% set txt_lien = txt_lien ~ "s" %}
				{% endif %}
				<a class="blue" href={{ "/espaceRecruteur/showCandidatures/" ~ tri_offre.id_offre }}> {{ txt_lien }} </a>
			{% endif %}#}
					</div>
					<div class="h-flex">
						<a href={{ "/espaceRecruteur/editerUneOffre/" ~ tri_offre.id_offre_commerciale ~ "/" ~ tri_offre.id_offre }} class="gray">Modifier</a> <span>&nbsp;-&nbsp;</span>
						<a href={{ "/espaceRecruteur/dupliquerUneOffre/" ~ tri_offre.id_offre_commerciale ~ "/" ~ tri_offre.id_offre }} class="gray">Dupliquer</a> <span>&nbsp;-&nbsp;</span>
						<form id="renouveler{{ tri_offre.id_offre }}">
							<a href="{{ path('app_renouveller_off', {id_offer: tri_offre.id_offre}) }}" onclick="if(confirm('Êtes vous sur de vouloir renouveller cette offre ?'))" class="gray">Renouveller</a> <span>&nbsp;-&nbsp;</span>
						</form>
						{% if tri_offre.statut == 1 %}
							<form id="suspendre{{ tri_offre.id_offre }}">
								<a href="{{ path('app_suspend_off', {id_offer: tri_offre.id_offre}) }}" onclick="if(confirm('Êtes vous sur de vouloir suspendre cette offre ?'))" class="gray">Suspendre</a> <span>&nbsp;-&nbsp;</span>

							</form>
						{% endif %}
						<form id="delete{{ tri_offre.id_offre }}">
							<a href="{{ path('app_remove_off', {id_offer: tri_offre.id_offre}) }}" class="rouge" onclick="if(confirm('Êtes vous sur de vouloir supprimer cette offre ?'))" class="red">Supprimer</a>
						</form>
					</div>
				</div>
			</div>
		{% endfor %}

		{#  {% if tri_offres|length > 1 %}
		<form id="edit_offres" class="p-3">
			<div class="d-flex justify-content-around w-100">
				<div class="submit submit_large" id="nombre" style="margin-top:5px">
					<input type="submit" class="btn button-inscription" value="Renouveller les offres sélectionnées" onClick="editOffres(event, 'renouveler', 'null');">
				</div>


				<div class="submit submit_large" id="nombre" style="margin-top:5px">
					<input type="submit" class="btn button-inscription" value="Suspendre les offres sélectionnées" onClick="editOffres(event, 'suspendre', 'null');">
				</div>
			</div>
		</form>
	{% endif %}#}

		<div align="center" id="pagination_de">
			{{ knp_pagination_render(tri_offres, 'directemploi/composants/paginationTemplate.html.twig') }}
		</div>

	{% else %}
		<div id="tableListingOffres">
			<p class="text-secondary text-center">Aucune offre trouvée dans la base de données</p>
		</div>
	{% endif %}
</div>

<script type="text/javascript">
	const sendTriForm = function(event, formname, value) {
		formdata = new FormData(formname);
		var datas = {
			"data" : value,
		};

		$.ajax({
			type: "POST",
			url: "espaceRecruteur",
			data: datas,
		}).done(function (data) {
		});
		event.preventDefault();
		$("#liste_offres").load(" #liste_offres");
	};

	const deleteOffer = function(id_offer) {
		let stats_div = document.querySelectorAll('div[name="offres_stat"]');
		if (confirm("Êtes vous sur de vouloir supprimer cette offre ?")) {
			// id_offer = document.getElementById("delete" + id_offer);
			window.location.href = "/espaceRecruteur/removeOffre?id_off=" +/""
			// formdata = new FormData(formname);
			// var datas = {
			// 	"offer_to_delete" : id_offer,
			// };

			// $.ajax({
			// 	type: "POST",
			// 	url: "espaceRecruteur/removeOffre",
			// 	data: datas,
			// }).done(function (data) {
			// });
			// event.preventDefault();
			// $("#liste_offres").load(" #liste_offres");
			// for (var i in stats_div)
			// 	$("#" + stats_div[i].id).load(" #" + stats_div[i].id);
		}
	};

	const editOffres = function(event, operation, unique_id) {

		let id = "";
		let formname = null;
		if (unique_id != 'null') {
			id = unique_id;
			formname = document.getElementById(operation + unique_id)
		} else {
			let count = 0;
			let ids = document.querySelectorAll('input[type="checkbox"]')
			formname = document.getElementById("edit_offres");
			for (var i in ids) {
				if (ids[i].checked) {
					id += ids[i].value;
					id += "_";
				}
			}
			if (id != "")
				id = id.slice(0, -1);
		}
		if (id.length > 0) {
			formdata = new FormData(formname);
			var datas = {
				"ids_to_manage" : id,
				"operation" : operation,
			};

			$.ajax({
				type: "POST",
				url: "espaceRecruteur",
				data: datas,
			}).done(function (data) {
			});
			$("#liste_offres").load(" #liste_offres");
		}
		event.preventDefault();
	}
</script>
