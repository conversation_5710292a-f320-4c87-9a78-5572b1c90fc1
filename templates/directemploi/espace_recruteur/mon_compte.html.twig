<section>
	<div class="de-div call-to-action">
		<div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
			<div style="flex: 1 1 100%; margin-bottom: 1rem;">
				<h4 style="margin: 0;">
					<strong>Bienvenue dans votre Compte Recruteur !</strong>
				</h4>
			</div>
			<p style="margin: 0; flex: 1;">
				Vous avez actuellement <span class="blue">{{ user_stats.getNbOffresEmploi() }} offres d'emploi</span>, <span class="blue">{{ user_stats.getNbOffresStage() }} offres de stage</span> et <span class="blue">{{ user_stats.getNbOffresAlternance() }} offres en alternance</span> publiées sur le site.
			</p>
			<a href="{{ path('app_offresproposees') }}" class="btn button-inscription" style="margin-left: auto;">Souscrire à un produit</a>
		</div>
	</div>
</section>


<section>
	<h4 style="color: #f6ca03"><strong>Vos produits</strong></h4>
	<hr>
	{% if (SouscriptionsCommercialesValides)|length > 0 %}
		{% for key, SCV in SouscriptionsCommercialesValides %}
			<div class="de-div product">
				<div class="h-flex-between align-items-center">


					{% set nom_ameliore = SCV['nom_offre'] %}
					{% if SCV['for_offre'] == 1 %}
						{% set nom_ameliore = (SCV['nom_offre'] ~ (SCV['plateforme_diffusion'] == '1' ? ' + Plateforme de diffusion' : '')) %}
					{% endif %}

					<div class="v-flex">
						<span>{{ nom_ameliore }}</span>
						<span>Arrive à échéance le {{ SCV['date_fin_souscription']|date('d/m/Y') }}</span>
					</div>


					{% if (SCV['credit'] > 0) %}
						{% if (SCV['etat_paiement'] == '2') %}
							<span class="situation">Crédits restants : {{ SCV['credit'] > 50 ? 'illimités' : SCV['credit'] }}</span>

						{% elseif (SCV['etat_paiement'] == '1') %}
							<span class="situation">En attente de validation</span>
						{% elseif (SCV['etat_paiement'] == '0') %}
							<span class="situation">Bon de commande non imprimé</span>
						{% endif %}
						{% if SCV['com_liste_type_offre_commerciale_id_type_offre_commerciale'] != 4 %}
							{% if (SCV['etat_paiement'] == '2') %}
								{% if (user.getTypeDiffusion() == 1) %}
									<div class="text-center"><a href="{{ path('app_diffusion_offre_unique', {id_off_co: SCV['com_offre_commerciale_id_offre_commerciale']}) }}" title="Diffuser une offre" class="btn button-inscription">Diffuser une offre</a></div>
								{% else %}
									<span class="situation">Vos offres sont diffusés automatiquement</span>
								{% endif %}
								{# {% else %}
		<div class="text-center"><a href="{{ "/espaceRecruteur/imprimerBonCommande/" ~ SCV['getIdLotSouscription'] }} " title="Imprimer le bon de commande" class="btn button-inscription">Imprimer le bon de commande</a></div>#}
							{% endif %}
						{% endif %}
					{% else %}
						<span class="situation">Crédit épuisé</span>

					{% endif %}
				</div>
			</div>
		{% endfor %}
	{% else %}
		<div class="text-center"><p class="text-secondary m-0">Aucune offre commerciale souscrite</p></div><br/>
	{% endif %}

</section>
<section>
	<div class="h-flex-between align-items-center">
		<h4 style="color: #f6ca03;border: 0"><strong>Information sur mon entreprise</strong></h4>

		{# <a href="/espaceRecruteur/gestionCompte" title="Modifier mon entreprise" class="blue">Modifier mon entreprise</a> #}
		<a href="{{ path('app_edit_company', {'id': id_entreprise}) }}" title="Modifier mon entreprise" class="blue">Modifier mon entreprise</a>
	</div>
	<hr>

	<div class="item"><strong>Société : </strong> {{ user_entreprise['raison_sociale'] }} </div>
	{# <div class="item"><strong>Responsable du compte : </strong> {{ user_civilite ~ ' ' ~ ((user.getNom())|lower|capitalize) ~ ' ' ~ ((user.getPrenom())|lower|capitalize) }}</div> #}

	<div class="item">
		<strong>Description de l'entreprise : </strong><br/>

		{% if not user_entreprise %}
			L'entreprise n'existe plus
		{% else %}
			{{ user_entreprise['presentation_societe']|nl2br }}
		{% endif %}
	</div>
	{% if user_entreprise['site_web'] %}
		<div class="item">
			<strong>Site internet : </strong>
			<a href="{{ user_entreprise['site_web'] }}"/> {{ user_entreprise['site_web'] }} <a/>
		</div>
	{% endif %}
	<br/>
	<div class="item">
		<strong>Adresse : </strong>
		{{ user_entreprise['adresse1'] }}
		{% if user_entreprise['adresse2'] %}
			{{ user_entreprise['adresse2'] }}
		{% endif %}
		{{ user_entreprise['code_postal'] }}
		{{ user_entreprise['nom_ville'] }}
	</div>
	<br />
	{# <h4 style="color: #f6ca03"><strong>Coordonnées</strong></h4>

	<a href="{{ path('app_edit_user', {'id': id}) }}" title="Modifier mes coordonnées" class="blue">Modifier mes coordonnées</a> #}
	<div class="h-flex-between align-items-center">
		<h4 style="color: #f6ca03;border: 0"><strong>Coordonnées</strong></h4>

		<a href="{{ path('app_edit_user', {'id': id}) }}" title="Modifier mes coordonnées" class="blue">Modifier mes coordonnées</a>
	</div>
	<hr>
	<div class="item"><strong>Responsable du compte : </strong> {{ user_civilite ~ ' ' ~ ((user.getNom())|lower|capitalize) ~ ' ' ~ ((user.getPrenom())|lower|capitalize) }}</div>


	<div class="item">
		<strong>Email : </strong>
		<a href="mailto:{{ user.email }}">{{ user.email }}</a><br />
	</div>
	<div class="item">
		<strong>Tél. : </strong>
		{{ user.telephone }}
	</div>
	{% if user.fax %}
		<div class="item">
			<strong>Fax : </strong>
			{{ user.fax }}
		</div>
	{% endif %}

	{% if user.mobile %}
		<div class="item">
			<strong>Mobile : </strong>
			{{ user.mobile }}
		</div>
	{% endif %}

	
</section>