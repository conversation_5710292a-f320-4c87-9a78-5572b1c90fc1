<section>
	<div class="de-div call-to-action">
		<div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
			<div style="flex: 1 1 100%; margin-bottom: 1rem;">
				<h4 style="margin: 0;">
					<strong>Bienvenue dans la rubrique gestion des candidatures !</strong>
				</h4>
			</div>
			<p style="display: flex; align-items: center; width: 100%; margin: 0;">
				V<PERSON> pouvez gérer les candidatures reçues pour vos offres d'emploi&nbsp;<strong style="color: #44A2C6"> en cliquant </strong>&nbsp;sur le lien suivant&nbsp;:
				<a href="{{ path('app_espace_recruteur_candidatures') }}" class="btn button-inscription" style="margin-left: auto;">Gestion des candidatures</a>
			</p>
		</div>
	</div>
</section>


<script type="text/javascript">

	const hideElement = function(Elem2Hide) {
		Elem2Hide.hidden = true;
	}

	const showElement = function(Elem2Show) {
		Elem2Show.hidden = false;
	}

	window.addEventListener("load", function() {
		hideElement(document.getElementById("onglet_c1"));
		hideElement(document.getElementById("onglet_c2"));
	});

	const switch_div = function(show_onglet) {
		hideElement(document.getElementById("onglet_c0"));
		hideElement(document.getElementById("onglet_c1"));
		hideElement(document.getElementById("onglet_c2"));

		$("#" + show_onglet.id).load(" #" + show_onglet.id);
		showElement(show_onglet);
	}
</script>
