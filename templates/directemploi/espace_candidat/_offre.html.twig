{#% extends 'directemploi/espace_candidat/index.html.twig' %#}

{% block stylesheets %}
	<link rel="stylesheet" href="{{ asset('assets/css/offres_candid.min.css') }}">
{% endblock %}

<h2>Mes offres</h2>
<div id="mes_offres">

	{% for key, user_offer in user_offers %}
		{% set entr_offre_id = user_offer.getOffOffreIdOffre() %}
		{% if entr_offre_id is defined and entr_offre_id != null %}
			<table id="_offre-section">
				<tr>
					<td class="title">
						Entreprise : {{offre_info_visible[entr_offre_id].title}}<br>
					</td>
				</tr>
				<tr>
					<td class="left-infos">
						intitule : {{offre_info_visible[entr_offre_id].intitule}}
						<br>ville : {{offre_info_visible[entr_offre_id].ville}}
						{% set societe = offre_info_visible[entr_offre_id].societe %}
						{% if societe is defined and societe != null %}
							<br>societe : {{societe}}
						{% endif %}
						{% set contrat_type = offre_info_visible[entr_offre_id].contrat %}
						{% if contrat_type is defined and contrat_type != null %}
							<br>Contrat : {{contrat_type}}
						{% endif %}
					</td>
					<td class="right-infos">
						<span style="font-weight:bold;">Date de candidature</span><br>
						{{ user_offer.getDateCandidature|date('d/m/Y') }}
					</td>
				</tr>
				<tr>
					<td>
						<div id="my_line-{{entr_offre_id}}" class="sep-line" style="display:none;"></div>
					</td>
				</tr>
				<tr>
					<td class="left-infos">
						<div id="left-{{entr_offre_id}}" style="display:none;">
							<div class="my-h3">Informations renseignées :</div><br>
								{% set candid_nom, candid_prenom, candid_cp, candid_ville, candid_mail, candid_numero, candid_date_soumission =
								candid_info_hide[entr_offre_id].nom,
								candid_info_hide[entr_offre_id].prenom,
								candid_info_hide[entr_offre_id].cp,
								candid_info_hide[entr_offre_id].ville,
								candid_info_hide[entr_offre_id].mail,
								candid_info_hide[entr_offre_id].numero,
								candid_info_hide[entr_offre_id].date_soumission
								%}
								<span class="candid-infos-row">Nom : <span class="candid-infos-left">{{candid_nom | upperAll}}</span></span><br>
								<span class="candid-infos-row">Prénom : <span class="candid-infos-left">{{candid_prenom | lowerAllUpperFirst}}</span></span><br>
								<span class="candid-infos-row">Ville : <span class="candid-infos-left">{{candid_ville | lowerAllUpperFirst}}</span></span><br>
								<span class="candid-infos-row">Code postal : <span class="candid-infos-left">{{candid_cp}}</span></span><br><br>
	
						</div>
					</td>
					<td class="right-infos">
						<div id="right-{{entr_offre_id}}" style="display:none">
							<div class="my-h3">Contact :</div><br>
								<span class="candid-infos-row">Adresse mail : <span class="candid-infos-right">{{candid_mail}}</span></span><br>
								<span class="candid-infos-row">Téléphone : <span class="candid-infos-right">{{candid_numero}}</span></span><br>
								<span class="candid-infos-row">Date : <span class="candid-infos-right">{{candid_date_soumission|date('d/m/Y')}}</span></span><br>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<div id="mission-profile-{{entr_offre_id}}" class="mission-profile" style="text-align:justify;display:none;">
							{% set entreprise_mission, entreprise_profil =  
							entreprise_info_hide[entr_offre_id].entreprise_mission, entreprise_info_hide[entr_offre_id].entreprise_profile %}
							<div class="my-h3">Profile :</div>
							{{entreprise_mission}}

							<br><br>

							<div class="my-h3">Mission :</div>
							{{entreprise_profil}}

						</div>
					</td>
				</tr>
				<tr>
					<td id="my_btn_td">
						<button id="show-more-btn-hidden" class="more-info-btn" onclick="hideUnhideDiv('{{entr_offre_id}}')">voir plus</button>
					</td>
				</tr>
			</table>
		{% endif %}
	{% endfor %}
</div>
