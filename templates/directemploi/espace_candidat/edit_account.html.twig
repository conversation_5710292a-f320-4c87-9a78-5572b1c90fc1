{% extends 'directemploi/layout.html.twig' %}
{% block title %}Modification compte
{% endblock %}

{% block content %}
	<h1>Modification compte candidat</h1>

	<div class="obli">
        <a href="/" title="Accueil">Accueil</a> -
        <a href="/espaceCandidat" title="Candidat">Candidat</a>
	</div>

	<div class="obli">
		<p>Les champs marqués d'une astérisque  <span class="ob">*</span> sont obligatoires</p>
	</div>

	{{ form_start(EditCompte) }}
    {{ form_row(EditCompte._token) }}

	<div class="form_box">
		<h3>Votre identifiant et mot de passe</h3>
		<table class="formulaire">
			<tr>
				<td class="label">{{ form_label(EditCompte.login, "Login")}} <span class="ob">*</span></td>
			</tr>
			<tr>
				<td>{{ form_widget(EditCompte.login,{ 'attr': {'placeholder': 'Login', 'value': user.getLogin()} })}}</td>
			</tr>
			<tr>
				<td class='label'>{{ form_label(EditCompte.email,"Email")}} <span class="ob">*</span></td>
			</tr>
			<tr>
				<td>{{ form_widget(EditCompte.email,{ 'attr': {'placeholder': 'Email', 'value': user.getEmail()} })}}</td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(EditCompte.passCrypt.first,"Nouveau mot de passe")}}
						
				</td>
				<td class='label'>{{ form_label(EditCompte.passCrypt.second,"Confirmation nouveau mot de passe")}}
						
				</td>
			</tr>
			<tr>
				<td>{{ form_widget(EditCompte.passCrypt.first,{ 'attr': {'placeholder': 'Nouveau mot de passe'} })}}</td>
				<td>{{ form_widget(EditCompte.passCrypt.second,{ 'attr': {'placeholder': 'Nouveau mot de passe'} })}}</td>
			</tr>

		</table>
	</div>

	<div class="form_box">
		<h3>Votre état civil</h3>
		<table class="formulaire">
            <tr>
                <td class="label">{{ form_label(EditCompte.user_civilite_id_user_civilite, "Civilite")}} <span class="ob">*</span></td>
            </tr>
			<tr>
                <td>{{ form_widget(EditCompte.user_civilite_id_user_civilite) }}</td>
            </tr>
            <tr>
                <td class="label">{{ form_label(EditCompte.nom, "Nom")}} <span class="ob">*</span></td>
                <td class='label etatcivil'>{{ form_label(EditCompte.prenom,"Prénom")}} <span class="ob">*</span></td>
            </tr>
			<tr>
                <td>{{ form_widget(EditCompte.nom,{ 'attr': {'placeholder': 'Nom', 'value': user.nom()} })}}</td>
                <td>{{ form_widget(EditCompte.prenom,{ 'attr': {'placeholder': 'Prénom', 'value': user.prenom()} })}}</td>
            </tr>
            <tr>
                <td class="label">{{ form_label(EditCompte.adresse1, "Adresse")}} <span class="ob">*</span></td>
				<td class='label'>{{ form_label(EditCompte.complement_adresse, "Complement d'adresse") }}</td>
            </tr>
			<tr>
                <td>{{ form_widget(EditCompte.adresse1,{ 'attr': {'placeholder': 'Adresse', 'value': user_adresse.adresse1()} })}}</td>
				<td>{{ form_widget(EditCompte.complement_adresse,{ 'attr': {'placeholder': 'Complément d\'adresse', 'value': user_adresse.adresse2()} })}}</td>
            </tr>
            <tr>
                <td class='label'>{{ form_label(EditCompte.code_postal,"Code Postal")}} <span class="ob">*</span></td>
                <td class='label'>{{ form_label(EditCompte.ville, "Ville")}} <span class="ob">*</span></td>
            </tr>
			<tr>
                <td>{{ form_widget(EditCompte.code_postal,{ 'attr': {'placeholder': 'Code postal', 'value': user_adresse.codePostal()} })}}</td>
			    <td>{{ form_widget(EditCompte.ville,{ 'attr': {'placeholder': 'Ville', 'value': user_adresse.nomVille()} })}}</td>
            </tr>
            <tr>
                <td class='label'>{{ form_label(EditCompte.pays,"Pays")}} <span class="ob">*</span></td>
            </tr>

			<tr>
                <td>{{ form_widget(EditCompte.pays,{ 'separator':" ---------- " })}}</td>
            </tr>
            <tr>
                <td class='label'>{{ form_label(EditCompte.telephone,"Téléphone portable")}} <span class="ob">*</span></td>
            </tr>
			<tr>
                <td>{{ form_widget(EditCompte.telephone,{ 'attr': {'placeholder': 'Téléphone Portable', 'value': user.telephone()} })}}</td>
            </tr>
		</table>
</div>


<div class="form_box" id="form">
	<h3>Votre domaine d'activité et votre formation</h3>
	<table class="formulaire">
		<tr>
			<td class='label'>{{ form_label(EditCompte.domaine_activite, "Domaine d'activité de l'entreprise")}} <span class="ob">*</span></td>
				<td class='label'>{{ form_label(EditCompte.metier_metier, "Métier")}} <span class="ob">*</span></td>
			</tr>
			<tr>
				<td>{{ form_widget(EditCompte.domaine_activite, {'attr': {'value': situation.offListeSecteurActiviteIdSecteurActivite()} })}}</td>
				<td>{{ form_widget(EditCompte.metier_metier)}}</td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(EditCompte.formation_level, "Niveau de formation")}} <span class="ob">*</span></td>
				<td class='label'>{{ form_label(EditCompte.ec_ecole, "Etablissement / Spécialité")}} <span class="ob">*</span></td>
			</tr>
			<tr>
				<td>{{ form_widget(EditCompte.formation_level, {'attr': {'value': situation.offListeTypeFormationIdTypeFormation()} })}}</td>
				<td>{{ form_widget(EditCompte.ec_ecole)}}</td>
			</tr>

            <tr>
				<td class='label'>{{ form_label(EditCompte.nom_diplome, "Nom du diplome")}}
						
				</td>
				<td class='label'>{{ form_label(EditCompte.annee_obtention, "Année d'obtention")}}
						
				</td>
			</tr>

            <tr>
				<td>{{ form_widget(EditCompte.nom_diplome, {'attr': {'placeholder': "Nom du diplome", 'value': (situation.nomDiplome() == '0' ? '' : situation.nomDiplome())}})}}</td>
				<td>{{ form_widget(EditCompte.annee_obtention, {'attr': {'placeholder': "Année d'obtention", 'value': (situation.anneeObtentionDiplome() == '0' ? '' : situation.anneeObtentionDiplome())}})}}</td>
			</tr>

		</table>
	</div>
    
	<div class="form_box">
		<h3>Votre situation actuelle</h3>
		<table
			class="formulaire">
            <tr>
				<td class="label">{{ form_label(EditCompte.poste_actuel, "Poste actuel (ou dernier poste) ")}}</td>
				<td class="label">{{ form_label(EditCompte.domaine_activite_situation, "Secteur d'activite du poste")}}</td>
			</tr>
            
			<tr>
				<td>{{ form_widget(EditCompte.poste_actuel,{ 'attr': {'placeholder': 'Intitulé du poste', 'value': (situation.posteActuel() == '0' ? '' : situation.posteActuel())} })}}</td>
				<td>{{ form_widget(EditCompte.domaine_activite_situation,{ 'attr': {'placeholder': "Sélectionnez votre secteur d'activité" }})}}</td>

			</tr>

			<tr>
				<td class='label'>{{ form_label(EditCompte.remuneration,"Salaire du poste")}}</td>
				<td class='label'>{{ form_label(EditCompte.activite,"Activité")}}</td>
			</tr>
			<tr>
				<td>{{ form_widget(EditCompte.remuneration)}}</td>
				<td>{{ form_widget(EditCompte.activite)}}</td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(EditCompte.experience,"Nombre d'années d'expérience")}}</td>
				<td class='label'>{{ form_label(EditCompte.disponibilite,"Disponibilité")}}</td>
			</tr>
			<tr>
				<td>{{ form_widget(EditCompte.experience,{ 'attr': {'placeholder': 'Sélectionnez votre expérience'} })}}</td>
				<td>{{ form_widget(EditCompte.disponibilite,{ 'attr': {'placeholder': 'Sélectionnez votre disponibilité'} })}}</td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(EditCompte.mobilite,"Mobilité")}}</td>
				<td></td>
			</tr>
			<tr>
				<td>{{ form_widget(EditCompte.mobilite,{ 'attr': {'placeholder': 'Sélectionnez votre mobilité'} })}}</td>
				<td></td>
			</tr>
		</table>
	</div>

	<div class="form_box">
		<h3>Vos compétences</h3>
		<table class="formulaire">
			<tr>
				<td class='label'>{{ form_label(EditCompte.competence,"Liste des compétences")}}</td>
				<td></td>
			</tr>
			<tr>
				<td colspan='2'>{{ form_widget(EditCompte.competence,{ 'attr': {'placeholder': 'Word, powerpoint, excel', 'style' : "width:882px", 'value': competences ? competences.getListeCompetences() : ''} })}}</td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(EditCompte.langue1,"Langue étrangère 1")}}</td>
				<td class='label'>{{ form_label(EditCompte.level1,"Niveau")}}</td>
			</tr>

			<tr>
				<td>{{ form_widget(EditCompte.langue1,{ 'attr': {'placeholder': 'Sélectionnez la langue'} })}}</td>
				<td>{{ form_widget(EditCompte.level1,{ 'attr': {'placeholder': 'Sélectionnez votre niveau'} })}}</td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(EditCompte.langue2,"Langue étrangère 2")}}</td>
				<td class='label'>{{ form_label(EditCompte.level2,"Niveau")}}</td>
			</tr>

			<tr>
				<td>{{ form_widget(EditCompte.langue2,{ 'attr': {'placeholder': 'Sélectionnez la langue'} })}}</td>
				<td>{{ form_widget(EditCompte.level2,{ 'attr': {'placeholder': 'Sélectionnez votre niveau'} })}}</td>
			</tr>
            
			<tr>
				<td>{{ form_label(EditCompte.permis,"Permis B")}}<br><br>{{ form_widget(EditCompte.permis[0])}}&nbsp;{{ form_label(EditCompte.permis[0])}}&ensp;
					{{ form_widget(EditCompte.permis[1])}}&nbsp;{{ form_label(EditCompte.permis[1])}}</td>
				<td></td>
			</tr>
		</table>
	</div>

    {% if user_veille|length %}
        <div class="form_box">
            <h3>Votre recherche d'emploi</h3>
            <table class="formulaire">
                <tr>
                    <td class='label'>{{ form_label(EditCompte.poste_souhaite,'Type de poste recherché')}}</td>
                    <td class='label'>{{ form_label(EditCompte.remuneration_souhaitee,"Salaire souhaité")}}</td>
                </tr>

                <tr>
                    <td>{{ form_widget(EditCompte.poste_souhaite,{ 'attr': {'placeholder': 'Type de poste', 'value': user_veille|length ? user_veille.getTypeContratSerialized : ''} })}}</td>
                    <td>{{ form_widget(EditCompte.remuneration_souhaitee,{ 'attr': {'placeholder': 'Sélectionnez votre salaire souhaité'} })}}</td>
                </tr>

                <tr>
                    <td class='label'>{{ form_label(EditCompte.contrat_souhaite,"Type de contrat souhaité")}}</td>
                </tr>
                <tr>
                    <td>{{ form_widget(EditCompte.contrat_souhaite,{ 'attr': {'placeholder': 'Sélectionnez un type de contrat'} })}}</td>
                </tr>
            </table>
        </div>
    {% endif %}

	<div class="form_box">
		<h3>Paramètres de confidentialité</h3>
		<table class="formulaire">
			<tr>
				<td>Rendre mon profil visible
					<i>(votre profil sera consultable par les recruteur)</i>
				</td>
				<td>{{ form_widget(EditCompte.profil_visible[0])}}&nbsp;{{ form_label(EditCompte.profil_visible[0])}}&emsp;
					{{ form_widget(EditCompte.profil_visible[1])}}&nbsp;{{ form_label(EditCompte.profil_visible[1])}}</td>
			</tr>

            {% if user_cv|length %}
                <tr>
                    <td>Rendre mon profil anonyme
                        <i>(vos informations civiles seront masquées, veillez à utiliser un CV)</i>
                    </td>
                    <td>{{ form_widget(EditCompte.profil_anonyme[0])}}&nbsp;{{ form_label(EditCompte.profil_anonyme[0])}}&emsp;
                        {{ form_widget(EditCompte.profil_anonyme[1])}}&nbsp;{{ form_label(EditCompte.profil_anonyme[1])}}</td>
                </tr>

                <tr>
                    <td>Rendre mon CV visible
                        <i>(les recruteurs pourront télécharger votre CV, veillez à utiliser un CV)</i>
                    </td>
                    <td>{{ form_widget(EditCompte.cv_visible[0])}}&nbsp;{{ form_label(EditCompte.cv_visible[0])}}&emsp;
                        {{ form_widget(EditCompte.cv_visible[1])}}&nbsp;{{ form_label(EditCompte.cv_visible[1])}}</td>
                </tr>
            {% endif %}

		</table>
	</div>

    <div align="center">
        <button type="submit" class="button_insc_cand" id="btn_submit">Enregistrer</button>
    </div>

    {{ form_end(EditCompte, {'render_rest': false}) }}

{% block javascripts %}

<script>

    var domaine = document.getElementById("edit_compte_candidat_domaine_activite");
    var formation = document.querySelector("#edit_compte_candidat_formation_level");

    function preloadSelectiveFields(data, divname) {
		data = data.split("_");
		for (let i in document.querySelector("#" + divname).options) {
			if (data.includes(document.querySelector("#" + divname).options[i].value))
				document.querySelector("#" + divname).options[i].selected = true;
		}
	}

    sendform = function(elem2send, impacted, impactedData) {
        elem2send = document.getElementById(elem2send);
        let form = elem2send.closest("form");
        let data = elem2send.name + "=" + elem2send.value;

        fetch(form.action, {
            method: form.getAttribute("method"),
            body: data,
            headers: {
                "Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
            }
        }).then(response => response.text()).then(html => {
            let content = document.createElement("html");
			content.innerHTML = html;
			let nouveauSelect = content.querySelector("#" + impacted);
			document.querySelector("#" + impacted).replaceWith(nouveauSelect);
            preloadSelectiveFields(impactedData, impacted);
        })
    }

    formation.addEventListener("change", function () {
        sendform("edit_compte_candidat_formation_level", "edit_compte_candidat_ec_ecole");
    });

    domaine.addEventListener("change", function () {
        sendform("edit_compte_candidat_domaine_activite", "edit_compte_candidat_metier_metier");
    });

    window.onload = () => {
        preloadSelectiveFields('{{user_adresse.geoListePaysIdPays()}}', "edit_compte_candidat_pays")
        preloadSelectiveFields('{{user.userCiviliteIdUserCivilite()}}', "edit_compte_candidat_user_civilite_id_user_civilite")

        {% if situation|length %}
            preloadSelectiveFields('{{situation.offListeSecteurActiviteIdSecteurActivite()}}', "edit_compte_candidat_domaine_activite")
            preloadSelectiveFields('{{situation.offListeTypeFormationIdTypeFormation()}}', "edit_compte_candidat_formation_level")
            preloadSelectiveFields('{{situation.offListeSecteurActiviteIdSecteurActivitePoste()}}', "edit_compte_candidat_domaine_activite_situation")
            preloadSelectiveFields('{{situation.idOffFourchetteRemuneration()}}', "edit_compte_candidat_remuneration")
            preloadSelectiveFields('{{situation.userSituationActiviteIdSituationActivite()}}', "edit_compte_candidat_activite")
            preloadSelectiveFields('{{situation.userSituationExperienceIdSituationExperience()}}', "edit_compte_candidat_experience")
            preloadSelectiveFields('{{situation.userDisponibiliteIdDisponibilite()}}', "edit_compte_candidat_disponibilite")
            preloadSelectiveFields('{{situation.userSituationMobiliteIdSituationMobilite()}}', "edit_compte_candidat_mobilite")
        {% endif %}

        {% if competences|length %}
            preloadSelectiveFields('{{competences.idChoixLangue1()}}', "edit_compte_candidat_langue1")
            preloadSelectiveFields('{{competences.idNiveauLangue1()}}', "edit_compte_candidat_level1")
            preloadSelectiveFields('{{competences.idChoixLangue2()}}', "edit_compte_candidat_langue2")
            preloadSelectiveFields('{{competences.idNiveauLangue2()}}', "edit_compte_candidat_level2")
            '{{ competences.permisB() }}' == 1 ? document.querySelector("#edit_compte_candidat_permis_0").checked = true : 0;
            '{{ competences.permisB() }}' == 0 ? document.querySelector("#edit_compte_candidat_permis_1").checked = true : 0;
        {% endif %}

        {% if user_cv|length %}
            {% if user_veille|length %}
                preloadSelectiveFields('{{user_cv.getOffFourchetteRemunerationIdFourchetteRemunerationSouhaite()}}', "edit_compte_candidat_remuneration_souhaitee")
            {% endif %}
            '{{ competences.isProfilVisible() }}' == 1 ? document.querySelector("#edit_compte_candidat_profil_visible_0").checked = true : 0;
            '{{ competences.isProfilVisible() }}' == 0 ? document.querySelector("#edit_compte_candidat_profil_visible_1").checked = true : 0;
            '{{ user_cv.isVisible() }}' == 1 ? document.querySelector("#edit_compte_candidat_cv_visible_0").checked = true : 0;
            '{{ user_cv.isVisible() }}' == 0 ? document.querySelector("#edit_compte_candidat_cv_visible_1").checked = true : 0;
            '{{ user_cv.isAnonym() }}' == 1 ? document.querySelector("#edit_compte_candidat_profil_anonyme_0").checked = true : 0;
            '{{ user_cv.isAnonym() }}' == 0 ? document.querySelector("#edit_compte_candidat_profil_anonyme_1").checked = true : 0;
        {% endif %}

        {% if user_veille|length %}
            preloadSelectiveFields('{{user_veille.getMotsCles()}}', "edit_compte_candidat_poste_souhaite")
            preloadSelectiveFields('{{user_veille.getRemunerationSerialized()}}', "edit_compte_candidat_remuneration_souhaitee")
            preloadSelectiveFields('{{user_veille.getTypeContratSerialized()}}', "edit_compte_candidat_contrat_souhaite")
        {% endif %}

        sendform("edit_compte_candidat_domaine_activite", "edit_compte_candidat_metier_metier", '{{ situation.idMetier }}');
        sendform("edit_compte_candidat_formation_level", "edit_compte_candidat_ec_ecole", '{{situation.ecEcoleIdEcole()}}');
    };

{# 		
        document.querySelector("#edit_compte_candidat_nom_diplome").value = '{{  }}'
		document.querySelector("#edit_compte_candidat_annee_obtention").value = '{{  }}'

		document.querySelector("#edit_compte_candidat_metier_metier").value = '{{ situation.idMetier() }}'
        document.querySelector("#edit_compte_candidat_ec_ecole").value = '{{ situation.ecEcoleIdEcole() }}' #}

</script>

{% endblock %}{% endblock %}
