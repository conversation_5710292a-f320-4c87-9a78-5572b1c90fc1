<style>

    @media (min-width: 992px) {
    .header.container {
        width: 85%;
        height: 3em;
        justify-content: space-between;
    }
    
    

    .vertical_bar {
        border-right: 1px solid #000;
        height: 25px;
        margin-left: 0.5em;
        align-self: center;
    }

    .navbar_menu {
        margin-left: 3em;
    }

        .navbar_menu a{
           font-size: 16px!important;
            text-decoration: none!important;
        }
    header .logo {
        width: 10%;
        margin-left: 2em;
    }

    div.navbar_menu div.dropdown > a {
        padding-left: 0.5em;
    }
    
}

</style>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

<header>
    <nav>
        <div class="header container">
            <div class="logo">
                <a href="https://www.directemploi.com">
                    <img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="logo direct emploi" style="width: 100%">
                </a>
            </div>
            <div class="navbar_menu">
                <!--
                <div class="dropdown">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        Emploi
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ path('app_theme_stage') }}">Stage</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_theme_alternance') }}">Alternance</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_theme_jeune_diplome') }}">Emploi jeunes diplômés</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_theme_interim') }}">Emploi intérim</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_theme_recrutement') }}">Emploi cabinet de recrutement</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_theme_handicap') }}">Emploi handicap</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_conseil_index') }}">Conseils</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_actualite') }}">Actualités</a></li>
                    </ul>
                </div> -->
                <a id="header-entreprises" href="/entreprises">Entreprises</a>
                <a id="header-actualite" href="{{ path('app_articles_list') }}">Actualités</a>
            </div>
            <div class="navbar_menu">
                <a href="{{ path('app_diffusion_offre_unique') }}">Publier une offre</a>
                <div class="vertical_bar"></div>
                <div class="dropdown">
                    {% if app.user and "ROLE_CANDID" in app.user.getRoles() %}
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa-solid fa-user" style="margin-right: 0.5em"></i> {{ app.user.prenom }} {{ app.user.nom }}</a>
                    {% elseif not app.user %}
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa-solid fa-user" style="margin-right: 0.5em"></i> Candidat</a>
                    {% endif %}
                    {# {% if app.user and (app.user and "ROLE_CVTBETA" in app.user.getRoles()) %}
                        <a href="/cvtheque/index">Cvthèque BETA test</a>
                    {% else #}
                    <ul class="dropdown-menu">
                        <div class="login">
                            {% if not app.user %}
                                <li style="padding-top: 15px; padding-bottom: 0;">
                                    <a href="{{ path('app_login') }}">Se connecter</a>
                                </li>
                                <hr>
                                <li style="padding-top: 0; padding-bottom: 15px;">
                                    <a href="{{ path('app_register_candidat_e1') }}">S'inscrire</a>
                                </li>
                            {% endif %}
                            {% if app.user and "ROLE_CANDID" in app.user.getRoles() %}
                                {% if app.user and "ROLE_ADMIN" in app.user.getRoles() %}
                                    <li style="padding-top: 20px; padding-bottom: 0;">
                                        <a href="{{ path('app_admin_accueil_theme') }}">Admin</a>
                                    </li> <hr>
                                {% endif %}
                                <li style="padding-top: 15px; padding-bottom: 0;">
                                    <a href="/espaceCandidat">Mon espace</a>
                                </li>
                                <hr>
                                <li style="padding-top: 0; padding-bottom: 15px;">
                                    <a href="/logout">Déconnexion</a>
                                </li>
                            {% endif %}
                        </div>
                    </ul>
                </div>
                <div class="dropdown" id="recrut">
                    {% if app.user and "ROLE_RECRUT" in app.user.getRoles() %}
                        {% if companyName is defined %}
                            <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa-solid fa-user-tie" style="margin-right: 0.5em;"></i> {{ companyName }}</a>
                        {% else %}
                            <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa-solid fa-user-tie" style="margin-right: 0.5em;"></i> {{ app.user.prenom }} {{ app.user.nom }}</a>
                        {% endif %}
                        {# <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa-solid fa-user-tie" style="margin-right: 0.5em;"></i>
                            {{ app.user.prenom }} {{ app.user.nom }} </a>          #}
                    {% elseif not app.user %}
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa-solid fa-user-tie" style="margin-right: 0.5em;"></i>
                            Recruteur</a>
                    {% endif %}
                    <ul class="dropdown-menu">
                        <div class="login">
                            {% if not app.user %}
                                <li style="padding-top: 20px; padding-bottom: 0;">
                                    <a href="{{ path('app_login') }}">Se connecter</a>
                                </li>
                                <hr>
                                <li style="padding-top: 0; padding-bottom: 0;">
                                    <a href="{{ path('app_inscriptionRecruteurvalide') }}">S'inscrire</a>
                                </li>
                            {% endif %}
                            {% if app.user and "ROLE_ADMIN" in app.user.getRoles() %}
                            <li style="padding-top: 20px; padding-bottom: 0;">
                                <a href="{{ path('app_admin_index') }}">Admin</a>
                            </li> <hr>
                            {% endif %}
                            {% if app.user and "ROLE_RECRUT" in app.user.getRoles() %}
                                <li style="padding-top: 20px; padding-bottom: 0;">
                                    <a href="/espaceRecruteur">Mon espace</a>
                                </li>
                                <li>
                                <a href="{{ path('app_espace_recruteur', {show_cvt: 'active'}) }}">Cvthèque</a>
                                </li>
                                <li>
                                <a href="{{ path('app_espace_recruteur_candidatures') }}">Gestion des candidatures</a>
                                </li>
                            {% endif %}
                            <hr>
                            <li>
                                <a href="{{ path('app_offresproposees') }}">Les offres proposées</a>
                            </li>
                            <li>
                                <a href="/lereseau">Le réseau Direct Emploi</a>
                            </li>
                            {% if app.user and "ROLE_RECRUT" in app.user.getRoles() %}
                                <hr>
                                <li style="padding-top: 0; padding-bottom: 20px;">
                                    <a href="/logout">Déconnexion</a>
                                </li>
                            {% endif %}
                        </div>
                    </ul>
                </div>
            </div>
        </div>
        <div class="header container_mobile">
            <div class="logo">
                <a href="https://www.directemploi.com">
                    <img src="{{ asset("/images/direct-emploi/new_logo.png") }}" alt="logo direct emploi" style="width: 100%">
                </a>
            </div>
            <div class="navbar_menu">
                <div class="dropdown">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        Emploi
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ path('app_theme_stage') }}">Stage</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_theme_alternance') }}">Alternance</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_theme_jeune_diplome') }}">Emploi jeunes diplômés</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_theme_interim') }}">Emploi intérim</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_theme_recrutement') }}">Emploi cabinet de recrutement</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_theme_handicap') }}">Emploi handicap</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_conseil_index') }}">Conseils</a></li>
                        <li><a class="dropdown-item" href="{{ path('app_articles_list') }}">Actualités</a></li>
                    </ul>
                </div>
                <a href="/entreprises">Entreprises</a>
                <div class="dropdown">
                    {% if not app.user or (app.user and "ROLE_CANDID" in app.user.getRoles()) %}
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Candidat</a>
                    {% endif %}
                    {% if app.user and (app.user and "ROLE_RECRUT" in app.user.getRoles()) %}
                        <a href="/cvthequeRedirect">Cvthèque</a>
                    {% endif %}
                    <ul class="dropdown-menu">
                        <div class="login">
                            {% if not app.user %}
                                <li style="padding-top: 15px; padding-bottom: 0;">
                                    <a href="{{ path('app_login') }}">Se connecter</a>
                                </li>
                                <hr>
                                <li style="padding-top: 0; padding-bottom: 15px;">
                                    <a href="{{ path('app_register_candidat_e1') }}">S'inscrire</a>
                                </li>
                            {% endif %}
                            {% if app.user and "ROLE_CANDID" in app.user.getRoles() %}
                                <li style="padding-top: 15px; padding-bottom: 0;">
                                    <a href="/espaceCandidat">Mon espace</a>
                                </li>
                                <hr>
                                <li style="padding-top: 0; padding-bottom: 15px;">
                                    <a href="/logout">Déconnexion</a>
                                </li>
                            {% endif %}
                        </div>
                    </ul>
                </div>
                <div class="dropdown" id="recrut">
                    {% if not app.user or (app.user and "ROLE_RECRUT" in app.user.getRoles()) %}
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Recruteur</a>
                    {% endif %}
                    <ul class="dropdown-menu">
                        <div class="login">
                            {% if not app.user %}
                                <li style="padding-top: 20px; padding-bottom: 0;">
                                    <a href="{{ path('app_login') }}">Se connecter</a>
                                </li>
                                <hr>
                                <li style="padding-top: 0; padding-bottom: 0;">
                                    <a href="{{ path('app_inscriptionRecruteurvalide') }}">S'inscrire</a>
                                </li>
                            {% endif %}
                            {% if app.user and "ROLE_RECRUT" in app.user.getRoles() %}
                                <li style="padding-top: 20px; padding-bottom: 0;">
                                    <a href="/espaceRecruteur">Mon espace</a>
                                </li>
                            {% endif %}
                            <hr>
                            <li>
                                <a href="{{ path('app_offresproposees') }}">Les offres proposées</a>
                            </li>
                             <li>
                                <a href="/lereseau">Le réseau Direct Emploi</a>
                            </li>
                            {% if app.user and "ROLE_RECRUT" in app.user.getRoles() %}
                                <hr>
                                <li style="padding-top: 0; padding-bottom: 20px;">
                                    <a href="/logout">Déconnexion</a>
                                </li>
                            {% endif %}
                        </div>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
</header>
