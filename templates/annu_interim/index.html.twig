{% extends 'directemploi/layout.html.twig' %}

{% block title %}Annuaire intérim{% endblock %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/main_article.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/responsive.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/direct_emploi.min.css') }}">
	<link rel="stylesheet" href="{{ asset('assets/css/new_main_DE.min.css') }}">
{% endblock %}

{% block javascripts %}
	<script src="{{ asset('assets/js/new_script.js') }}" defer></script>
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.13.1/jquery-ui.min.js"></script>
    <script src="{{ asset('assets/js/annuInterim.js') }}" defer></script>
{% endblock %}

{% block content %}

<!-- CONTENU -->
<article class="contenu_gen clearfix">
    <div class="site">

        <!-- IMAGE DU HAUT -->
        <div id="DE_container_rech_cadre" style="background: url('/images/direct-emploi/bandeau_interim_v3.jpeg')">
            <div class="box_rech home_stage clearfix" id="DE_box_rech_cadre" style="height:46px">
                <div class="inside">
                    <h2 class="h3_title" id="DE_h3_title_cadre">
                        ESPACE INTERIM
                    </h2>
                </div>
            </div>
        </div>

        <!-- div cadre -->
        <div class='cadre'>
            <table id='table_cadre'>
                <tbody>
                    <tr>
                        <!-- Cadre gauche -->
                        <td style="vertical-align:top;width:48%;">
                            <fieldset style="background-color: #4BA8C5 ;;height:338px;margin-top: 28px;width: 100%;">
                                {{ form_start(form) }}
                                <table width="100%" cellspacing="0" cellpadding="0" border="0" id="moteur_avance" style="text-align:center;font-size:19px">
                                    <tr>
                                        <th style='color:white;font:bold;font-size:20px;'>
                                            Je trouve l'agence d'intérim près de chez moi parmi {{ toutesLesAgences|length }} agences
                                        </th>
                                    </tr>
                                    <tr>
                                        <td class="padB">
                                            {{form_widget(form.region)}}
                                        </td>
                                    <tr>
                                    <tr>
                                        <td class="padB">
                                            {{form_widget(form.departement)}}
                                        </td>
                                    <tr>
                                        <td colspan="2">
                                            <input type="submit" name="commit" value="Consulter les agences">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <img src="{{ asset('images/carte_france_interim.png') }}" style="width: 29%">
                                        </td>
                                    </tr>
                                </table>
                                {{ form_end(form) }}
                            </fieldset>
                            <script>
                                let region = document.querySelector("#annu_interim_agences_region");
                                region.addEventListener("change", function() {
                                    let form = document.querySelector('[name="annu_interim_agences"]');
                                    let data = this.name + " = " + this.value;
                                    
                                    fetch(form.action, {
                                        method: form.getAttribute("method"),
                                        body: data,
                                        headers: {
                                            "Content-Type" : "application/x-www-form-urlencoded; charset:UTF-8" 
                                        }
                                    })
                                    .then((response) => response.text())
                                    .then(html => {
                                        let content = document.createElement("html");
                                        content.innerHTML = html;
                                        
                                        let nouveauSelect = content.querySelector("#annu_interim_agences_departement");
                                        document.querySelector('#annu_interim_agences_departement').replaceWith(nouveauSelect);
                                    }).catch(error => {
                                        console.log(error);
                                    })
                                });
                            </script>
                            <h3 style="margin-top: 41px;" class="h3_title">Les groupes d'intérim se présentent</h3>
                            <div style='text-align:-moz-center'>
                                <div>
                                    <div class="slider-container">
                                        <a class="arrowL">&lt;</a>
                                            <div id="slideshow">
                                            {% for interim in interims %}
                                                {% if interim['logo_carousel'] == interims[0].logo_carousel %}
                                                <div>
                                                    <a target="target" style= "height:520px;"
                                                        href="{{ path("app_entreprises_details",
                                                        {'id': interim['id_entreprise'], 'slug': interim['raison_sociale']|replace({"/": "-", "'": "", "È": "e", "É": "e", "ç": "c", "é": "e", "è": "e", "ê": "e", "ô":  "o", "à": "a", " – ": "-", " - ": "-", " / ": "-", " ": "-", "(": "", ")": ""})|lower}) }}">
                                                        <img style="height:520px;" src="{{asset("images/direct-emploi/annuaire_interim/" ~ interim['logo_carousel']) }}" alt="{{interim['logo_carousel']}}">
                                                    </a>
                                                </div>
                                                {% else %}
                                                <div class="hidden">
                                                    <a target="target" style= "height:520px;"
                                                        href="{{ path("app_entreprises_details",
                                                        {'id': interim['id_entreprise'], 'slug': interim['raison_sociale']|replace({"/": "-", "'": "", "È": "e", "É": "e", "ç": "c", "é": "e", "è": "e", "ê": "e", "ô":  "o", "à": "a", " – ": "-", " - ": "-", " / ": "-", " ": "-", "(": "", ")": ""})|lower}) }}">
                                                        <img style="height:520px;" src="{{ asset("images/direct-emploi/annuaire_interim/" ~ interim['logo_carousel']) }}" alt="{{interim['logo_carousel']}}">
                                                    </a>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                            </div>
                                        <a class="arrowR">&gt;</a>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <!-- Fin cadre gauche -->

                        <td style="vertical-align:top;width:4%;"></td>

                        <!-- Cadre droit -->
                        <td style="vertical-align:top;width:48%;">
                        <div class="box_dossiers_cadre">
                            <!-- DEBUT BOX DOSSIERS -->
                            <h2 style="margin-top:17px">L'interview</h2>
                            <div class="image_dossier1_cadre">
                            {% for articleHaut in article %}
                                {% if articleHaut.zone_affichage == 3 %}
                                    <a href={{ "/article/" ~ articleHaut.id_article ~ "/" ~ strtourl(articleHaut.titre) }}>
                                        <div class="td img">
                                            <img alt="{{ articleHaut.titre }}" style="height:186px" src="{{ asset('uploads/article/' ~ articleHaut.image_petite) }}">
                                        </div>
                                    </a>
                                        <div class="box_text_dossier_cadre">
                                            <a href={{ "/article/" ~ articleHaut.id_article ~ "/" ~ strtourl(articleHaut.titre) }}></a>
                                            <h5 style="margin-top:0px;">
                                                <a href={{ "/article/" ~ articleHaut.id_article ~ "/" ~ strtourl(articleHaut.titre) }}></a>
                                                <a href={{ "/article/" ~ articleHaut.id_article ~ "/" ~ strtourl(articleHaut.titre) }}>
                                                    {{ articleHaut.titre }}
                                                </a>
                                            </h5>
                                            <div class="chapodossier_cadre">
                                                {{ articleHaut.chapo }}
                                            </div>
                                        <p class="suspensions_cadre" style="margin:0% 0%"> ... </p>
                                        </div>
                                    </div>
                                {% endif %}
                            {% endfor %}
                            <!-- FIN BOX DOSSIERS -->
                            <!-- LOGO -->
                            <div class="box_recrut_cadre" style="margin-top: 27px;">
                                <h3 style="margin-top: 10px;">Ils recrutent en interim</h3>
                                <div class="int clearfix clearfix2">
                                    <div class="scrollcarousel">
                                    {% for entreprise in entreprises %}
                                        {% set counter = ( counter | default(1) ) + 1 %}
                                        <a href="#home">
                                        {% set styleVariable = 'border-right:1px solid #C2C2C2;' %}
                                        {% if counter < 10 %}
                                            {% if counter == 5 or counter == 9 %}
                                                {% set styleVariable = "border-right: none;" %}
                                            {% endif %}
                                            <a href="{{ path('app_entreprises_details', {id: entreprise.idEntreprise, slug: entreprise.raisonSociale}) }}" style={{ styleVariable }}>
                                                <img src="{{ asset('/uploads/logos/' ~ entreprise.logo) }}">
                                            </a>
                                            {% if counter == 5 %}
                                                <br>
                                                <div style="width:100%;height:15px;"></div>
                                                <div style="width:100%;border-bottom:1px solid #C2C2C2;"></div>
                                                <br>
                                            {% endif %}
                                        {% endif %}
                                        </a>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <!-- FIN LOGO -->
                            <!-- OFFRE DU MOMENT -->
                            {% if offOffres is defined and offOffres|length > 0 %}
                                <div style='margin-top: 41px;' class="liste_pub_offre_entreprise" id="liste_pub_offre_entreprise_page_cadre">
                                    <h3 class="h3_title">L'entreprise d'interim de la semaine</h3>
                                    <div class="bande_couleur_entreprise_cadre" style="background: linear-gradient(#4BA8C5, #4B93C5)">
                                        <div class="bande_entreprise_cadre">
                                            <!-- Barre de Pub Offre 3 entreprises-->
                                            <div class="case_entreprise_cadre" style="height: 159px;">
                                                <!-- Logo de l'entreprise-->
                                                    <div class="logo_image">
                                                        <img alt="{{offOffres[0]['logo']}}" src="{{ asset('/uploads/logos/' ~ offOffres[0]['logo']) }}">
                                                    </div>
                                                    <div class="contenu clearfix">
                                                    <!-- Nom de l'entreprise-->
                                                            <h4 style="color:#ffffff; border-color:#ffffff;">{{ offOffres[0]['raison_sociale'] }}</h4>
                                                        <div class="offre" style="">
                                                        <!-- Liste des offre -->
                                                        {% for offOffre in offOffres %}
                                                            <li style="color:#ffffff;">
                                                                <a style="color:#ffffff" href={{ "/candidatOffre/" ~ offOffre[0].idOffre }} title="{{ offOffre[0].intitule }}">
                                                                    {{ offOffre[0].intitule }}
                                                                </a>
                                                            </li>
                                                        {% endfor %}
                                                            <div class="voir_toutes_offres">
                                                                <a style="color:#ffffff" href="/candidatOffre/search" title="Voir toutes les offres">Voir toutes les offres</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        </td>
                        <!-- Fin cadre droit -->
                    </tr>
                </tbody>
            </table>

            <h3 class="h3_title">L'actualité de l'intérim</h3>
            <!-- Article bas de page -->
            <div style="text-align:text-align:-moz-center">
                <table style="width: 100%;">
                    <tbody>
                        <tr>
                            <td style="height:50px;width:49%;vertical-align:middle;">
                            {% for articleBas in article %}
                                {% if articleBas.zone_affichage == 1 %}
                                    <div class="box_actus clearfix">
                                        <div class="image_carres_themes" style="background-image:url(/uploads/article/{{articleBas.image_petite}});width:450px;margin-left:3px;float:left">
                                            <a style=");width:450px;" class="actu_third" href={{ "/article/" ~ articleBas.id_article ~ "/" ~ strtourl(articleBas.titre) }} title={{ articleBas.titre }}>
                                                <span style="width:420px;max-width:430px;">{{ articleBas.titre }}</span>
                                            </a>
                                        </div>
                                        <div class="carres_theme_text" style="width:450px;margin-left:3px;float:left">
                                            <p class="carres_theme_txt">
                                                {{ articleBas.chapo|u.truncate(250, '...', false) }}
                                            </p>
                                            <p class="suspensions" style="text-align:center;margin-top:-10px;"> ... </p>
                                        </div>
                                    </div>
                                {% endif %}
                            {% endfor %}
                            </td>
                            <td style="width:2%"></td>
                            <td style="height:50px;width:49%;vertical-align:middle;">
                            {% for articleBas in article %}
                                {% if articleBas.zone_affichage == 2 %}
                                    <div class="box_actus clearfix">
                                        <div class="image_carres_themes" style="background-image:url(/uploads/article/{{articleBas.image_petite}});width:450px;margin-right:3px;float:right;">
                                            <a style=");width:450px;" class="actu_third" href={{ "/article/" ~ articleBas.id_article ~ "/" ~ strtourl(articleBas.titre) }} title={{ articleBas.titre }}>
                                                <span style="width:420px;max-width:430px;">{{  articleBas.titre }}</span>
                                            </a>
                                        </div>
                                        <div class="carres_theme_text" style="width:450px;margin-right:3px;float:right">
                                            <p class="carres_theme_txt">
                                                {{ articleBas.chapo|u.truncate(250, '...', false) }}
                                            </p>
                                            <p class="suspensions" style="text-align:center;margin-top:-10px;"> ... </p>
                                        </div>
                                    </div>
                                {% endif %}
                            {% endfor %}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- Fin article bas de page -->

            <div id="la"></div>
            <div id="result" style="display:none"></div>

            <!-- Logo chargement -->
            <div id="loading1" style="display:none">
                <p>
                    <img alt="Chargement" title="Chargement" src="/images/icones/loading.gif">
                    <br>Chargement des données…
                </p>
            </div>

        </div> <!-- Fin div cadre -->
    <div> <!-- Fin div site  -->
</article>
<script>
    const form = document.querySelector('[name="annu_interim_agences"]');
    let liste = document.querySelector('#result');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        fetch(form.action, {
            method: "POST",
            body: new FormData(e.target),
        })
        .then(response => response.text())
        .then(html => {
            liste.innerHTML = html;
            liste.style.display = "block";
            liste.scrollIntoView(true);
        })
    })
</script>
{% endblock %}