{% extends 'directemploi/new_layout.html.twig' %}

{% block title %}Annuaire intérim{% endblock %}

{% block stylesheets %}
	<link rel="stylesheet" href="{{ asset('assets/css/new_main_DE.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_annu_interim.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/new_responsive.min.css') }}">
{% endblock %}

{% block javascripts %}
	<script src="{{ asset('assets/js/new_script.min.js') }}" defer></script>
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.13.1/jquery-ui.min.js"></script>
    <script src="{{ asset('assets/js/annuInterim.min.js') }}" defer></script>
{% endblock %}

{% block content %}
<section>
    <div class="container bandeau_annu_interim">
        <div class="cadre_titre_annu_interim">
            <div class="cadre_wrapper_annu_interim">
                <div class="titre_annu_interim">
                    <h3>ANNUAIRE INTERIM</h3>
                </div>
            </div>
        </div>
    </div>
    <div class="container milieu_annu_interim">
        <div class="annu_interim_colonne_gauche">
            <div class="form_recherche_agence">
                <div class="form_recherche_agence_titre">
                    <h4>Je trouve l'agence d'intérim près de chez moi parmi {{ toutesLesAgences|length }} agences</h4>
                </div>
                {{ form_start(form) }}
                    {{form_widget(form.region)}}
                    {{form_widget(form.departement)}}
                    <input type="submit" name="commit" value="Consulter les agences">
                    <img src="{{ asset('images/carte_france_interim.png') }}">
                {{ form_end(form) }}
            </div>
            <script>
                let region = document.querySelector("#annu_interim_agences_region");
                region.addEventListener("change", function() {
                    let form = document.querySelector('[name="annu_interim_agences"]');
                    let data = this.name + " = " + this.value;
                    
                    fetch(form.action, {
                        method: form.getAttribute("method"),
                        body: data,
                        headers: {
                            "Content-Type" : "application/x-www-form-urlencoded; charset:UTF-8" 
                        }
                    })
                    .then((response) => response.text())
                    .then(html => {
                        let content = document.createElement("html");
                        content.innerHTML = html;
                        
                        let nouveauSelect = content.querySelector("#annu_interim_agences_departement");
                        document.querySelector('#annu_interim_agences_departement').replaceWith(nouveauSelect);
                    }).catch(error => {
                        console.log(error);
                    })
                });
            </script>
            <div class="carousel_container">
                <h6><b>Les groupes d'intérim se présentent</b></h6>
                <div style='text-align:-moz-center'>
                    <div>
                        <div class="slider-container">
                            <a class="arrowL">&lt;</a>
                                <div id="slideshow">
                                {% for interim in interims %}
                                    {% if interim['logo_carousel'] == interims[0].logo_carousel %}
                                    <div>
                                        <a target="target" style= "height:520px;"
                                            href="{{ path("app_entreprises_details",
                                            {'id': interim['id_entreprise'], 'slug': interim['raison_sociale']|replace({"/": "-", "'": "", "È": "e", "É": "e", "ç": "c", "é": "e", "è": "e", "ê": "e", "ô":  "o", "à": "a", " – ": "-", " - ": "-", " / ": "-", " ": "-", "(": "", ")": ""})|lower}) }}">
                                            <img style="height:520px;" src="{{asset("images/direct-emploi/annuaire_interim/" ~ interim['logo_carousel']) }}" alt="{{interim['logo_carousel']}}">
                                        </a>
                                    </div>
                                    {% else %}
                                    <div class="hidden">
                                        <a target="target" style= "height:520px;"
                                            href="{{ path("app_entreprises_details",
                                            {'id': interim['id_entreprise'], 'slug': interim['raison_sociale']|replace({"/": "-", "'": "", "È": "e", "É": "e", "ç": "c", "é": "e", "è": "e", "ê": "e", "ô":  "o", "à": "a", " – ": "-", " - ": "-", " / ": "-", " ": "-", "(": "", ")": ""})|lower}) }}">
                                            <img style="height:520px;" src="{{ asset("images/direct-emploi/annuaire_interim/" ~ interim['logo_carousel']) }}" alt="{{interim['logo_carousel']}}">
                                        </a>
                                    </div>
                                    {% endif %}
                                {% endfor %}
                                </div>
                            <a class="arrowR">&gt;</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="annu_interim_colonne_droite">
            <div class="card_wrapper_actualité_annu_interim">
                {% for article in articles %}
                <div class="card_actualité_annu_interim">
                    <a href={{ "/article/" ~ article['id_article'] ~ "/" ~ strtourl(article['titre']) }} class="card card-zoom">
                        <img src="{{ asset("/uploads/article/" ~ article['image_petite']) }} " class="card-img-top" alt="{{article['image_petite']}}" style="height: 100%; width: 100%; align-self: center">
                        <div class="card-body">
                            <h5 class="card-title">{{article['titre']}}</h5>
                            <p class="card-text">{{article['chapo']|u.truncate(300, '...', false)}}</p>
                        </div>
                        <div class="card-footer">
                                <small class="text-muted">
                                    <!-- <strong>Actualité :</strong>
                                        {% if article['theme'] == 'Emploi' and article['type'] != 'Article' %}
                                            {{article['type']}}
                                        {% elseif article['type'] == 'Article' %}
                                            Dossier
                                        {% else %}
                                            {{article['theme']}}
                                        {% endif %}-->
                                </small>
                        </div>
                    </a>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    <div class="container" id="result" style="display:none"></div>
    <script>
    const form = document.querySelector('[name="annu_interim_agences"]');
    let liste = document.querySelector('#result');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        fetch(form.action, {
            method: "POST",
            body: new FormData(e.target),
        })
        .then(response => response.text())
        .then(html => {
            liste.innerHTML = html;
            liste.style.display = "block";
            liste.scrollIntoView(true);
        })
    })
</script>
</section>
{% endblock %}