{% if path == '/Medicale/home/' %}
    {% set prefix = '/Medicale/lists-offre/' %}
{% elseif path == "/theme/offres-stage" %}
    {% set prefix = '../lists-offre/stage-' %}
{% elseif path == '/theme/offres-alternance' %}
    {% set prefix = '../lists-offre/alternance-' %}
{% elseif path == "/theme/offre-emploi-jeune-diplome" %}
    {% set prefix = '../' %}
{% elseif path == "/theme/offres-emploi-interim" %}
    {% set prefix = '../lists-offre/interim-' %}
{% elseif path == "/theme/offres-emploi-cabinet-de-recrutement" %}
    {% set prefix = '../lists-offre/cdi-' %}
{% else %}
    {% set prefix = '/Medicale/lists-offre/' %}
{% endif %}
<style>
    .offres-list ul {
        list-style: none;
        padding: 0;
        width: 23%;
    }

    .offres-list li {
        margin-bottom: 0.75em;
        line-height: 1.5;
    }

    .offres-list a {
        text-decoration: none;
        font-size: 0.89rem;
        color: #333;
        transition: color 0.3s ease;
    }

    .offres-list a:hover {
        color: #007bff;
        text-decoration: underline;
    }

    .offres-list i {
        margin-right: 6px;
        color: #63BF7A;
        font-size: 7px;
    }

    @media screen and (max-width: 768px) {
        .offres-list ul {
            width: 48%;
        }
    }

    @media screen and (max-width: 480px) {
        .offres-list ul {
            width: 100%;
        }
    }

    .titre-section {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1em;
        color: #222;
    }
</style>
<section>
    <h2 class="text-uppercase text-center" style="font-size: 1.2rem;">
        <span class="yellow">Toutes</span> les offres Médicales
    </h2>

    <div class="menu-offres">
        <ul class="navbar-nav h-flex">
            <li class="nav-item">
                <a class="nav-link click-scroll" href="#section_1">Métier</a>
            </li>
            <li class="nav-item">
                <a class="nav-link click-scroll" href="#section_2">Région</a>
            </li>
            <li class="nav-item">
                <a class="nav-link click-scroll" href="#section_3">Département</a>
            </li>
            <li class="nav-item">
                <a class="nav-link click-scroll" href="#section_4">Ville</a>
            </li>
        </ul>

        <!-- Métiers -->
        <div id="section_1" class="container-fluid" style="display:block;">
            <p class="titre-section">Toutes nos offres Santé / Paramédical / Biologie / Pharmacie / Médico-social / Social classées par métier (H/F)</p>

            {% set metiers = listeMetiers|sort((a, b) => a.LibelleMetier <=> b.LibelleMetier) %}
            {% set total = metiers|length %}
            {% set columns = 4 %}
            {% set perCol = (total / columns)|round(0, 'ceil') %}

            <div class="d-flex justify-content-between flex-wrap offres-list" style="gap: 20px;">
                {% for col in 0..(columns - 1) %}
                    {% set columnItems = metiers|slice(col * perCol, perCol) %}
                    <ul>
                        {% for metier in columnItems %}
                            <li>
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;"></i>
                                <a href="https://www.carriere-medicale.com/Medicale/lists-offre/?contrat=&q={{ metier.LibelleMetier | url_encode }}&localisation=&localisation_field=">
                                    {{ metier.LibelleMetier }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                {% endfor %}
            </div>
        </div>

        <!-- Régions -->
        <div id="section_2" class="container-fluid" style="display:none;">
            <p class="titre-section">Toutes nos offres Santé / Paramédical / Biologie / Pharmacie classées par région</p>

            {% set regions = listeRegions|sort %}
            {% set total = regions|length %}
            {% set columns = 4 %}
            {% set perCol = (total / columns)|round(0, 'ceil') %}

            <div class="d-flex justify-content-between flex-wrap offres-list" style="gap: 20px;">
                {% for col in 0..(columns - 1) %}
                    {% set columnItems = regions|slice(col * perCol, perCol) %}
                    <ul>
                        {% for region in columnItems %}
                            <li>
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;"></i>
                                <a href="https://www.carriere-medicale.com/Medicale/lists-offre/?contrat=&q=&localisation={{ region }}&localisation_field=region">
                                    {{ region }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                {% endfor %}
            </div>
        </div>

        <!-- Départements -->
        <div id="section_3" class="container-fluid" style="display:none;">
            <p class="titre-section">Toutes nos offres Santé / Paramédical / Biologie / Pharmacie classées par département</p>

            {% set departements = listeDepartements|sort((a, b) => a.libelle <=> b.libelle) %}
            {% set total = departements|length %}
            {% set columns = 4 %}
            {% set perCol = (total / columns)|round(0, 'ceil') %}

            <div class="d-flex justify-content-between flex-wrap offres-list" style="gap: 20px;">
                {% for col in 0..(columns - 1) %}
                    {% set columnItems = departements|slice(col * perCol, perCol) %}
                    <ul>
                        {% for departement in columnItems %}
                            <li>
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;"></i>
                                <a href="https://www.carriere-medicale.com/Medicale/lists-offre/?contrat=&q=&localisation={{ departement|split(' (')[0]|trim }}&localisation_field=departement">
                                    {{ departement }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                {% endfor %}
            </div>
        </div>

        <!-- Villes -->
        <div id="section_4" class="container-fluid" style="display:none;">
            <p class="titre-section">Toutes nos offres Santé / Paramédical / Biologie / Pharmacie classées par ville</p>
            {% set ville_redirects = {
                'Alençon': 'ALENCON',
                'Angoulême': 'ANGOULEME',
                'Besançon': 'BESANCON',
                'Béziers': 'BEZIERS',
                'Compiègne': 'COMPIEGNE',
                'Créteil': 'CRETEIL',
                'Guéret': 'GUERET',
                'La Rochelle': 'LA ROCHELLE',
                'Le Havre': 'LE HAVRE',
                'Le Mans': 'LE MANS',
                'Lons-le-Saulnier': 'LONS-LE-SAUNIER',
                'Mâcon': 'MACON',
                'Nîmes': 'NIMES',
                'Orléans': 'ORLEANS',
                'Périgueux': 'PERIGUEUX',
                'Saint-Lô': 'SAINT-LO'
            } %}
            {% set villes = codePostal|merge([{ libelle: "Vierzon" }])|sort((a, b) => a.libelle <=> b.libelle) %}
            {% set total = villes|length %}
            {% set columns = 4 %}
            {% set perCol = (total / columns)|round(0, 'ceil') %}

            <div class="d-flex justify-content-between flex-wrap offres-list" style="gap: 20px;">
                {% for col in 0..(columns - 1) %}
                    {% set columnItems = villes|slice(col * perCol, perCol) %}
                    <ul>
                        {% for ville in columnItems %}
                            {% set villeNom = ville.libelle %}
                            {% set redir = ville_redirects[villeNom] ?? villeNom %}
                            <li>
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;"></i>
                                <a href="https://www.carriere-medicale.com/Medicale/lists-offre/?contrat=&q=&localisation={{ redir }}&localisation_field=ville">
                                    {{ villeNom }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                {% endfor %}
            </div>
        </div>
    </div>
</section>

<!-- JS activation dynamique des sections -->
<script>
    document.addEventListener("DOMContentLoaded", function () {
        const navLinks = document.querySelectorAll(".menu-offres .nav-link");

        navLinks.forEach(link => {
            link.addEventListener("click", function (e) {
                e.preventDefault();

                navLinks.forEach(l => l.classList.remove("active"));
                this.classList.add("active");

                document.querySelectorAll(".container-fluid").forEach(section => {
                    section.style.display = "none";
                });

                const target = this.getAttribute("href");
                const section = document.querySelector(target);
                if (section) section.style.display = "block";
            });
        });
    });
</script>
