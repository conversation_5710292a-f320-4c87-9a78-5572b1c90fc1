<style>
    /* Styles de base pour le conteneur d'autocomplétion */
    .autocomplete {
        position: relative;
        display: inline-block;
        width: 100%;
    }

    /* Style pour le champ de saisie */
    .autocomplete input {
        width: 100%;
        border: 1px solid #d4d4d4;
        background-color: #fff;
        font-size: 16px;
    }

    input[type=text]:focus {
        background: transparent;
        outline: none;
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Style pour la liste d'autocomplétion */
    .autocomplete-items {
        position: absolute;
        border: 1px solid #d4d4d4;
        border-bottom: none;
        border-top: none;
        z-index: 99999 !important; /* Z-index très élevé pour éviter tout conflit */
        background-color: #fff;
        left: 0;
        right: 0;
        top: 100%;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 0 0 4px 4px;
        /* Assurer que la liste reste visible même si le parent a overflow:hidden */
        overflow: visible !important;
    }

    /* Style pour chaque élément de la liste */
    .autocomplete-items div {
        padding: 12px 15px;
        cursor: pointer;
        background-color: #fff;
        border-bottom: 1px solid #e9e9e9;
        text-align: left;
        font-size: 14px;
        color: #333;
        transition: background-color 0.2s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Style pour le survol d'un élément */
    .autocomplete-items div:hover {
        background-color: #f1f8ff;
    }

    /* Style pour l'élément actif lors de la navigation au clavier */
    .autocomplete-active {
        background-color: #1e90ff !important;
        color: #fff !important;
    }

    /* Effet de mise en évidence du texte correspondant à la recherche */
    .autocomplete-items div strong {
        color: #63BF7A;
        font-weight: 600;
    }

    .autocomplete-active strong {
        color: #fff !important;
    }

    /* Assurer que les conteneurs parents ne masquent pas la liste */
    .form-item {
        position: relative;
        overflow: visible !important;
    }

    .job-form {
        overflow: visible !important;
    }

    .job-search-card {
        overflow: visible !important;
    }

    /* Style pour les petits écrans */
    @media (max-width: 768px) {
        .autocomplete-items {
            position: fixed;
            top: auto;
            left: 10px;
            right: 10px;
            width: calc(100% - 20px);
            max-height: 50vh;
        }
    }
</style>
<div class="form-padding">
    <form autocomplete="off" action="{{ path('app_Medicale_candidat_search') }}" method="get">
        <div class="job-search-card">
            <div class="job-icon text-center">
                <i class="fa-solid fa-briefcase-medical" style="color: #ffffff;font-size: 3rem;"></i>
            </div>
            <div class="job-form">
                <div class="form-item ">
                    {{ form_widget(searchForm.contrat, {'attr': { 'class':"form-select", 'name':"contrat", 'id':"contrat"}}) }}
                </div>
                <div class="form-item middle-form">
                    <div class="autocomplete" style="width:300px;">
                        <input id="mot_cle"  type="text"  name="q" class="form-control" placeholder="Métier, domaine, mots clés">
                    </div>
                </div>

                <div class="form-item">
                    <div class="autocomplete" style="width:300px;">
                        <input id="select_box" type="text" name="localisation" class="form-control" placeholder="Région, département, ville">
                    </div>
                    {{ form_widget(searchForm.localisation_field, {'value': ''}) }}
                </div>
            </div>
            <div class="text-center title-col job-button"  >
                <button aria-label="Recherche" type="submit" class="btn w-100 px-4 button-black">Recherche</button>
            </div>
        </div>
    </form>
</div>

<script>
    // Function to fetch job names (metiers)
    async function fetchMetiers(key) {
        try {
            let response = await fetch(`/Medicale/api/metier-search?key=${encodeURIComponent(key)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            let data = await response.json();
            return data;
        } catch (error) {
            console.error('Fetching metiers failed:', error);
            return [];
        }
    }

    // Autocomplete function for job names (metiers)
    function autocompleteMetier(inp) {
        var currentFocus;
        inp.addEventListener("input", async function (e) {
            var a, b, i, val = this.value.trim();
            closeAllLists();
            if (!val) {
                return false;
            }
            currentFocus = -1;

            let metiers = await fetchMetiers(val);

            a = document.createElement("DIV");
            a.setAttribute("id", this.id + "autocomplete-list");
            a.setAttribute("class", "autocomplete-items");
            this.parentNode.appendChild(a);

            for (i = 0; i < metiers.length; i++) {
                let name = metiers[i].libelle_metier;

                if (normalizeString(name).includes(normalizeString(val))) {
                    b = document.createElement("DIV");
                    b.innerHTML = "<strong>" + name.substr(0, val.length) + "</strong>";
                    b.innerHTML += name.substr(val.length);
                    b.innerHTML += "<input type='hidden' value='" + name + "'>";
                    b.addEventListener("click", function (e) {
                        inp.value = this.getElementsByTagName("input")[0].value;
                        closeAllLists();
                    });
                    a.appendChild(b);
                }
            }
        });

        inp.addEventListener("keydown", function (e) {
            var x = document.getElementById(this.id + "autocomplete-list");
            if (x) x = x.getElementsByTagName("div");
            if (e.keyCode == 40) { // Arrow down
                currentFocus++;
                addActive(x);
            } else if (e.keyCode == 38) { // Arrow up
                currentFocus--;
                addActive(x);
            } else if (e.keyCode == 13) { // Enter
                e.preventDefault();
                if (currentFocus > -1 && x) {
                    x[currentFocus].click();
                }
            }
        });

        function addActive(x) {
            if (!x) return false;
            removeActive(x);
            if (currentFocus >= x.length) currentFocus = 0;
            if (currentFocus < 0) currentFocus = (x.length - 1);
            x[currentFocus].classList.add("autocomplete-active");
        }

        function removeActive(x) {
            for (var i = 0; i < x.length; i++) {
                x[i].classList.remove("autocomplete-active");
            }
        }

        function closeAllLists(elmnt) {
            var x = document.getElementsByClassName("autocomplete-items");
            for (var i = 0; i < x.length; i++) {
                if (elmnt != x[i] && elmnt != inp) {
                    x[i].parentNode.removeChild(x[i]);
                }
            }
        }

        document.addEventListener("click", function (e) {
            closeAllLists(e.target);
        });
    }

    // Initialize autocomplete for job names (metiers)
    autocompleteMetier(document.getElementById("mot_cle"));
</script>

<script>
    // Function to fetch locations
    async function fetchLocations(key) {
        try {
            let response = await fetch(`/Medicale/api/location-search?key=${encodeURIComponent(key)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            let data = await response.json();
            return data;
        } catch (error) {
            console.error('Fetching locations failed:', error);
            return [];
        }
    }

    // Autocomplete function for locations
    function autocompleteLocation(inp) {
        var currentFocus;
        inp.addEventListener("input", async function (e) {
            var a, b, i, val = this.value.trim();
            closeAllLists();
            if (!val) {
                return false;
            }
            currentFocus = -1;

            let locations = await fetchLocations(val);

            a = document.createElement("DIV");
            a.setAttribute("id", this.id + "autocomplete-list");
            a.setAttribute("class", "autocomplete-items");
            this.parentNode.appendChild(a);

            for (i = 0; i < locations.length; i++) {
                let name = locations[i].libelle;
                let type = locations[i].type;
                let codeInsee = locations[i].code_insee || '';

                let displayText = name;
                if (type === 'departement' && codeInsee) {
                    displayText = `${name} - ${codeInsee}`;
                }

                // Recherche sur nom ou code insee
                if (
                    normalizeString(name).includes(normalizeString(val)) ||
                    normalizeString(codeInsee).includes(normalizeString(val))
                ) {
                    b = document.createElement("DIV");

                    let matchStart = normalizeString(displayText).indexOf(normalizeString(val));
                    if (matchStart !== -1) {
                        b.innerHTML =
                            displayText.substr(0, matchStart) +
                            "<strong>" +
                            displayText.substr(matchStart, val.length) +
                            "</strong>" +
                            displayText.substr(matchStart + val.length);
                    } else {
                        b.innerHTML = displayText;
                    }

                    // Stocke le vrai libellé sans le code insee
                    b.innerHTML += `<input type='hidden' value='${name}' data-type='${type}' data-insee='${codeInsee}'>`;

                    b.addEventListener("click", function (e) {
                        inp.value = this.getElementsByTagName("input")[0].value + (type === 'departement' && codeInsee ? ` - ${codeInsee}` : '');
                        let selectedType = this.getElementsByTagName("input")[0].getAttribute('data-type');
                        document.querySelector('[name="localisation_field"]').value = selectedType;
                        closeAllLists();
                    });

                    a.appendChild(b);
                }
            }
        });

        inp.addEventListener("keydown", function (e) {
            var x = document.getElementById(this.id + "autocomplete-list");
            if (x) x = x.getElementsByTagName("div");
            if (e.keyCode == 40) { // Arrow down
                currentFocus++;
                addActive(x);
            } else if (e.keyCode == 38) { // Arrow up
                currentFocus--;
                addActive(x);
            } else if (e.keyCode == 13) { // Enter
                e.preventDefault();
                if (currentFocus > -1 && x) {
                    x[currentFocus].click();
                }
            }
        });

        function addActive(x) {
            if (!x) return false;
            removeActive(x);
            if (currentFocus >= x.length) currentFocus = 0;
            if (currentFocus < 0) currentFocus = (x.length - 1);
            x[currentFocus].classList.add("autocomplete-active");
        }

        function removeActive(x) {
            for (var i = 0; i < x.length; i++) {
                x[i].classList.remove("autocomplete-active");
            }
        }

        function closeAllLists(elmnt) {
            var x = document.getElementsByClassName("autocomplete-items");
            for (var i = 0; i < x.length; i++) {
                if (elmnt != x[i] && elmnt != inp) {
                    x[i].parentNode.removeChild(x[i]);
                }
            }
        }

        document.addEventListener("click", function (e) {
            closeAllLists(e.target);
        });
    }

    // Initialize autocomplete for locations
    autocompleteLocation(document.getElementById("select_box"));

    // Detect location type when the input changes
    document.getElementById("select_box").addEventListener("input", function () {
        detectLocationType(this.value);
    });

    async function detectLocationType(locationInput) {
        let input = locationInput.trim().toLowerCase();
        let locations = await fetchLocations(input);
        let locationField = document.querySelector('[name="localisation_field"]');

        let found = locations.find(loc => loc.libelle.toLowerCase() === input);

        if (found) {
            locationField.value = found.type; // Set the type (ville, département, région)
        } else {
            locationField.value = ""; // Clear if no match
        }

        updateFormAction();
    }

    function updateFormAction() {
        let form = document.querySelector("form");
        let locationType = document.querySelector('[name="localisation_field"]').value;

        let searchParams = new URLSearchParams(new FormData(form));
        if (locationType) {
            searchParams.set("localisation_field", locationType);
        }
        searchParams.set("page", "1");

        form.action = form.action.split("?")[0] + "?" + searchParams.toString();
    }
</script>

<script>
    // Cette fonction était référencée mais non définie dans votre code
    function normalizeString(str) {
        return str.toLowerCase()
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .trim();
    }
</script>
<script>
    // Test de création d'éléments d'autocomplétion pour vérifier leur visibilité
    function testAutocompleteVisibility() {
        console.log("Exécution du test de visibilité autocomplete");

        // Créer manuellement un élément de test pour l'autocomplétion
        const motCleInput = document.getElementById("mot_cle");
        if (!motCleInput) {
            console.error("Élément #mot_cle non trouvé");
            return;
        }

        // Nettoyer d'abord tout élément existant
        const existingLists = document.querySelectorAll(".autocomplete-items");
        existingLists.forEach(list => list.parentNode.removeChild(list));

        // Créer une liste de test
        const testList = document.createElement("DIV");
        testList.setAttribute("id", "test-autocomplete-list");
        testList.setAttribute("class", "autocomplete-items");
        testList.style.zIndex = "10000";
        testList.style.position = "absolute";
        testList.style.backgroundColor = "yellow"; // Couleur distinctive pour le test
        testList.style.border = "3px solid red";   // Bordure très visible

        // Ajouter quelques éléments à la liste
        for (let i = 1; i <= 5; i++) {
            const item = document.createElement("DIV");
            item.innerHTML = `<strong>Test Item ${i}</strong>`;
            item.style.padding = "15px";
            testList.appendChild(item);
        }

        // Ajouter la liste au parent de l'input
        motCleInput.parentNode.appendChild(testList);

        console.log("Liste de test créée avec " + testList.children.length + " éléments");
    }

    // Vérifier s'il y a des CSS qui masquent potentiellement nos éléments
    function checkForConflictingStyles() {
        console.log("Vérification des styles en conflit...");

        // Obtenir tous les styles appliqués qui pourraient affecter notre élément
        const allStyleSheets = document.styleSheets;
        let conflictingRules = [];

        try {
            for (let i = 0; i < allStyleSheets.length; i++) {
                const ss = allStyleSheets[i];
                try {
                    const rules = ss.cssRules || ss.rules;
                    for (let j = 0; j < rules.length; j++) {
                        const rule = rules[j];
                        if (rule.selectorText &&
                            (rule.selectorText.includes('autocomplete') ||
                                rule.selectorText.includes('form') ||
                                rule.selectorText.includes('job-form'))) {

                            // Vérifier les propriétés qui pourraient causer des problèmes
                            const cssText = rule.style.cssText;
                            if (cssText.includes('z-index') ||
                                cssText.includes('position') ||
                                cssText.includes('display: none') ||
                                cssText.includes('visibility: hidden') ||
                                cssText.includes('opacity: 0')) {

                                conflictingRules.push({
                                    selector: rule.selectorText,
                                    css: cssText
                                });
                            }
                        }
                    }
                } catch (e) {
                    console.warn("Impossible d'accéder aux règles CSS:", e);
                }
            }

            console.log("Règles CSS potentiellement conflictuelles:", conflictingRules);
        } catch (e) {
            console.error("Erreur lors de l'analyse des styles:", e);
        }
    }

    // Modifier le code des fonctions d'autocomplétion pour ajouter des logs
    const originalFetchMetiers = fetchMetiers;
    fetchMetiers = async function(key) {
        console.log("Appel à fetchMetiers avec clé:", key);
        const result = await originalFetchMetiers(key);
        console.log("Résultat fetchMetiers:", result);
        return result;
    };

    // Override de closeAllLists pour voir quand il est appelé
    const originalCloseAllLists = document.querySelector('script:not([src])').textContent.match(/function\s+closeAllLists[\s\S]*?\}/)[0];
    eval(`window.originalCloseAllLists = ${originalCloseAllLists}`);

    function closeAllLists(elmnt) {
        console.log("closeAllLists appelé", elmnt);
        const before = document.querySelectorAll('.autocomplete-items').length;
        window.originalCloseAllLists(elmnt);
        const after = document.querySelectorAll('.autocomplete-items').length;
        console.log(`Éléments autocomplete avant: ${before}, après: ${after}`);
    }

    // Exécuter les tests au chargement
    document.addEventListener('DOMContentLoaded', function() {
        console.log("DOM chargé, exécution des tests d'autocomplétion");
        setTimeout(() => {
            testAutocompleteVisibility();
            checkForConflictingStyles();

            // Ajouter un bouton pour tester l'autocomplétion manuellement
            const testButton = document.createElement('button');
            testButton.textContent = "Tester l'autocomplétion";
            testButton.style.position = 'fixed';
            testButton.style.top = '10px';
            testButton.style.right = '10px';
            testButton.style.zIndex = '10001';
            testButton.style.padding = '10px';
            testButton.style.backgroundColor = '#63BF7A';
            testButton.style.color = 'white';
            testButton.style.border = 'none';
            testButton.style.borderRadius = '5px';
            testButton.style.cursor = 'pointer';

            testButton.addEventListener('click', testAutocompleteVisibility);
            document.body.appendChild(testButton);

            console.log("Bouton de test ajouté");
        }, 1000);
    });
</script>
<script>
    // Remplacer complètement la logique d'attachement des éléments d'autocomplétion
    function fixAutocomplete() {
        const motCleInput = document.getElementById("mot_cle");
        const localisationInput = document.getElementById("select_box");

        if (motCleInput && localisationInput) {
            // Supprimer les anciens écouteurs d'événements (ne fonctionne que si on a des références)
            const oldInputEvent = motCleInput.oninput;
            motCleInput.oninput = null;

            // Ajouter un nouvel écouteur d'événements
            motCleInput.addEventListener("input", async function(e) {
                console.log("Nouvel événement input sur mot_clé");
                const val = this.value.trim();

                // Supprimer toutes les anciennes listes
                document.querySelectorAll('.autocomplete-items').forEach(el => {
                    el.parentNode.removeChild(el);
                });

                if (!val) return;

                try {
                    // Simuler quelques données pour le test
                    const metiers = [
                        { libelle_metier: "Médecin généraliste" },
                        { libelle_metier: "Infirmier" },
                        { libelle_metier: "Aide-soignant" },
                        { libelle_metier: "Kinésithérapeute" },
                        { libelle_metier: "Pharmacien" }
                    ];

                    // Créer la liste avec une référence explicite
                    const listContainer = document.createElement("DIV");
                    listContainer.id = "fixed-autocomplete-list";
                    listContainer.className = "autocomplete-items";

                    // Style explicite pour garantir la visibilité
                    listContainer.style.position = "absolute";
                    listContainer.style.top = "100%";
                    listContainer.style.left = "0";
                    listContainer.style.right = "0";
                    listContainer.style.backgroundColor = "white";
                    listContainer.style.border = "1px solid #d4d4d4";
                    listContainer.style.zIndex = "10000";
                    listContainer.style.maxHeight = "300px";
                    listContainer.style.overflowY = "auto";
                    listContainer.style.boxShadow = "0 2px 5px rgba(0,0,0,0.2)";

                    // Remplir avec des éléments
                    metiers.forEach(metier => {
                        if (metier.libelle_metier.toLowerCase().includes(val.toLowerCase())) {
                            const item = document.createElement("DIV");
                            item.innerHTML = "<strong>" + metier.libelle_metier + "</strong>";
                            item.style.padding = "10px";
                            item.style.cursor = "pointer";
                            item.style.backgroundColor = "#fff";
                            item.style.borderBottom = "1px solid #d4d4d4";

                            item.addEventListener("click", function() {
                                motCleInput.value = metier.libelle_metier;
                                document.querySelectorAll('.autocomplete-items').forEach(el => {
                                    el.parentNode.removeChild(el);
                                });
                            });

                            listContainer.appendChild(item);
                        }
                    });

                    // Ajouter au DOM s'il y a des éléments
                    if (listContainer.children.length > 0) {
                        this.parentNode.appendChild(listContainer);
                        console.log("Liste d'autocomplétion créée avec " + listContainer.children.length + " éléments");
                    }
                } catch (error) {
                    console.error("Erreur lors de la création de l'autocomplétion:", error);
                }
            });

            console.log("Correction de l'autocomplétion appliquée");
        } else {
            console.error("Éléments d'entrée non trouvés");
        }
    }

    // Exécuter la correction après le chargement complet
    window.addEventListener('load', function() {
        setTimeout(fixAutocomplete, 1500);
    });
</script>