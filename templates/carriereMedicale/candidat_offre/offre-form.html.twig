{% extends 'carriereMedicale/base.html.twig' %}
{% set title = offreDetails[0].intitule|raw ~ " - " ~  offreDetails[0].reference ~ " - " ~ offreDetails['libelleContrat']|raw %}
{% if offreDetails[0].geoVille is defined and offreDetails[0].geoVille != null %}{% set title = title ~ " " ~ offreDetails[0].geoVille %}
{% elseif departement is defined and departement != null %}{% set title = title ~ " " ~ departement.libelle %}
{% elseif region.libelle is defined and region.libelle != null %}{% set title = title ~ " " ~ region.libelle %}{% endif %}
{% set title = title[:100] %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}

{% set description_string = "retrouver tous les details de notre offre " ~ offreDetails[0].intitule|raw ~ " " ~ offreDetails[0].geoVille|raw ~ " " ~ offreDetails[0].reference|raw ~ " sur Carriere Medicale" %}
{% block meta_description %}{{ description_string }}{% endblock %}
{% block meta_description_og %}{{ description_string }}{% endblock %}
{% block meta_description_twitter %}{{ description_string }}{% endblock %}

{% block body %}

<div class="is-sticky">
    {% block head %}
    {% include 'carriereMedicale/header.html.twig' %}
{% endblock %}
<style>
    .offre-form-header {
        position: relative;
        /* autres styles nécessaires */
    }

    .btn-passer-au-formulaire {
        position: absolute;
        bottom: 0;
        right: 0;
        padding: 10px 20px;
        background-color: #63BF7A; /* Choisissez la couleur du bouton */
        color: white; /* Couleur du texte */
        border: none;
        border-radius: 5px;
        text-decoration: none;
        font-weight: bold;
    }

    .btn-passer-au-formulaire:hover {
        background-color: #d98c35; /* Couleur au survol */
    }

</style>
    <section class="offre-form-header d-flex justify-content-center align-items-center" id="">
        <h5 class="text-center text-uppercase mb-0">
            <span style="color:#63BF7A">DESCRIPTIF</span> DE L'OFFRE
        </h5>
        <!-- Bouton ajouté ici -->
        <a href="#offre-form" class="btn-passer-au-formulaire">
            Passez au formulaire
        </a>
    </section>


    <section class="login-form">

        <div class="container">
            <div class="row">
                <div class="col-lg-8">
            <div class="card p-5 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
                <h6 class="mb-3">Informations clés</h6>
                <ul>
                    <li class="mb-2">
                        {% set contrat = offreDetails[0].OffListeTypeContratIdTypeContrat %}
                        {# {% if contrat is defined and contrat == 1 or contrat == 2 or contrat == 3 %} #}
                            {% if (offreDetails[0].OffListeTypeContratIdTypeContrat == 1) or offreDetails[0].OffListeTypeContratIdTypeContrat == 2 %}
                                <b>Offre d'emploi :</b>
                            {% elseif (offreDetails[0].OffListeTypeContratIdTypeContrat == 3) %}
                                <b>Offre en freelance :</b>
                            {% elseif (offreDetails[0].OffListeTypeContratIdTypeContrat == 4) %}
                                <b>Offre en interim :</b>
                            {% elseif (offreDetails[0].OffListeTypeContratIdTypeContrat == 6) %}
                                <b>Offre en alternance :</b>
                            {% elseif (offreDetails[0].OffListeTypeContratIdTypeContrat == 7 or offreDetails[0].OffListeTypeContratIdTypeContrat == 8) %}
                                <b>Offre de stage</b>
                            {% else %}
                                <b>Offre d'emploi :</b>
                            {% endif %}
                        {# {% endif %} #}
                        <span>
                            {% if offreDetails[0].intitule is defined and offreDetails[0].intitule != null %}
                                {{ offreDetails[0].intitule|raw }}
                            {% endif %}
                        </span>
                    </li>

                    {% if offreDetails['raison_sociale'] is defined and offreDetails['raison_sociale'] != null %}
                    <li class="mb-2"><b>Société</b><span> : {{offreDetails['raison_sociale']}}</span> </li>
                    {% endif %}

                    {% if offreDetails[0].dateSoumission is defined and offreDetails[0].dateSoumission != null %}
                    <li class="mb-2"><b>Date de publication</b> : {{offreDetails[0].dateSoumission|date('d/m/Y')}} </li>
                    {% endif %}

                    {% if offreDetails[0].reference is defined and offreDetails[0].reference != null %}
                    <li class="mb-2"><b>Reference de l'offre</b> : {{ offreDetails[0].reference }} </li>
                    {% endif %}

                    {% if offreDetails['libelleSecteur'] is defined and offreDetails['libelleSecteur'] != null %}
                    <li class="mb-2"><b>Secteur d'activité</b> : {{ offreDetails['libelleSecteur']}} </li>
                    {% endif %}

                    {% if region.libelle is defined and region.libelle != null %}
                    <li class="mb-2"><b>Localisation</b> :  <a href="{{ path('app_candidat_emploi_region', {region: region.idRegion }) }}">{{ region.libelle }}</a>
                        :
                        {% if departement is defined and departement != null %}
                        <a href="{{ path('app_candidat_emploi_departement', {departement: departement.idDepartement }) }}">{{ departement.libelle }}</a>
                        {% endif %}
                        {% if offreDetails[0].geoVille is defined and offreDetails[0].geoVille != null %}
                                -
                            {{offreDetails[0].geoVille}}
                        {% endif %}
                    </li>
                    {% endif %}


                    {% if offreDetails['libelleContrat'] is defined and offreDetails['libelleContrat'] != null %}
                    <li class="mb-2"><b>Type de contrat </b>: <a href="{{ path('app_candidat_emploi_contrat', {contrat: offreDetails['id_type_contrat']}) }}">{{offreDetails['libelleContrat']}}</a> </li>
                    {% endif %}


                    {% if (dateDebut is defined and dateDebut != null and dateDebut != '0000-00-00') %}
                    <li class="mb-2"><b>Debut du contrat</b> : {{ dateDebut }} </li>
                    {% endif %}


                    {% if (offreDetails[0].DureeContrat is defined and offreDetails[0].DureeContrat != null) %}
                    <li class="mb-2"><b>Durée du contrat</b> : {{ offreDetails[0].DureeContrat }} </li>
                    {% endif %}


                    {% if offreDetails['libelleRemuneration'] is defined and offreDetails['libelleRemuneration'] != null %}
                    <li class="mb-2"><b>Salaire </b> : {{ offreDetails['libelleRemuneration']}} </li>
                    {% endif %}

                    {% if offreDetails['libelleExperience'] is defined and offreDetails['libelleExperience'] != null %}
                        <li class="mb-2"><b>Expérience requise </b> : {{ offreDetails['libelleExperience'] }}</li>
                    {% endif %}

                    {% if offreDetails['libelleFormation'] is defined and offreDetails['libelleFormation'] != null %}
                        <li class="mb-2"><b>Formation requise </b> : {{ offreDetails['libelleFormation'] }}</li>
                    {% endif %}




                </ul>
            </div>
            </div>
                <div class="col-lg-4">
                    <div class="info_entre" style="text-align: center;">
                        <div class="img img_logo">
                            <div class="int">
                                <img src="{{ asset('/uploads/logos/' ~ offreDetails['logo']) }}" alt="{{offreDetails['logo']}}" style="width: 100%; height: 100%">
                            </div>
                        </div>
                        <div class="voir_toutes_offres">
                            <a href="{{ path('app_Medicale_entreprisesDetails', {id: offreDetails['id_entreprise'], slug: offreDetails['raison_sociale'] | raison }) }}"  title="Découvrir l'entreprise">Découvrir
                                <span class="ordi">l'entreprise,</span>
                                toutes les offres
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card p-5 mt-3" style="background-color:#F9F9F9; border-radius:20px;">


                {% if (offreDetails[0].isAnonym == 0 and offreDetails[0].nomSociete == null) %}
                <h6 class="mb-2">L'entreprise</h6>
                <hr>
                    <div>
                {{offreDetails['presentation_societe']|raw|nl2br}}
                    </div>
                {% endif %}
                <h6 class="mb-2 mt-3">Description de l'offre</h6>
                <hr>
                <div>
                    {{offreDetails[0].mission|raw|nl2br}}
                </div>
                {% if offreDetails[0].profil != '' %}
                <h6 class="mb-2 mt-3">Profil recherché</h6>
                <hr>
                    <div>
                        {{offreDetails[0].profil|raw|nl2br}}
                    </div>
                {% endif %}




            </div>
            <hr>
            <section id="offre-form">
            <section class="offre-form-header d-flex justify-content-center align-items-center" id="">
                <h5 class="text-center text-uppercase mb-0"><span style="color:#63BF7A">Formulaire de </span>réponse à l’offre</h5>
            </section>

            {{ form_start(form) }}
            {% if user is not null %}
                <div class="info-section mb-0">
                    <div class="row">
                        <div class="col-lg-4 col-sm-12">
                            {{ form_row(form.nom, {'value': user.getNom()}) }}
                            {{ form_errors(form.nom) }}
                        </div>
                        <div class="col-lg-4 col-sm-12">
                            {{ form_row(form.prenom, {'value': user.getPrenom()}) }}
                            {{ form_errors(form.prenom) }}
                        </div>
                        <div class="col-lg-4 col-sm-12">
                            {{ form_row(form.user_civilite_id_user_civilite ,{'value':  user.getUserCiviliteIdUserCivilite()}) }}
                            {{ form_errors(form.user_civilite_id_user_civilite) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            {{ form_row(form.email, {'value': user.getEmail()}) }}
                            {{ form_errors(form.email) }}
                        </div>
                    </div>
                </div>
            {% else %}
            <div class="info-section mb-0">
                <div class="row">
                    <div class="col-lg-4 col-sm-12">
                        {{ form_row(form.nom) }}
                        {{ form_errors(form.nom) }}
                    </div>
                    <div class="col-lg-4 col-sm-12">
                        {{ form_row(form.prenom) }}
                        {{ form_errors(form.prenom) }}
                    </div>
                    <div class="col-lg-4 col-sm-12">
                        {{ form_row(form.user_civilite_id_user_civilite) }}
                        {{ form_errors(form.user_civilite_id_user_civilite) }}
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6 col-sm-12">
                        {{ form_row(form.email) }}
                        {{ form_errors(form.email) }}
                    </div>
                </div>
            </div>
            {% endif %}
            {% if situation is not null %}
            <div class="info-section mb-0">
                    <hr>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputDiplome">Année d'obtention du diplôme</label>
                                {{ form_row(form.anneeObtentionDiplome, {
                                    'attr': {'class': 'anneeObtentionDiplome form-control'},
                                    'label': false,
                                    'value': (situation.anneeObtentionDiplome() == '0' ? '' : situation.anneeObtentionDiplome())
                                }) }}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="activiteFormControl">Domaine d'expertise</label>
                                {{ form_row(form.domaine_activite, {
                                    'attr': {'class': 'domaine-activite-liste form-control'},
                                    'label': false,
                                    'value': situation.offListeSecteurActiviteIdSecteurActivite() is not null ? situation.offListeSecteurActiviteIdSecteurActivite() : ''
                                }) }}

                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="formationFormControl">Niveau de formation</label>
                                {{ form_row(form.formation_level, {
                                    'attr': {'class': 'formation-level-liste form-control'},
                                    'label': false,
                                    'value': situation.offListeTypeFormationIdTypeFormation()
                                }) }}

                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="metierFormControl">Établissement / Spécialité</label>
                                {{ form_row(form.ec_ecole, {
                                    'attr': {'class': 'ec-ecole-liste form-control'},
                                    'label': false,
                                }) }}

                            </div>
                        </div>
                    </div>

                </div>
            {% else %}
                <div class="info-section mb-0">
                    <hr>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="activiteFormControl">Domaine d'expertise</label>
                                {{ form_row(form.domaine_activite, {
                                    'attr': {'class': 'domaine-activite-liste form-control'},
                                    'label': false,

                                }) }}

                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="formationFormControl">Niveau de formation</label>
                                {{ form_row(form.formation_level, {
                                    'attr': {'class': 'formation-level-liste form-control'},
                                    'label': false,

                                }) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                {{ form_label(form.metier_metier, "Métier") }}
                                {% if form.metier_metier.vars.required %}
                                    <span class="required-asterisk">*</span>
                                {% endif %}
                                {{ form_widget(form.metier_metier, {'attr': {'class': 'form-select form-control'}}) }}
                                {{ form_errors(form.metier_metier) }}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputDiplome">Année d'obtention du diplôme</label>
                                {{ form_row(form.anneeObtentionDiplome, {
                                    'attr': {'class': 'anneeObtentionDiplome form-control'},
                                    'label': false,

                                }) }}
                            </div>
                        </div>

                    </div>

                </div>
            {% endif %}

                <div class="info-section mb-0">
                    <hr>
                    <div class="row">
                        {% if cv is not null %}
                        <div class="col-lg-6 col-sm-12">

                                <div class="form-group">
                                    <label for="metierFormControl">CV enregistré</label>
                                    {{ form_row(form.cvexist,{'label': false,}) }}
                                </div>




                        </div>
                        {% endif %}
                        <div class="col-lg-6">
                            <p><strong>Télécharger un cv</strong></p>
                            <div class="form-group" style="margin-bottom:0;">
                                {{ form_row(form.cv_file, {'label': false}) }}
                                <small id="fileHelp" class="form-text text-muted">Taille max. de 1 Mo. Formats acceptés : doc, docx, pdf, rtf.</small>

                            </div>

                        </div>

                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <p><strong>Télécharger une lettre de motivation</strong></p>
                            <div class="form-group" >
                                {{ form_row(form.lm, {'label': false}) }}
                                <small id="fileHelp" class="form-text text-muted mb-0">Taille max. de 1 Mo. Formats acceptés : doc, docx, pdf, rtf.</small>

                            </div>

                        </div>

                    </div>
                </div>
                <div class="info-section mb-0">
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <div class="bottom-login">
                                <div>
                                    <p><strong>J'accepte <a href="#"style="color:#63BF7A;">les conditions générales</a> de Carriere Médicale</strong></p>
                                    <p>- Coordonnées transmises aux recruteurs, lecture du CV à des fins de recrutement</p>
                                    <p>- Envois potentiels d’offres d’emploi, stage, alternance, formations et communications partenaires</p>
                                </div>
                                <div class="form-check" style="margin-left:auto;">
                                    {{ form_row(form.cgu, {'label': false, 'attr': {'class': 'form-check-input', 'style': 'max-height: 24px;'}}) }}


                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="text-center">
                    <button aria-label="S'inscrire" type="submit" class="btn mb-2 px-4 button-black">Postuler maintenant</button>
                </div>
            </section>
            <div class="card p-5 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
                <h6 class="mb-3">Attention</h6>
                <ul>
                    <li class="mb-2">Nous vous conseillons de désactiver les bloqueurs de publicité de type AdBlock pour assurer la bonne validation du formulaire.</li>
                    <li>Si vous ne parvenez pas à postuler, utilisez notre <a href="#" class="yellow">formulaire simplifié.</a></li>

                </ul>
            </div>

        </div>
    </section>

    {% block footer %}
        {% include 'carriereMedicale/footer.html.twig' %}
    {% endblock %}
    <script>
        var ecoleListe = document.querySelector('.ec-ecole-liste');
        var formationLevelListe = document.querySelector('.formation-level-liste');
        {% if situation is not null and situation.getEcEcoleIdEcole() is not null %}
        var ecoleid = {{ situation.getEcEcoleIdEcole()|json_encode|raw }};
        {% endif %}
        // Fonction pour mettre à jour la liste des écoles
        function updateEcoleList(selectedFormationLevel) {
            fetch('/ajax/get-ecoles/' + selectedFormationLevel)
                .then(response => response.json())
                .then(data => {
                    ecoleListe.innerHTML = ''; // Effacer les options existantes

                    data.forEach(function (ecole) {
                        var option = document.createElement('option');
                        option.value = ecole.id;
                        option.text = ecole.nom_ecole;
                        ecoleListe.add(option);
                    });
                })
                .catch(error => {
                    console.error('Une erreur s\'est produite lors de la récupération des écoles:', error);
                });
        }

        // Appeler la fonction pour mettre à jour la liste des écoles au chargement de la page
        {% if situation is not null and situation.offListeTypeFormationIdTypeFormation is not null %}
        updateEcoleList({{ situation.offListeTypeFormationIdTypeFormation()|json_encode|raw }});
        {% endif %}
        formationLevelListe.addEventListener('change', function () {
            var selectedFormationLevel = this.value;

            if (selectedFormationLevel !== 23) {
                updateEcoleList(selectedFormationLevel);
            }
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const domaineSelect = document.querySelector('#submit_candidature_domaine_activite'); // Adjust the ID as necessary
            const metierSelect = document.querySelector('#submit_candidature_metier_metier'); // Adjust the ID as necessary

            domaineSelect.addEventListener('change', function() {
                const selectedId = this.value;
                const url = `https://www.carriere-medicale.com/Medicale/ajax/get-metiers/${selectedId}`;
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        metierSelect.innerHTML = ''; // Clear existing options
                        data.forEach(metier => {
                            const option = new Option(metier.libelle_metier, metier.id);
                            metierSelect.add(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching métiers:', error);
                    });
            });
        });

    </script>
</div>
{% endblock %}