{% extends 'carriereMedicale/base.html.twig' %}

{% set title_seo = "Carrière Medicale - Offres d'emploi - Formation continue dans le secteur de Medicale" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% set description_seo = "sur Carrière Medicale, recherchez toutes les annonces d'emploi : CDD, CDI, alternance, stage, intérim..." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .single-logo
        {
            width: 60%;
            margin: auto;
            display: block;
        }
        .single-logo img
        {

        }
        .section-padding{
            padding:60px 0;
        }

        .owl-dots{
            text-align: center;
        }

        .owl-dot {
            display: inline-block;
            height: 15px !important;
            width: 15px !important;
            background-color: #222222 !important;
            opacity: 0.8;
            border-radius: 50%;
            margin: 0 5px;
        }

        .owl-dot.active {
            background-color: #63BF7A !important;
        }
    </style>
    <div class="is-sticky">

        {% block head %}
            {% include 'carriereMedicale/header.html.twig' %}
        {% endblock %}
        <section class="espace-ingenieur-header" id="">
            <h5 class="text-center text-uppercase text-white"><span style="color:#63BF7A">ESPACE MÉDECINS : </span> Des opportunités de carrière</h5>
            <div class="header-text">
                <div class="container ">
                    <h6 class="text-uppercase text-white">BIENVENUE DANS NOTRE ESPACE MÉDECINS</h6>
                    <p class="text-white justified">Bienvenue dans l'Espace Médecins, dédié à l'univers professionnel de la santé en entreprise. Explorez les métiers médicaux, avec un focus sur le rôle préventif clé du médecin du travail. Notre article phare décrit ses missions, les compétences requises, et les perspectives d'évolution. Découvrez également les entreprises actives dans le recrutement médical. L'Espace Médecin est votre passerelle vers un domaine où prévention, bien-être des salariés et opportunités professionnelles se rencontrent. Explorez, apprenez, et ouvrez la porte à de nouvelles perspectives dans le monde passionnant de la médecine au travail.
                    </p>
                </div>
            </div>
        </section>

        <section class="section-embauche">
            <h6 class="text-center text-uppercase mb-5"><span style="color: #63BF7A;">LES POSTES</span> PROPOSés</h6>
            <div class="container">

                <div class="row offres-list">
                    <div class="col-lg-3 col-md-6">
                        <!-- Première colonne de 15 éléments -->
                        <ul style="list-style: none; padding: 0;">
                            <li onclick="redirectTo('Cardiologue')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Cardiologue</a>
                            </li>
                            <li onclick="redirectTo('Chirurgien dentiste')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Chirurgien dentiste</a>
                            </li>
                            <li onclick="redirectTo('Chirurgien digestif')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Chirurgien digestif</a>
                            </li>
                            <li onclick="redirectTo('Chirurgien orthopédiste')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Chirurgien orthopédiste</a>
                            </li>
                            <li onclick="redirectTo('Chirurgien urologue')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Chirurgien urologue</a>
                            </li>
                            <li onclick="redirectTo('Chirurgien viscéral')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Chirurgien viscéral</a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <!-- Première colonne de 15 éléments -->
                        <ul style="list-style: none; padding: 0;">
                            <li onclick="redirectTo('Dermatologue')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Dermatologue</a>
                            </li>
                            <li onclick="redirectTo('Endocrinologue')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Endocrinologue</a>
                            </li>
                            <li onclick="redirectTo('Gériatre')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Gériatre</a>
                            </li>
                            <li onclick="redirectTo('Gynécologue obstétricien')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Gynécologue obstétricien</a>
                            </li>
                            <li onclick="redirectTo('Médecin anesthésiste')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin anesthésiste</a>
                            </li>
                            <li onclick="redirectTo('Médecin coordonnateur')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin coordonnateur</a>
                            </li>

                        </ul>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <!-- Première colonne de 15 éléments -->
                        <ul style="list-style: none; padding: 0;">
                            <li onclick="redirectTo('Médecin dim')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin dim</a>
                            </li>
                            <li onclick="redirectTo('Médecin du sport')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin du sport</a>
                            </li>
                            <li onclick="redirectTo('Médecin du travail')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin du travail</a>
                            </li>
                            <li onclick="redirectTo('Médecin Gastro-entérologue')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin Gastro-entérologue</a>
                            </li>
                            <li onclick="redirectTo('Médecin généraliste')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin généraliste</a>
                            </li>
                            <li onclick="redirectTo('Médecin pédiatre')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin pédiatre</a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <!-- Première colonne de 15 éléments -->
                        <ul style="list-style: none; padding: 0;">
                            <li onclick="redirectTo('Médecin pneumologue')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin pneumologue</a>
                            </li>
                            <li onclick="redirectTo('Médecin polyvalent')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin polyvalent</a>
                            </li>
                            <li onclick="redirectTo('Médecin psychiatre')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin psychiatre</a>
                            </li>
                            <li onclick="redirectTo('Médecin radiologue')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin radiologue</a>
                            </li>
                            <li onclick="redirectTo('Médecin urgentiste')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Médecin urgentiste</a>
                            </li>
                            <li onclick="redirectTo('Neurologue')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Neurologue</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section class="zoom-metier">
            <div class="container">
                <h6 class="text-center text-uppercase mb-5"><span style="color: #63BF7A;">ZOOM</span> Métier</h6>
                <div class="row">
                    <div class="col-lg-6 img-container">
                        <img src="{{ imgpath }}" alt="{{ Article.titre }}"/>

                    </div>
                    <div class="col-lg-6 txt-container v-flex justify-content-center align-items-center">
                        <div class="">
                            {% if Article is defined and Article %}
                                <h6 class="justified text-uppercase mb-4">{{ Article.titre }}</h6>
                                <p>{{ Article.chapo|length > 50 ? Article.chapo|slice(0, 300) ~ '...' : Article.chapo }}</p>
                                <div class="text-center mt-4">
                                    <a href="{{ articlepath }}" class="btn button-black">
                                        Lire la suite
                                    </a>
                                </div>
                            {% else %}
                                <p>Aucun article disponible.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section>
            <div class="container">
                <h6 class="text-center"><span style="color: #63BF7A;">ILS RECRUTENT</span> DANS LE MEDICAL</h6>
                <div class="brand-carousel section-padding owl-carousel">
                    {% for boite in boites %}
                        <div class="single-logo">
                            <img src="{{ asset("/uploads/" ~ boite.image) }}"  alt="{{boite.image}}" class="img-fluid" style="width: 94px; height: 75px;" />
                        </div>
                    {% endfor %}
                </div>
            </div>
        </section>
        <section class="la-une">
            <div class="container">
                <h6 class="text-center text-uppercase  mb-5"><span style="color: #63BF7A;">l’entreprise</span> à la une</h6>
                <div class="w-100 card-offre">
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="logo-carde vertical-center">
                                <img class="img-fluid " src="{{ asset('uploads/logos/1233140_84.jpg') }}"  alt="Assistance publique hôpitaux de Paris "/>
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="h-flex justify-content-between w-100">
                                {% if offresMedicaleDentego is not empty %}
                                    {% set total_offres = offresMedicaleDentego|length %}
                                    {% set offres_par_groupe = (total_offres / 4)|round(0, 'ceil') %} <!-- Ajustement pour 4 lignes -->
                                    <div>
                                        <ul>
                                            {% for i in 0..offres_par_groupe-1 %}
                                                {% set index = i %}
                                                {% for j in 0..3 %} <!-- Boucle interne pour afficher 4 offres à la fois -->
                                                    {% if offresMedicaleDentego[index] is defined %}
                                                        <li>
                                                            <i class="fa-solid fa-circle fa-2xs" style="color: #63BF7A; font-size: 7px;"></i>
                                                            <span><a href="https://www.carriere-medical.com/Medical/submitCandidat/{{ offresMedicaleDentego[index].id_offre }}">{{ offresMedicaleDentego[index].intitule }}</a></span>
                                                        </li>
                                                    {% endif %}
                                                    {% set index = index + offres_par_groupe %} <!-- Augmenter l'index pour sauter au prochain groupe -->
                                                {% endfor %}
                                            {% endfor %}
                                        </ul>
                                    </div>
                                {% else %}
                                    <p>Aucune offre disponible actuellement.</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                </div>
                <div class="text-center mt-4"><a href="{{ path('app_Medicale_candidat_search') }}" class="btn button-black">Voir toutes les offres</a></div>
            </div>
        </section>
    </div>
    <script>
        function redirectTo(value) {
            // Encode the value to include it in the URL
            const encodedValue = encodeURIComponent(value);
            // Construct the URL with the encoded value
            const url = `https://www.carriere-medical.com/Medical/lists-offre/?contrat=&localisation=&localisation_field=&q=${encodedValue}`;
            // Redirect to the constructed URL
            window.location.href = url;
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
    <script>
        $('.brand-carousel').owlCarousel({
            loop: true,
            margin: 10,
            autoplay: true,
            autoplayTimeout: 1500, // Augmentez cette valeur pour ralentir la vitesse
            slideBy:2,
            responsive: {
                0: {
                    items: 1
                },
                600: {
                    items: 3
                },
                1000: {
                    items: 8
                }
            }
        })

    </script>
    {% block footer %}
        {% include 'carriereMedicale/footer.html.twig' %}
    {% endblock %}
{% endblock %}
