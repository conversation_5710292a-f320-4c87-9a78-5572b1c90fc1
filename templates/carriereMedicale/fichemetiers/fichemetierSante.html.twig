{% extends 'carriereMedicale/base.html.twig' %}

{% set lettre = app.request.query.get('lettre') %}
{% set page = app.request.query.get('page') %}

{% if lettre and page %}
    {% set title_seo = "Trouvez tous les métiers Medicale qui recrutent | Carriere Medicale| pour les metiers qui commencent par la lettre '" ~ lettre ~ "'. Page : " ~ page %}
    {% set description_seo = "Quels sont les métiers qui commencent par la lettre '" ~ lettre ~ "' et qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines… <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Data analyst, Ebéniste, Facteur… dans la page :"~ page  %}
{% elseif lettre and page is null %}
    {% set title_seo = "Trouvez tous les métiers Medicale qui recrutent | Carriere Medicale| pour les metiers qui commencent par la lettre '" ~ lettre ~ "'"  %}
    {% set description_seo = "Quels sont les métiers qui commencent par la lettre '" ~ lettre ~ "' et qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines… <PERSON><PERSON><PERSON>, Barmaid, <PERSON><PERSON><PERSON>ur, Data analyst, Ebéniste, Facteur… "  %}
{% elseif lettre is null and page %}
    {% set title_seo = "Trouvez tous les métiers Medicale qui recrutent | Carriere Medicale| pour la page'" ~ page ~ "'"  %}
    {% set description_seo = "Quels sont les métiers pour la page '" ~ page ~ "' et qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines… Acheteur, Barmaid, Câbleur, Data analyst, Ebéniste, Facteur… "  %}
{% else %}
    {% set title_seo = "Trouvez tous les métiers Medicale qui recrutent | Carriere Medicale" %}
    {% set description_seo = "Quels sont les métiers Medicale qui recrutent en 2023 ? Consultez la liste des métiers porteurs classés par domaines… Acheteur, Barmaid, Câbleur, Data analyst, Ebéniste, Facteur…"  %}


{% endif %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .alphabet-filter {
            text-align: center;
            padding: 10px;
            margin: 10px;
        }

        .alphabet-filter a {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border: 0.5px solid #ddd; /* Couleur de la bordure */
            border-radius: 4px; /* Coins arrondis */
            text-decoration: none; /* Supprime le soulignement des liens */
            color: #333; /* Couleur du texte */
            font-weight: bold; /* Rend le texte en gras */
            transition: background-color 0.3s, color 0.3s; /* Animation au survol */
        }

        .alphabet-filter a:hover {
            background-color: #faaf40; /* Couleur de fond au survol */
            color: #fff; /* Couleur du texte au survol */
        }

    </style>
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereMedicale/header.html.twig' %}
        {% endblock %}


        <section class="btp-header d-flex flex-column align-items-center" id="">
            <h5 class="text-center text-uppercase mb-0 text-white"> Liste des métiers<br> des domaines<br><br> <span style="color:#63BF7A"> Santé / Paramédical / Social </span></h5>
        </section>

        <section class="section-embauche env">
            {#
            <div class="alphabet-filter">
                {% for lettre in range('A', 'Z') %}
                    <a href="{{ path('app_Medicale_fichemetierMedicale', {'lettre': lettre}) }}">{{ lettre }}</a>
                {% endfor %}
            </div>
            #}
            <div class="container">
                <div class="row">
                    {% for metier in metiersParPage %}
                        <div class="col-lg-4 col-md-6 col-sm-12">
                            <a href="{{ '/Medicale/lists-offre/?q=' ~ metier }}" target="_blank">
                                <div class="w-100 info-button text-center">
                                    <h6>{{ metier }}</h6>
                                </div>
                            </a>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                <!-- Pagination Personnalisée -->

            </div>
        </section>

        {% block footer %}
            {% include 'carriereMedicale/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}