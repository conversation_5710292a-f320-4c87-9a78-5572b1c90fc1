{% extends 'carriereMedicale/base.html.twig' %}

{% set title_seo = "Carrière Medicale - Offres d'emploi - Formation continue dans le secteur de Medicale-LES MÉTIERS DE social" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% set description_seo = "sur Carrière Medicale, recherchez toutes les annonces d'emploi : CDD, CDI, alternance, stage, intérim...-LES MÉTIERS DE social" %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <style>
        .single-logo
        {
            width: 60%;
            margin: auto;
            display: block;
        }
        .single-logo img
        {

        }
        .section-padding{
            padding:60px 0;
        }

        .owl-dots{
            text-align: center;
        }

        .owl-dot {
            display: inline-block;
            height: 15px !important;
            width: 15px !important;
            background-color: #222222 !important;
            opacity: 0.8;
            border-radius: 50%;
            margin: 0 5px;
        }

        .owl-dot.active {
            background-color: #63BF7A !important;
        }
    </style>
    <div class="is-sticky">

        {% block head %}
            {% include 'carriereMedicale/header.html.twig' %}
        {% endblock %}
        <section class="espace-ingenieur-header" style="background-image: url('{{ asset("/carriereMedicale/Front/images/espace-metier-social.png") }}');" id="">
            <h5 class="text-center text-uppercase text-white"><span style="color:#63BF7A">Les offres d’emploi  </span> du domaine <br><span style="color:#63BF7A">social</span> par métier </h5>
            <div class="header-text">
                <div class="container ">
                    <p class="text-white justified">
                        Retrouvez des milliers d’opportunités de carrière d'assistant maternel, d'animateur centre loisirs, éducateur spécialisé …
                </div>
            </div>
        </section>

        <section class="section-embauche">
            <h6 class="text-center text-uppercase mb-5"><span style="color: #63BF7A;">LES POSTES</span> PROPOSés</h6>
            <div class="container">

                <div class="row offres-list">
                    <div class="col-lg-4 col-md-6">
                        <!-- Première colonne de 15 éléments -->
                        <ul style="list-style: none; padding: 0;">
                            <li onclick="redirectTo('Accompagnateur éducatif et social')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Accompagnateur éducatif et social</a>
                            </li>
                            <li onclick="redirectTo('Animateur centre loisirs')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Animateur centre loisirs</a>
                            </li>
                            <li onclick="redirectTo('Assistant de service social')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Assistant de service social</a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <!-- Première colonne de 15 éléments -->
                        <ul style="list-style: none; padding: 0;">
                            <li onclick="redirectTo('Assistant maternel')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Assistant maternel</a>
                            </li>
                            <li onclick="redirectTo('Conseiller en économie sociale et familiale')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Conseiller en économie sociale et familiale</a>
                            </li>
                            <li onclick="redirectTo('Conseiller en insertion professionnelle')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Conseiller en insertion professionnelle</a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-lg-4 col-md-6">
                        <!-- Première colonne de 15 éléments -->
                        <ul style="list-style: none; padding: 0;">
                            <li onclick="redirectTo('Educateur spécialisé')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Educateur spécialisé</a>
                            </li>
                            <li onclick="redirectTo('Moniteur éducateur')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Moniteur éducateur</a>
                            </li>
                            <li onclick="redirectTo('Travailleur familial')">
                                <i class="fa-solid fa-chevron-right fa-2xs" style="color: #63BF7A;font-size: 7px;"></i>
                                <a href="#">Travailleur familial</a>
                            </li>
                        </ul>
                    </div>

                </div>
            </div>
        </section>

        <section class="zoom-metier">
            <div class="container">
                <h6 class="text-center text-uppercase mb-5"><span style="color: #63BF7A;">ZOOM</span> Métier</h6>
                <div class="row">
                    <div class="col-lg-6 img-container">
                        <img src="{{ asset('uploads/article/120200205154936_pte.jpg') }}" alt="{{ asset('uploads/article/120200205154936_pte.jpg') }}"/>
                    </div>
                    <div class="col-lg-6 txt-container v-flex justify-content-center align-items-center">
                        <div class="">
                            <h6 class="justified text-uppercase mb-4">ASSISTANT MATERNEL</h6>
                            <p>L’assistant maternel prend la relève des parents pour s’occuper de leurs enfants. Il est en charge de veiller à l’éducation des enfants, en général de 0 à 3 ans. Aussi connu sous les noms de nourrice ou nounou, l’assistant maternel accueille en général à son domicile un maximum de deux enfants (hors dérogations pour 3 ou 4 enfants. )</p>
                            {# <div class="text-center mt-4"><a href="https://www.carriere-medicale.com/Medicale/actualitesdetails/2780/" class="btn button-black">Lire la suite</a></div> #}
                        </div>
                    </div>
                </div>
            </div>
        </section>
        {#
        <section>
            <div class="container">
                <h6 class="text-center"><span style="color: #63BF7A;">Les entreprises </span> qui recrutent</h6>
                <div class="brand-carousel section-padding owl-carousel">
                    {% for boite in boites %}
                        <div class="single-logo">
                            <img src="{{ asset("/uploads/" ~ boite.image) }}"  alt="{{boite.image}}" class="img-fluid" style="width: 94px; height: 75px;" />
                        </div>
                    {% endfor %}
                </div>
            </div>
        </section>
        #}

    </div>
    <script>
        function redirectTo(value) {
            // Encode the value to include it in the URL
            const encodedValue = encodeURIComponent(value);
            // Construct the URL with the encoded value
            const url = `https://www.carriere-medicale.com/Medicale/lists-offre/?contrat=&localisation=&localisation_field=&q=${encodedValue}`;
            // Redirect to the constructed URL
            window.location.href = url;
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
    <script>
        $('.brand-carousel').owlCarousel({
            loop: true,
            margin: 10,
            autoplay: true,
            autoplayTimeout: 1500, // Augmentez cette valeur pour ralentir la vitesse
            slideBy:2,
            responsive: {
                0: {
                    items: 1
                },
                600: {
                    items: 3
                },
                1000: {
                    items: 6
                }
            }
        })

    </script>
    {% block footer %}
        {% include 'carriereMedicale/footer.html.twig' %}
    {% endblock %}
{% endblock %}
