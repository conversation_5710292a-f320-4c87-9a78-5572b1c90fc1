{% extends 'carriereMedicale/base.html.twig' %}

{% set title = "Contact" %}

{% block title %}{{ title }}{% endblock %}
{% block title_og %}{{ title }}{% endblock %}
{% block title_twitter %}{{ title }}{% endblock %}

{% set description_seo = "" %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}
{% block body %}
    <div class="is-sticky">

        {% block head %}
            {% include 'carriereMedicale/header.html.twig' %}
        {% endblock %}
<section class="contact-header d-flex justify-content-center align-items-center" id="">
    <h5 class="text-center text-uppercase mb-0 text-white"><span style="color:#63BF7A"> Con</span>Tact</h5>
</section>
<section class="login-form">


    <div class="container">
        <form method="POST" action="{{ path('app_Medicale_contact') }}" id="contactForm">
            <div class="info-section">
                <h6>Formulaire de contact</h6>
                <hr>
                <div class="row">
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label for="sujetFormControl">Sujet</label>
                            <select class="form-select form-control" id="sujetFormControl" name="sujet">
                                <option value="">Sélectionnez un sujet</option>
                                <option value="Question Recruteur - Commercial">Question Recruteur - Commercial</option>
                                <option value="Question Recruteur - Technique">Question Recruteur - Technique</option>
                                <option value="Question Partenariat">Question Partenariat</option>
                                <option value="Question Candidat">Question Candidat</option>
                                <option value="Autre">Autre</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label for="inputAdresse">Votre adresse</label>
                            <input type="text" class="form-control" id="inputAdresse" name="adresse" placeholder="Votre adresse">
                        </div>
                    </div>
                    <div class="col-lg-12 col-sm-12">
                        <div class="form-group">
                            <label for="textAreaMessage">Message</label>
                            <textarea class="form-control" id="textAreaMessage" name="message" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <button type="submit" class="btn mb-2 px-4 button-black">Envoyer</button>
                </div>
                <div class="card p-4 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
                    <p class="justified" style=" font-size: 13px;">
                        Les informations recueillies à partir de ce formulaire font l'objet d'un traitement informatique destiné à
                        <strong>Direct Emploi - Direct Performance</strong> pour une gestion <strong>de comptes et clientèle</strong>.
                        Le destinataire de ces données est notre <strong>service commercial</strong>.
                        Conformément à la loi « informatique et libertés » du 6 janvier 1978 modifiée, vous disposez d'un droit
                        d'accès et de rectification aux informations qui vous concernent. Vous pouvez accéder aux informations vous concernant
                        en vous adressant à : <a href="mailto: <EMAIL>" class="yellow"><EMAIL></a>.
                        Vous pouvez également, pour des motifs légitimes, vous opposer au traitement des données vous concernant.
                    </p>
                </div>
            </div>
        </form>

        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            document.querySelector('#contactForm').addEventListener('submit', async function (e) {
                e.preventDefault(); // Empêche la soumission classique du formulaire

                const form = e.target;
                const formData = new FormData(form);

                try {
                    const response = await fetch(form.action, {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (response.ok) {
                        // Afficher SweetAlert pour le succès
                        Swal.fire({
                            title: 'Succès',
                            text: result.success,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            form.reset(); // Réinitialiser le formulaire après envoi
                        });
                    } else {
                        // Afficher SweetAlert pour une erreur
                        Swal.fire({
                            title: 'Erreur',
                            text: result.error || 'Une erreur est survenue.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                } catch (error) {
                    // Afficher SweetAlert pour une erreur inattendue
                    Swal.fire({
                        title: 'Erreur',
                        text: 'Impossible de soumettre le formulaire. Veuillez réessayer plus tard.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        </script>


    </div>
</section>


{% block footer %}
    {% include 'carriereMedicale/footer.html.twig' %}
{% endblock %}
</div>
{% endblock %}