{% extends 'carriereMedicale/base.html.twig' %}

{% set lettre = app.request.query.get('lettre') %}
{% set page = app.request.query.get('page') %}
{% if page %}
    {% set title_seo_part = " : Retrouvez les " %}
    {% if totalCountWithStatusOne > 0 %}
        {% set title_seo_part = title_seo_part ~ totalCountWithStatusOne ~ " postes à pourvoir chez " %}
    {% else %}
        {% set title_seo_part = title_seo_part ~ " postes à pourvoir chez " %}
    {% endif %}
    {% set title_seo = entreprise.raisonSociale ~ title_seo_part ~ entreprise.raisonSociale ~ "| Page : " ~ page %}

    {% set fullAddress = '' %}
    {% if adresse.adresse1 is defined %}{% set fullAddress = fullAddress ~ adresse.adresse1 %}{% endif %}
    {% if adresse.adresse2 is defined %}{% set fullAddress = fullAddress ~ ' - ' ~ adresse.adresse2 %}{% endif %}
    {% if adresse.codePostal is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.codePostal %}{% endif %}
    {% if adresse.nomVille is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.nomVille %}{% endif %}
    {% if totalCountWithStatusOne > 0 %}
        {% set description_seo = entreprise.raisonSociale ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationSociete|raw|nl2br ~ "| Page : "~ page~"|"~ totalCountWithStatusOne ~ " postes" %}
    {% else %}
        {% set description_seo = entreprise.raisonSociale ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationSociete|raw|nl2br ~ "| Page : "~ page %}
    {% endif %}

{% else %}
    {% set title_seo_part = " : Retrouvez les " %}
    {% if totalCountWithStatusOne > 0 %}
        {% set title_seo_part = title_seo_part ~ totalCountWithStatusOne ~ " postes à pourvoir chez " %}
    {% else %}
        {% set title_seo_part = title_seo_part ~ " postes à pourvoir chez " %}
    {% endif %}
    {% set title_seo = entreprise.raisonSociale ~ title_seo_part ~ entreprise.raisonSociale %}

    {% set fullAddress = '' %}
    {% if adresse.adresse1 is defined %}{% set fullAddress = fullAddress ~ adresse.adresse1 %}{% endif %}
    {% if adresse.adresse2 is defined %}{% set fullAddress = fullAddress ~ ' - ' ~ adresse.adresse2 %}{% endif %}
    {% if adresse.codePostal is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.codePostal %}{% endif %}
    {% if adresse.nomVille is defined %}{% set fullAddress = fullAddress ~ ' ' ~ adresse.nomVille %}{% endif %}
    {% if totalCountWithStatusOne > 0 %}
        {% set description_seo = entreprise.raisonSociale ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationSociete|raw|nl2br ~ "| Page : "~ page~"|"~ totalCountWithStatusOne ~ " postes" %}
    {% else %}
        {% set description_seo = entreprise.raisonSociale ~ ' : ' ~ fullAddress ~ ' | ' ~ entreprise.presentationSociete|raw|nl2br ~ "| Page : "~ page %}
    {% endif %}
{% endif %}

{% block title %}{{ title_seo[:150] }}{% endblock %}
{% block title_og %}{{ title_seo[:150] }}{% endblock %}
{% block title_twitter %}{{ title_seo[:150] }}{% endblock %}
{% block meta_description %}{{ description_seo[:550] }}{% endblock %}
{% block meta_description_og %}{{ description_seo[:550] }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo[:550] }}{% endblock %}
{% block body %}
<div class="is-sticky">
    {% block head %}
    {% include 'carriereMedicale/header.html.twig' %}
{% endblock %}
    <style>
        .offres-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .offres-grid .offre-item {
            border: 1px solid #ddd;
            padding: 1rem;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            height: 100%; /* Ensure all cards take full height of their container */
            display: flex;
            flex-direction: column;
            justify-content: space-between; /* Distribute space evenly */
        }

        .offres-grid .offre-item a {
            color: #63BF7A;
            text-decoration: none;
        }

        .offres-grid .offre-item a:hover {
            text-decoration: underline;
        }

        .offres-grid .offre-item .text-secondary {
            color: #6c757d;
        }

        .offres-grid .offre-item .fa-chevron-right {
            margin-right: 5px;
        }

        /* Ensure all cards have the same height */
        .offres-grid .col-md-3 {
            display: flex;
        }

        .offres-grid .col-md-3 > .offre-item {
            flex: 1; /* Make cards stretch to fill the height */
        }

        @media (max-width: 768px) {
            .offres-grid .col-md-3 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .offres-grid .col-md-3 {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }
    </style>
    <section class="ajouter-cv-header" id="">
        <div class="container">
            {% if breadcrumb is defined %}
                {% include '/_partials/_breadcrumb.html.twig'  %}
            {% endif %}
            <h1 class="text-center text-uppercase mb-3" style="font-size: 1.25rem; line-height: 1.4;"><span class="yellow">Emploi</span> {{entreprise.raisonSociale}} ({{ totalCountWithStatusOne }} offres en ligne)</h1>
        </div>
    </section>


    <section class="la-une entreprise-profile">
        <div class="container">
            <div class="w-100 card-entreprise">
                <div class="row">
                    <div class="col-lg-8">
                        <h6>{{entreprise.raisonSociale}}</h6>
                        <p>{{fullAddress}}</p>
                        <a href="{{entreprise.siteWeb}}">{{entreprise.siteWeb}}</a>
                    </div>
                    <div class="col-lg-4">

                        <div class="w-100 vertical-center">
                            {% if entreprise.logo != null %}
                                <div class="logo-entreprise ">
                                    <img src="{{ asset('/uploads/logos/' ~ entreprise.logo) }}" alt="{{entreprise.raisonSociale}}">
                                </div>
                            {% else %}
                                <div class="logo-entreprise ">
                                    <img src="{{ asset('carriereMedicale/Front/images/logo noir.png') }}" alt="{{entreprise.raisonSociale}}">
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="content">
        <div class="container">
            <div class="header-and-text">
                <h6>Présentation de l'entreprise</h6>
                <hr>
                <p class="justified">{{entreprise.presentationSociete|raw}}</p>

            </div>
        </div>

    </section>
    <section class="entreprise-emploi">
        <div class="container">
            {# Préparation dynamique des compteurs #}
            {% set emploiTypes = ['CDI', 'CDD', 'Interim', 'FreeLance / Indépendant'] %}
            {% set emploiCount = 0 %}
            {% set alternanceCount = 0 %}
            {% set stageCount = 0 %}
            {% for offre in offres %}
                {% if offre.contrat in emploiTypes %}
                    {% set emploiCount = emploiCount + 1 %}
                {% elseif offre.contrat == 'Alternance' %}
                    {% set alternanceCount = alternanceCount + 1 %}
                {% elseif offre.contrat == 'Stage' %}
                    {% set stageCount = stageCount + 1 %}
                {% endif %}
            {% endfor %}

            {# Barre de navigation filtres #}
            <div class="menu-offres mb-4 text-center">
                <ul class="nav nav-pills justify-content-center">
                    {% if emploiCount > 0 %}
                        <li class="nav-item">
                            <a class="nav-link filtre-btn active" href="#" data-filter="emploi">
                                Emploi ({{ emploiCount }})
                            </a>
                        </li>
                    {% endif %}
                    {% if alternanceCount > 0 %}
                        <li class="nav-item">
                            <a class="nav-link filtre-btn" href="#" data-filter="alternance">
                                Alternance ({{ alternanceCount }})
                            </a>
                        </li>
                    {% endif %}
                    {% if stageCount > 0 %}
                        <li class="nav-item">
                            <a class="nav-link filtre-btn" href="#" data-filter="stage">
                                Stage ({{ stageCount }})
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>


            <style>
                .nav-pills .nav-link {
                    padding: 0.5rem 1rem;
                    border-radius: 5px;
                }

                .nav-pills .nav-link.active {
                    background-color: #63BF7A !important;
                    color: #fff !important;
                }
                .job-card {
                    background-color: #fff;
                    border-radius: 10px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                    padding: 20px;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    transition: box-shadow 0.2s ease;
                }
                .job-card:hover {
                    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15);
                }
                .job-title {
                    font-size: 1.1rem;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 15px;
                    text-decoration: none;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    min-height: 48px;
                }
                .job-title:hover {
                    color: #63BF7A;
                }
                .job-icon {
                    margin-right: 8px;
                    font-size: 1rem;
                    background: none !important;
                    padding: 0 !important;
                    color: #63BF7A;
                }
                .job-info {
                    font-size: 0.9rem;
                    color: #333;
                }
                .btn-postuler {
                    background-color: #63BF7A !important;
                    color: white !important;
                    border: none !important;
                    padding: 6px 14px;
                    font-size: 0.875rem;
                    border-radius: 5px;
                }
                .btn-postuler:hover {
                    background-color: #d9942e !important;
                }
                .btn-sauvegarder {
                    color: #999;
                    font-size: 0.85rem;
                    text-decoration: none;
                }
                .btn-sauvegarder:hover {
                    color: #63BF7A;
                }
            </style>

            <div class="container-fluid">
                <div class="row pt-4" id="offres-grid">
                    {% for offre in offres %}
                        {% set typeContrat = offre.contrat %}
                        {% if typeContrat in emploiTypes %}
                            {% set filtreContrat = 'emploi' %}
                        {% elseif typeContrat == 'Alternance' %}
                            {% set filtreContrat = 'alternance' %}
                        {% elseif typeContrat == 'Stage' %}
                            {% set filtreContrat = 'stage' %}
                        {% else %}
                            {% set filtreContrat = 'autre' %}
                        {% endif %}

                        <div class="col-12 col-sm-6 col-lg-3 mb-4 d-flex offre-col" data-type="{{ filtreContrat }}">
                            <div class="job-card w-100">
                                <div>
                                    {% if offre.user_user_id_user is defined and (offre.user_user_id_user in [993430, 1013221, 1207731]) %}
                                        <a href="{{ offre.rep_url }}" target="_blank" class="job-title">
                                            {{ offre.intitule|raw }}
                                        </a>
                                    {% else %}
                                        <a href="{{ '/Medicale/submitCandidat/' ~ offre.id_offre }}" class="job-title">
                                            {{ offre.intitule|raw }}
                                        </a>
                                    {% endif %}

                                    <div class="job-info d-flex align-items-center mb-2">
                                        <i class="fa-regular fa-calendar job-icon me-2"></i>
                                        <span>{{ offre.date_soumission|date('d/m/Y') }}</span>
                                    </div>
                                    <div class="job-info d-flex align-items-center mb-2">
                                        <i class="fa-solid fa-briefcase job-icon me-2"></i>
                                        <span>{{ offre.contrat }}</span>
                                    </div>
                                    <div class="job-info d-flex align-items-center">
                                        <i class="fa-solid fa-location-dot job-icon me-2"></i>
                                        <span>
                             {% if offre.lieu.ville is not empty or offre.lieu.departement is not empty %}
                                                {{ offre.lieu.ville }}{% if offre.lieu.departement %}, {{ offre.lieu.departement }}{% endif %}
                                            {% else %}
                                                France
                                            {% endif %}
                        </span>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <a href="{{ '/Medicale/submitCandidat/' ~ offre.id_offre }}" class="btn btn-sm btn-postuler">Postuler</a>
                                    <a href="#" class="btn-sauvegarder">
                                        <i class="fa-regular fa-heart" style="color: #63BF7A !important;"></i> Sauvegarder
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <p>Aucune offre trouvée.</p>
                        </div>
                    {% endfor %}
                </div>
            </div>



        </div>
        <script>
            window.addEventListener('load', function () {
                console.log("✅ JS chargé et DOM prêt");

                const tabs = document.querySelectorAll('.filtre-btn');
                const rows = document.querySelectorAll('#offres-grid > .col-12');

                function filterOffers(type) {
                    console.log("Filtrage en cours pour :", type);
                    rows.forEach(row => {
                        const offreType = row.getAttribute('data-type');
                        if (offreType === type) {
                            row.classList.remove('d-none');
                            row.classList.add('d-flex');
                        } else {
                            row.classList.remove('d-flex');
                            row.classList.add('d-none');
                        }
                    });
                }

                tabs.forEach(tab => {
                    tab.addEventListener('click', function (e) {
                        e.preventDefault();
                        tabs.forEach(t => t.classList.remove('active'));
                        this.classList.add('active');
                        const filter = this.getAttribute('data-filter');
                        filterOffers(filter);
                    });
                });

                filterOffers('emploi');
            });
        </script>


    </section>
    {% block footer %}
        {% include 'carriereMedicale/footer.html.twig' %}
    {% endblock %}
</div>



{% endblock %}
