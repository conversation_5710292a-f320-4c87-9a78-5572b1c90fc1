{% extends 'carriereRestauration/base.html.twig' %}
{% block title %}Se connecter{% endblock %}

{% block body %}

<div class="is-sticky">
    {% block head %}
    {% include 'carriereRestauration/header.html.twig' %}
{% endblock %}

    <section class="ajouter-cv-header d-flex justify-content-center align-items-center" id="">
        <h1 class="text-center text-uppercase mb-0" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#e2971b"> Enregistrer un</span> Curriculum Vitae</h1>
    </section>
    <section class="login-form">


        <div class="container">
            {{ form_start(form) }}
                <div class="info-section">
                    <h6>Ajouter votre cv</h6>
                    <hr>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="inputLogin">Titre du CV</label>
                                {{ form_row(form.titre, {'label': false}) }}
                            </div>
                        </div>
                    </div>
                    <div class="d-flex flex-row flex-wrap justify-content-between mb-2">
                        <span class="col-form-label d-inline pt-0">Rendre mon CV visible</span>
                        <div class="d-flex flex-row">
                            {{ form_row(form.isvisible, {'label': false}) }}
                        </div>

                    </div>
                    <div class="d-flex flex-row flex-wrap justify-content-between mb-2">
                        <span class="col-form-label d-inline pt-0">Rendre mon CV anonyme</span>
                        <div class="d-flex flex-row">
                            {{ form_row(form.isanonym, {'label': false}) }}

                        </div>
                    </div>
                </div>
                <div class="info-section">
                    <h6>CV word / PDF</h6>
                    <hr>
                    <div class="row">
                        <div class="col-lg-6">
                            <p><strong>Déposer un CV </strong></p>
                            <div class="form-group">
                                {{ form_row(form.cv_file, {'label': false}) }}
                                <small id="fileHelp" class="form-text text-muted">Taille max. de 1 Mo. Formats acceptés : doc, docx, pdf, rtf.</small>
                            </div>
                        </div>
                    </div>

                </div>


                <div class="text-center">
                    <button type="submit" class="btn mb-2 px-4 button-black">Ajouter un cv</button>
                </div>
            {{ form_end(form) }}
            <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@10/dist/sweetalert2.min.css">
            <script>
                document.addEventListener('DOMContentLoaded', function () {
                    const successMessage = '{{ app.session.flashBag.get("success") | first }}';

                    if (successMessage) {
                        Swal.fire({
                            icon: 'success',
                            title: 'CV ajoutée avec succès',
                            showConfirmButton: true,
                            timer: 1500 // délai en millisecondes
                        });
                    }
                });
            </script>
        </div>
    </section>


    {% block footer %}
        {% include 'carriereRestauration/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}