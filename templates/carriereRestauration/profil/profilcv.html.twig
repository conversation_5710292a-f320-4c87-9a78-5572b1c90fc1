{% extends 'carriereRestauration/base.html.twig' %}
{% block title %}Se connecter{% endblock %}

{% block body %}

<div class="is-sticky">
    {% block head %}
    {% include 'carriereRestauration/header.html.twig' %}
{% endblock %}

    <nav class="navbar navbar-expand-lg submenu"style="position:fixed; z-index: 998; margin-top:72.5px">
        <div class="container">


            <div class="collapse navbar-collapse ms-auto" id="navbarNavSubMenu">

                <ul class="navbar-nav ">
                    <li class="nav-item">
                        <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidat') }}">Mon profil</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidatprofilcv') }}">Mon CV</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidatprofiloffre') }}">Mes offres</a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link click-scroll" href="{{ path('app_BTP_espaceCandidatprofilmail') }}">Mes alertes mail</a>
                    </li>

                </ul>
                <a href="#" class=" ml-auto text-white-50">Se désabonner</a>
            </div>

        </div>
    </nav>

    <section class="candidat-section profil-cv" id="">
        <div class="container">
            <h1 style="font-weight: 500;font-size: 0.75rem; line-height: 1.5">Vous avez enregistré <span class="yellow">1 CV</span>. Vous êtes abonné à <span class="yellow">1 alerte mail</span>.</h1>
            <div class="d-flex flex-row justify-content-between align-items-center flex-wrap sub-title">
                <h6>Liste des CV enregistrés</h6>

                <a  href="{{ path('app_BTP_ajoutercv') }}" class="btn px-4 button-black">Ajouter un CV</a>

            </div>
            <hr>
            <div class="h-flex">
                <p class="m-0">Trier par</p>
                <a href="#" class="h-flex px-2 align-items-center"><i class="fa-solid fa-circle fa-xs" style="color: #40fa74;"></i> <p class="m-0 px-1">En ligne</p></a>
                <a href="#" class="h-flex px-2 align-items-center"><i class="fa-solid fa-circle fa-xs" style="color: #FA4040;"></i> <p class="m-0 px-1">Hors ligne</p></a>

            </div>
            <div class="row">
                {% if cvs != null %}
                    {% for cv in cvs %}
                        <div class="col-lg-6 col-md-6 col-sm-12">
                            <div class="w-100 profil-card">
                                <div class="body-card">
                                    <div class="first-card-section justify-content-between">
                                        <div>
                                            <span class="mr-1" style="font-weight: 700;">{{ cv.titre }}</span>
                                            {% if cv.isvisible ==0 %}
                                                <i class="fa-solid fa-circle fa-xs" style="color: #FA4040;"></i>
                                            {% else %}
                                                <i class="fa-solid fa-circle fa-xs" style="color: #40fa74;"></i>
                                            {% endif %}
                                        </div>
                                        <p class="mb-1 mt-3">Le CV a été consulté 0 fois</p>
                                    </div>
                                        <div class="card-section">
                                        <div class="card-divider"></div>
                                        <div class="section-list">
                                            <div class="v-flex justify-content-between align-items-lg-start align-items-center">
                                                <a href="{{ path('app_BTP_modifiercv', {'id': cv.idcv}) }}" class="text-muted">Modifier</a>

                                                <form method="post" action="{{ path('cv_onoff') }}">
                                                    <input type="hidden" name="cv_id" value="{{ cv.idcv }}">
                                                    {% if cv.isvisible == 0 %}
                                                        <button type="submit" style="border:none;background: none;padding: 0;margin: 0" class="text-muted">Passer le cv en ligne</button>
                                                    {% else %}
                                                        <button type="submit" style="border:none;background: none;padding: 0;margin: 0" class="text-muted">Passer le cv hors ligne</button>
                                                    {% endif %}
                                                </form>

                                                <form method="post" action="{{ path('cv_delete') }}">
                                                    <input type="hidden" name="cv_id" value="{{ cv.idcv }}">
                                                    <button type="submit" style="border:none;background: none;padding: 0;margin: 0" class="text-muted">Supprimer</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}

            </div>

        </div>
    </section>
    <section class="section-embauche">
        <div class="container">
            <h6 class="text-uppercase text-center mb-4"><span style="color: #e2971b;">Un passeport </span>pour l'embauche</h6>
            <div class="row ">
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <a href="#">

                        <div class="w-100 info-button">
                            <p class="text-center button-title">Conseil CV</p>
                            <p class="text-center m-0">Tout savoir sur la conception d’un cv</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <a href="#">

                        <div class="w-100 info-button">
                            <p class="text-center button-title">Conseils lettre de motivation</p>
                            <p class="text-center m-0">Les clés d'une lettre de motivation réussie</p>
                        </div>
                    </a>

                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <a href="#">

                        <div class="w-100 info-button">
                            <p class="text-center button-title">Les métiers de A à Z</p>
                            <p class="text-center m-0">Découvrez notre guide des métiers</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">

                    <a href="#">
                        <div class="w-100 info-button">
                            <p class="text-center button-title">Conseils entretien d’embauche</p>
                            <p class="text-center m-0">L'entretien : un face à face à préparer</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <a href="#">
                        <div class="w-100 info-button">
                            <p class="text-center button-title">Conseils recherche d’emploi</p>
                            <p class="text-center m-0">Comment faire une recherche d'emploi</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <a href="#">
                        <div class="w-100 info-button">
                            <p class="text-center button-title">Les domaines de A à Z</p>
                            <p class="text-center m-0">Découvrez notre guide des domaines</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>


    {% block footer %}
        {% include 'carriereRestauration/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}