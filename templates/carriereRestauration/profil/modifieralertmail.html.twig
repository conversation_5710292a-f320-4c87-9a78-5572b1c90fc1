{% extends 'carriereRestauration/base.html.twig' %}
{% block title %}Se connecter{% endblock %}

{% block body %}

    <div class="is-sticky">
        {% block head %}
            {% include 'carriereRestauration/header.html.twig' %}
        {% endblock %}

        <section class="login-header d-flex justify-content-center align-items-center" id="">
            <h1 class="text-center text-uppercase mb-0 text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#e2971b"> ESPACE </span>CANDIDAT</h1>
        </section>
        <section class="login-form">


            <div class="container">
                {{ form_errors(InscriptionEtape3Form) }}
                {{ form_start(InscriptionEtape3Form) }}
                <div class="info-section">
                    {{ form_row(InscriptionEtape3Form._token) }} <!-- Add the CSRF token here -->
                    <h6>Édition d'une alerte mail</h6>
                    <hr>
                    <div class="row">
                        <div class="col-lg-12 col-sm-12">
                            <div class="form-group">
                                <label for="inputalerte">Nom de mon alerte</label>

                                {{ form_row(InscriptionEtape3Form.nom_alerte, {'label': false}) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="contratFormControl">Type de contrat *</label>
                                {{ form_row(InscriptionEtape3Form.contrat, {'label': false}) }}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="experienceFormControl">Experience</label>
                                {{ form_row(InscriptionEtape3Form.experience, {'label': false}) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="lang2FormControl">Domaine de votre métier * </label>
                                {{ form_row(InscriptionEtape3Form.domaine_activite, {'attr': {'class': 'domaine-activite-liste form-control'},'label': false}) }}
                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="niveau2FormControl">Région *</label>
                                {{ form_row(InscriptionEtape3Form.geo_liste_region, {'attr': {'class': 'geo_liste_region form-control'},'label': false}) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="lang2FormControl">Métier</label>
                                {{ form_row(InscriptionEtape3Form.metier_metier, {'attr': {'class': 'metier-metier-liste form-control'},'label': false}) }}

                            </div>
                        </div>
                        <div class="col-lg-6 col-sm-12">
                            <div class="form-group">
                                <label for="niveau2FormControl">Département</label>
                                {{ form_row(InscriptionEtape3Form.geo_departement, {'attr': {'class': 'geo_departement form-control'},'label': false}) }}
                            </div>
                        </div>
                    </div>
                    <div class="form-check">
                        {{ form_label(InscriptionEtape3Form.handi, 'Handi :', {'label_attr': {'class': 'col-form-label d-inline pt-0'}}) }}

                        <div class="form-check mx-2">
                            {{ form_widget(InscriptionEtape3Form.handi[0], {'attr': {'class': 'form-check-input'}}) }}
                            {{ form_label(InscriptionEtape3Form.handi[0], 'Non', {'label_attr': {'class': 'form-check-label'}}) }}
                        </div>
                        <div class="form-check mx-2">
                            {{ form_widget(InscriptionEtape3Form.handi[1], {'attr': {'class': 'form-check-input'}}) }}
                            {{ form_label(InscriptionEtape3Form.handi[1], 'Oui', {'label_attr': {'class': 'form-check-label'}}) }}
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <button type="submit" class="btn mb-3 px-4 button-black">Enregistrer</button>
                </div>
                <div class="text-center">
                    <a href="{{ path('app_BTP_espaceCandidatprofilmail') }}" type="button" class="btn btn-outline-secondary btn-outline-grey px-4">Retour</a>
                </div>
                {{ form_end(InscriptionEtape3Form) }}

            </div>

        </section>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                var geo_liste_region = document.querySelector('.geo_liste_region');
                var geo_departement = document.querySelector('.geo_departement');
                var idregiondefault = "{{ idregion }}";
                var iddepartementdefault = "{{ iddepartement }}";

                // Simulate fetching data for regions from the server
                fetch('/ajax/get-region/')
                    .then(response => response.json())
                    .then(data => {
                        // Clear the options
                        geo_liste_region.innerHTML = '';

                        // Update the options
                        data.forEach(function (region) {
                            var option = document.createElement('option');
                            option.value = region.id;
                            option.text = region.libelle;

                            // Set selected attribute if it matches the default value
                            if (region.id == idregiondefault) {
                                option.selected = true;
                            }

                            geo_liste_region.add(option);
                        });

                        // If idregiondefault is not found in the fetched data, set it as the first option
                        if (!idregiondefault) {
                            geo_liste_region.options[0].selected = true;
                        }

                        // Fetch departments for the selected region or the default region
                        var selectedgeo_liste_region = geo_liste_region.value || idregiondefault;
                        fetch('/ajax/get-departements/' + selectedgeo_liste_region)
                            .then(response => response.json())
                            .then(data => {
                                // Clear the options
                                geo_departement.innerHTML = '';

                                // Update the options
                                data.forEach(function (departement) {
                                    var option = document.createElement('option');
                                    option.value = departement.id;
                                    option.text = departement.libelle;

                                    // Set selected attribute if it matches the default value
                                    if (departement.id == iddepartementdefault) {
                                        option.selected = true;
                                    }

                                    geo_departement.add(option);
                                });

                                // If iddepartementdefault is not found in the fetched data, set it as the first option
                                if (!iddepartementdefault) {
                                    geo_departement.options[0].selected = true;
                                }
                            })
                            .catch(error => {
                                console.error('Error fetching departments:', error);
                            });
                    })
                    .catch(error => {
                        console.error('Error fetching regions:', error);
                    });

                // Event listener for geo_liste_region change
                geo_liste_region.addEventListener('change', function () {
                    var selectedgeo_liste_region = this.value;

                    // Simulate fetching data from the server
                    fetch('/ajax/get-departements/' + selectedgeo_liste_region)
                        .then(response => response.json())
                        .then(data => {
                            // Clear the options
                            geo_departement.innerHTML = '';

                            // Update the options
                            data.forEach(function (departement) {
                                var option = document.createElement('option');
                                option.value = departement.id;
                                option.text = departement.libelle;
                                geo_departement.add(option);
                            });
                        })
                        .catch(error => {
                            console.error('Error fetching departments:', error);
                        });
                });
            });
        </script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                var domaineActiviteListe = document.querySelector('.domaine-activite-liste');
                var metierMetierListe = document.querySelector('.metier-metier-liste');
                var libelmetier = "{{ motclemetier }}";

                // Simulate fetching data from the server
                fetch('/ajax/get-metiers-with-mot-cle/' + libelmetier)
                    .then(response => response.json())
                    .then(data => {
                        // Update the options
                        data.forEach(function (metier) {
                            var option = document.createElement('option');
                            option.value = metier.id;
                            option.text = metier.libelle_metier;

                            // Set selected attribute for the default metier
                            if (metier.libelle_metier === libelmetier) {
                                option.selected = true;
                            }

                            metierMetierListe.add(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching metiers:', error);
                    });

                // Event listener for domaineActiviteListe change
                domaineActiviteListe.addEventListener('change', function () {
                    var selectedDomaineActivite = this.value;

                    // Simulate fetching data from the server
                    fetch('/ajax/get-metiers/' + selectedDomaineActivite)
                        .then(response => response.json())
                        .then(data => {
                            // Clear the options
                            metierMetierListe.innerHTML = '';

                            // Update the options
                            data.forEach(function (metier) {
                                var option = document.createElement('option');
                                option.value = metier.id;
                                option.text = metier.libelle_metier;
                                metierMetierListe.add(option);
                            });
                        })
                        .catch(error => {
                            console.error('Error fetching metiers:', error);
                        });
                });
            });
        </script>

        {% block footer %}
            {% include 'carriereRestauration/footer.html.twig' %}
        {% endblock %}
    </div>
{% endblock %}