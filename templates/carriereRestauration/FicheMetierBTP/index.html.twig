{% extends 'carriereRestauration/base.html.twig' %}
{% set title_seo = "Fiche metier *** | Carriere BTP" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% set description_seo = "Fiche metier ***" ~"|"~ " Actualité de l'emploi : " %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}

{% block body %}
    <style>
        .card-body {
            padding: 20px;
        }

        .button-black {
            background-color: black;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
        }

        .button-black:hover {
            background-color: #333;
        }

    </style>
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereRestauration/header.html.twig' %}
        {% endblock %}
        <section class="offre-form-header d-flex justify-content-center align-items-center"  id="">
            <h1 class="text-center text-uppercase mb-0" style="font-size: 1.25rem; line-height: 1.5;">
                Fiche Métier<span style="color: #e2971b"> {{ metier.getLibelleMetier() }}</span>
            </h1>
        </section>
{% if fichemetier != null %}
        <div class="container">
            <div class="card p-4 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
                <div class="card p-4 mt-3" style="background-color:#E3E3E3; border-radius:20px;">
                        <h6>{{ metier.getLibelleMetier() }}</h6>
                        <p class="justified">
                            {{ fichemetier.getPetitDescription() }}
                        </p>
                </div>
            </div>
        </div>
        <section class="content">
            <div class="container" >
            <div class="header-and-text contenu_article" >
                <h1 style="font-size: 1.25rem; line-height: 1.5; color: #e2971b"> {{ fichemetier.getQ1() }}</h1>
                <hr>
                <p class="justified">
                    {{ fichemetier.getD1() }}
                </p>
            </div>
                <div class="header-and-text contenu_article" >
                    <h1 style="font-size: 1.25rem; line-height: 1.5; color: #e2971b">{{ fichemetier.getQ2() }}</h1>
                    <hr>
                    <p class="justified">
                        {{ fichemetier.getD2() }}
                    </p>
                </div>
                <div class="header-and-text contenu_article" >
                    <h1 style="font-size: 1.25rem; line-height: 1.5; color: #e2971b">{{ fichemetier.getQ3() }}</h1>
                    <hr>
                    <p class="justified">
                        {{ fichemetier.getD3() }}
                    </p>
                </div>
                <div class="header-and-text contenu_article" >
                    <h1 style="font-size: 1.25rem; line-height: 1.5; color: #e2971b">{{ fichemetier.getQ4() }}</h1>
                    <hr>
                    <p class="justified">
                        {{ fichemetier.getD4() }}
                    </p>
                </div>
                {% if fichemetier.getQ5() != null and fichemetier.getD5() != null %}
                <div class="header-and-text contenu_article" >
                    <h1 style="font-size: 1.25rem; line-height: 1.5; color: #e2971b">{{ fichemetier.getQ5() }}</h1>
                    <hr>
                    <p class="justified">
                        {{ fichemetier.getD5() }}
                    </p>
                </div>
                {% endif %}
               </div>
        </section>
        {% if offres != null %}
        <section class="article-section">
            <div class="container">
                <h6 class="text-center text-uppercase "><span class="yellow">exemples </span>d’offres d’emploi</h6>
                <div class="row">
                    {% for offre in offres %}
                    <div class="col-lg-4 col-md-12 mb-3">
                        <div class="card" style="background-color:#F7F7F7;">
                            <div class="card-header text-center" style="background-color:#F7F7F7;">
                                <h6 style="color:#e2971b;">{{ offre.intitule }}</h6>
                            </div>
                            <div class="card-body d-flex flex-column justify-content-between" style="background-color:#E3E3E3;">
                                <div>
                                    <p class="mb-2"><strong>Lieu :</strong>{{ offre.intitule }}</p>
                                    {% if offre.getOffListeTypeContratIdTypeContrat() == 2 %}
                                        <p class="mb-2"><strong>Type de contrat :</strong>CDI</p>
                                    {% elseif offre.getOffListeTypeContratIdTypeContrat() == 1 %}
                                        <p class="mb-2"><strong>Type de contrat :</strong>CDD</p>
                                    {% elseif offre.getOffListeTypeContratIdTypeContrat() == 4 %}
                                        <p class="mb-2"><strong>Type de contrat :</strong>Interim</p>
                                    {% elseif offre.getOffListeTypeContratIdTypeContrat() == 3 %}
                                        <p class="mb-2"><strong>Type de contrat :</strong>FreeLance/Indépendant</p>
                                    {% elseif offre.getOffListeTypeContratIdTypeContrat() == 6 %}
                                        <p class="mb-2"><strong>Type de contrat :</strong>Alternance</p>
                                    {% elseif offre.getOffListeTypeContratIdTypeContrat() == 8 %}
                                        <p class="mb-2"><strong>Type de contrat :</strong>Stage</p>
                                        {% endif %}
                                    </div>
                            </div>
                            <br>
                                <div class="d-flex justify-content-between mt-auto">
                                    <p class="mb-0">Date : {{offre.getDateSoumission()|format_datetime('long', 'none', locale='fr')}}</p>
                                    <button class="btn btn-primary px-3 py-2 mb-2">Voir l'offre</button>
                                </div>

                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-center mt-4"><a href="https://www.carriere-restauration.com/Res/lists-offre/?contrat=&q{{ metier.getLibelleMetier() }}=&localisation=&localisation_field=" class="btn button-black">Voir toutes les offres  </a></div>
            </div>
        </section>
        {% endif %}
        <style>
            .single-logo
            {
                width: 60%;
                margin: auto;
                display: block;
            }
            .single-logo img
            {

            }
            .section-padding{
                padding:60px 0;
            }

            .owl-dots{
                text-align: center;
            }

            .owl-dot {
                display: inline-block;
                height: 15px !important;
                width: 15px !important;
                background-color: #222222 !important;
                opacity: 0.8;
                border-radius: 50%;
                margin: 0 5px;
            }

            .owl-dot.active {
                background-color: #e2971b !important;
            }
        </style>
        <section>
            <div class="container">
                <h6 class="text-center"><span style="color: #e2971b;">Les entreprises </span>qui recrutent  {{ metier.getLibelleMetier() }}</h6>
                <div class="brand-carousel section-padding owl-carousel">
                    {% for boite in boites %}
                        {% set urlParts = boite.url|split('/') %}
                        {% set id = urlParts[4] %}
                        {% set slug = urlParts[5] %}
                        <div class="single-logo">
                            <a href="{{ path('app_BTP_entreprisesDetails', {id: id, slug: slug}) }}">
                                <img src="{{ asset("/uploads/" ~ boite.image) }}" alt="{{ boite.image }}" class="img-fluid" style="width: 94px; height: 75px;" />
                            </a>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </section>

        <section class="section-embauche env">
            <div class="container">
                <h6 class="text-center"><span style="color: #e2971b;">Les métiers </span><span style="color: black;">proches</span></h6>

                <div class="row">
                    {% for metier in metiers %}
                        <div class="col-lg-4 col-md-6 col-sm-12">
                            <a href="{{ '/BTP/lists-offre/?q=' ~ metier }}" target="_blank">
                                <div class="w-100 info-button text-center">
                                    <h6>{{ metier }}</h6>
                                </div>
                            </a>
                        </div>
                    {% endfor %}
                </div>

            </div>
        </section>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
        <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
        <script>
            $('.brand-carousel').owlCarousel({
                loop: true,
                margin: 10,
                autoplay: true,
                autoplayTimeout: 1500, // Augmentez cette valeur pour ralentir la vitesse
                slideBy:2,
                responsive: {
                    0: {
                        items: 1
                    },
                    600: {
                        items: 3
                    },
                    1000: {
                        items: 8
                    }
                }
            })

        </script>
{% else %}
    <div class="container">
        <div class="card p-4 mt-3" style="background-color:#F9F9F9; border-radius:20px;">
            <div class="card p-4 mt-3" style="background-color:#E3E3E3; border-radius:20px;">
                <h6>Fiche metier pour {{ metier.getLibelleMetier() }} non disponible pour le moment </h6>

            </div>
        </div>
    </div>
        {% endif %}
        {% block footer %}
        {% include 'carriereRestauration/footer.html.twig' %}
    {% endblock %}
    </div>
{% endblock %}