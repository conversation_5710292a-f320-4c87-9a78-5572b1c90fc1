{% extends 'carriereBTP/base.html.twig' %}
{% block title %}Edit Fiche Metier | Carriere BTP{% endblock %}

{% block body %}
    <div class="is-sticky">
        {% block head %}
            {% include 'carriereBTP/header.html.twig' %}
        {% endblock %}

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
        <section class="ajouter-cv-header" id="">
            <div class="container">
                <h5 class="text-center text-uppercase mb-3"><span style="color:#e2971b">Fiche  </span>metier  {{ metier.getLibelleMetier }}</h5>
            </div>
        </section>
        <div class="container">
            <form method="post" action="{{ path('app_BTP_admin_pushUpdatedFicheMetiertobase') }}" enctype="multipart/form-data">
                <input type="hidden" name="idmetier" value="{{ metier.getIdMetier() }}">
                <div class="info-section">
                    <h6>Des informations sur la fiche</h6>
                    <hr>
                    <div class="form-group">
                        <label for="nomMetier">Nom Metier *</label>
                        <input type="text" class="form-control" id="nomMetier" value="{{ metier.getLibelleMetier }}" disabled name="nomMetier" placeholder="Nom Metier">
                    </div>
                    <div class="form-group">
                        <label for="smalldesc">Petit paragraphe descriptif du métier</label>
                        <textarea class="form-control" id="smalldesc" name="smalldesc" rows="4"></textarea>
                    </div>
                </div>
                <div class="info-section">
                    <div class="form-group">
                        <label for="titre1">Titre 1 </label>
                        <input type="text" class="form-control" id="titre1" value="Qu’est-ce qu’un (nom du metier)" name="titre1">
                    </div>
                    <div class="form-group">
                        <label for="paragraphe1">Paragraphe pour titre 1 </label>
                        <textarea class="form-control" id="paragraphe1" name="paragraphe1" rows="4"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="titre2">Titre 2 </label>
                        <input type="text" class="form-control" id="titre2" value="Quelles sont les missions d’un (nom du metier)" name="titre2">
                    </div>
                    <div class="form-group">
                        <label for="paragraphe2">Paragraphe pour titre 2 </label>
                        <textarea class="form-control" id="paragraphe2" name="paragraphe2" rows="4"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="titre3">Titre 3 </label>
                        <input type="text" class="form-control" id="titre3" value="Quelles sont les qualités requises pour réussir en tant qu’(nom du metier)" name="titre3">
                    </div>
                    <div class="form-group">
                        <label for="paragraphe3">Paragraphe pour titre 3 </label>
                        <textarea class="form-control" id="paragraphe3" name="paragraphe3" rows="4"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="titre4">Titre 4 </label>
                        <input type="text" class="form-control" id="titre4" value="Quelles sont les formations demandées pour exercer le métier (nom du metier)" name="titre4">
                    </div>
                    <div class="form-group">
                        <label for="paragraphe4">Paragraphe pour titre 4 </label>
                        <textarea class="form-control" id="paragraphe4" name="paragraphe4" rows="4"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="titre5">Titre 5 </label>
                        <input type="text" class="form-control" id="titre5" value="Quelles sont les évolutions possibles après le métier de {{ metier.getLibelleMetier }}" name="titre5">
                    </div>
                    <div class="form-group">
                        <label for="paragraphe5">Paragraphe pour titre 5 </label>
                        <textarea class="form-control" id="paragraphe5" name="paragraphe5" rows="4"></textarea>
                    </div>
                </div>
                <div class="text-center">
                    <a href="{{ path('app_BTP_admin_EditMetier') }}" type="submit" class="btn mb-2 px-4 button-black">Annuler</a>
                    <button type="button" id="fill-fields" style="background-color: #ffca2c" class="btn mb-2 px-4 button-black">Générer avec GPT-4</button>
                    <button type="submit" style="background-color: #ffca2c" class="btn mb-2 px-4 button-black">Créer</button>
                </div>
            </form>
        </div>
        {% block footer %}
            {% include 'carriereBTP/footer.html.twig' %}
        {% endblock %}
    </div>

    <script>
        document.getElementById('fill-fields').addEventListener('click', async () => {
            const metier = "{{ metier.getLibelleMetier }}";

            try {
                const response = await fetch('https://www.carriere-btp.com/BTP/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ metier })
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const data = await response.json();
                console.log(data);

                // Supposons que les sections soient séparées par des "\n\n"
                const sections = data.split('\n\n');
                document.getElementById('smalldesc').value = sections[0];
                document.getElementById('paragraphe1').value = sections[1];
                document.getElementById('paragraphe2').value = sections[2];
                document.getElementById('paragraphe3').value = sections[3];
                document.getElementById('paragraphe4').value = sections[4];
                document.getElementById('paragraphe5').value = sections[5];
            } catch (error) {
                console.error('There was a problem with the fetch operation:', error);
            }
        });
    </script>
{% endblock %}
