{% extends 'carriereBTP/base.html.twig' %}
{% block title %}Nos conseils recrutement et emploi entretien d'embauche | Carriere BTP{% endblock %}

{% block body %}
<div class="is-sticky">
    {% block head %}
    {% include 'carriereBTP/header.html.twig' %}
{% endblock %}

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <section class="ajouter-cv-header" id="">
        <div class="container">
            <h5 class="text-center text-uppercase mb-3"><span style="color:#e2971b">Modifier  </span>un metier</h5>
        </div>
    </section>
    <div class="container">
        {# votre_template.twig #}

        <form method="post" action="{{ path('app_BTP_admin_pushUpdatedMetiertobase') }}" enctype="multipart/form-data">
            <input type="hidden" name="idmetier" value="{{ metier.getIdMetier() }}">
            <input type="hidden" name="idseo" value="{{ Seo.getIdSeoEditReferencement }}">
            <div class="info-section">
                <h6>Des informations sur le métier</h6>
                <hr>
                <div class="form-group">
                    <label for="nomMetier">Nom Metier *</label>
                    <input type="text" class="form-control" id="nomMetier" value="{{ metier.getLibelleMetier }}" name="nomMetier" placeholder="Nom Metier">
                </div>
                <div class="form-group">
                    <label for="numeroSecteur">Numéro de Secteur *</label>
                    <select class="form-control" id="numeroSecteur" name="numeroSecteur">
                        <option value="4" {% if metier.getDomaineIdDomaine == 4 %}selected{% endif %}> 4 : #BTP / Construction / Génie Civil</option>
                        <option value="11" {% if metier.getDomaineIdDomaine == 11 %}selected{% endif %}> 11 : #Electronique / Electricité</option>
                        <option value="12" {% if metier.getDomaineIdDomaine == 12 %}selected{% endif %}> 12 : #Energie / Environnement</option>
                        <option value="18" {% if metier.getDomaineIdDomaine == 18 %}selected{% endif %}> 18 : #Immobilier / Urbanisme / Aménagement</option>
                        <option value="46" {% if metier.getDomaineIdDomaine == 46 %}selected{% endif %}> 46 : #Architecture / Art / Design</option>
                    </select>
                </div>

            </div>

            <div class="info-section">
                <h6>Partie référencement SEO</h6>
                <hr>
                <div class="form-group">
                    <label for="preposition">Preposition *</label>
                    <input type="text" class="form-control" id="preposition" value="{{ Seo.getPreposition }}" name="preposition" placeholder="(de,l',en,de)...">
                </div>
                <div class="form-group">
                    <label for="imageMetier">L'image disponible</label>
                    {% if Seo.photopath == "" %}
                        <p>Pas d'image disponible</p>
                    {% else %}
                        <img src="{{ asset('/carriereBTP/Front/images/seo/' ~ Seo.photopath) }}" alt="Description de l'image" style="max-width: 500px;max-height: 500px">
                    {% endif %}
                </div>
                <div class="form-group">
                    <label for="imageMetier">Modifer l'image du metier</label>
                    <input type="file" class="form-control" id="imageMetier" name="imageMetier">
                </div>
                <div class="form-group">
                    <label for="motsCles">Mots clés *</label>
                    <input type="text" class="form-control" value="{{ Seo.getKeywords }}" id="motsCles" name="motsCles" placeholder="Mots clés">
                </div>
                <div class="form-group">
                    <label for="paragraphe">Paragraphe *</label>
                    <textarea class="form-control" id="paragraphe"  name="paragraphe" rows="4">{{ Seo.getParagrapheContenu }}</textarea>
                </div>
            </div>

            <div class="text-center">
                <a href="{{ path('app_BTP_admin_EditMetier') }}" style="background-color: #ffca2c" type="submit" class="btn mb-2 px-4 button-black">Annuler</a>
                <button type="submit" class="btn mb-2 px-4 button-black">Modifier</button>
            </div>
        </form>

    </div>
    {% block footer %}
        {% include 'carriereBTP/footer.html.twig' %}
    {% endblock %}
</div>
{% endblock %}