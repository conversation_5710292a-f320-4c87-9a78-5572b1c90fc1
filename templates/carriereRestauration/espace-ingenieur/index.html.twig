{% extends 'carriereRestauration/base.html.twig' %}

{% set title_seo = "Carrière Restauration - Offres d'emploi - Formation continue dans le secteur du Restauration" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% set description_seo = "sur Carrière Restauration, recherchez toutes les annonces d'emploi : CDD, CDI, alternance, stage, intérim..." %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}

{% block body %}
    <style>
        .single-logo
        {
            width: 60%;
            margin: auto;
            display: block;
        }
        .single-logo img
        {

        }
        .section-padding{
            padding:60px 0;
        }

        .owl-dots{
            text-align: center;
        }

        .owl-dot {
            display: inline-block;
            height: 15px !important;
            width: 15px !important;
            background-color: #222222 !important;
            opacity: 0.8;
            border-radius: 50%;
            margin: 0 5px;
        }

        .owl-dot.active {
            background-color: #e2971b !important;
        }
    </style>
<div class="is-sticky">

    {% block head %}
    {% include 'carriereRestauration/header.html.twig' %}
{% endblock %}
    <section class="espace-ingenieur-header" id="">
        <h1 class="text-center text-uppercase text-white" style="font-size: 1.25rem; line-height: 1.5;"><span style="color:#e2971b">ESPACE MANAGERS : </span> Des opportunités de carrières</h1>
        <div class="header-text" style="margin-top: 120px">
            <div class="container ">
                <h6 class="text-uppercase text-white">BIENVENUE DANS NOTRE ESPACE MANAGERS</h6>
                <p class="text-white justified">Les métiers d’ingénieurs dans le domaine du Restauration sont nombreux. Nous avons choisi de sélectionner une vingtaine de métiers d’ingénieur, les plus représentatifs et souvent les plus recherchés par les entreprises. En un clic, vous pouvez postuler aux offres en relation avec ces métiers d’ingénieurs. Vous pouvez également retrouver régulièrement des témoignages et interviews de professionnels du recrutement qui recherchent des ingénieurs dans le cadre de mission de recrutement pour de nombreux entreprises du Restauration. Avec nos zoom métiers, nous vous donnons toutes les informations pour découvrir des métiers d’ingénieurs dans des horizons très différents.</p>
            </div>
        </div>
    </section>

    <section class="section-embauche">
        <h6 class="text-center text-uppercase mb-5"><span style="color: #e2971b;">LES POSTES</span> PROPOSés</h6>
        <div class="container">

            <div class="row offres-list">
                <div class="col-lg-4 col-md-6">
                    <!-- Première colonne -->
                    <ul style="list-style: none; padding: 0;">
                        <li onclick="redirectTo('Manager de restaurant')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Manager de restaurant</a>
                        </li>
                        <li onclick="redirectTo('Directeur de restaurant')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Directeur de restaurant</a>
                        </li>
                        <li onclick="redirectTo('Directeur restauration rapide')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Directeur restauration rapide</a>
                        </li>
                        <li onclick="redirectTo('Directeur d’hôtel')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Directeur d’hôtel</a>
                        </li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-6">
                    <!-- Deuxième colonne -->
                    <ul style="list-style: none; padding: 0;">
                        <li onclick="redirectTo('Directeur de centre de loisirs')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Directeur de centre de loisirs</a>
                        </li>
                        <li onclick="redirectTo('Directeur de gites ruraux')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Directeur de gites ruraux</a>
                        </li>
                        <li onclick="redirectTo('Directeur cafétaria')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Directeur cafétaria</a>
                        </li>
                        <li onclick="redirectTo('Directeur de camping')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Directeur de camping</a>
                        </li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-6">
                    <!-- Troisième colonne -->
                    <ul style="list-style: none; padding: 0;">
                        <li onclick="redirectTo('Responsable restauration')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Responsable restauration</a>
                        </li>
                        <li onclick="redirectTo('Responsable de salle')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Responsable de salle</a>
                        </li>
                        <li onclick="redirectTo('Responsable hôtelier')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Responsable hôtelier</a>
                        </li>
                        <li onclick="redirectTo('Responsable bar')">
                            <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b;font-size: 7px;"></i>
                            <a href="#">Responsable bar</a>
                        </li>
                    </ul>
                </div>
            </div>

        </div>
    </section>

    <section class="zoom-metier">
        <div class="container">
            <h6 class="text-center text-uppercase mb-5"><span style="color: #e2971b;">ZOOM</span> Métier</h6>
            <div class="row">
                <div class="col-lg-6 img-container">
                    <img src="{{ imgpath }}" alt="{{ Article.titre }}"/>

                </div>
                <div class="col-lg-6 txt-container v-flex justify-content-center align-items-center">
                    <div class="">
                        {% if Article is defined and Article %}
                            <h6 class="justified text-uppercase mb-4">{{ Article.titre }}</h6>
                            <p>{{ Article.chapo|length > 50 ? Article.chapo|slice(0, 300) ~ '...' : Article.chapo }}</p>
                            <div class="text-center mt-4">
                                <a href="{{ articlepath }}" class="btn button-black">
                                    Lire la suite
                                </a>
                            </div>
                        {% else %}
                            <p>Aucun article disponible.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section>
        <div class="container">
        <h6 class="text-center"><span style="color: #e2971b;">ILS RECRUTENT</span> DANS LA RESTAURATION</h6>
        <div class="brand-carousel section-padding owl-carousel">
            {% for boite in boites %}
                <div class="single-logo">
                    <img src="{{ asset("/uploads/" ~ boite.image) }}"  alt="{{boite.image}}" class="img-fluid" style="width: 94px; height: 75px;" />
                </div>
            {% endfor %}
        </div>
        </div>
    </section>
    <section class="la-une">
        <div class="container">
            <h6 class="text-center text-uppercase  mb-5"><span style="color: #e2971b;">l’entreprise</span> à la une</h6>
            <div class="w-100 card-offre">
                <div class="row">
                    <div class="col-lg-2 col-sm-12">
                        <div class="logo-carde vertical-center">
                            <img class="img-fluid " src="https://www.carriere-restauration.com/uploads/logos/1233056_3.jpg"  alt="Job&talent"/>
                        </div>
                    </div>
                    <div class="col-lg-10 col-sm-12">
                        <div class="h-flex justify-content-between w-100">
                            {% if offresBtpNgeIng is not empty %}
                                {% set total_offres = offresBtpNgeIng|length %}
                                {% set offres_par_ul = (total_offres / 3)|round(0, 'ceil') %}
                                <div>
                                    <ul>
                                        {% for i in 0..offres_par_ul-1 %}
                                            {% if offresBtpNgeIng[i] is defined %}
                                                <li>
                                                    <i class="fa-solid fa-circle fa-2xs" style="color: #e2971b; font-size: 7px;"></i>
                                                    <span><a href="https://www.carriere-restauration.com/Res/submitCandidat/{{ offresBtpNgeIng[i].id_offre }}">{{ offresBtpNgeIng[i].intitule }}</a></span>
                                                </li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </div>
                                <div>
                                    <ul>
                                        {% for i in offres_par_ul..(2 * offres_par_ul - 1) %}
                                            {% if offresBtpNgeIng[i] is defined %}
                                                <li>
                                                    <i class="fa-solid fa-circle fa-2xs" style="color: #e2971b; font-size: 7px;"></i>
                                                    <span><a href="https://www.carriere-restauration.com/Res/submitCandidat/{{ offresBtpNgeIng[i].id_offre }}">{{ offresBtpNgeIng[i].intitule }}</a></span>
                                                </li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </div>
                                <div>
                                    <ul>
                                        {% for i in (2 * offres_par_ul)..(3 * offres_par_ul - 1) %}
                                            {% if offresBtpNgeIng[i] is defined %}
                                                <li>
                                                    <i class="fa-solid fa-circle fa-2xs" style="color: #e2971b; font-size: 7px;"></i>
                                                    <span><a href="https://www.carriere-restauration.com/Res/submitCandidat/{{ offresBtpNgeIng[i].id_offre }}">{{ offresBtpNgeIng[i].intitule }}</a></span>
                                                </li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% else %}
                                <p>Aucune offre disponible actuellement.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

            </div>
            <div class="text-center mt-4"><a href="{{ path('app_Res_candidat_search') }}" class="btn button-black">Voir toutes les offres</a></div>
        </div>
    </section>
</div>
    <script>
        function redirectTo(value) {
            // Encode the value to include it in the URL
            const encodedValue = encodeURIComponent(value);
            // Construct the URL with the encoded value
            const url = `https://www.carriere-restauration.com/Res/lists-offre/?contrat=&localisation=&localisation_field=&q=${encodedValue}`;
            // Redirect to the constructed URL
            window.location.href = url;
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
    <script>
        $('.brand-carousel').owlCarousel({
            loop: true,
            margin: 10,
            autoplay: true,
            autoplayTimeout: 1500, // Augmentez cette valeur pour ralentir la vitesse
            slideBy:2,
            responsive: {
                0: {
                    items: 1
                },
                600: {
                    items: 3
                },
                1000: {
                    items: 8
                }
            }
        })

    </script>
    {% block footer %}
        {% include 'carriereRestauration/footer.html.twig' %}
    {% endblock %}
    {% endblock %}
