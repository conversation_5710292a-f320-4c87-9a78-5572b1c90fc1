{% if path == '/BTP/home/' %}
    {% set prefix = '/BTP/lists-offre/' %}
{% elseif path == "/theme/offres-stage" %}
    {% set prefix = '../lists-offre/stage-' %}
{% elseif path == '/theme/offres-alternance' %}
    {% set prefix = '../lists-offre/alternance-' %}
{% elseif path == "/theme/offre-emploi-jeune-diplome" %}
    {% set prefix = '../' %}
{% elseif path == "/theme/offres-emploi-interim" %}
    {% set prefix = '../lists-offre/interim-' %}
{% elseif path == "/theme/offres-emploi-cabinet-de-recrutement" %}
    {% set prefix = '../lists-offre/cdi-' %}
{% else %}
    {% set prefix = '/BTP/lists-offre/' %}
{% endif %}
<section style="margin-left: auto; margin-right: auto;">
    <h1 class="text-center text-uppercase mb-5" style="font-size: 1.25rem; line-height: 1.5;">Métiers</h1>
    <div class="menu-offres">
        <div id="section_1" class="container" >
            <div class="h-flex justify-content-between offres-list">
                {% set totalMetiers = listeMetiers | length %}
                {% set metiersParColonne = (totalMetiers / 5) | round(0, 'ceil') %}
                {% set counter = 0 %}
                <ul class="departement-list" style="list-style: none; padding: 0;">
                    {% for metier in listeMetiers %}
                    {% set counter = counter + 1 %}
                    {% if counter > metiersParColonne %}
                </ul>
                <ul class="departement-list" style="list-style: none; padding: 0;">
                    {% set counter = 1 %}
                    {% endif %}
                    <li>
                        <i class="fa-solid fa-chevron-right fa-2xs" style="color: #e2971b; font-size: 7px;"></i>
                        <a href="{{ prefix ~ 'france-metier-' ~ struct_home_search(metier.LibelleMetier) ~ '-secteurs' }}">{{ metier.LibelleMetier }}</a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</section>

