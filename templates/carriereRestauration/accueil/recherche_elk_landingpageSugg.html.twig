<div class="form-padding">
    <form action="{{ path('app_Res_candidat_search') }}" method="get">
        <div class="job-search-card">
            <div class="job-icon text-center">
                <i class="fa-solid fa-briefcase" style="color: #ffffff;font-size: 3rem;"></i>
            </div>
            <div class="job-form">
                <div class="form-item ">
                    {{ form_widget(searchForm.contrat, {'attr': { 'class':"form-select", 'name':"contrat", 'id':"contrat"}}) }}
                </div>

                <div class="form-item middle-form">
                    <select name="localisation" id="inputlocalisation" class="form-control" aria-describedby="localisationHelp">
                        <option value="">Localisation</option>
                        <!-- Les options supplémentaires doivent être ajoutées ici -->
                        <!-- Par exemple : <option value="option1">Option 1</option> -->
                    </select>

                </div>

                <div class="form-item">
                    {{ form_widget(searchForm.q)  }}
                </div>

            </div>
            <div class="text-center title-col job-button" >
                <button aria-label="Recherche"  type="submit" class="btn w-100 px-4 button-black">Recherche</button>
            </div>
        </div>
    </form>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        var combinedData = {{ combinedData|json_encode|raw }};
        var selectBox = document.getElementById('inputlocalisation');
        if (selectBox) {
            // Ajoutez une option par défaut
            selectBox.innerHTML = '<option value="">Selectionnez une localisation</option>';
            // Supposons que combinedData est déjà défini
            combinedData.forEach(function(data) {
                var option = new Option(data.libelle );
                selectBox.appendChild(option);
            });
        }
    });
</script>
