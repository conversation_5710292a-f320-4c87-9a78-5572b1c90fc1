<div id="cv-edit-form" class="form_box"> 
	<h2 style="text-align: center;">Modifier votre CV</h2>
	<br>

	{{ form_start(UpdateCV, {'action': path('app_espace_candidat_gestion_cv'), 'method': 'POST'}) }}

	<div class="form_box">
		<h3>Déposer votre CV</h3>
		<br><br>
		<table class="formulaire">
			<tr>
				<td>
				{% if user_cv is defined and user_cv is not null %}
					{{ form_widget(UpdateCV.file)}}
				{% else %}
					{{ form_widget(UpdateCV.file, {'attr': {'required' : 'required'}}) }}
				{% endif %}
				<br></td>
			</tr>
		</table>
		<br><br>
	</div>

	<div class="form_box">
		<h3>Paramètres de confidentialité</h3>
		<table class="formulaire">
			{% if user_cv is defined and user_cv is not null %}
				{% set isVisible, isAnonym = user_cv.getIsVisible(), user_cv.getIsAnonym() %}
			{% else %}
				{% set isVisible, isAnonym = 2, 2 %}
			{% endif %}
			<tr>
				<td style="text-align = left;">Rendre mon CV visible
					<i>(votre CV sera consultable par les recruteur)</i>
				</td>
				<td>{{ form_widget(UpdateCV.cv_visible[0], {'attr': { 'checked' : isVisible == 1 }}) }}&nbsp;{{ form_label(UpdateCV.cv_visible[0])}}&emsp;
					{{ form_widget(UpdateCV.cv_visible[1], {'attr': { 'checked' : isVisible == 0 }}) }}&nbsp;{{ form_label(UpdateCV.cv_visible[1])}}
				</td>
			</tr>
			<tr>
				<td>Rendre mon CV anonyme
					<i>(les recruteurs pourront télécharger votre CV, veillez à utiliser un CV)</i>
				</td>
				<td>{{ form_widget(UpdateCV.cv_anonyme[0], {'attr': { 'checked' : isAnonym == 1 }}) }}&nbsp;{{ form_label(UpdateCV.cv_anonyme[0])}}&emsp;
					{{ form_widget(UpdateCV.cv_anonyme[1], {'attr': { 'checked' : isAnonym == 0 }}) }}&nbsp;{{ form_label(UpdateCV.cv_anonyme[1])}}
				</td>
			</tr>
		</table>
	</div>
	<br>
	<button type="submit" id="cv-save-btn">Enregistrer</button>
	{{ form_end(UpdateCV, {'render_rest': false}) }}
	
</div>
