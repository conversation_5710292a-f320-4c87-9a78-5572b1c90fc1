<h2 style="text-align: center;">Mon CV</h2>

<div id="mon_cv">
	{% if user_cv is defined and user_cv is not null %}
		{% set fileName = user_cv.getNomFichierCvStockage() %}
		{% if ( fileName | exten) == "doc" %}
			DOCX FILE FOUND
		{% else %}
			{% if (fileName | extenImg == "pdf") %}
				<embed src={{"/uploads_test/cv_cand_save/"~ fileName}} width="790" height="1000" type="application/pdf"/>
			{% elseif (fileName | extenImg == "img") %}
				<img src={{"/uploads_test/cv_cand_save/"~ fileName}} width="100%" alt="cv img" height="auto" />
			{% else %}
				Fichier déposé invalide.
			{% endif %}
		{% endif %}
		
		{#% set wordFile = '/images/direct-emploi/test-cv-doc.docx' %}
		{% set pdfFile = '/images/direct-emploi/test-cv-doc.pdf' %}
		{% set conv_cmd = "unoconv -f pdf '" ~ wordFile ~ "' -o '" ~ pdfFile ~ "'" %}
		{% set output = '' %}
		{% set returnVar = 0 %}
		{% set result = exec(conv_cmd ~ " 2>&1") %}
		{% set output = result.output %}
		{% set returnVar = result.return_var %}
		{% if returnVar == 0 %}
			<embed src="{{ pdfFile }}" width="790" height="1000" type="application/pdf"/>
		{% else %}
			<p>Erreur lors de la conversion du fichier Word en PDF : {{ output }}</p>
		{% endif %#}
	{% else %}
		Aucun document déposé.
	{% endif %}

</div>

<div class="floatR"> <br>
	<button id="redir_update_cv" onclick="loadContent('espaceCandidat/gestionCV')">
		<a class="submit sub_jaune sub_large" title="Modifier mon CV">
			Modifier mon CV
		</a>
	</button>
	<br><br>
	<em>Modifier votre CV<br> candidat pour le<br> mettre à jour</em>
</div>
