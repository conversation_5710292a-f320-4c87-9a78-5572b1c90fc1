<header class="head clearfix">
	<div id="bt_menu" class="smart bt bt_menu"></div>
	<a class="smart bt bt_user" id="bt_user" href="#" title="&nbsp;">&nbsp;</a>
	<div class="menu_user clearfix">
		<div class="site">
			<div class="box_user">
				<div class="item menu_cand" onmouseover="showSMenu('smenu','candidat')" onmouseout="hideSMenu('smenu')">
					<a class="niv1" href="javascript:void(0)" title="Candidat">Candidat</a>
					<div class="smenu">
						<div class="int">
							<div class="smart">
								<a href="#" title="Connexion">Connexion</a>
							</div>
							<div class="ordi">
								<form class="form_menu" method="post" action="#">
									<input type="hidden" name="redirect" id="redirect" value="1">
									<input type="hidden" name="passCrypt" id="passCrypt" value="">
									<div class="formulaire clearfix">
										<label for="monLogin" accesskey="l">Login/Email&nbsp;</label>
										<div class="input">
											<input type="text" name="monLogin" id="monLogin" value="" onkeypress="refuserToucheEntree(event,'monBoutonDeConnexion')" placeholder="Login/Email">
										</div>
										<label for="monPassword" accesskey="p">Mot de passe&nbsp;</label>
										<div class="input">
											<input type="password" name="monPassword" id="monPassword" value="" onkeypress="refuserToucheEntree(event,'monBoutonDeConnexion')" placeholder="Mot de passe">
										</div>
										<input type="submit" name="monBoutonDeConnexion" value="Se connecter" id="monBoutonDeConnexion" accesskey="c" onclick="$('passCrypt') . value = cryptPass( $('monPassword').value , '' );" class="sub_jaune">
										<div class="pass_oubli2">
											<a href="#" title="Mot de passe oublié ?">Mot de passe oublié ?</a>
										</div>
									</div>
									<!-- fin class formulaire -->
								</form>
								<hr>
							</div>
							<a href="{{ path('app_register_candidat_e1') }}" title="S’inscrire">S’inscrire</a>
						</div>
					</div>
					<!-- fin SmenuCandidat -->
				</div>
				<!-- fin menuCandidat -->
				<div class="item menu_recrut" onmouseover="showSMenu('smenu','recruteur')" onmouseout="hideSMenu('smenu')">
					<a class="niv1" href="javascript:void(0)" title="Recruteur">Recruteur</a>
					<div class="smenu">
						<div class="int">
							<div class="smart">
								<a href="#" title="Connexion">Connexion</a>
							</div>
							<div class="ordi">
								<form class="form_menu" method="post" action="/user/login">
									<input type="hidden" name="redirect" id="redirect" value="1">
									<input type="hidden" name="passCrypt" id="passCrypt" value="">
									<div class="formulaire clearfix">
										<label for="monLogin" accesskey="l">Login/Email&nbsp;</label>
										<div class="input">
											<input type="text" name="monLogin" id="monLoginRecruteur" value="" onkeypress="refuserToucheEntree(event,'monBoutonDeConnexionRecruteur')" placeholder="Login/Email">
										</div>
										<label for="monPassword" accesskey="p">Mot de passe&nbsp;</label>
										<div class="input">
											<input type="password" name="monPassword" id="monPasswordRecruteur" value="" onkeypress="refuserToucheEntree(event,'monBoutonDeConnexionRecruteur')" placeholder="Mot de passe">
										</div>
										<input type="submit" name="monBoutonDeConnexionRecruteur" value="Se connecter" id="monBoutonDeConnexionRecruteur" accesskey="c" onclick="$('passCrypt') . value = cryptPass( $('monPasswordRecruteur').value , '' );" class="sub_jaune">
										<div class="pass_oubli2">
											<a href="#" title="Mot de passe oublié ?">Mot de passe oublié ?</a>
										</div>
									</div>
									<!-- fin class formulaire -->
								</form>
								<hr>
							</div>
							<a href="{{ path('app_register_candidat_e1') }}" title="S’inscrire">S’inscrire</a>
							<hr>
							<div class="ordi">
								<a href="#" title="Les offres proposées">Les offres proposées</a>
								<a href="{{ path('app_offre_commerciale_essai') }}" title="Essai dépôt gratuit">Essai dépôt gratuit</a>
								<a href="#" title="Le réseau Direct Emploi">Le réseau Direct Emploi</a>
							</div>
						</div>
					</div>
				</div>
				<!-- fin recruteur -->
				<div class="item menu_forma" onmouseover="showSMenu('smenu','formateur')" onmouseout="hideSMenu('smenu')">
					<a class="niv1" href="javascript:void(0)" title="Formateur">Formateur</a>
					<div class="smenu">
						<div class="int">
							<div class="smart">
								<a href="#" title="Connexion">Connexion</a>
							</div>
							<div class="ordi">
								<form class="form_menu" method="post" action="#">
									<input type="hidden" name="redirect" id="redirect" value="1">
									<input type="hidden" name="passCrypt" id="passCrypt" value="">
									<div class="formulaire clearfix">
										<label for="monLogin" accesskey="l">Login/Email&nbsp;</label>
										<div class="input">
											<input type="text" name="monLogin" id="monLogin" value="" onkeypress="refuserToucheEntree(event,'monBoutonDeConnexion')" placeholder="Login/Email">
										</div>
										<label for="monPassword" accesskey="p">Mot de passe&nbsp;</label>
										<div class="input">
											<input type="password" name="monPassword" id="monPassword" value="" onkeypress="refuserToucheEntree(event,'monBoutonDeConnexion')" placeholder="Mot de passe">
										</div>
										<input type="submit" name="monBoutonDeConnexion" value="Se connecter" id="monBoutonDeConnexion" accesskey="c" onclick="$('passCrypt') . value = cryptPass( $('monPassword').value , '' );" class="sub_jaune">
										<div class="pass_oubli2">
											<a href="#" title="Mot de passe oublié ?">Mot de passe oublié ?</a>
										</div>
									</div>
								</form>
								<hr>
							</div>
							<a href="{{ path('app_register_candidat_e1') }}" title="S’inscrire">S’inscrire</a>
							<div class="ordi">
								<hr>
								<a href="#" title="L'offre proposée">L'offre proposée</a>
								<a href="#" title="Le réseau Direct Emploi">Le réseau Direct Emploi</a>
							</div>
						</div>
					</div>
				</div>
				<!-- fin formateur -->
			</div>
			<!-- fin box_user -->
		</div>
	</div>
	<!-- fin menu_user -->
	<div class="bandeau">
		<div class="site">
			<div class="logo">
				<a href={{path('directemploi')}}>
					<img src="{{ asset('images/directemploi/directemploi.jpg') }}" alt="logo_direct_emploi"/>
				</a>
			</div>
			<div class="pub_haut">
				<img src="" alt="Pub google">
			</div>
		</div>
	</div>
</header>
<nav class="navi">
	<div class="site">
		<div id="menu_all" class="menu">
			<div id="menu_empl" class="item" onmouseover="hover('smenu_empl')" onmouseout="unHover('smenu_empl')">
				<a class="niv1" href="{{ path('blog_index') }}">Emplois</a>
				<div id="smenu_empl" class="smenu">
					<a href="#">Recherche avancée</a>
					<a href="#">Emploi cabinet de recrutement</a>
					<a href="#">Emploi Jeunes diplômés</a>
					<a href="#">Emploi intérim</a>
					<a href="#">Emploi handicap</a>
				</div>
			</div>
			<div id="menu_alte" class="item">
				<a class="niv1" href="{{ path('blog_index') }}">Alternances</a>
			</div>
			<div id="menu_stag" class="item" onmouseover="hover('smenu_stag')" onmouseout="unHover('smenu_stag')">
				<a class="niv1" href="{{ path('app_theme_stage') }}">Stages</a>
				<div id="smenu_stag" class="smenu">
					<a href="{{ path('app_theme_écoles') }}">Annuaire des écoles</a>
				</div>
			</div>
			<div id="menu_form" class="item">
				<a class="niv1" href="{{ path('blog_index') }}">Formations</a>
			</div>
			<div id="menu_form" class="item">
				<a class="niv1" href="{{ path('app_entreprises') }}">Entreprises</a>
			</div>
			<div id="menu_zoom" class="item" onmouseover="hover('smenu_zoom')" onmouseout="unHover('smenu_zoom')">
				<a class="niv1" href="{{ path('app_zoom_sectoriel', {label: "aidealapersonne"})}}">Zoom Sectoriel</a>
				<div id="smenu_zoom" class="smenu">
					<a href="{{ path('app_zoom_sectoriel', {label: "aidealapersonne"})}}">Aide à la personne</a>
					<a href="{{ path('app_zoom_sectoriel', {label: "btp"})}}">BTP</a>
					<a href="{{ path('app_zoom_sectoriel', {label: "distribution"})}}">Distribution</a>
					<a href="{{ path('app_zoom_sectoriel', {label: "finance"})}}">Finance</a>
					<a href="{{ path('app_zoom_sectoriel', {label: "industrie"})}}">Industrie</a>
					<a href="{{ path('app_zoom_sectoriel', {label: "informatique"})}}">Informatique</a>
					<a href="{{ path('app_zoom_sectoriel', {label: "restauration"})}}">Restauration</a>
					<a href="{{ path('app_zoom_sectoriel', {label: "medical"})}}">Santé</a>
				</div>
			</div>
			<div id="menu_maga" class="item">
				<a class="niv1" href="{{ path('blog_index') }}">Actualités</a>
			</div>
			<div id="menu_conseils" class="item" onmouseover="hover('smenu_conseils')" onmouseout="unHover('smenu_conseils')">
				<a class="niv1" href="{{ path('app_conseil_index') }}">Conseils</a>
				<div id="smenu_conseils" class="smenu">
					<a href="{{ path('app_conseil_listeMetiers') }}">Les métiers de A à Z</a>
					<a href="{{ path('app_conseil_listeDomaines') }}">Les domaines de A à Z</a>
					<a href="{{ path('app_conseil_cv') }}">Le CV</a>
					<a href="{{ path('app_conseil_lm') }}">La lettre de motivation</a>
					<a href="{{ path('app_conseil_ee') }}">L'entretien d'embauche</a>
					<a href="{{ path('app_conseil_re') }}">La recherche d'emploi</a>
				</div>
			</div>
		</div>
	</nav>
