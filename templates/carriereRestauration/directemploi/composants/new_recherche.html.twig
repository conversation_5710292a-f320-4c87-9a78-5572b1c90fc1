


<div class="container recherche">
    <div class="recherche_wrapper">
        <div class="recherche_texte">
            <h2 style="color: #ffc107;text-align:center;"><strong>TROUVEZ VOTRE {{intitule}} PARMI NOS<br> {{nb_offres|number_format(0,'', ' ')}} OFFRES</strong></h2>
            {% if intitule == 'OFFRES' %}
                <h6 style="color: white">CDI, CDD, intérim, alternance, stage...</h6>
            {% endif %}
        </div>

        <div class="recherche_texte_responsive">
            <h2 style="color: #ffc107"><strong>Trouvez votre futur {{intitule | lower }} parmi nos<br> {{nb_offres|number_format(0,'', ' ')}} OFFRES</strong></h2>
            {% if intitule == 'OFFRES' %}
                <h6 style="color: white">CDI, CDD, intérim, alternance, stage...</h6>
            {% endif %}
        </div>
        <div class="formulaire_recherche">
            <form method="get" action="{{ path('app_candidat_search') }}">

                <div class="row mt-2 mb-2 p-0">
                    <div class="col-11">
                        <input type="text" name="q" id="mot_cle" value="" class="form-control" placeholder="Métier, domaine, mots clés">
                    </div>
                </div>
                <div class="row mt-2 mb-2 p-0">
                    <div class="col-11">
                        <input type="text" name="localisation" id="localisation" class="form-control" placeholder="Région, département, ville">
                        <input type="hidden" name="localisation_field" id="localisation_field" >
                    </div>
                </div>
                <div class="row mt-2 mb-2 p-0">
                    <div class="col-11">
                        <input type="text" name="localisation" id="localisation" class="form-control" placeholder="Région, département, ville">
                        <input type="hidden" name="localisation_field" id="localisation_field" >
                    </div>
                </div>
                <div class="row mt-2 mb-2 p-0" style="flex-wrap: nowrap">
                    <div class="col-3">
                        <select class="form-select" name="contrat" id="contrat" onchange="changer_color()">
                        {% if intitule_long == 'OFFRES DE STAGE' %}
                            <option value="8">Stage</option>
                        {% elseif intitule_long == "OFFRES D'ALTERNANCE" %}
                            <option value="6">Alternance</option>
                        {% elseif intitule_long == 'OFFRES EN INTERIM' %}
                            <option value="4">Interim</option>
                        {% elseif intitule_long == 'OFFRES EN CABINET DE RECRUTEMENT' %}
                            <option value="2">CDI</option>
                        {% else %}
                            <option value="">Contrat</option>
                            <option value="2">CDI</option>
                            <option value="1">CDD</option>
                            <option value="4">Interim</option>
                            <option value="3">FreeLance / Indépendant</option>
                            <option value="6">Alternance</option>
                            <option value="8">Stage</option>
                        {% endif %}
                        </select>
                    </div>
                    <div class="col-8">
                            <button type="submit" class="btn btn-warning"><strong>Rechercher</strong></button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
