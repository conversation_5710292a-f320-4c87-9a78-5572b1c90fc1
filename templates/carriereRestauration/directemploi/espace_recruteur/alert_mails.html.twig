<table width="100%" class="listing_offres" cellspacing="0" cellpadding="0">
	<tbody>
		
		{% if (alert_mails) %}
			{% for key, alert_mail in alert_mails %}
				<tr class="listing_offre {{ key % 2 == 0 ? 'even' : 'odd' }} ">
					<td style="padding:10px" class="intitule">
						{{ alert_mail.getLibelle() }}
					</td>
					<td id="note_cv_up">
			            <a {# href="{{ path('cvthequeAlerte/list?search= ~ alert_mail.getIdRecruteurMailAlert()') }}" #} title="Voir les profils">Voir les profils</a>
						|
			            <a {# href="{{ path('(espaceRecruteur/editAlerteMailCv?id_recruteur_cv_veille= ~ alert_mail.getIdRecruteurMailAlert())') }}" #} title="Modifier les critères">Modifier les critères</a>
						|
						{% set alert_mail = alert_mail.getIdRecruteurMailAlert() %}
			            <a {# href="{{ path('espaceRecruteur/deleteAlerteMailCv?id_recruteur_cv_veille= ~ alert_mail.getIdRecruteurMailAlert()') }}" #} class="rouge" title="Supprimer">Supprimer</a>
                        {# /\ #}
                        {# ajouter ça a la ligne #}
    					{# 'update' => 'list_alerte_mail_cv' #}
				    </span>
					</td>
				</tr>
				<tr><td height="10px" colspan="3"></TD></tr>
			{% endfor %}

			{% set i = 1 %}
			{% for i in i..(10 - (alert_mails)|length) %}
				<tr class="listing_offre {{ i % 2 == 0 ? 'even' : 'odd' }} ">
					<td style="padding:10px" class="intitule"></td>
					<td style="padding:10px" id="note_cv_up">
						<span class="intitule" style="padding:10px; color:green;">
            				<a style="color:green" {# href="{{ path('cvthequeAlerte/create') }}" #} title="Enregistrer une nouvelle alerte">Enregistrer une nouvelle alerte</a>
						</span>
					</td>
				</tr>
				{% set i = i + 1 %}
            {% endfor %}
        {% else %}
			<div id="txt_cv_no_com" style="padding-bottom: 10px;">
				Vous n'avez aucune alerte mail
			</div>
			{% set i = 1 %}
			{% for i in i..10 %}
				<tr class="listing_offre {{ i % 2 == 0 ? 'even' : 'odd' }}">
                	<td style="padding:10px" class="intitule"></td>
                	<td style="padding:10px" id="note_cv_up">
                		<span class="intitule" style="padding:10px; color:green;">
                			<a style="color:green" {# href="{{ path('cvthequeAlerte/create') }}" #} title="Enregistrer une nouvelle alerte">Enregistrer une nouvelle alerte</a>
						</span>
					</td>
				</tr>
				{% set i = i + 1 %}
            {% endfor %}
		{% endif %}
	</tbody>
</table>