<h2>Candidatures non retenues</h2>

{% if message_candidatures_ininteressantes == null %}

<div class="listing_candidature_ininteressant">
	<table>

		<thead>
			<tr>
				<td> Nom Candidat </td>
				<td> Prénom </td>
				<td> Date de postulation </td>
				<td> Nom de l'offre </td>
			</tr>
		</thead>

		<tbody>

			{% set compteur_couleur = 0 %}
			{% for id_candidature, infos_candidature in infos_candidatures_ininteressantes %}
				{% if compteur_couleur % 2 == 0 %}
					<tr class='couleur1'>
				{% else %}
					<tr class='couleur2'>
                {% endif %}
				<td> {{ infos_candidature.nom_candidat }} </td>
				<td> {{ infos_candidature.prenom_candidat }} </td>
				<td> {{ infos_candidature.date_candidature|date('Y-m-d') }} </td>
				<td> {{ infos_candidature.nom_offre }} </td>
				
			    {% set compteur_couleur = compteur_couleur + 1 %}
			{% endfor %}
		</tbody>
	</table>
</div>

{% else %}

    <div class="message_candidature">
        {{ message_candidatures_ininteressantes }}
    </div>

{% endif %}