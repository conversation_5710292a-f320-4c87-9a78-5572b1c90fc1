<div class="recruteur_candidatures">

	<div class="tab_recruteur_candidature">
		<div class="recrutG">
			{% for onglet in onglets_recrut_candidatures %}
				<div class="item onglet_gestion">
                    <h2>
						<a href="#" name="{{ onglet['titre'] }}" onclick="switch_div({{ onglet['id'] }})"> {{ onglet['titre'] }} </a>
                   	</h2>
				</div>
			{% endfor %}
        </div>
        
        <div id="recrutD_candidature" class="recrutD">
			<div id="onglet_c0"> {% include '/directemploi/espace_recruteur/candidatures_tri.html.twig' %} </div>
			<div id="onglet_c1"> {% include '/directemploi/espace_recruteur/candidatures_interessantes.html.twig' %} </div>
			<div id="onglet_c2"> {% include '/directemploi/espace_recruteur/candidatures_ininteressantes.html.twig' %} </div>
		</div>
	</div>

</div>

<script type="text/javascript">

	const hideElement = function(Elem2Hide) {
		Elem2Hide.hidden = true;
	}

	const showElement = function(Elem2Show) {
		Elem2Show.hidden = false;
	}

	window.addEventListener("load", function() {
		hideElement(document.getElementById("onglet_c1"));
		hideElement(document.getElementById("onglet_c2"));
	});

	const switch_div = function(show_onglet) {
		hideElement(document.getElementById("onglet_c0"));
		hideElement(document.getElementById("onglet_c1"));
		hideElement(document.getElementById("onglet_c2"));

		$("#" + show_onglet.id).load(" #" + show_onglet.id);
		showElement(show_onglet);
	}
</script>
