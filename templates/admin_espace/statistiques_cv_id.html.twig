{% extends 'admin_espace/_header.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">
    <style>
        /* Ajoutez ici vos styles personnalisés si nécessaire */
    </style>
{% endblock %}

{% block content %}

{# faire un tableau bootstrap avec nom, prenom, email, id_offre, intitule et date_candidature #}
<div class="container">
  <h3 class="my-admin-subtitle-h3">Liste des candidatures</h3>
  {# bouton de téléchargement du tableau #}
<div class="container">
  <a href="{{ path('app_admin_statistiques_cv_id_download', {'id': app.request.attributes.get('_route_params')['id']}) }}" class="btn btn-primary">Exporter les candidatures</a>
</div>
  <div class="table-responsive">
    <table class="table table-striped">
      <thead class="thead-dark">
        <tr>
          {# <th scope="col">#</th> #}
          <th scope="col">Nom</th>
          <th scope="col">Prénom</th>
          <th scope="col">Email</th>
          <th scope="col">ID Offre</th>
          <th scope="col">Intitulé</th>
          <th scope="col">Date de candidature</th>
        </tr>
      </thead>
      <tbody>
        {% for candidature in candidatures %}
        <tr>
          {# <th scope="row">{{ candidature.id_candidature }}</th> #}
          <td>{{ candidature.nom }}</td>
          <td>{{ candidature.prenom }}</td>
          <td>{{ candidature.email }}</td>
          <td>{{ candidature.id_offre }}</td>
          <td>{{ candidature.intitule }}</td>
          <td>{{ candidature.date_candidature|date('d/m/Y') }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div> 
</div>

{# <pre>
    {{ dump(app.request.attributes.get('_route_params')) }}
</pre> #}



{% endblock %}