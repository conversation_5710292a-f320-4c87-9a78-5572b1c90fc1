{% extends "admin_espace/_header.html.twig" %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">

    <style>
        .status-true {
            color: green;
            font-size: 1.5rem;
        }

        .status-false {
            color: red;
            font-size: 1.5rem;
        }

        .table-scripts {
            width: 100%;
            margin-top: 20px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            overflow: hidden;
        }

        .table-scripts th, .table-scripts td {
            text-align: center;
            vertical-align: middle;
        }

        .circle {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            display: inline-block;
        }

        .circle-green {
            background-color: green;
        }

        .circle-red {
            background-color: red;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container">
        <h1 class="text-center">Su<PERSON><PERSON> Scrip<PERSON></h1>

        <!-- Barre de recherche -->
        <div class="d-flex justify-content-between mb-3">
            <input type="text" id="searchBar" class="form-control w-50" placeholder="Rechercher un script...">
            <div>
                <button class="btn btn-outline-primary" onclick="sortTable('script_name')">Trier par Nom</button>
                <button class="btn btn-outline-primary" onclick="sortTable('status')">Trier par Statut</button>
                <button class="btn btn-outline-primary" onclick="sortTable('execution_date')">Trier par Date</button>
            </div>
        </div>

        <!-- Résumé des données -->
        <div class="mb-4">
            <strong>Total des Scripts :</strong> {{ totalScripts }}
            <br>
            <strong>Scripts Réussis / Échoués :</strong> {{ totalSuccess }} / {{ totalFailure }}
        </div>

        <!-- Tableau -->
        <table class="table table-striped table-scripts" id="scriptsTable">
            <thead>
            <tr>
                <th>Nom du Script ({{ totalScripts }})</th>
                <th>Heure de Début</th>
                <th>Heure de Fin</th>
                <th>Statut ({{ totalSuccess }}/{{ totalFailure }})</th>
                <th>Date d'Exécution</th>
                <th>Action</th> <!-- New column for the Launch button -->
            </tr>
            </thead>
            <tbody>
            {% for script in scripts %}
                <tr>
                    <td data-column="script_name">{{ script.script_name }}</td>
                    <td>{{ script.start_time|date('H:i:s') }}</td>
                    <td>{{ script.end_time ? script.end_time|date('H:i:s') : 'Non terminé' }}</td>
                    <td data-column="status">
                        <span class="circle {{ script.status ? 'circle-green' : 'circle-red' }}"></span>
                    </td>
                    <td data-column="execution_date">{{ script.start_time|date('d/m/Y') }}</td>
                    <td>
                        <form class="launch-script-form" data-script-name="{{ script.script_name }}">
                            <button type="submit" class="btn btn-sm btn-primary">Lancer</button>
                        </form>
                    </td>
                </tr>
            {% else %}
                <tr>
                    <td colspan="6">Aucun script enregistré pour le moment.</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
    <script>
        document.querySelectorAll('.launch-script-form').forEach(form => {
            form.addEventListener('submit', function (event) {
                event.preventDefault(); // Prevent the form from submitting normally

                const scriptName = this.dataset.scriptName;

                fetch('{{ path("app_admin_launch_script") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    body: JSON.stringify({ script_name: scriptName }),
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(data.message); // Or display a success message in the UI
                        } else {
                            // Display the error message and available commands
                            let errorMessage = data.message;
                            if (data.available_commands) {
                                errorMessage += '\n\nAvailable commands:\n' + data.available_commands.join('\n');
                            }
                            alert(errorMessage);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Une erreur est survenue. Veuillez vérifier les logs du serveur.');
                    });
            });
        });    </script>
    <script>
        // Recherche dynamique
        document.getElementById('searchBar').addEventListener('input', function () {
            const searchQuery = this.value.toLowerCase();
            const rows = document.querySelectorAll('#scriptsTable tbody tr');

            rows.forEach(row => {
                const scriptName = row.querySelector('td[data-column="script_name"]').innerText.toLowerCase();
                row.style.display = scriptName.includes(searchQuery) ? '' : 'none';
            });
        });

        // Tri des colonnes
        function sortTable(column) {
            const table = document.getElementById('scriptsTable').querySelector('tbody');
            const rows = Array.from(table.rows);

            rows.sort((a, b) => {
                let aValue = a.querySelector(`td[data-column="${column}"]`).innerText.trim();
                let bValue = b.querySelector(`td[data-column="${column}"]`).innerText.trim();

                if (column === 'status') {
                    aValue = a.querySelector(`td[data-column="${column}"] .circle`).classList.contains('circle-green') ? 1 : 0;
                    bValue = b.querySelector(`td[data-column="${column}"] .circle`).classList.contains('circle-green') ? 1 : 0;
                } else if (column === 'execution_date') {
                    const parseDate = (dateStr) => {
                        const [day, month, year] = dateStr.split('/').map(Number);
                        return new Date(year, month - 1, day);
                    };
                    aValue = parseDate(aValue);
                    bValue = parseDate(bValue);
                }

                return aValue > bValue ? 1 : (aValue < bValue ? -1 : 0);
            });

            table.innerHTML = '';
            rows.forEach(row => table.appendChild(row));
        }
    </script>
{% endblock %}