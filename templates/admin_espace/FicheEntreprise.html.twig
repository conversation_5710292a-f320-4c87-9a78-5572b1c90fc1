<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Liste des entreprises</title>

    {% block stylesheets %}
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">

        <style>
            #loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(255, 255, 255, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                display: none;
            }

            .spinner {
                border: 6px solid #f3f3f3;
                border-top: 6px solid #007bff;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .company-image {
                width: 100px;
                height: 100px;
                object-fit: cover;
                border-radius: 8px;
            }
            .table-hover tbody tr:hover {
                background-color: #f8f9fa;
            }
            .action-button {
                background-color: #0d6efd;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                transition: background-color 0.3s ease;
            }
            .action-button:hover {
                background-color: #0b5ed7;
            }
            #editor {
                height: 400px;
                margin-bottom: 20px;
            }
            .swal2-popup {
                width: 800px !important;
                height: auto !important;
            }
        </style>
    {% endblock %}
</head>
<body>

{% block body %}
    <div id="loading-overlay">
        <div class="spinner"></div>
    </div>
    <div class="container mt-5">
        <h1 class="mb-4">Company List</h1>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                <tr>
                    <th scope="col">Company Image</th>
                    <th scope="col">Company Name</th>
                    <th scope="col">Action</th>
                </tr>
                </thead>
                <tbody>
                {% for entreprise in entreprises %}
                    <tr>
                        <td>
                            <img src="{{ asset('uploads/logos/' ~ entreprise.logo) }}" alt="{{ entreprise.raison_sociale }}" class="company-image">
                        </td>
                        <td>{{ entreprise.raison_sociale }}</td>
                        <td>
                            <button class="action-button" onclick="openEditModal({{ entreprise.id_entreprise }})">Modify Description</button>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Roboto&family=Open+Sans&family=Lobster&display=swap" rel="stylesheet">


    <script src="https://cdn.tiny.cloud/1/impmbv273mph5bocy71yc8jonbu25sgaw2jzgzbmkkalg9ap/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>

    <script>
        async function openEditModal(id) {
            const response = await fetch(`/admin/GetFicheEntreprise/${id}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
            });

            const data = await response.json();
            if (data.status !== 'success') {
                Swal.fire('Erreur !', 'Impossible de récupérer la description', 'error');
                return;
            }

            const description = data.description || '';

            const container = document.createElement('div');
            container.innerHTML = `<textarea id="tinymce-editor" class="tinymce-textarea" style="width:100%;">${description}</textarea>`;

            Swal.fire({
                title: 'Modifier la description',
                html: container,
                showCancelButton: true,
                confirmButtonText: 'Modifier',
                cancelButtonText: 'Annuler',
                width: '900px',
                willOpen: () => {
                    setTimeout(() => {
                        tinymce.init({
                            selector: '#tinymce-editor',
                            language: 'fr',
                            language_url: '/js/tinymce/langs/fr.js', // assure-toi que ce fichier existe
                            height: 400,
                            menubar: false,
                            plugins: ['lists', 'link', 'table', 'autolink'],
                            toolbar: 'undo redo | bold italic underline | fontsize fontfamily | forecolor backcolor | alignleft aligncenter alignright alignjustify | lineheight',
                            fontsize_formats: '8px 10px 12px 14px 16px 18px 24px 36px 48px 72px',
                            font_family_formats:
                                'Roboto=Roboto, sans-serif;' +
                                'Open Sans=Open Sans, sans-serif;' +
                                'Lobster=Lobster, cursive;' +
                                'Arial=arial,helvetica,sans-serif;' +
                                'Courier New=courier new,courier;' +
                                'Georgia=georgia,palatino;' +
                                'Tahoma=tahoma,arial,helvetica,sans-serif;' +
                                'Times New Roman=times new roman,times;' +
                                'Verdana=verdana,geneva;',
                            lineheight_formats: "1 1.1 1.2 1.3 1.4 1.5 1.6 1.8 2 2.5 3 4 5",
                            inline_styles: true,
                            style_formats_merge: true,
                            span_wrap: true,
                            formats: {
                                bold: { inline: 'strong', remove: 'all' },
                                italic: { inline: 'em', remove: 'all' },
                                underline: { inline: 'u', remove: 'all' }
                            },
                            valid_children: "+body[style],+p[span],+span[strong|em|u]"
                        });
                    }, 50);
                },
                preConfirm: () => {
                    const content = tinymce.get('tinymce-editor')?.getContent() || '';
                    return fetch(`/admin/UpdateFicheEntreprise/${id}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ description: content }),
                    })
                        .then(res => res.json())
                        .then(data => {
                            if (data.status === 'success') {
                                Swal.fire('Succès', data.message, 'success').then(() => {
                                    document.getElementById('loading-overlay').style.display = 'flex';
                                    location.reload(); // recharge après affichage du spinner
                                });
                            } else {
                                Swal.fire('Erreur', 'Échec de la mise à jour', 'error');
                            }
                        })
                        .catch(() => {
                            Swal.fire('Erreur', 'Erreur lors de la mise à jour', 'error');
                        });
                },

                willClose: () => {
                    if (tinymce.get('tinymce-editor')) {
                        tinymce.remove('#tinymce-editor');
                    }
                }
            });
        }
    </script>



{% endblock %}

</body>
</html>
