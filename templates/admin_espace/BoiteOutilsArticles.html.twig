{% extends "admin_espace/_header.html.twig" %}

{% block content %}
    {# Add Bootstrap CSS and SweetAlert #}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
        /* Custom Styles (same as before) */
        .table-container {
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .table thead th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            border-bottom: 2px solid #0056b3;
        }

        .table tbody tr:hover {
            background-color: #f1f1f1;
            transition: background-color 0.3s ease;
        }

        .table tbody td {
            vertical-align: middle;
        }

        .btn-custom {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }

        .btn-custom:hover {
            background-color: #218838;
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 20px;
        }
    </style>

    <div class="container-fluid table-container">
        <h1 class="page-title">Boîte à Outils - Articles</h1>
        <div class="d-flex justify-content-end mb-2">
            <a href="{{ path('admin_conseil_list') }}" class="btn btn-primary">
                <i class="fas fa-list"></i> Articles Conseils
            </a>
        </div>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                <tr>
                    <th>Site</th>
                    <th>Page concerné</th>
                    <th>Article disponible</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody>
                {% for article in articlesData %}
                    <tr>
                        <td>{{ article.site }}</td>
                        {% if article.site == "CarriereBTP" or article.site == "CarriereDistribution" or article.site == "CarriereIndustrie" or article.site == "CarriereInformatique" or article.site == "CarriereMedicale" or article.site == "CarriereRestauration" or article.route == "/pagecadre" %}
                            <td>Zoom métier</td>
                        {% else %}
                            <td>{{ article.route }}</td>
                        {% endif %}
                        <td>
                            {% for articleObject in article.articles %}
                                <span class="article-title">{{ articleObject.titre }}</span>
                                {% if not loop.last %}<br>{% endif %}
                            {% endfor %}
                        </td>
                        <td>
                            {% for articleObject in article.articles %}
                                <button class="btn btn-custom change-article-btn"
                                        data-site="{{ article.site }}"
                                        data-route="{{ article.route }}"
                                        data-article-id="{{ articleObject.getIdArticle() }}"
                                        data-article-title="{{ articleObject.titre }}">
                                Changer article {{ loop.index }}
                                </button>
                            {% endfor %}
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    {# Add Bootstrap JS #}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Add event listeners to all "Change Article" buttons
            document.querySelectorAll('.change-article-btn').forEach(button => {
                button.addEventListener('click', function () {
                    const site = this.dataset.site;
                    const route = this.dataset.route;
                    const articleId = this.dataset.articleId; // Ensure this is correct
                    const articleTitle = this.dataset.articleTitle;

                    // Debugging: Log the data being sent
                    console.log('Data being sent:', { site, route, articleId, articleTitle });

                    // Fetch all available articles via AJAX
                    fetch('/admin/get-available-articles')
                        .then(response => response.json())
                        .then(articles => {
                            // Sort articles alphabetically by title
                            articles.sort((a, b) => a.titre.localeCompare(b.titre, 'fr', { sensitivity: 'base' }));

                            // Display SweetAlert with the sorted list of articles
                            Swal.fire({
                                title: `Sélectionnez l'article à changer pour "${articleTitle}"`,
                                html: `
                            <select id="new-article-select" class="form-control">
                                ${articles.map(article => `
                                    <option value="${article.id}">${article.titre}</option>
                                `).join('')}
                            </select>
                        `,
                                showCancelButton: true,
                                confirmButtonText: 'Modifier',
                                cancelButtonText: 'Annuler',
                                preConfirm: () => {
                                    const selectedArticleId = document.getElementById('new-article-select').value;
                                    const selectedArticleTitle = articles.find(article => article.id == selectedArticleId).titre;

                                    // Debugging: Log the selected article
                                    console.log('Selected article:', { selectedArticleId, selectedArticleTitle });

                                    // Show confirmation popup
                                    Swal.fire({
                                        title: 'Confirmer le changement',
                                        html: `Êtes-vous sûr de vouloir changer <b>${articleTitle}</b> avec <b>${selectedArticleTitle}</b> pour le site: <b>${site}</b> ?`,
                                        showCancelButton: true,
                                        confirmButtonText: 'Oui',
                                        cancelButtonText: 'Non',
                                    }).then(result => {
                                        if (result.isConfirmed) {
                                            // Send data to the controller
                                            fetch('/admin/update-article', {
                                                method: 'POST',
                                                headers: {
                                                    'Content-Type': 'application/json',
                                                },
                                                body: JSON.stringify({
                                                    site: site,
                                                    route: route,
                                                    oldArticleId: articleId,
                                                    newArticleId: selectedArticleId,
                                                }),
                                            })
                                                .then(response => response.json())
                                                .then(data => {
                                                    console.log('Response from server:', data);
                                                    if (data.success) {
                                                        Swal.fire('Succès!', 'L\'article a été modifié.', 'success').then(() => {
                                                            location.reload();
                                                        });
                                                    } else {
                                                        Swal.fire('Erreur!', 'Une erreur s\'est produite.', 'error');
                                                    }
                                                })
                                                .catch(error => {
                                                    console.error('Error:', error);
                                                });
                                        }
                                    });
                                },
                            });
                        });
                });
            });
        });

    </script>
{% endblock %}