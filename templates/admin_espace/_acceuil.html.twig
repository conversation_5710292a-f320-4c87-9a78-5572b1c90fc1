{% block stylesheets %}
  <link rel="stylesheet" href="{{ asset('assets/css/admin_actu.min.css') }}" media="screen">
  <link rel="stylesheet" href="{{ asset('assets/css/admin_accueil.min.css') }}" media="screen">
{% endblock %}

{% extends "admin_espace/_header.html.twig" %}

{% block content %}

    {% include 'admin_tools/count_home_page.html.twig' %}

<section style="padding-top: 5ch;">
  <h2>Gestion des utilisateurs / Souscriptions aux offres commerciales</h2>
  <hr>
  <table class="menu_superadmin formulaire" cellspacing="0">
    <tbody><tr>
    
      <td width="48%">
          <div class="label">Module&nbsp;Recherche</div>
          <a href="{{ path('app_admin_client_info') }}" title="Suivi des clients">Suivi&nbsp;des&nbsp;clients</a><br>
          <a href="{{ path('app_admin_client_user_info') }}" title="Trouver un utilisateur">Rechercher&nbsp;un&nbsp;client</a><br> 
          <a href="{{ path('app_admin_client_candid_create') }}" title="Trouver un utilisateur">Créer&nbsp;un&nbsp;compte&nbsp;Candidat</a><br>
          <a href="{{ path('app_admin_client_recrut_create') }}" title="Trouver un utilisateur">Créer&nbsp;un&nbsp;/&nbsp;plusieurs&nbsp;compte&nbsp;Recruteur</a><br>
          <a href="{{ path('app_admin_seo_edit') }}" title="Modifier le SEO de referencement">Modifier&nbsp;le&nbsp;SEO&nbsp;de&nbsp;referencement</a>
          <!--span style="color:#4b84ad;font-size:15px;font-weight:700;t-decoration: none;">Utilisateur</style-->
          
          <div class="label">Actualité</div>
          <a href="{{ path('app_admin_espace') }}" title="Articles">Articles</a> <br>
          <a href="{{ path('app_admin_conseil_publish') }}" title="Conseil">Conseils</a> <br>
          
          <div class="label">Statistiques</div>
          <a href="{{ path('app_admin_statistiques_offres_multiples') }}">Stats offres multiples</a> <br>
          <a href="{{ path('app_admin_statistiques_talentplug') }}" title="Stats consultation Cvthèque">Stats diffusion TalentPlug</a><br>
          <a href="{{ path('app_admin_statistiques_offre_repartition') }}">Répartition des offres par client</a><br>
          <a href="{{ path('app_admin_statistiques_offre_france') }}">Entreprises avec des offres sans localisation</a><br>
          <a href="{{ path('app_admin_stats_candidatures') }}">Candidatures par date</a><br>
      </td>
      
      <td class="sep_ligne">&nbsp;</td>
        <td valign="top">
          <div class="label">Souscription commerciale</div>

        <a href="{{ path('app_admin_lookup_souscriptions') }}" title="Souscriptions commerciales">Souscriptions commerciales</a>			<br>
        <!--a class="dead" href="#" title="Abonnements arrivant à échéance">Abonnements arrivant à échéance</a>			<br>
        <a class="dead" href="#" title="Renouvellement d'abonnements">Renouvellement d'abonnements</a>			<br>
        <a class="dead" href="#" title="Abonnements échus">Abonnements échus</a>			<br>
        <a class="dead" href="#" title="les comptes recruteurs qui ont un abonnement ou un pack d'offre qui a commencé il y a moins de 2 mois" >Comptes récents</a>			<br>
        <a class="dead" href="#" title="comptes qui n'ont diffusé aucunes nouvelles annonces depuis plus d'un mois" >Comptes inactifs</a>			<br>
        <a class="dead" href="#" title="Promotions">Promotions</a--> <br>

        <div class="label">Boite à outils</div>
        <a href="{{ path('app_admin_cvt_show_consultation') }}">Consultation Cvthèque</a> <br>
        <a href="{{ path('app_admin_cvt_show_new_account') }}">Nouveaux inscrits Cvthèque</a> <br>
        {% if access == 1 %}
          <a href="{{ path('app_admin_export_candidatures') }}">Export des Candidatures CSV</a> <br>
        {% endif %}
      </td>
    </tr></tbody>
  </table>
</section>
    

<section style="padding-top: 5ch;">
  <h2>Gestion des scripts / Paramétrage du site</h2>
  <hr>
  <table class="menu_superadmin formulaire" cellspacing="0">
    <tbody><tr>
    
      <td width="48%">
          <div class="label">Gestion des scripts</div>
          <a href="{{ path('app_admin_scripts_index') }}" title="Suivi des clients">Suivi&nbsp;des&nbsp;scripts</a><br>
      </td>
      
      <td class="sep_ligne">&nbsp;</td>
        <td valign="top">
          <div class="label">Espace publicitaire et gestion</div>
          <a href="{{ path('app_admin_publicite_index') }}" title="gestions des logos">Gestion Logos</a><br>
          <a href="{{ path('app_admin_manage_footer', {id:1}) }}" title="gestion des footers">Gestion Footers</a><br>
          <a href="{{ path('app_admin_manage_static_page', {app_id:1}) }}" title="gestion des footers">Gestion Pages Statiques</a><br>
          <!--a class="dead" href="#" title="Promotions">Promotions</a--><br>
      </td>

    </tr></tbody>
  </table>
</section>

<br><br><br><br>


{% endblock %}