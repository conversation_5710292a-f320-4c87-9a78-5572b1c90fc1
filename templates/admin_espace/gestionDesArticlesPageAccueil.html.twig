{% extends "admin_espace/_header.html.twig" %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert.min.js"></script>
    <style>
        .article-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 0.9em;
            font-family: 'Segoe UI', sans-serif;
            min-width: 400px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            overflow: hidden;
        }

        .article-table thead tr {
            background-color: #2c3e50;
            color: #ffffff;
            text-align: left;
        }

        .article-table th,
        .article-table td {
            padding: 15px 20px;
        }

        .article-table tbody tr {
            border-bottom: 1px solid #dddddd;
            transition: background-color 0.2s;
        }

        .article-table tbody tr:nth-of-type(even) {
            background-color: #f8f9fa;
        }

        .article-table tbody tr:last-of-type {
            border-bottom: 2px solid #2c3e50;
        }

        .article-table tbody tr:hover {
            background-color: #e9ecef;
            cursor: default;
        }

        .article-title {
            display: block;
            margin: 8px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .update-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .update-btn:hover {
            background: #2980b9;
        }

        @media screen and (max-width: 768px) {
            .article-table {
                font-size: 0.8em;
            }

            .article-table th,
            .article-table td {
                padding: 12px 15px;
            }
        }
    </style>

    <h1 class="mb-4">Gestion des articles de la pages d'accueil</h1>

    <form method="post" action="{{ path('app_admin_gestion_article_PageAccueil') }}">
        <table class="article-table">
            <thead>
            <tr>
                <th>Site</th>
                <th>Articles</th>
            </tr>
            </thead>
            <tbody>
            {% for site, articles in siteArticles %}
                <tr>
                    <td><strong>{{ site }}</strong></td>
                    <td>
                        {% for article in articles %}
                            <span class="article-title">
                                    <span>{{ article.titre }}</span>
                                    <button type="button" class="update-btn" data-site="{{ site }}" data-article="{{ article.id_article }}">
                                        <span>🛠️ Modifier</span>
                                    </button>
                                </span>
                        {% endfor %}
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </form>
    {# En haut de votre template #}
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}
    <script>
        $('.update-btn').on('click', function() {
            // Retrieve the site value (which might be a string)
            let siteId = $(this).data('site');
            const oldArticleId = $(this).data('article');
            const articles = {{ allArticleTitles|json_encode|raw }};

            // Define the mapping from site names to numeric IDs
            const siteMapping = {
                "Direct Emploi": 1,
                "Direct Etudiant": 2,
                "Direct Alternance": 3,
                "Carriere BTP": 4,
                "Carriere Distribution": 5,
                "Carriere Industrie": 6,
                "Carriere Informatique": 7,
                "Carriere Medical": 8,
                "Carriere Restauration": 9
            };

            // If siteId is a string and exists in our mapping, convert it to the corresponding numeric ID
            if (typeof siteId === 'string' && siteMapping.hasOwnProperty(siteId)) {
                siteId = siteMapping[siteId];
            }

            // Now you have a numeric siteId. Continue with the rest of your code:
            // For example, building the modal interface for selecting an article:
            const content = $('<div>').css({
                'max-height': '400px',
                'overflow-y': 'auto'
            });

            // Search field
            const searchInput = $('<input>')
                .attr('placeholder', 'Rechercher...')
                .css({
                    'width': '100%',
                    'margin-bottom': '10px',
                    'padding': '8px',
                    'border': '1px solid #ddd'
                });

            // Article list container
            const articleList = $('<div>').addClass('article-list');

            // Function to update the list of articles based on search input
            const updateList = (filter = '') => {
                articleList.empty();
                articles.filter(title => title.toLowerCase().includes(filter.toLowerCase()))
                    .forEach(title => {
                        const item = $('<div>')
                            .addClass('article-item')
                            .css({
                                'padding': '10px',
                                'cursor': 'pointer',
                                'border-bottom': '1px solid #eee',
                                'transition': 'background 0.3s'
                            })
                            .text(title)
                            .hover(
                                function() { $(this).css('background', '#f8f9fa'); },
                                function() { $(this).css('background', 'white'); }
                            )
                            .on('click', function() {
                                fetch('{{ path('app_admin_update_article_site') }}', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/x-www-form-urlencoded',
                                    },
                                    body: new URLSearchParams({
                                        site_id: siteId,
                                        old_article_id: oldArticleId,
                                        new_article_title: title
                                    })
                                })
                                    .then(response => {
                                        if (response.ok) window.location.reload();
                                        else alert('Erreur lors de la mise à jour');
                                    });
                            });

                        articleList.append(item);
                    });
            };

            // Update the list whenever the user types in the search field
            searchInput.on('input', () => updateList(searchInput.val()));
            updateList();
            content.append(searchInput, articleList);

            // Display the modal with SweetAlert
            swal({
                title: "Choisir un nouvel article",
                content: content[0],
                buttons: false,
                className: 'custom-swal'
            });
        });
    </script>

    <style>
        .custom-swal {
            width: 80% !important;
            max-width: 600px;
        }

        .article-list {
            border-radius: 4px;
            background: white;
        }
    </style>
{% endblock %}