{% extends "admin_espace/_header.html.twig" %}

{% block content %}
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">Liste des Conseils</h1>
            <div>
                <a href="{{ path('app_admin_accueil_theme') }}" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left"></i> Retour Admin
                </a>
                <a href="{{ path('app_admin_index') }}" class="btn btn-primary">
                    <i class="fas fa-toolbox"></i> BoiteàOutils
                </a>
            </div>
        </div>
        <table class="table table-striped">
            <thead class="table-primary">
            <tr>
                <th>Titre</th>
                <th>Chapo</th>
                <th>Date de création</th>
                <th>Nom du groupe</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            {% for conseil in conseils %}
                <tr>
                    <td>{{ conseil.titre }}</td>
                    <td>{{ conseil.description[:80] ~ '...' }}</td>
                    <td>{{ conseil.date ? conseil.date|date('d/m/Y') : '' }}</td>
                    <td>{{ conseil.groupe }}</td>
                    <td>
                        <a href="{{ path('admin_conseil_show', {'id': conseil.id}) }}" class="btn btn-sm btn-info">Voir</a>
                        <a href="{{ path('admin_conseil_edit', {'id': conseil.id}) }}" class="btn btn-sm btn-warning">Modifier</a>
                    </td>
                </tr>
            {% else %}
                <tr>
                    <td colspan="5" class="text-center">Aucun conseil trouvé.</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
{% endblock %}