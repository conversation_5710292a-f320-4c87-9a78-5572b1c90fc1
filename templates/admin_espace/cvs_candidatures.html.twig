{% extends 'admin_espace/_header.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">
{% endblock %}

{% block content %}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
          {{ form_start(form, {'attr': {'class': 'form-inline', 'align': 'center'}}) }}
          {{ form_widget(form.startDate, {'attr': {'class': 'form-control mr-sm-2', 'placeholder': 'Date de début'}}) }}
          {{ form_widget(form.endDate, {'attr': {'class': 'form-control mr-sm-2', 'placeholder': 'Date de fin'}}) }}
          <button type="submit" class="btn btn-primary">Filtrer</button>
          {{ form_end(form) }}
        </div>
      </div>

        <div class="table-responsive">
            <table class="table table-striped">
            <thead class="thead-dark">
                <tr>
                <th scope="col">Raison sociale</th>
                <th scope="col">Nombre de candidatures</th>
                </tr>
            </thead>
            <tbody>
                {% for key, candidature in candidatures %}
                <tr>
                <td>{{ key }}</td>
                <td>{{ candidature }}</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                <td>Total</td>
                <td>{{ total }}</td>
                </tr>
            </table>

</div>

{% endblock %}