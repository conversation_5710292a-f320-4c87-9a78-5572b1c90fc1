{% extends 'admin_espace/_header.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1>Volumes et position Stage</h1>
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Entreprise</th>
                        <th>Nombre d'offres</th>
                        {# <th>Nombre de candidatures / mois</th> #}
                        <th>Position</th>

                    </tr>
                </thead>
                <tbody>
                    {% for volume in volumes %}
                        <tr>
                            <td>{{ volume.raison_sociale }}</td>
                            <td>{{ volume.nombre_offres }}</td>
                            {# <td>{{ volume.nombre_candidatures }}</td> #}
                            <td>
                                <form action="{{ path('app_admin_modifier_position_stage', { 'id': volume.id_entreprise }) }}" method="post">
                                    <input type="number" name="position" value="{{ volume.position }}" class="form-control" min="-2" max="7">
                            </td>
                            <td>
                                    <button type="submit" class="btn btn-primary">Modifier</button>
                                </form>
                            </td>
            
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
