{% extends 'admin_espace/_header.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
{% endblock %}

{% block content %}
    <div class="container mt-4">
        <h1 class="text-center mb-4">Statistiques CV par Offre</h1>

        <div class="card p-4 mb-4">
            <h5 class="mb-3">Filtrer par entreprise et par dates</h5>
            {{ form_start(form) }}
            <div class="mb-3">
                {{ form_row(form.entreprise, {'attr': {'class': 'form-control'}}) }}
            </div>
            <div class="mb-3">
                {{ form_row(form.start_date, {'attr': {'class': 'form-control'}}) }}
            </div>
            <div class="mb-3">
                {{ form_row(form.end_date, {'attr': {'class': 'form-control'}}) }}
            </div>
            <div>
                {{ form_row(form.submit, {'attr': {'class': 'btn btn-primary'}}) }}
            </div>
            {{ form_end(form) }}
        </div>

        {% if stats is not empty %}
            <div class="card p-4">
                <h2 class="mb-4">Résultats</h2>
                <table id="stats-table" class="table table-bordered table-striped">
                    <thead class="thead-dark">
                    <tr>
                        <th>Intitulé</th>
                        <th>Nombre de CV</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for stat in stats %}
                        <tr>
                            <td>{{ stat.intitule }}</td>
                            <td>{{ stat.nombre_cv }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                <button id="export-btn" class="btn btn-success mt-3">Exporter en XLSX</button>
            </div>
        {% else %}
            <p class="text-danger mt-4">Aucune donnée disponible.</p>
        {% endif %}
    </div>

    <script>
        document.getElementById('export-btn').addEventListener('click', function () {
            // Récupération du nom de l'entreprise à partir du formulaire
            const entrepriseName = "{{ form.entreprise.vars.value }}"; // Le nom de l'entreprise sélectionnée

            // Récupération des données du tableau HTML
            const table = document.getElementById('stats-table');
            const rows = Array.from(table.querySelectorAll('tr')).map(row =>
                Array.from(row.querySelectorAll('th, td')).map(cell => cell.textContent)
            );

            // Création du workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(rows);

            // Ajout du worksheet au workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Statistiques');

            // Exportation en fichier XLSX avec le nom de l'entreprise dans le fichier
            const filename = 'statistiques_cv_par_offre_' + entrepriseName + '.xlsx';
            XLSX.writeFile(wb, filename);
        });
    </script>
{% endblock %}
