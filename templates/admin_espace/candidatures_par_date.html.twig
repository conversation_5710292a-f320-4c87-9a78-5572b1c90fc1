{% extends "admin_espace/_header.html.twig" %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
{% endblock %}

{% block content %}
    <div class="container">
        <h1>Candidatures par date</h1>

        <div class="row">
            <div class="col-md-6 offset-md-3">
                <form method="POST" action="{{ path('app_admin_stats_candidatures') }}" class="mt-4">
                    {{ form_start(form) }}
                    <div class="mb-3">
                        {{ form_label(form.startDate, 'Date de début', {'label_attr': {'class': 'form-label'}}) }}
                        {{ form_widget(form.startDate, {'attr': {'class': 'form-control'}}) }}
                    </div>
                    <div class="mb-3">
                        {{ form_label(form.endDate, 'Date de fin', {'label_attr': {'class': 'form-label'}}) }}
                        {{ form_widget(form.endDate, {'attr': {'class': 'form-control'}}) }}
                        {% if form_errors(form.endDate) %}
                        <div class="text-danger">{{ form_errors(form.endDate) }}</div>
                        {% endif %}
                    </div>
                    <button type="submit" class="btn btn-primary">Rechercher</button>
                    {{ form_end(form) }}
                </form>
            </div>
        </div>

        {% if totalCandidatures is defined %}
            {% if totalCandidatures > 0 %}
                <div class="mt-4 text-center">
                    <p>Nombre total de candidatures dans la période : <strong>{{ totalCandidatures }}</strong></p>
                    <p>Période sélectionnée : <strong>{{ startDate|date('d/m/Y') }} - {{ endDate|date('d/m/Y') }}</strong></p>
                    <p>Réponse par mail : <strong>{{ totalRepMail }}</strong></p>
                    <p>Réponse par url : <strong>{{ totalRepUrl }}</strong></p>
                </div>
            {% else %}
                <div class="mt-4 text-center">
                    <p>Aucune candidature n'a été trouvée dans la période spécifiée.</p>
                    <p>Période sélectionnée : {{ startDate|date('d/m/Y') }} - {{ endDate|date('d/m/Y') }}</p>
                </div>
            {% endif %}
        {% endif %}
    </div>
{% endblock %}
