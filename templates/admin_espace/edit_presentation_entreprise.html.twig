{% extends 'admin_espace/_header.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    {# <link rel="stylesheet" href="{{ asset('assets/js/ckeditor.min.js') }}"> #}
    <link rel="stylesheet" href="{{ asset('directEmploi_new/Front/css/styles.min.css') }}">
{% endblock %}

{% block content %}
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <h2>Modifier la présentation de l'entreprise</h2>
                <form method="post" action="">
                    <div class="form-group">
                        <label for="presentation">Présentation de l'entreprise</label>
                        <textarea name="presentation" class="form-control tinymce-textarea" rows="15">{{ contenuhtml|raw }}</textarea>
                    </div>
                    <button type="submit" class="btn btn-primary mt-3">Enregistrer</button>
                </form>
            </div>
        </div>
    </div>
    {% for label, messages in app.flashes %}
        {% for message in messages %}
            <div class="alert alert-{{ label }}">{{ message }}</div>
        {% endfor %}
    {% endfor %}

{% endblock %}


{% block javascript %}
<script src="https://cdn.tiny.cloud/1/impmbv273mph5bocy71yc8jonbu25sgaw2jzgzbmkkalg9ap/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>

<script>
    tinymce.init({
    selector: 'textarea.tinymce-textarea',
    language: 'fr',
    height: 400,
    menubar: false,

    // ✅ Plugins essentiels uniquement
    plugins: [
    'lists', 'link', 'table', 'autolink',
    'textcolor', 'colorpicker'
    ],

    // ✅ Barre d'outils simple et efficace
    toolbar: 'undo redo | bold | fontsize | forecolor backcolor | alignleft aligncenter alignright alignjustify | lineheight',

    // ✅ Tailles de police en px
    fontsize_formats: '8px 10px 12px 14px 16px 18px 24px 36px 48px 72px',
    lineheight_formats: "1 1.1 1.2 1.3 1.4 1.5 1.6 1.8 2 2.5 3 4 5",

    // ✅ Appliquer les styles en inline (pas dans des classes CSS)
    inline_styles: true,
    style_formats_merge: true,
    span_wrap: true,

    formats: {
    bold: { inline: 'strong', remove: 'all' },
    italic: { inline: 'em', remove: 'all' },
    underline: { inline: 'u', remove: 'all' }
},

    valid_children: "+body[style],+p[span],+span[strong|em|u]"
});
</script>
{% endblock %}

