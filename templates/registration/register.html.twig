{% extends 'directemploi/layout.html.twig' %}
{% block title %}Inscription
{% endblock %}

{% block content %}
	<h1>Création compte candidat</h1>

		<div class="obli">
		<p>Les champs marqués d'une astérisque
			<span class="ob">
				*</span>
			sont obligatoires</p>
	</div>

	{{ form_start(registrationForm) }}

	<div class="form_box">
		<h3>Votre identifiant et mot de passe</h3>
		<table class="formulaire">

			<tr><td class="label">{{ form_label(registrationForm.login, "Login")}}<span class="ob"> *</span></td></tr>
			<tr><td>{{ form_widget(registrationForm.login,{ 'attr': {'placeholder': 'Login'} })}}</td></tr>
			<tr><td class='label'>{{ form_label(registrationForm.email,"Email")}}<span class="ob"> *</span></td></tr>
			<tr><td>{{ form_widget(registrationForm.email,{ 'attr': {'placeholder': 'Email'} })}}</td></tr>
			<tr><td class='label'>{{ form_label(registrationForm.passCrypt,"Mot de passe")}}<span class="ob"> *</span></td></tr>
			<tr><td>{{ form_widget(registrationForm.passCrypt,{ 'attr': {'placeholder': 'Mot de passe'} })}}</td></tr>
			
		</table>
	</div>

	<div class="form_box">
		<h3>Votre état civil</h3>
		<table class="formulaire">
			<tr>
				<td class='label'>{{ form_label(registrationForm.prenom,"Prénom")}}<span class="ob"> *</span></td>
				<td class='label'>{{ form_label(registrationForm.nom,"Nom")}}<span class="ob"> *</span></td>
				<td class='label'>{{ form_label(registrationForm.user_civilite_id_user_civilite,"Civilité")}}<span class="ob"> *</span></td>
			</tr>
				
			<tr>
				<td>{{ form_widget(registrationForm.prenom,{ 'attr': {'placeholder': 'Prénom'} })}}</td>
				<td>{{ form_widget(registrationForm.nom,{ 'attr': {'placeholder': 'Nom'} })}}</td>
				<td>{{ form_widget(registrationForm.user_civilite_id_user_civilite,{ 'attr': {'placeholder': 'Login'} })}}</td>
			</tr>


			<tr>
				<td class='label'>{{ form_label(registrationForm.telephone,"Téléphone portable")}}<span class="ob"> *</span></td>
				<td class='label'>Pays<span class="ob"> *</span></td>
			</tr>
				
			<tr>
				<td>{{ form_widget(registrationForm.telephone,{ 'attr': {'placeholder': 'Téléphone Portable'} })}}</td>
				<td>Pays</td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(registrationForm.adresse,"Adresse")}}<span class="ob"> *</span></td>
				<td class='label'>{{ form_label(registrationForm.complement_adresse, "Complement d'adresse") }}</td>
			</tr>
			
			<tr>
				<td>{{ form_widget(registrationForm.adresse,{ 'attr': {'placeholder': 'Adresse'} })}}</td>
				<td>{{ form_widget(registrationForm.complement_adresse,{ 'attr': {'placeholder': 'Complément d\'adresse'} })}}</td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(registrationForm.ville, "Ville")}}<span class="ob"> *</span></td>
				<td class='label'>{{ form_label(registrationForm.code_postal,"Code Postal")}}<span class="ob"> *</span></td>
					
			</tr>
			<tr>
				<td>{{ form_widget(registrationForm.code_postal,{ 'attr': {'placeholder': 'Code postal'} })}}</td>
				<td>{{ form_widget(registrationForm.ville,{ 'attr': {'placeholder': 'Ville'} })}}</td>
			</tr>

		</table>
	</div>


	<div class="form_box">
		<h3>Votre domaine d'activité et votre formation</h3>
		<table class="formulaire">

			<tr>
				<td class='label'>{{ form_label(registrationForm.domaine_activite, "Domaine d'activité de l'entreprise")}}<span class="ob"> *</span></td>
			</tr>
			<tr>
				<td>{{ form_widget(registrationForm.domaine_activite, "Domaine d'activité de l'entreprise")}}</td>
			</tr>
		</table>
	</div>

	<div class="act">
		<button type="submit" class="btn" id="btn_submit">S'inscrire</button>
	</div>



	{# <div class="form_box">
									<h3>Votre état civil</h3>
									{# {{ form_row(registrationForm.nom) }}
											{{ form_row(registrationForm.prenom) }}
											{{ form_row(registrationForm.civilite) }}
											{{ form_row(registrationForm.phoneportable) }}
											{{ form_row(registrationForm.pays) }}
											{{ form_row(registrationForm.adresse) }} #}
{# {{ form_row(registrationForm.complementadresse) }} #}
	{# {{ form_row(registrationForm.codepostal) }}
											{{ form_row(registrationForm.ville) }} #}

{# <div class="form_box">
		<h3>Votre domaine d'activité et votre formation</h3>
		{# {{ form_row(registrationForm.domaineactivite) }}
				{{ form_row(registrationForm.niveauformation) }}
	</div> #}

{# <div class="form_box">
	<h3>Je désire également</h3>
	{# {{ form_row(registrationForm.cv) }}
											{{ form_row(registrationForm.agreenewsletter) }} #}
	{# {{ form_row(registrationForm.agreeTerms) }}
	<button type="submit" class="btn">s'inscrire</button>
</div> #}
{{ form_end(registrationForm) }}{% endblock %}
