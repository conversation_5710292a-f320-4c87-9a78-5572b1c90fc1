{% extends 'directemploi/layout.html.twig' %}

{% set title_seo = "Postulez aux offres d'emploi en créant votre compte facilement | Direct Emploi" %}
{% block title %}{{ title_seo }}{% endblock %}
{% block title_og %}{{ title_seo }}{% endblock %}
{% block title_twitter %}{{ title_seo }}{% endblock %}

{% set description_seo = "Créez un compte sur Direct Emploi pour postuler et être tenu informé en direct des nouvelles offres qui correspondent le mieux à votre profil" %}
{% block meta_description %}{{ description_seo }}{% endblock %}
{% block meta_description_og %}{{ description_seo }}{% endblock %}
{% block meta_description_twitter %}{{ description_seo }}{% endblock %}

{% block content %}
	<div class="container">
		{% if breadcrumb is defined %}
			{% include '/_partials/_breadcrumb.html.twig'  %}
		{% endif %}
		<h1>Créer un compte candidat</h1>

		<div id="msform">
			<ul id="progressbar" class="d-flex flex-row justify-content-around">
				<li class="active" id="account"><strong>Créer mon compte</strong></li>
				<li id="personal"><strong>Définir mon profil</strong></li>
				<li id="payment"><strong>Créer mon alerte mail</strong></li>
			</ul>

		</div>

		<div class="obli">
			<p>Les champs marqués d'une astérisque
				<span class="ob">
				*</span>
				sont obligatoires</p>
		</div>

		{{ form_start(InscriptionEtape1Form) }}

		<div class="row">
			<h3>Votre identifiant et mot de passe</h3>
				<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
					<div class="form-group">
						{{ form_row(InscriptionEtape1Form.login) }}
						{{ form_errors(InscriptionEtape1Form.login) }}
					</div>
				</div>
				<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
					<div class="form-group">
						{{ form_row(InscriptionEtape1Form.email) }}
						{{ form_errors(InscriptionEtape1Form.email) }}
					</div>	
				</div>
				<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
					<div class="form-group">
						{{ form_row(InscriptionEtape1Form.passCrypt.first) }}
						{{ form_errors(InscriptionEtape1Form.passCrypt) }}
					</div>	
				</div>
				<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
					<div class="form-group">
						{{ form_row(InscriptionEtape1Form.passCrypt.second) }}
						{{ form_errors(InscriptionEtape1Form.passCrypt) }}
					</div>	
				</div>
				{{ form_row(InscriptionEtape1Form._token) }}
		</div>
			{# <table class="formulaire">

				<tr>
					<td class="label">{{ form_label(InscriptionEtape1Form.login, "Login")}}<span class="ob">
						*</span>
					</td>
				</tr>
				<tr>
					<td>{{ form_widget(InscriptionEtape1Form.login,{ 'attr': {'placeholder': 'Login'} })}}</td>
				</tr>
				<tr>
					<td class='label'>{{ form_label(InscriptionEtape1Form.email.first,"Email")}}<span class="ob">
						*</span>
					</td>
					<td class='label'>{{ form_label(InscriptionEtape1Form.email.second,"Confirmation Email")}}<span class="ob">
						*</span>
					</td>
				</tr>
				<tr>
					<td>{{ form_widget(InscriptionEtape1Form.email.first,{ 'attr': {'placeholder': 'Email'} })}}</td>
					<td>{{ form_widget(InscriptionEtape1Form.email.second,{ 'attr': {'placeholder': 'Confirmation Email'} })}}</td>

				</tr>
				<tr>
					<td class='label'>{{ form_label(InscriptionEtape1Form.passCrypt.first,"Mot de passe")}}<span class="ob">
						*</span>
					</td>
					<td class='label'>{{ form_label(InscriptionEtape1Form.passCrypt.second,"Confirmation Mot de passe")}}<span class="ob">
						*</span>
					</td>
				</tr>
				<tr>
					<td>{{ form_widget(InscriptionEtape1Form.passCrypt.first,{ 'attr': {'placeholder': 'Mot de passe'} })}}</td>
					<td>{{ form_widget(InscriptionEtape1Form.passCrypt.second,{ 'attr': {'placeholder': 'Confirmation Mot de passe'} })}}</td>
				</tr>

			</table> #}

		<div class="row">
			<h3>Votre état civil</h3>
			{# <table class="formulaire">
				<tr>
					<td class='label etatcivil'>{{ form_label(InscriptionEtape1Form.prenom,"Prénom")}}<span class="ob">
						*</span>
					</td>
					<td class='label etatcivil'>{{ form_label(InscriptionEtape1Form.nom,"Nom")}}<span class="ob">
						*</span>
					</td>
					<td class='label etatcivil'>{{ form_label(InscriptionEtape1Form.user_civilite_id_user_civilite,"Civilité")}}<span class="ob">
						*</span>
					</td>
				</tr>

				<tr>
					<td>{{ form_widget(InscriptionEtape1Form.prenom,{ 'attr': {'placeholder': 'Prénom', 'class': 'etatcivil'} })}}</td>
					<td>{{ form_widget(InscriptionEtape1Form.nom,{ 'attr': {'placeholder': 'Nom', 'class': 'etatcivil'} })}}</td>
					<td>{{ form_widget(InscriptionEtape1Form.user_civilite_id_user_civilite, { 'attr': {'class': 'etatcivil'} })}}</td>
				</tr>
			</table> #}

			<div class="col-lg-4 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.prenom) }}
					{{ form_errors(InscriptionEtape1Form.prenom) }}
				</div>
			</div>
			<div class="col-lg-4 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.nom) }}
					{{ form_errors(InscriptionEtape1Form.nom) }}
				</div>
			</div>
			<div class="col-lg-4 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.user_civilite_id_user_civilite) }}
					{{ form_errors(InscriptionEtape1Form.user_civilite_id_user_civilite) }}
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.telephone) }}
					{{ form_errors(InscriptionEtape1Form.telephone) }}
				</div>
			</div>
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.pays) }}
					{{ form_errors(InscriptionEtape1Form.pays) }}
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.code_postal) }}
					{{ form_errors(InscriptionEtape1Form.code_postal) }}
				</div>
			</div>
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.ville) }}
					{{ form_errors(InscriptionEtape1Form.ville) }}
				</div>
			</div>

			{# <table class="formulaire">

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape1Form.telephone,"Téléphone portable")}}<span class="ob">*</span>
					</td>
					<td class='label'>{{ form_label(InscriptionEtape1Form.pays,"Pays")}}
					</td>
				</tr>
				<tr>
					<td>{{ form_widget(InscriptionEtape1Form.telephone,{ 'attr': {'placeholder': 'Téléphone Portable'} })}}</td>
					<td>{{ form_widget(InscriptionEtape1Form.pays,{ 'attr': {'placeholder': 'Pays'}, 'separator':" ---------- " })}}</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape1Form.adresse1,"Adresse")}}
					</td>
					<td class='label'>{{ form_label(InscriptionEtape1Form.complement_adresse, "Complement d'adresse") }}</td>
				</tr>

				<tr>
					<td>{{ form_widget(InscriptionEtape1Form.adresse1,{ 'attr': {'placeholder': 'Adresse'} })}}</td>
					<td>{{ form_widget(InscriptionEtape1Form.complement_adresse,{ 'attr': {'placeholder': 'Complément d\'adresse'} })}}</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape1Form.code_postal,"Code Postal")}}<span class="ob">*</span></td>
					<td class='label'>{{ form_label(InscriptionEtape1Form.ville, "Ville")}}<span class="ob">*</span></td>
				</tr>
				<tr>
					<td>{{ form_widget(InscriptionEtape1Form.code_postal,{ 'attr': {'placeholder': 'Code postal'} })}}</td>
					<td>{{ form_widget(InscriptionEtape1Form.ville,{ 'attr': {'placeholder': 'Ville'} })}}</td>
				</tr> #}

				{# <tr>
				<td class='label'>{{ form_label(InscriptionEtape1Form.geo_liste_region,"geo_liste_region")}}<span class="ob"> *</span></td>
				<td class='label'>{{ form_label(InscriptionEtape1Form.geo_departement, "geo_departement")}}<span class="ob"> *</span></td>
			</tr>
			<tr>
				<td>{{ form_widget(InscriptionEtape1Form.geo_liste_region,{ 'attr': {'placeholder': 'geo_liste_region'} })}}</td>
				<td>{{ form_widget(InscriptionEtape1Form.geo_departement,{ 'attr': {'placeholder': 'geo_departement'} })}}</td>
			</tr> #}

			{# </table> #}
		{# </div> #}


		<div class="row">
			<h3>Votre domaine d'activité et votre formation</h3>
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.domaine_activite) }}
					{{ form_errors(InscriptionEtape1Form.domaine_activite) }}
				</div>
			</div>
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.metier_metier) }}
					{{ form_errors(InscriptionEtape1Form.metier_metier) }}
				</div>
			</div>
			<div class="col-lg-12 col-sm-12">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.formation_level) }}
					{{ form_errors(InscriptionEtape1Form.formation_level) }}
				</div>
			</div>
			{# <div class="col-lg-6 col-sm-12">
				<div class="form-group">
					{{ form_row(InscriptionEtape1Form.ec_ecole) }}
					{{ form_errors(InscriptionEtape1Form.ec_ecole) }}
				</div>
			</div> #}
			{# <table class="formulaire">

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape1Form.domaine_activite, "Domaine d'activité de l'entreprise")}}<span class="ob">
						*</span>
					</td>
					<td class='label'>{{ form_label(InscriptionEtape1Form.metier_metier, "Métier")}}<span class="ob">
						*</span>
					</td>
				</tr>
				<tr>
					<td>{{ form_widget(InscriptionEtape1Form.domaine_activite)}}</td>
					<td>{{ form_widget(InscriptionEtape1Form.metier_metier)}}</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape1Form.formation_level, "Niveau de formation")}}<span class="ob">
						*</span>
					</td>
					<td class='label'>{{ form_label(InscriptionEtape1Form.ec_ecole, "Etablissement / Spécialité")}}<span class="ob">
						*</span>
					</td>
				</tr>
				<tr>
					<td>{{ form_widget(InscriptionEtape1Form.formation_level)}}</td>
					<td>{{ form_widget(InscriptionEtape1Form.ec_ecole)}}</td>
				</tr>
			</table> #}
		</div>


		<div class="row">
			<div class="col-12">
				<div class="bottom-login">
					<i class="fa-regular fa-file-lines" style="color: #44a2c6cc; font-size:57px"></i>
					<div>
						<p><strong>Déposer gratuitement mon CV </strong> pour être contacté par plus de 5000 recruteurs</p>
						<div class="form-group">
							{{ form_row(InscriptionEtape1Form.cv_file) }}
							<small id="fileHelp" class="form-text text-muted">Taille max. de 1 Mo. Formats acceptés : doc, docx, pdf, rtf.</small>
						</div>
					</div>
				</div>
			</div>
			<div class="col-12">
				<div class="bottom-login">
					<i class="fa-regular fa-newspaper" style="color: #44a2c6cc; font-size:50px"></i>
					<div>
						<p><strong>M’abonner gratuitement à la newsletter de Direct Emploi et son réseau de sites spécialisés :</strong></p>
						<p>Préparation à l’entretien d’embauche, CV, conseils de négociation de salaire, fiches métiers, offres d’emploi ciblées, informations sur les formations, offres partenaires.</p>
					</div>
					<div class="form-check" style="margin-left:auto;">
						{{ form_row(InscriptionEtape1Form.newsletter) }}
					</div>
				</div>
			</div>
			<div class="col-12">
				<div class="bottom-login">

					<i class="fa-regular fa-square-check" style="color: #44a2c6cc; font-size:50px"></i>
					<div>
						<p><strong>J'accepte <a href="#"style="color:#44a2c6cc;">les conditions générales</a> de Direct Emploi</strong> <span class="required"> * </span></p>
						<p>- Coordonnées transmises aux recruteurs, lecture du CV à des fins de recrutement</p>
						<p>- Envois potentiels d’offres d’emploi, stage, alternance, formations et communications partenaires</p>


					</div>
					<div class="form-check" style="margin-left:auto;">
						{{ form_row(InscriptionEtape1Form.csg) }}

					</div>
				</div>
			</div>
			{# <div class="boxB">
				<h3>Je désire également</h3>

				<ul class="inscriptionPlus">
					<li class="clearfix">
						<i class="fa-regular fa-file-lines" style="color: #faaf40; font-size:57px"></i>
						<div class="text">
							<strong>Déposer gratuitement mon CV</strong>
							<span class="ordi">pour être contacté par plus de 5000 recruteurs
						</span><br/>

							{{ form_widget(InscriptionEtape1Form.cv_file)}}<br/>
							<span class="taillemax">Taille max. de 1 Mo. Formats acceptés : doc, docx, pdf, rtf.</span>
						</div>
					</li>

					<li class="clearfix">
						<div class="icon newsletter" style="padding-top:22px">
							&nbsp;
						</div>

						<div class="text">
							<strong>M’abonner gratuitement à la newsletter de Direct Emploi et son réseau de sites spécialisés :</strong><br>
							préparation à l’entretien d’embauche, CV, conseils de négociation de salaire, fiches métiers, offres d’emploi ciblées, informations sur les formations, offres partenaires

						</div>
						<div class="coche">
							{{ form_widget(InscriptionEtape1Form.newsletter)}}
						</div>
					</li>


					<li class="clearfix">
						<div class="icon legal-notice" style="padding-top:22px"></div>
						<div class="text">
							<strong>J'accepte les
								<a href="https://www.directemploi.com/page/mentions-legales" target="_blank">
									conditions générales //rajouter les mentions légales
								</a>
							</strong>
							<span class="ob">*</span>
							<span></br>
								- Coordonnées transmises aux recruteurs, lecture du CV à des fins de recrutement</br>- Envois potentiels d’offres d’emploi, stage, alternance, formations et communications partenaires
			</span>
						</div>

						<div class="coche">{{ form_widget(InscriptionEtape1Form.csg)}}</div>
					</li>
				</ul> #}


				<div style="text-align:center">{{ form_widget(InscriptionEtape1Form.save, { 'label': 'Valider et continuer' }) }}</div></div>
				{% endblock %}
				{% block javascripts %}

				<script>
	window.onload = () => { // let region = document.querySelector("#inscription_candidat_etape1_geo_liste_region");
		let domaine = document.querySelector("#inscription_candidat_etape1_domaine_activite");
		let formation = document.querySelector("#inscription_candidat_etape1_formation_level");

// region.addEventListener("change", function () {
// let form = this.closest("form");
// let data = this.name + "=" + this.value;

// fetch(form.action, {
// method: form.getAttribute("method"),
// body: data,
// headers: {
// "Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
// }
// }).then(response => response.text()).then(html => {
// let content = document.createElement("html");
// content.innerHTML = html;
// let nouveauSelect = content.querySelector("#inscription_candidat_etape1_geo_departement");
// document.querySelector("#inscription_candidat_etape1_geo_departement").replaceWith(nouveauSelect);
// })
// });

		domaine.addEventListener("change", function () {
			let form = this.closest("form");
			let data = this.name + "=" + this.value;

			fetch(form.action, {
				method: form.getAttribute("method"),
				body: data,
				headers: {
					"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
				}
			}).then(response => response.text()).then(html => {
				let content = document.createElement("html");
				content.innerHTML = html;
				let nouveauSelect = content.querySelector("#inscription_candidat_etape1_metier_metier");
				document.querySelector("#inscription_candidat_etape1_metier_metier").replaceWith(nouveauSelect);
			})
		});

		formation.addEventListener("change", function () {
			let form = this.closest("form");
			let data = this.name + "=" + this.value;

			fetch(form.action, {
				method: form.getAttribute("method"),
				body: data,
				headers: {
					"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
				}
			}).then(response => response.text()).then(html => {
				let content = document.createElement("html");
				content.innerHTML = html;
				let nouveauSelect = content.querySelector("#inscription_candidat_etape1_ec_ecole");
				document.querySelector("#inscription_candidat_etape1_ec_ecole").replaceWith(nouveauSelect);
			})
		});
	}
  </script>
        </div>
        </div>
  {% endblock %}
