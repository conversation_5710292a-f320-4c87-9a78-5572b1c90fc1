{% extends 'directemploi/layout.html.twig' %}
{% block title %}Inscription
{% endblock %}

{% block content %}
	<div class="container">
		<h1><PERSON><PERSON><PERSON> un compte candidat</h1>

		{# <ul class="filArianneInscription clearfix" style="display: block; margin-left: auto; margin-right: auto; width: 75%;">
			<li class="step_account"><h2>C<PERSON>er mon compte</h2></li>
			<li class="arrow"></li>
			<li class="active step_profil"><h2>Définir mon profil</h2></li>
			<li class="arrow"></li>
			<li class="step_alerte"><h2>Créer&nbsp;mon&nbsp;alerte&nbsp;mail</h2></li>
		</ul> #}

		<div id="msform">
			<ul id="progressbar" class="d-flex flex-row justify-content-around">
				<li id="account"><strong><PERSON><PERSON>er mon compte</strong></li>
				<li  class="active" id="personal"><strong>D<PERSON><PERSON>ir mon profil</strong></li>
				<li id="payment"><strong>C<PERSON>er mon alerte mail</strong></li>
			</ul>

		</div>

		<div class="obli">
			<p>Les champs marqués d'une astérisque
				<span class="ob">
				*</span>
				sont obligatoires</p>
		</div>

		{{ form_start(InscriptionEtape2Form) }}

		{{ form_row(InscriptionEtape2Form._token) }}

		<div class="row">
			<h3>Votre situation actuelle</h3>
			
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.remuneration) }}
					{{ form_errors(InscriptionEtape2Form.remuneration) }}
				</div>
			</div>
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.activite) }}
					{{ form_errors(InscriptionEtape2Form.activite) }}
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.experience) }}
					{{ form_errors(InscriptionEtape2Form.experience) }}
				</div>
			</div>
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.disponibilite) }}
					{{ form_errors(InscriptionEtape2Form.disponibilite) }}
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.mobilite) }}
					{{ form_errors(InscriptionEtape2Form.mobilite) }}
				</div>
			</div>
			{# <table
					class="formulaire">

				profil_visible
						cv_visible
						profil_anonyme

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape2Form.remuneration,"Salaire du poste")}}</td>
					<td class='label'>{{ form_label(InscriptionEtape2Form.activite,"Activité")}}</td>
				</tr>
				<tr>
					<td>{{ form_widget(InscriptionEtape2Form.remuneration)}}</td>
					<td>{{ form_widget(InscriptionEtape2Form.activite,{ 'attr': {'placeholder': "Sélectionnez votre activité actuelle"} })}}</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape2Form.experience,"Nombre d'années d'expérience")}}</td>
					<td class='label'>{{ form_label(InscriptionEtape2Form.disponibilite,"Disponibilité")}}</td>
				</tr>
				<tr>
					<td>{{ form_widget(InscriptionEtape2Form.experience,{ 'attr': {'placeholder': 'Sélectionnez votre expérience'} })}}</td>
					<td>{{ form_widget(InscriptionEtape2Form.disponibilite,{ 'attr': {'placeholder': 'Sélectionnez votre disponibilité'} })}}</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape2Form.mobilite,"Mobilité")}}</td>
					<td></td>
				</tr>
				<tr>
					<td>{{ form_widget(InscriptionEtape2Form.mobilite,{ 'attr': {'placeholder': 'Sélectionnez votre mobilité'} })}}</td>
					<td></td>
				</tr>
			</table> #}
		</div>

		<div class="row">
			<h3>Vos compétences</h3>

			<div class="col-lg-12 col-sm-12">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.competence, { 'attr': {'placeholder': 'Word, powerpoint, excel', 'style' : "width:882px"} })}}
					{{ form_errors(InscriptionEtape2Form.competence) }}
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.langue1) }}
					{{ form_errors(InscriptionEtape2Form.langue1) }}
				</div>
			</div>
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.level1) }}
					{{ form_errors(InscriptionEtape2Form.level1) }}
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.langue2) }}
					{{ form_errors(InscriptionEtape2Form.langue2) }}
				</div>
			</div>
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.level2) }}
					{{ form_errors(InscriptionEtape2Form.level2) }}
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-12 col-sm-12">
				<div class="form-group">
					{{ form_label(InscriptionEtape2Form.permis,"Permis B")}}&emsp;{{ form_widget(InscriptionEtape2Form.permis[0])}}&nbsp;{{ form_label(InscriptionEtape2Form.permis[0])}}&ensp;
						{{ form_widget(InscriptionEtape2Form.permis[1])}}&nbsp;{{ form_label(InscriptionEtape2Form.permis[1])}}
				</div>
			</div>
		</div>
			{# <table class="formulaire">

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape2Form.competence,"Liste des compétences")}}</td>
					<td></td>
				</tr>
				<tr>
					<td colspan='2'>{{ form_widget(InscriptionEtape2Form.competence,{ 'attr': {'placeholder': 'Word, powerpoint, excel', 'style' : "width:882px"} })}}</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape2Form.langue1,"Langue étrangère 1")}}</td>
					<td class='label'>{{ form_label(InscriptionEtape2Form.level1,"Niveau")}}</td>
				</tr>

				<tr>
					<td>{{ form_widget(InscriptionEtape2Form.langue1,{ 'attr': {'placeholder': 'Sélectionnez la langue'} })}}</td>
					<td>{{ form_widget(InscriptionEtape2Form.level1,{ 'attr': {'placeholder': 'Sélectionnez votre niveau'} })}}</td>
				</tr>

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape2Form.langue2,"Langue étrangère 2")}}</td>
					<td class='label'>{{ form_label(InscriptionEtape2Form.level2,"Niveau")}}</td>
				</tr>

				<tr>
					<td>{{ form_widget(InscriptionEtape2Form.langue2,{ 'attr': {'placeholder': 'Sélectionnez la langue'} })}}</td>
					<td>{{ form_widget(InscriptionEtape2Form.level2,{ 'attr': {'placeholder': 'Sélectionnez votre niveau'} })}}</td>
				</tr>

				<tr>
					<td>{{ form_label(InscriptionEtape2Form.permis,"Permis B")}}&emsp;{{ form_widget(InscriptionEtape2Form.permis[0])}}&nbsp;{{ form_label(InscriptionEtape2Form.permis[0])}}&ensp;
						{{ form_widget(InscriptionEtape2Form.permis[1])}}&nbsp;{{ form_label(InscriptionEtape2Form.permis[1])}}</td>
					<td></td>
				</tr>

			</table> #}

		<div class="row">
			<h3>Votre recherche d'emploi</h3>
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.contrat_souhaitee) }}
					{{ form_errors(InscriptionEtape2Form.contrat_souhaitee) }}
				</div>
			</div>
			<div class="col-lg-6 col-sm-12" style="padding-right: 2em;">
				<div class="form-group">
					{{ form_row(InscriptionEtape2Form.remuneration_souhaitee) }}
					{{ form_errors(InscriptionEtape2Form.remuneration_souhaitee) }}
				</div>
			</div>
			{# <table class="formulaire">

				<tr>
					<td class='label'>{{ form_label(InscriptionEtape2Form.contrat_souhaitee,"Type de contrat souhaité")}}</td>
					<td class='label'>{{ form_label(InscriptionEtape2Form.remuneration_souhaitee,"Salaire souhaité")}}</td>
				</tr>

				<tr>
					<td>{{ form_widget(InscriptionEtape2Form.contrat_souhaitee,{ 'attr': {'placeholder': 'Sélectionnez un type de contrat'} })}}</td>
					<td>{{ form_widget(InscriptionEtape2Form.remuneration_souhaitee,{ 'attr': {'placeholder': 'Sélectionnez votre salaire souhaité'} })}}</td>
				</tr>
			</table> #}
		</div>


		<div class="row">
			<h3>Paramètres de confidentialité</h3>
			<table class="formulaire">

				<tr>
					<td>Rendre mon profil visible<span class="ob">
						*</span>
						<i>(votre profil sera consultable par les recruteur)</i>
					</td>
					<td>{{ form_widget(InscriptionEtape2Form.profil_visible[0])}}&nbsp;{{ form_label(InscriptionEtape2Form.profil_visible[0])}}&emsp;
						{{ form_widget(InscriptionEtape2Form.profil_visible[1])}}&nbsp;{{ form_label(InscriptionEtape2Form.profil_visible[1])}}</td>
				</tr>

				<tr>
					<td>Rendre mon profil anonyme<span class="ob">
						*</span>
						<i>(vos informations civiles seront masquées, veillez à utiliser un CV)</i>
					</td>
					<td>{{ form_widget(InscriptionEtape2Form.profil_anonyme[0])}}&nbsp;{{ form_label(InscriptionEtape2Form.profil_anonyme[0])}}&emsp;
						{{ form_widget(InscriptionEtape2Form.profil_anonyme[1])}}&nbsp;{{ form_label(InscriptionEtape2Form.profil_anonyme[1])}}</td>
				</tr>

				<tr>
					<td>Rendre mon CV visible<span class="ob">
						*</span>
						<i>(les recruteurs pourront télécharger votre CV)</i>
					</td>
					<td>{{ form_widget(InscriptionEtape2Form.cv_visible[0])}}&nbsp;{{ form_label(InscriptionEtape2Form.cv_visible[0])}}&emsp;
						{{ form_widget(InscriptionEtape2Form.cv_visible[1])}}&nbsp;{{ form_label(InscriptionEtape2Form.cv_visible[1])}}</td>
				</tr>

			</table>
		</div>

		<div style="text-align:center">{{ form_widget(InscriptionEtape2Form.save, { 'label': 'Valider et continuer' }) }}</div>
	</div>

{% endblock %}
