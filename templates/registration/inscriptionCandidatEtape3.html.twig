{% extends 'directemploi/layout.html.twig' %}
{% block title %}Inscription
{% endblock %}

{% block content %}


	<h1><PERSON><PERSON>er un compte candidat</h1>

	<ul class="filArianneInscription clearfix" style="display: block; margin-left: auto; margin-right: auto; width: 75%;">
		<li class="step_account"><h2>C<PERSON>er mon compte</h2></li>
		<li class="arrow"></li>
		<li class="step_profil"><h2>Définir mon profil</h2></li>
		<li class="arrow"></li>
		<li class="active step_alerte"><h2>Créer&nbsp;mon&nbsp;alerte&nbsp;mail</h2></li>
	</ul>

	<div class="obli">
		<p>Les champs marqués d'une astérisque
			<span class="ob">
				*</span>
			sont obligatoires</p>
	</div>

	{{ form_start(InscriptionEtape3Form) }}
	
	{{ form_row(InscriptionEtape3Form._token) }}

	<div class="form_box">
		<h3>Création / Edition d'une alerte mail</h3>
		<table class="formulaire">

			<tr>
				<td class='label' colspan='2'>{{ form_label(InscriptionEtape3Form.nom_alerte,"Nom de mon alerte")}}
					<span class="ob">
						*</span>
				</td>
			</tr>
			<tr>
				<td colspan='2'>{{ form_widget(InscriptionEtape3Form.nom_alerte, { 'attr': {'placeholder': 'Nom de mon alerte', 'style' : "width:882px"}})}}</td>
			</tr>

			<tr>
				<td class='label'>{{ form_label(InscriptionEtape3Form.contrat,"Type de contrat")}}
					<span class="ob">
						*</span>
				</td>
				<td class='label'>{{ form_label(InscriptionEtape3Form.experience,"Expérience")}}</td>
			</tr>
			<tr>
				<td>{{ form_widget(InscriptionEtape3Form.contrat, { 'attr': {'placeholder': "Sélectionnez votre contrat"} })}}</td>
				<td>{{ form_widget(InscriptionEtape3Form.experience, { 'attr': {'placeholder': "Sélectionnez votre experience"} })}}</td>
			</tr>


			<tr>
				<td class='label'>{{ form_label(InscriptionEtape3Form.domaine_activite,"Domaine de votre métier")}}
					<span class="ob">
						*</span>
				</td>
				<td class='label'>{{ form_label(InscriptionEtape3Form.geo_liste_region,"Région")}}</td>
			</tr>
			<tr>
				<td>{{ form_widget(InscriptionEtape3Form.domaine_activite)}}</td>
				<td>{{ form_widget(InscriptionEtape3Form.geo_liste_region,{ 'attr': {'placeholder': "Sélectionnez votre région"} })}}</td>
			</tr>


			<tr>
				<td class='label'>{{ form_label(InscriptionEtape3Form.metier_metier,"Métier")}}
					<span class="ob">
						*</span>
				</td>
				<td class='label'>{{ form_label(InscriptionEtape3Form.geo_departement,"Département")}}</td>
			</tr>
			<tr>
				<td>{{ form_widget(InscriptionEtape3Form.metier_metier,{ 'attr': {'placeholder': "Sélectionnez métier"} })}}</td>
				<td>{{ form_widget(InscriptionEtape3Form.geo_departement,{ 'attr': {'placeholder': "Sélectionnez votre département"} })}}</td>
			</tr>


			<tr>
				<td colspan='2' style='text-align:center'>{{ form_widget(InscriptionEtape3Form.handi[0])}}&nbsp;{{ form_label(InscriptionEtape3Form.handi[0])}}&emsp;
					{{ form_widget(InscriptionEtape3Form.handi[1])}}&nbsp;{{ form_label(InscriptionEtape3Form.handi[1])}}&emsp;&emsp;N'afficher que les offres ouvertes aux personnes en situation de handicap</td>
			</tr>

		</table>
	</div>

	<table style="width:100%">
		<tr>
			<td style="text-align:center">
				<a class="submit sub_blue" style="width:96px" href="{{ path('app_espace_candidat') }}">Passer</a>
			</td>
			<td style="text-align:center">{{ form_widget(InscriptionEtape3Form.save, { 'label': 'Enregistrer' }) }}</td>
		</tr>
	</table>


	{% block javascripts %}

		<script>
			window.onload = () => {
let region = document.querySelector("#inscription_candidat_etape3_geo_liste_region");
let domaine = document.querySelector("#inscription_candidat_etape3_domaine_activite");

region.addEventListener("change", function () {
let form = this.closest("form");
let data = this.name + "=" + this.value;

fetch(form.action, {
method: form.getAttribute("method"),
body: data,
headers: {
"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
}
}).then(response => response.text()).then(html => {
let content = document.createElement("html");
content.innerHTML = html;
let nouveauSelect = content.querySelector("#inscription_candidat_etape3_geo_departement");
document.querySelector("#inscription_candidat_etape3_geo_departement").replaceWith(nouveauSelect);
})
});

domaine.addEventListener("change", function () {
let form = this.closest("form");
let data = this.name + "=" + this.value;

fetch(form.action, {
method: form.getAttribute("method"),
body: data,
headers: {
"Content-Type": "application/x-www-form-urlencoded;charset:utf-8"
}
}).then(response => response.text()).then(html => {
let content = document.createElement("html");
content.innerHTML = html;
let nouveauSelect = content.querySelector("#inscription_candidat_etape3_metier_metier");
document.querySelector("#inscription_candidat_etape3_metier_metier").replaceWith(nouveauSelect);
})
});

}
		</script>
	{% endblock %}


{% endblock %}
