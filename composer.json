{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.0.2", "ext-ctype": "*", "ext-iconv": "*", "doctrine/annotations": "^1.0", "doctrine/dbal": "2.*", "doctrine/doctrine-bundle": "^2.5", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.11", "elasticsearch/elasticsearch": "^8.5", "knplabs/knp-paginator-bundle": "^5.8", "lexik/jwt-authentication-bundle": "*", "payum/payum-bundle": "^2.5.2", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.2", "ressio/pharse": "^0.2.0", "sensio/framework-extra-bundle": "^6.1", "symfony/apache-pack": "^1.0", "symfony/asset": "6.0.*", "symfony/console": "6.0.*", "symfony/dotenv": "6.0.*", "symfony/expression-language": "6.0.*", "symfony/flex": "^2", "symfony/form": "6.0.*", "symfony/framework-bundle": "6.0.*", "symfony/google-mailer": "6.0.*", "symfony/http-client": "6.0.*", "symfony/intl": "6.0.*", "symfony/mailer": "6.0.*", "symfony/mime": "6.0.*", "symfony/monolog-bundle": "^3.1", "symfony/notifier": "6.0.*", "symfony/process": "6.0.*", "symfony/property-access": "6.0.*", "symfony/property-info": "6.0.*", "symfony/proxy-manager-bridge": "6.0.*", "symfony/runtime": "6.0.*", "symfony/security-bundle": "6.0.*", "symfony/security-http": "6.0.*", "symfony/serializer": "6.0.*", "symfony/string": "6.0.*", "symfony/translation": "6.0.*", "symfony/twig-bundle": "6.0.*", "symfony/validator": "6.0.*", "symfony/web-link": "6.0.*", "symfony/yaml": "6.0.*", "twig/extra-bundle": "^2.12|^3.0", "twig/intl-extra": "^3.4", "twig/string-extra": "^3.4", "twig/twig": "^2.12|^3.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "symfony/browser-kit": "6.0.*", "symfony/css-selector": "6.0.*", "symfony/debug-bundle": "6.0.*", "symfony/maker-bundle": "^1.38", "symfony/phpunit-bridge": "^6.0", "symfony/stopwatch": "6.0.*", "symfony/web-profiler-bundle": "6.0.*"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true, "php-http/discovery": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.0.*"}}}