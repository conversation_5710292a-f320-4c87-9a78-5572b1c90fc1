
###> symfony/framework-bundle ###
/.idea
/.env
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/public/uploads/
/var/
/vendor/
/public/carriereBTP/Front/images
composer.lock
.env.local
###< symfony/framework-bundle ###

###> symfony/phpunit-bridge ###
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

### dossier images###
public/images/
