{"doctrine/annotations": {"version": "1.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "2.1.1"}, "doctrine/collections": {"version": "1.6.8"}, "doctrine/common": {"version": "3.2.1"}, "doctrine/dbal": {"version": "3.3.1"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.4", "ref": "f98f1affe028f8153a459d15f220ada3826b5aa2"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "config/packages/test/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.1", "ref": "ee609429c9ee23e22d6fa5728211768f51ed2818"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "2.0.4"}, "doctrine/instantiator": {"version": "1.4.0"}, "doctrine/lexer": {"version": "1.2.2"}, "doctrine/migrations": {"version": "3.4.1"}, "doctrine/orm": {"version": "2.11.1"}, "doctrine/persistence": {"version": "2.3.0"}, "doctrine/sql-formatter": {"version": "1.1.2"}, "egulias/email-validator": {"version": "3.1.2"}, "elastic/transport": {"version": "v8.5.0"}, "elasticsearch/elasticsearch": {"version": "v8.5.0"}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.5"}, "guzzlehttp/guzzle": {"version": "7.5.0"}, "guzzlehttp/promises": {"version": "1.5.2"}, "guzzlehttp/psr7": {"version": "2.4.3"}, "knplabs/knp-components": {"version": "v3.5.0"}, "knplabs/knp-paginator-bundle": {"version": "v5.8.0"}, "laminas/laminas-code": {"version": "4.5.1"}, "lexik/jwt-authentication-bundle": {"version": "2.21", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "e9481b233a11ef7e15fe055a2b21fd3ac1aa2bb7"}, "files": ["./config/packages/lexik_jwt_authentication.yaml"]}, "monolog/monolog": {"version": "2.3.5"}, "myclabs/deep-copy": {"version": "1.10.2"}, "nikic/php-parser": {"version": "v4.13.2"}, "payum/payum-bundle": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.4", "ref": "518ac22defa04a8a1d82479ed362e2921487adf0"}}, "phar-io/manifest": {"version": "2.0.3"}, "phar-io/version": {"version": "3.1.0"}, "php-http/discovery": {"version": "1.14.3"}, "php-http/httplug": {"version": "2.3.0"}, "php-http/promise": {"version": "1.1.0"}, "phpdocumentor/reflection-common": {"version": "2.2.0"}, "phpdocumentor/reflection-docblock": {"version": "5.3.0"}, "phpdocumentor/type-resolver": {"version": "1.6.0"}, "phpspec/prophecy": {"version": "v1.15.0"}, "phpstan/phpdoc-parser": {"version": "1.2.0"}, "phpunit/php-code-coverage": {"version": "9.2.10"}, "phpunit/php-file-iterator": {"version": "3.0.6"}, "phpunit/php-invoker": {"version": "3.1.1"}, "phpunit/php-text-template": {"version": "2.0.4"}, "phpunit/php-timer": {"version": "5.0.3"}, "phpunit/phpunit": {"version": "9.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "9.3", "ref": "a6249a6c4392e9169b87abf93225f7f9f59025e6"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "psr/cache": {"version": "3.0.0"}, "psr/container": {"version": "2.0.2"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-factory": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/link": {"version": "2.0.1"}, "psr/log": {"version": "3.0.0"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "ressio/pharse": {"version": "0.2.0"}, "sebastian/cli-parser": {"version": "1.0.1"}, "sebastian/code-unit": {"version": "1.0.8"}, "sebastian/code-unit-reverse-lookup": {"version": "2.0.3"}, "sebastian/comparator": {"version": "4.0.6"}, "sebastian/complexity": {"version": "2.0.2"}, "sebastian/diff": {"version": "4.0.4"}, "sebastian/environment": {"version": "5.1.3"}, "sebastian/exporter": {"version": "4.0.4"}, "sebastian/global-state": {"version": "5.0.3"}, "sebastian/lines-of-code": {"version": "1.0.3"}, "sebastian/object-enumerator": {"version": "4.0.4"}, "sebastian/object-reflector": {"version": "2.0.4"}, "sebastian/recursion-context": {"version": "4.0.4"}, "sebastian/resource-operations": {"version": "3.0.3"}, "sebastian/type": {"version": "2.3.4"}, "sebastian/version": {"version": "3.0.2"}, "sensio/framework-extra-bundle": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "symfony/apache-pack": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.0", "ref": "9d254a22efca7264203eea98b866f16f944b2f09"}, "files": ["public/.htaccess"]}, "symfony/asset": {"version": "v6.0.3"}, "symfony/browser-kit": {"version": "v6.0.3"}, "symfony/cache": {"version": "v6.0.3"}, "symfony/cache-contracts": {"version": "v3.0.0"}, "symfony/config": {"version": "v6.0.3"}, "symfony/console": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/css-selector": {"version": "v6.0.3"}, "symfony/debug-bundle": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.1", "ref": "0ce7a032d344fb7b661cd25d31914cd703ad445b"}, "files": ["config/packages/dev/debug.yaml"]}, "symfony/dependency-injection": {"version": "v6.0.3"}, "symfony/deprecation-contracts": {"version": "v3.0.0"}, "symfony/doctrine-bridge": {"version": "v6.0.3"}, "symfony/dom-crawler": {"version": "v6.0.3"}, "symfony/dotenv": {"version": "v6.0.3"}, "symfony/error-handler": {"version": "v6.0.3"}, "symfony/event-dispatcher": {"version": "v6.0.3"}, "symfony/event-dispatcher-contracts": {"version": "v3.0.0"}, "symfony/expression-language": {"version": "v6.0.3"}, "symfony/filesystem": {"version": "v6.0.3"}, "symfony/finder": {"version": "v6.0.3"}, "symfony/flex": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/form": {"version": "v6.0.3"}, "symfony/framework-bundle": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.4", "ref": "3cd216a4d007b78d8554d44a5b1c0a446dab24fb"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/google-mailer": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "f8fd4ddb9b477510f8f4bce2b9c054ab428c0120"}}, "symfony/http-client": {"version": "v6.0.3"}, "symfony/http-client-contracts": {"version": "v3.0.0"}, "symfony/http-foundation": {"version": "v6.0.3"}, "symfony/http-kernel": {"version": "v6.0.4"}, "symfony/intl": {"version": "v6.0.3"}, "symfony/mailer": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "bbfc7e27257d3a3f12a6fb0a42540a42d9623a37"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.36", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/mime": {"version": "v6.0.3"}, "symfony/monolog-bridge": {"version": "v6.0.3"}, "symfony/monolog-bundle": {"version": "3.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.7", "ref": "a7bace7dbc5a7ed5608dbe2165e0774c87175fe6"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/notifier": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.0", "ref": "c31585e252b32fe0e1f30b1f256af553f4a06eb9"}, "files": ["config/packages/notifier.yaml"]}, "symfony/options-resolver": {"version": "v6.0.3"}, "symfony/password-hasher": {"version": "v6.0.3"}, "symfony/phpunit-bridge": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "97cb3dc7b0f39c7cfc4b7553504c9d7b7795de96"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.24.0"}, "symfony/polyfill-intl-icu": {"version": "v1.24.0"}, "symfony/polyfill-intl-idn": {"version": "v1.24.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.24.0"}, "symfony/polyfill-mbstring": {"version": "v1.24.0"}, "symfony/polyfill-php81": {"version": "v1.24.0"}, "symfony/process": {"version": "v6.0.3"}, "symfony/property-access": {"version": "v6.0.3"}, "symfony/property-info": {"version": "v6.0.3"}, "symfony/proxy-manager-bridge": {"version": "v6.0.3"}, "symfony/routing": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "6.0", "ref": "ab9ad892b7bba7ac584f6dc2ccdb659d358c63c5"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/runtime": {"version": "v6.0.3"}, "symfony/security-bundle": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "09b5e809bd7a992061febd05b797c64a2d93b5cd"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v6.0.3"}, "symfony/security-csrf": {"version": "v6.0.3"}, "symfony/security-http": {"version": "v6.0.3"}, "symfony/serializer": {"version": "v6.0.3"}, "symfony/service-contracts": {"version": "v3.0.0"}, "symfony/stopwatch": {"version": "v6.0.3"}, "symfony/string": {"version": "v6.0.3"}, "symfony/translation": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "da64f5a2b6d96f5dc24914517c0350a5f91dee43"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v3.0.0"}, "symfony/twig-bridge": {"version": "v6.0.3"}, "symfony/twig-bundle": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.4", "ref": "bffbb8f1a849736e64006735afae730cb428b6ff"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "3eb8df139ec05414489d55b97603c5f6ca0c44cb"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v6.0.3"}, "symfony/var-exporter": {"version": "v6.0.3"}, "symfony/web-link": {"version": "v6.0.3"}, "symfony/web-profiler-bundle": {"version": "6.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/yaml": {"version": "v6.0.3"}, "theseer/tokenizer": {"version": "1.2.1"}, "twig/extra-bundle": {"version": "v3.3.7"}, "twig/intl-extra": {"version": "v3.4.2"}, "twig/string-extra": {"version": "v3.4.0"}, "twig/twig": {"version": "v3.3.7"}, "webmozart/assert": {"version": "1.10.0"}}