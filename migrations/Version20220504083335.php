<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220504083335 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE cv_lien_exp_pro_off_liste_sect_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_exp_pro_off_liste_sect_activite_off_liste_secteur_activite (cv_lien_exp_pro_off_liste_sect_activite_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_2BA542C95E1A0290 (cv_lien_exp_pro_off_liste_sect_activite_id), INDEX IDX_2BA542C9B78A629C (off_liste_secteur_activite_id), PRIMARY KEY(cv_lien_exp_pro_off_liste_sect_activite_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_exp_pro_off_liste_sect_activite_cv_experience_pro (cv_lien_exp_pro_off_liste_sect_activite_id INT NOT NULL, cv_experience_pro_id INT NOT NULL, INDEX IDX_9A6799955E1A0290 (cv_lien_exp_pro_off_liste_sect_activite_id), INDEX IDX_9A679995F6D4F645 (cv_experience_pro_id), PRIMARY KEY(cv_lien_exp_pro_off_liste_sect_activite_id, cv_experience_pro_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_formation_geo_departement (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_formation_geo_departement_geo_departement (cv_lien_formation_geo_departement_id INT NOT NULL, geo_departement_id INT NOT NULL, INDEX IDX_21872B647E037882 (cv_lien_formation_geo_departement_id), INDEX IDX_21872B64964DE230 (geo_departement_id), PRIMARY KEY(cv_lien_formation_geo_departement_id, geo_departement_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_formation_geo_departement_cv_formation (cv_lien_formation_geo_departement_id INT NOT NULL, cv_formation_id INT NOT NULL, INDEX IDX_8A2201AD7E037882 (cv_lien_formation_geo_departement_id), INDEX IDX_8A2201AD9112D88B (cv_formation_id), PRIMARY KEY(cv_lien_formation_geo_departement_id, cv_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_formation_geo_liste_pays (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_formation_geo_liste_pays_geo_liste_pays (cv_lien_formation_geo_liste_pays_id INT NOT NULL, geo_liste_pays_id INT NOT NULL, INDEX IDX_4C528D3B504D6EAC (cv_lien_formation_geo_liste_pays_id), INDEX IDX_4C528D3BA603EFEB (geo_liste_pays_id), PRIMARY KEY(cv_lien_formation_geo_liste_pays_id, geo_liste_pays_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_formation_geo_liste_pays_cv_formation (cv_lien_formation_geo_liste_pays_id INT NOT NULL, cv_formation_id INT NOT NULL, INDEX IDX_A7F99B2B504D6EAC (cv_lien_formation_geo_liste_pays_id), INDEX IDX_A7F99B2B9112D88B (cv_formation_id), PRIMARY KEY(cv_lien_formation_geo_liste_pays_id, cv_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_formation_geo_liste_region (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_formation_geo_liste_region_geo_liste_region (cv_lien_formation_geo_liste_region_id INT NOT NULL, geo_liste_region_id INT NOT NULL, INDEX IDX_92F45B333C59E2FB (cv_lien_formation_geo_liste_region_id), INDEX IDX_92F45B3319DE7EC1 (geo_liste_region_id), PRIMARY KEY(cv_lien_formation_geo_liste_region_id, geo_liste_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lien_formation_geo_liste_region_cv_formation (cv_lien_formation_geo_liste_region_id INT NOT NULL, cv_formation_id INT NOT NULL, INDEX IDX_C8B7EC013C59E2FB (cv_lien_formation_geo_liste_region_id), INDEX IDX_C8B7EC019112D88B (cv_formation_id), PRIMARY KEY(cv_lien_formation_geo_liste_region_id, cv_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lienformation_off_liste_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lienformation_off_liste_secteur_activite_cv_formation (cv_lienformation_off_liste_secteur_activite_id INT NOT NULL, cv_formation_id INT NOT NULL, INDEX IDX_AF1F34838E8D19DF (cv_lienformation_off_liste_secteur_activite_id), INDEX IDX_AF1F34839112D88B (cv_formation_id), PRIMARY KEY(cv_lienformation_off_liste_secteur_activite_id, cv_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_lienformation_off_liste_secteur_activite_off_liste_secteur_activite (cv_lienformation_off_liste_secteur_activite_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_9737B8928E8D19DF (cv_lienformation_off_liste_secteur_activite_id), INDEX IDX_9737B892B78A629C (off_liste_secteur_activite_id), PRIMARY KEY(cv_lienformation_off_liste_secteur_activite_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_log (id INT AUTO_INCREMENT NOT NULL, date_log DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_log_cv_cv (cv_log_id INT NOT NULL, cv_cv_id INT NOT NULL, INDEX IDX_7A54423167E054A3 (cv_log_id), INDEX IDX_7A54423140E0C9FD (cv_cv_id), PRIMARY KEY(cv_log_id, cv_cv_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_log_user_user (cv_log_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_476F439967E054A3 (cv_log_id), INDEX IDX_476F4399FF63CD9F (user_user_id), PRIMARY KEY(cv_log_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_commentaire (id INT AUTO_INCREMENT NOT NULL, commentaire LONGTEXT DEFAULT NULL, date_commentaire DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_commentaire_cv_cv (cv_recruteur_cv_commentaire_id INT NOT NULL, cv_cv_id INT NOT NULL, INDEX IDX_E2677317DF49666 (cv_recruteur_cv_commentaire_id), INDEX IDX_E26773140E0C9FD (cv_cv_id), PRIMARY KEY(cv_recruteur_cv_commentaire_id, cv_cv_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_commentaire_user_user (cv_recruteur_cv_commentaire_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_2FAECAB37DF49666 (cv_recruteur_cv_commentaire_id), INDEX IDX_2FAECAB3FF63CD9F (user_user_id), PRIMARY KEY(cv_recruteur_cv_commentaire_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_dossier (id INT AUTO_INCREMENT NOT NULL, nom_dossier VARCHAR(255) DEFAULT NULL, commentaire LONGTEXT DEFAULT NULL, date_creation DATETIME DEFAULT NULL, date_modification DATETIME DEFAULT NULL, nom_classe SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_dossier_user_user (cv_recruteur_cv_dossier_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_744301F035A241C (cv_recruteur_cv_dossier_id), INDEX IDX_744301F0FF63CD9F (user_user_id), PRIMARY KEY(cv_recruteur_cv_dossier_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_note (id INT AUTO_INCREMENT NOT NULL, note SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_note_cv_cv (cv_recruteur_cv_note_id INT NOT NULL, cv_cv_id INT NOT NULL, INDEX IDX_C1E40BBDAF01F264 (cv_recruteur_cv_note_id), INDEX IDX_C1E40BBD40E0C9FD (cv_cv_id), PRIMARY KEY(cv_recruteur_cv_note_id, cv_cv_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_note_user_user (cv_recruteur_cv_note_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_27DD38E9AF01F264 (cv_recruteur_cv_note_id), INDEX IDX_27DD38E9FF63CD9F (user_user_id), PRIMARY KEY(cv_recruteur_cv_note_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_sauvegarde (id INT AUTO_INCREMENT NOT NULL, date_sauvegarde DATE DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_sauvegarde_cv_cv (cv_recruteur_cv_sauvegarde_id INT NOT NULL, cv_cv_id INT NOT NULL, INDEX IDX_665AE5062ECB73F6 (cv_recruteur_cv_sauvegarde_id), INDEX IDX_665AE50640E0C9FD (cv_cv_id), PRIMARY KEY(cv_recruteur_cv_sauvegarde_id, cv_cv_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_sauvegarde_user_user (cv_recruteur_cv_sauvegarde_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_7C26B9A52ECB73F6 (cv_recruteur_cv_sauvegarde_id), INDEX IDX_7C26B9A5FF63CD9F (user_user_id), PRIMARY KEY(cv_recruteur_cv_sauvegarde_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cv_recruteur_cv_sauvegarde_cv_recruteur_cv_dossier (cv_recruteur_cv_sauvegarde_id INT NOT NULL, cv_recruteur_cv_dossier_id INT NOT NULL, INDEX IDX_B03894282ECB73F6 (cv_recruteur_cv_sauvegarde_id), INDEX IDX_B038942835A241C (cv_recruteur_cv_dossier_id), PRIMARY KEY(cv_recruteur_cv_sauvegarde_id, cv_recruteur_cv_dossier_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cvteque_profils (id INT AUTO_INCREMENT NOT NULL, cv_cv_id_cv INT DEFAULT NULL, id_reponse_offre_non_identifie INT DEFAULT NULL, email LONGTEXT DEFAULT NULL, prenom LONGTEXT DEFAULT NULL, nom LONGTEXT DEFAULT NULL, nom_fichier_cv_stockage LONGTEXT DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite INT DEFAULT NULL, user_disponibilite_id_disponibilite INT DEFAULT NULL, user_situation_activite_id_situation_activite INT DEFAULT NULL, ec_ecole_id_ecole INT DEFAULT NULL, ec_organisme_formation_id_organisme_formation INT DEFAULT NULL, id_ec_session_formation INT DEFAULT NULL, etablissement_autre LONGTEXT DEFAULT NULL, off_liste_type_formation_id_type_formation INT DEFAULT NULL, annee_obtention_diplome INT DEFAULT NULL, id_region INT DEFAULT NULL, id_departement INT DEFAULT NULL, id_metier INT DEFAULT NULL, id_off_fourchette_remuneration INT DEFAULT NULL, is_visible INT DEFAULT NULL, is_anonyme INT DEFAULT NULL, date_modification DATETIME DEFAULT NULL, date_creation_profil DATETIME DEFAULT NULL, mots_cles LONGTEXT DEFAULT NULL, geo_ville LONGTEXT DEFAULT NULL, lib_metier LONGTEXT DEFAULT NULL, lib_off_liste_secteur_activite LONGTEXT DEFAULT NULL, off_liste_type_contrat_id_type_contrat INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cvteque_profils_n (id INT AUTO_INCREMENT NOT NULL, cv_cv_id_cv INT DEFAULT NULL, id_reponse_offre_non_identifie INT DEFAULT NULL, email LONGTEXT DEFAULT NULL, prenom LONGTEXT DEFAULT NULL, nom LONGTEXT DEFAULT NULL, nom_fichier_cv_stockage LONGTEXT DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite INT DEFAULT NULL, user_disponibilite_id_disponibilite INT DEFAULT NULL, user_situation_activite_id_situation_activite INT DEFAULT NULL, ec_ecole_id_ecole INT DEFAULT NULL, ec_organisme_formation_id_organisme_formation INT DEFAULT NULL, id_ec_session_formation INT DEFAULT NULL, etablissement_autre LONGTEXT DEFAULT NULL, off_liste_type_formation_id_type_formation INT DEFAULT NULL, annee_obtention_diplome INT DEFAULT NULL, id_region INT DEFAULT NULL, id_departement INT DEFAULT NULL, id_metier INT DEFAULT NULL, id_off_fourchette_remuneration INT DEFAULT NULL, is_visible INT DEFAULT NULL, is_anonyme INT DEFAULT NULL, date_modification DATETIME DEFAULT NULL, date_creation_profil DATETIME DEFAULT NULL, mots_cles LONGTEXT DEFAULT NULL, geo_ville LONGTEXT DEFAULT NULL, lib_metier LONGTEXT DEFAULT NULL, lib_off_liste_secteur_activite LONGTEXT DEFAULT NULL, off_liste_type_contrat_id_type_contrat INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cvteque_profils_n1 (id INT AUTO_INCREMENT NOT NULL, cv_cv_id_cv INT DEFAULT NULL, id_reponse_offre_non_identifie INT DEFAULT NULL, email LONGTEXT DEFAULT NULL, prenom LONGTEXT DEFAULT NULL, nom LONGTEXT DEFAULT NULL, nom_fichier_cv_stockage LONGTEXT DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite INT DEFAULT NULL, user_disponibilite_id_disponibilite INT DEFAULT NULL, user_situation_activite_id_situation_activite INT DEFAULT NULL, ec_ecole_id_ecole INT DEFAULT NULL, ec_organisme_formation_id_organisme_formation INT DEFAULT NULL, id_ec_session_formation INT DEFAULT NULL, etablissement_autre LONGTEXT DEFAULT NULL, off_liste_type_formation_id_type_formation INT DEFAULT NULL, annee_obtention_diplome INT DEFAULT NULL, id_region INT DEFAULT NULL, id_departement INT DEFAULT NULL, id_metier INT DEFAULT NULL, id_off_fourchette_remuneration INT DEFAULT NULL, is_visible INT DEFAULT NULL, is_anonyme INT DEFAULT NULL, date_modification DATETIME DEFAULT NULL, date_creation_profil DATETIME DEFAULT NULL, mots_cles LONGTEXT DEFAULT NULL, geo_ville LONGTEXT DEFAULT NULL, lib_metier LONGTEXT DEFAULT NULL, lib_off_liste_secteur_activite LONGTEXT DEFAULT NULL, off_liste_type_contrat_id_type_contrat INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cvteque_profils_n2 (id INT AUTO_INCREMENT NOT NULL, cv_cv_id_cv INT DEFAULT NULL, id_reponse_offre_non_identifie INT DEFAULT NULL, email LONGTEXT DEFAULT NULL, prenom LONGTEXT DEFAULT NULL, nom LONGTEXT DEFAULT NULL, nom_fichier_cv_stockage LONGTEXT DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite INT DEFAULT NULL, user_disponibilite_id_disponibilite INT DEFAULT NULL, user_situation_activite_id_situation_activite INT DEFAULT NULL, ec_ecole_id_ecole INT DEFAULT NULL, ec_organisme_formation_id_organisme_formation INT DEFAULT NULL, id_ec_session_formation INT DEFAULT NULL, etablissement_autre LONGTEXT DEFAULT NULL, off_liste_type_formation_id_type_formation INT DEFAULT NULL, annee_obtention_diplome INT DEFAULT NULL, id_region INT DEFAULT NULL, id_departement INT DEFAULT NULL, id_metier INT DEFAULT NULL, id_off_fourchette_remuneration INT DEFAULT NULL, is_visible INT DEFAULT NULL, is_anonyme INT DEFAULT NULL, date_modification DATETIME DEFAULT NULL, date_creation_profil DATETIME DEFAULT NULL, mots_cles LONGTEXT DEFAULT NULL, geo_ville LONGTEXT DEFAULT NULL, lib_metier LONGTEXT DEFAULT NULL, lib_off_liste_secteur_activite LONGTEXT DEFAULT NULL, off_liste_type_contrat_id_type_contrat INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cvtheque_mots_cles (id INT AUTO_INCREMENT NOT NULL, mot_cle VARCHAR(255) DEFAULT NULL, id_secteur INT DEFAULT NULL, id_profils LONGTEXT DEFAULT NULL, date_modification DATETIME DEFAULT NULL, date_creation_profil DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_forum (id INT AUTO_INCREMENT NOT NULL, nom_forum VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, date_debut DATE DEFAULT NULL, date_fin DATE DEFAULT NULL, duree VARCHAR(255) DEFAULT NULL, site_internet VARCHAR(255) DEFAULT NULL, nom VARCHAR(255) DEFAULT NULL, prenom VARCHAR(255) DEFAULT NULL, telephone VARCHAR(255) DEFAULT NULL, fax VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_forum_ec_ecole (ec_calendrier_forum_id INT NOT NULL, ec_ecole_id INT NOT NULL, INDEX IDX_BFA1651712142D25 (ec_calendrier_forum_id), INDEX IDX_BFA16517CFF27E35 (ec_ecole_id), PRIMARY KEY(ec_calendrier_forum_id, ec_ecole_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_forum_off_liste_type_formation (ec_calendrier_forum_id INT NOT NULL, off_liste_type_formation_id INT NOT NULL, INDEX IDX_C2FBBC3612142D25 (ec_calendrier_forum_id), INDEX IDX_C2FBBC36A8BBB27F (off_liste_type_formation_id), PRIMARY KEY(ec_calendrier_forum_id, off_liste_type_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_forum_off_liste_secteur_activite (ec_calendrier_forum_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_733F263012142D25 (ec_calendrier_forum_id), INDEX IDX_733F2630B78A629C (off_liste_secteur_activite_id), PRIMARY KEY(ec_calendrier_forum_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_forum_geo_adresse (ec_calendrier_forum_id INT NOT NULL, geo_adresse_id INT NOT NULL, INDEX IDX_7DC887C712142D25 (ec_calendrier_forum_id), INDEX IDX_7DC887C7349A5BD2 (geo_adresse_id), PRIMARY KEY(ec_calendrier_forum_id, geo_adresse_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_forum_user_civilite (ec_calendrier_forum_id INT NOT NULL, user_civilite_id INT NOT NULL, INDEX IDX_88DA01A712142D25 (ec_calendrier_forum_id), INDEX IDX_88DA01A79002832A (user_civilite_id), PRIMARY KEY(ec_calendrier_forum_id, user_civilite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_stage (id INT AUTO_INCREMENT NOT NULL, type_stage VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, date_debut DATE DEFAULT NULL, date_fin DATE DEFAULT NULL, duree VARCHAR(255) DEFAULT NULL, nom VARCHAR(255) DEFAULT NULL, prenom VARCHAR(255) DEFAULT NULL, telephone VARCHAR(255) DEFAULT NULL, fax VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_stage_ec_ecole (ec_calendrier_stage_id INT NOT NULL, ec_ecole_id INT NOT NULL, INDEX IDX_90B52C9D19404666 (ec_calendrier_stage_id), INDEX IDX_90B52C9DCFF27E35 (ec_ecole_id), PRIMARY KEY(ec_calendrier_stage_id, ec_ecole_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_stage_ec_liste_annee_ecole (ec_calendrier_stage_id INT NOT NULL, ec_liste_annee_ecole_id INT NOT NULL, INDEX IDX_912D78B019404666 (ec_calendrier_stage_id), INDEX IDX_912D78B0F70135A (ec_liste_annee_ecole_id), PRIMARY KEY(ec_calendrier_stage_id, ec_liste_annee_ecole_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_stage_off_liste_type_formation (ec_calendrier_stage_id INT NOT NULL, off_liste_type_formation_id INT NOT NULL, INDEX IDX_3D2CAA4B19404666 (ec_calendrier_stage_id), INDEX IDX_3D2CAA4BA8BBB27F (off_liste_type_formation_id), PRIMARY KEY(ec_calendrier_stage_id, off_liste_type_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_stage_off_liste_secteur_activite (ec_calendrier_stage_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_1D7A6B0D19404666 (ec_calendrier_stage_id), INDEX IDX_1D7A6B0DB78A629C (off_liste_secteur_activite_id), PRIMARY KEY(ec_calendrier_stage_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_calendrier_stage_user_civilite (ec_calendrier_stage_id INT NOT NULL, user_civilite_id INT NOT NULL, INDEX IDX_9CA38DE219404666 (ec_calendrier_stage_id), INDEX IDX_9CA38DE29002832A (user_civilite_id), PRIMARY KEY(ec_calendrier_stage_id, user_civilite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_ecole (id INT AUTO_INCREMENT NOT NULL, nom_ecole VARCHAR(255) DEFAULT NULL, presentation_ecole LONGTEXT DEFAULT NULL, telephone VARCHAR(255) DEFAULT NULL, fax VARCHAR(255) DEFAULT NULL, site_internet VARCHAR(255) DEFAULT NULL, email_stage VARCHAR(255) DEFAULT NULL, email_emploi VARCHAR(255) DEFAULT NULL, email_ancien_eleve VARCHAR(255) DEFAULT NULL, url_xml_emploi VARCHAR(255) DEFAULT NULL, url_xml_stage VARCHAR(255) DEFAULT NULL, url_xml_alternance VARCHAR(255) DEFAULT NULL, logo VARCHAR(255) DEFAULT NULL, is_client SMALLINT DEFAULT NULL, classement INT DEFAULT NULL, volume_formation INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_ecole_geo_adresse (ec_ecole_id INT NOT NULL, geo_adresse_id INT NOT NULL, INDEX IDX_C471AD4FCFF27E35 (ec_ecole_id), INDEX IDX_C471AD4F349A5BD2 (geo_adresse_id), PRIMARY KEY(ec_ecole_id, geo_adresse_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_ecole_off_type_xml (ec_ecole_id INT NOT NULL, off_type_xml_id INT NOT NULL, INDEX IDX_42A8C242CFF27E35 (ec_ecole_id), INDEX IDX_42A8C242C2329246 (off_type_xml_id), PRIMARY KEY(ec_ecole_id, off_type_xml_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_ecole_ec_liste_type_ecole (ec_ecole_id INT NOT NULL, ec_liste_type_ecole_id INT NOT NULL, INDEX IDX_FB032AA0CFF27E35 (ec_ecole_id), INDEX IDX_FB032AA0D51A810 (ec_liste_type_ecole_id), PRIMARY KEY(ec_ecole_id, ec_liste_type_ecole_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_formation_temp (id INT AUTO_INCREMENT NOT NULL, nom_formation VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_formation_temp_ec_ecole (ec_formation_temp_id INT NOT NULL, ec_ecole_id INT NOT NULL, INDEX IDX_93D9B7C7632F2121 (ec_formation_temp_id), INDEX IDX_93D9B7C7CFF27E35 (ec_ecole_id), PRIMARY KEY(ec_formation_temp_id, ec_ecole_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_formation_temp_ec_organisme_formation (ec_formation_temp_id INT NOT NULL, ec_organisme_formation_id INT NOT NULL, INDEX IDX_FCEB1883632F2121 (ec_formation_temp_id), INDEX IDX_FCEB1883C540A215 (ec_organisme_formation_id), PRIMARY KEY(ec_formation_temp_id, ec_organisme_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_formation_temp_ec_liste_type_session (ec_formation_temp_id INT NOT NULL, ec_liste_type_session_id INT NOT NULL, INDEX IDX_9C9346F9632F2121 (ec_formation_temp_id), INDEX IDX_9C9346F9ADD459D9 (ec_liste_type_session_id), PRIMARY KEY(ec_formation_temp_id, ec_liste_type_session_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_lien_session_form_off_sect_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_lien_session_form_off_sect_activite_ec_session_formation (ec_lien_session_form_off_sect_activite_id INT NOT NULL, ec_session_formation_id INT NOT NULL, INDEX IDX_8834768AB9AD7B2 (ec_lien_session_form_off_sect_activite_id), INDEX IDX_8834768F551B8FE (ec_session_formation_id), PRIMARY KEY(ec_lien_session_form_off_sect_activite_id, ec_session_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_lien_session_form_off_sect_activite_off_liste_secteur_activite (ec_lien_session_form_off_sect_activite_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_40FBB76BAB9AD7B2 (ec_lien_session_form_off_sect_activite_id), INDEX IDX_40FBB76BB78A629C (off_liste_secteur_activite_id), PRIMARY KEY(ec_lien_session_form_off_sect_activite_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_lien_session_formation_off_liste_sous_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_ec_session_formation (ec_lien_session_formation_off_liste_sous_secteur_activite_id INT NOT NULL, ec_session_formation_id INT NOT NULL, INDEX IDX_2C0245D0AF8007FD (ec_lien_session_formation_off_liste_sous_secteur_activite_id), INDEX IDX_2C0245D0F551B8FE (ec_session_formation_id), PRIMARY KEY(ec_lien_session_formation_off_liste_sous_secteur_activite_id, ec_session_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite (ec_lien_session_formation_off_liste_sous_secteur_activite_id INT NOT NULL, off_liste_sous_secteur_activite_id INT NOT NULL, INDEX IDX_16E29B8BAF8007FD (ec_lien_session_formation_off_liste_sous_secteur_activite_id), INDEX IDX_16E29B8BCE509CB7 (off_liste_sous_secteur_activite_id), PRIMARY KEY(ec_lien_session_formation_off_liste_sous_secteur_activite_id, off_liste_sous_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_organisme_formation (id INT AUTO_INCREMENT NOT NULL, nom_organisme VARCHAR(255) DEFAULT NULL, presentation_organisme LONGTEXT DEFAULT NULL, telephone VARCHAR(255) DEFAULT NULL, fax VARCHAR(255) DEFAULT NULL, site_internet VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, ec_liste_type_organisme_formation_id_type_organisme_formation INT DEFAULT NULL, logo VARCHAR(255) DEFAULT NULL, is_client SMALLINT DEFAULT NULL, classement INT DEFAULT NULL, volume_formation INT DEFAULT NULL, logo_fond VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_organisme_formation_geo_adresse (ec_organisme_formation_id INT NOT NULL, geo_adresse_id INT NOT NULL, INDEX IDX_1433150C540A215 (ec_organisme_formation_id), INDEX IDX_1433150349A5BD2 (geo_adresse_id), PRIMARY KEY(ec_organisme_formation_id, geo_adresse_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_session_formation (id INT AUTO_INCREMENT NOT NULL, user_civilit�e_id_user_civilite_id INT NOT NULL, ec_ecole_id_ecole INT DEFAULT NULL, ec_organisme_formation_id_organisme_formation INT DEFAULT NULL, ec_liste_type_session_id_type_session INT DEFAULT NULL, nom_formation VARCHAR(255) DEFAULT NULL, duree DATE DEFAULT NULL, date_debut DATE DEFAULT NULL, date_fin DATE DEFAULT NULL, prix VARCHAR(255) DEFAULT NULL, presentation_formation LONGTEXT DEFAULT NULL, nom VARCHAR(255) DEFAULT NULL, prenom VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, telephone VARCHAR(255) DEFAULT NULL, fax VARCHAR(255) DEFAULT NULL, mobile VARCHAR(255) DEFAULT NULL, site_internet VARCHAR(255) DEFAULT NULL, statut SMALLINT DEFAULT NULL, reference VARCHAR(255) DEFAULT NULL, created_at DATETIME DEFAULT NULL, type_session SMALLINT DEFAULT NULL, sitemap_is_client SMALLINT DEFAULT NULL, sitemap_carriere_btp SMALLINT DEFAULT NULL, sitemap_carriere_industrie SMALLINT DEFAULT NULL, sitemap_carriere_informatique SMALLINT DEFAULT NULL, updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_6B9DBA02B16F300B (user_civilit�e_id_user_civilite_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_session_formation_geo_adresse (ec_session_formation_id INT NOT NULL, geo_adresse_id INT NOT NULL, INDEX IDX_91889352F551B8FE (ec_session_formation_id), INDEX IDX_91889352349A5BD2 (geo_adresse_id), PRIMARY KEY(ec_session_formation_id, geo_adresse_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_session_formation_off_liste_type_formation (ec_session_formation_id INT NOT NULL, off_liste_type_formation_id INT NOT NULL, INDEX IDX_3F31AE7BF551B8FE (ec_session_formation_id), INDEX IDX_3F31AE7BA8BBB27F (off_liste_type_formation_id), PRIMARY KEY(ec_session_formation_id, off_liste_type_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_stat_calendrier_forum (id INT AUTO_INCREMENT NOT NULL, date_log DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_stat_calendrier_forum_ec_calendrier_forum (ec_stat_calendrier_forum_id INT NOT NULL, ec_calendrier_forum_id INT NOT NULL, INDEX IDX_2E42C2578E254D36 (ec_stat_calendrier_forum_id), INDEX IDX_2E42C25712142D25 (ec_calendrier_forum_id), PRIMARY KEY(ec_stat_calendrier_forum_id, ec_calendrier_forum_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_stat_calendrier_stage (id INT AUTO_INCREMENT NOT NULL, date_log DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_stat_calendrier_stage_ec_calendrier_stage (ec_stat_calendrier_stage_id INT NOT NULL, ec_calendrier_stage_id INT NOT NULL, INDEX IDX_4F8ECD0585712675 (ec_stat_calendrier_stage_id), INDEX IDX_4F8ECD0519404666 (ec_calendrier_stage_id), PRIMARY KEY(ec_stat_calendrier_stage_id, ec_calendrier_stage_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_statistique (id INT AUTO_INCREMENT NOT NULL, adresse_ip VARCHAR(255) DEFAULT NULL, date_log DATETIME DEFAULT NULL, type_log SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_statistique_ec_session_formation (ec_statistique_id INT NOT NULL, ec_session_formation_id INT NOT NULL, INDEX IDX_5BF9C58E232F3941 (ec_statistique_id), INDEX IDX_5BF9C58EF551B8FE (ec_session_formation_id), PRIMARY KEY(ec_statistique_id, ec_session_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ec_statistique_user_user (ec_statistique_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_18095843232F3941 (ec_statistique_id), INDEX IDX_18095843FF63CD9F (user_user_id), PRIMARY KEY(ec_statistique_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_article (id INT AUTO_INCREMENT NOT NULL, titre VARCHAR(255) DEFAULT NULL, chapo LONGTEXT DEFAULT NULL, contenu LONGTEXT DEFAULT NULL, date_creation DATETIME DEFAULT NULL, date_modification DATETIME DEFAULT NULL, image_une VARCHAR(255) DEFAULT NULL, image_evenement VARCHAR(255) DEFAULT NULL, image_petite VARCHAR(255) DEFAULT NULL, image_allong�ee VARCHAR(255) DEFAULT NULL, image_mini VARCHAR(255) DEFAULT NULL, is_hidden SMALLINT DEFAULT NULL, count_stat INT DEFAULT NULL, accueil_titre_prefix VARCHAR(255) DEFAULT NULL, image_320x273 VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_article_off_liste_secteur_activite (edit_article_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_722B70E2E7695588 (edit_article_id), INDEX IDX_722B70E2B78A629C (off_liste_secteur_activite_id), PRIMARY KEY(edit_article_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_article_edit_type_article (edit_article_id INT NOT NULL, edit_type_article_id INT NOT NULL, INDEX IDX_6DBDE3C1E7695588 (edit_article_id), INDEX IDX_6DBDE3C17F3CE950 (edit_type_article_id), PRIMARY KEY(edit_article_id, edit_type_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_article_edit_theme_article (edit_article_id INT NOT NULL, edit_theme_article_id INT NOT NULL, INDEX IDX_DE7B9D1FE7695588 (edit_article_id), INDEX IDX_DE7B9D1F38954BBE (edit_theme_article_id), PRIMARY KEY(edit_article_id, edit_theme_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_article_edit_sous_theme_article (edit_article_id INT NOT NULL, edit_sous_theme_article_id INT NOT NULL, INDEX IDX_F18B9958E7695588 (edit_article_id), INDEX IDX_F18B9958F4BFBBE (edit_sous_theme_article_id), PRIMARY KEY(edit_article_id, edit_sous_theme_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_article_user_user (edit_article_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_10269982E7695588 (edit_article_id), INDEX IDX_10269982FF63CD9F (user_user_id), PRIMARY KEY(edit_article_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_carte_departement (id INT AUTO_INCREMENT NOT NULL, coordonnee_departement LONGTEXT DEFAULT NULL, transformation VARCHAR(255) DEFAULT NULL, titre_ligne_1 VARCHAR(255) NOT NULL, coordonnee_x_titre_ligne_1 DOUBLE PRECISION DEFAULT NULL, coordonnee_y_titre_ligne1 DOUBLE PRECISION DEFAULT NULL, titre_ligne_2 VARCHAR(255) DEFAULT NULL, coordonnee_x_titre_ligne_2 DOUBLE PRECISION DEFAULT NULL, coordonnee_y_titre_ligne_2 DOUBLE PRECISION DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_carte_departement_geo_departement (edit_carte_departement_id INT NOT NULL, geo_departement_id INT NOT NULL, INDEX IDX_76249A0DB60CC0C0 (edit_carte_departement_id), INDEX IDX_76249A0D964DE230 (geo_departement_id), PRIMARY KEY(edit_carte_departement_id, geo_departement_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_carte_departement_edit_carte_region (edit_carte_departement_id INT NOT NULL, edit_carte_region_id INT NOT NULL, INDEX IDX_8E326622B60CC0C0 (edit_carte_departement_id), INDEX IDX_8E3266226FBE7ACB (edit_carte_region_id), PRIMARY KEY(edit_carte_departement_id, edit_carte_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_carte_region (id INT AUTO_INCREMENT NOT NULL, coordonn�ee_region LONGTEXT DEFAULT NULL, titre VARCHAR(255) DEFAULT NULL, viewbox_departements VARCHAR(255) DEFAULT NULL, image_fond VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_carte_region_geo_liste_region (edit_carte_region_id INT NOT NULL, geo_liste_region_id INT NOT NULL, INDEX IDX_AA1D1E656FBE7ACB (edit_carte_region_id), INDEX IDX_AA1D1E6519DE7EC1 (geo_liste_region_id), PRIMARY KEY(edit_carte_region_id, geo_liste_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_carte_region_edit_article (edit_carte_region_id INT NOT NULL, edit_article_id INT NOT NULL, INDEX IDX_2E3DEC626FBE7ACB (edit_carte_region_id), INDEX IDX_2E3DEC62E7695588 (edit_article_id), PRIMARY KEY(edit_carte_region_id, edit_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_carte_visite (id INT AUTO_INCREMENT NOT NULL, telephone VARCHAR(255) DEFAULT NULL, fax VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, site_internet VARCHAR(255) DEFAULT NULL, is_hidden SMALLINT DEFAULT NULL, nom_entreprise VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_carte_visite_edit_article (edit_carte_visite_id INT NOT NULL, edit_article_id INT NOT NULL, INDEX IDX_6A5B2464365DA7C7 (edit_carte_visite_id), INDEX IDX_6A5B2464E7695588 (edit_article_id), PRIMARY KEY(edit_carte_visite_id, edit_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_carte_visite_geo_adresse (edit_carte_visite_id INT NOT NULL, geo_adresse_id INT NOT NULL, INDEX IDX_E65DC032365DA7C7 (edit_carte_visite_id), INDEX IDX_E65DC032349A5BD2 (geo_adresse_id), PRIMARY KEY(edit_carte_visite_id, geo_adresse_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_focus_accueil (id INT AUTO_INCREMENT NOT NULL, edit_article_id_article_une_magazine INT DEFAULT NULL, edit_article_id_article_accueil_haut INT DEFAULT NULL, edit_article_id_article_accueil_bas INT DEFAULT NULL, edit_article_id_article_magazine_evt1 INT DEFAULT NULL, edit_article_id_article_magazine_evt2 INT DEFAULT NULL, edit_article_id_article_magazine_evt3 INT DEFAULT NULL, edit_article_id_article_magazine_centre INT DEFAULT NULL, edit_article_id_article_magazine_video INT DEFAULT NULL, edit_article_id_article_magazine_emploi INT DEFAULT NULL, edit_article_id_article_magazine_stage INT DEFAULT NULL, edit_article_id_article_magazine_alternance INT DEFAULT NULL, edit_article_id_article_magazine_continu INT DEFAULT NULL, edit_article_id_article_magazine_initial INT DEFAULT NULL, edit_article_id_article_magazine_zoom INT DEFAULT NULL, edit_habillage_id_habillage INT DEFAULT NULL, edit_article_id_article_accueil_ligne2_gauche INT DEFAULT NULL, edit_article_id_article_accueil_ligne2_droite INT DEFAULT NULL, edit_article_id_article_formation_gauche INT DEFAULT NULL, edit_article_id_article_formation_droite INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_focus_accueil_edit_app (edit_focus_accueil_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_2DFB15A739FA9219 (edit_focus_accueil_id), INDEX IDX_2DFB15A7570A9ED9 (edit_app_id), PRIMARY KEY(edit_focus_accueil_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_image_accueil (id INT AUTO_INCREMENT NOT NULL, nom VARCHAR(255) DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, url VARCHAR(255) DEFAULT NULL, target VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_image_accueil_edit_focus_accueil (edit_image_accueil_id INT NOT NULL, edit_focus_accueil_id INT NOT NULL, INDEX IDX_DCCFEA79C986DB92 (edit_image_accueil_id), INDEX IDX_DCCFEA7939FA9219 (edit_focus_accueil_id), PRIMARY KEY(edit_image_accueil_id, edit_focus_accueil_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_image_accueil_edit_theme_article (edit_image_accueil_id INT NOT NULL, edit_theme_article_id INT NOT NULL, INDEX IDX_7FB9621BC986DB92 (edit_image_accueil_id), INDEX IDX_7FB9621B38954BBE (edit_theme_article_id), PRIMARY KEY(edit_image_accueil_id, edit_theme_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_image_profil (id INT AUTO_INCREMENT NOT NULL, src VARCHAR(255) DEFAULT NULL, url VARCHAR(255) DEFAULT NULL, target VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_image_profil_edit_zoom_profil (edit_image_profil_id INT NOT NULL, edit_zoom_profil_id INT NOT NULL, INDEX IDX_735519E1EF77B2C5 (edit_image_profil_id), INDEX IDX_735519E1F96D9DBD (edit_zoom_profil_id), PRIMARY KEY(edit_image_profil_id, edit_zoom_profil_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_image_zoom (id INT AUTO_INCREMENT NOT NULL, src VARCHAR(255) DEFAULT NULL, url VARCHAR(255) DEFAULT NULL, target VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_image_zoom_edit_zoom_sectoriel (edit_image_zoom_id INT NOT NULL, edit_zoom_sectoriel_id INT NOT NULL, INDEX IDX_AB532EAF80DE7A6C (edit_image_zoom_id), INDEX IDX_AB532EAF179B07B3 (edit_zoom_sectoriel_id), PRIMARY KEY(edit_image_zoom_id, edit_zoom_sectoriel_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_photo_article (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_photo_article_edit_article (edit_lien_photo_article_id INT NOT NULL, edit_article_id INT NOT NULL, INDEX IDX_F0A9E0E39D70811F (edit_lien_photo_article_id), INDEX IDX_F0A9E0E3E7695588 (edit_article_id), PRIMARY KEY(edit_lien_photo_article_id, edit_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_photo_article_edit_photo (edit_lien_photo_article_id INT NOT NULL, edit_photo_id INT NOT NULL, INDEX IDX_2E9E95C99D70811F (edit_lien_photo_article_id), INDEX IDX_2E9E95C95590E8A5 (edit_photo_id), PRIMARY KEY(edit_lien_photo_article_id, edit_photo_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_departement (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_departement_edit_rss_thematique (edit_lien_rss_thematique_departement_id INT NOT NULL, edit_rss_thematique_id INT NOT NULL, INDEX IDX_6651E1476A97D0E4 (edit_lien_rss_thematique_departement_id), INDEX IDX_6651E1479C672457 (edit_rss_thematique_id), PRIMARY KEY(edit_lien_rss_thematique_departement_id, edit_rss_thematique_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_departement_geo_departement (edit_lien_rss_thematique_departement_id INT NOT NULL, geo_departement_id INT NOT NULL, INDEX IDX_B6E04F456A97D0E4 (edit_lien_rss_thematique_departement_id), INDEX IDX_B6E04F45964DE230 (geo_departement_id), PRIMARY KEY(edit_lien_rss_thematique_departement_id, geo_departement_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_region (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_region_edit_rss_thematique (edit_lien_rss_thematique_region_id INT NOT NULL, edit_rss_thematique_id INT NOT NULL, INDEX IDX_35AC6E2E49E1C286 (edit_lien_rss_thematique_region_id), INDEX IDX_35AC6E2E9C672457 (edit_rss_thematique_id), PRIMARY KEY(edit_lien_rss_thematique_region_id, edit_rss_thematique_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_region_geo_liste_region (edit_lien_rss_thematique_region_id INT NOT NULL, geo_liste_region_id INT NOT NULL, INDEX IDX_8188027349E1C286 (edit_lien_rss_thematique_region_id), INDEX IDX_8188027319DE7EC1 (geo_liste_region_id), PRIMARY KEY(edit_lien_rss_thematique_region_id, geo_liste_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_secteur_activite_edit_rss_thematique (edit_lien_rss_thematique_secteur_activite_id INT NOT NULL, edit_rss_thematique_id INT NOT NULL, INDEX IDX_997FF11F1A88EAE4 (edit_lien_rss_thematique_secteur_activite_id), INDEX IDX_997FF11F9C672457 (edit_rss_thematique_id), PRIMARY KEY(edit_lien_rss_thematique_secteur_activite_id, edit_rss_thematique_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_secteur_activite_off_liste_secteur_activite (edit_lien_rss_thematique_secteur_activite_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_C79248CE1A88EAE4 (edit_lien_rss_thematique_secteur_activite_id), INDEX IDX_C79248CEB78A629C (off_liste_secteur_activite_id), PRIMARY KEY(edit_lien_rss_thematique_secteur_activite_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_sous_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_sous_secteur_activite_edit_rss_thematique (edit_lien_rss_thematique_sous_secteur_activite_id INT NOT NULL, edit_rss_thematique_id INT NOT NULL, INDEX IDX_BA12FEE23732BFA7 (edit_lien_rss_thematique_sous_secteur_activite_id), INDEX IDX_BA12FEE29C672457 (edit_rss_thematique_id), PRIMARY KEY(edit_lien_rss_thematique_sous_secteur_activite_id, edit_rss_thematique_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_rss_thematique_sous_secteur_activite_off_liste_sous_secteur_activite (edit_lien_rss_thematique_sous_secteur_activite_id INT NOT NULL, off_liste_sous_secteur_activite_id INT NOT NULL, INDEX IDX_78F46E7E3732BFA7 (edit_lien_rss_thematique_sous_secteur_activite_id), INDEX IDX_78F46E7ECE509CB7 (off_liste_sous_secteur_activite_id), PRIMARY KEY(edit_lien_rss_thematique_sous_secteur_activite_id, off_liste_sous_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_video_article (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_video_article_edit_article (edit_lien_video_article_id INT NOT NULL, edit_article_id INT NOT NULL, INDEX IDX_7AEE545251B377A1 (edit_lien_video_article_id), INDEX IDX_7AEE5452E7695588 (edit_article_id), PRIMARY KEY(edit_lien_video_article_id, edit_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_video_article_edit_video (edit_lien_video_article_id INT NOT NULL, edit_video_id INT NOT NULL, INDEX IDX_39A5694B51B377A1 (edit_lien_video_article_id), INDEX IDX_39A5694B2CFA467 (edit_video_id), PRIMARY KEY(edit_lien_video_article_id, edit_video_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_zoom_sectoriel_article (id INT AUTO_INCREMENT NOT NULL, ordre_article SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_zoom_sectoriel_article_edit_zoom_sectoriel (edit_lien_zoom_sectoriel_article_id INT NOT NULL, edit_zoom_sectoriel_id INT NOT NULL, INDEX IDX_DFFE86E83036924A (edit_lien_zoom_sectoriel_article_id), INDEX IDX_DFFE86E8179B07B3 (edit_zoom_sectoriel_id), PRIMARY KEY(edit_lien_zoom_sectoriel_article_id, edit_zoom_sectoriel_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_zoom_sectoriel_article_edit_article (edit_lien_zoom_sectoriel_article_id INT NOT NULL, edit_article_id INT NOT NULL, INDEX IDX_F357AA9F3036924A (edit_lien_zoom_sectoriel_article_id), INDEX IDX_F357AA9FE7695588 (edit_article_id), PRIMARY KEY(edit_lien_zoom_sectoriel_article_id, edit_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_edit_zoom_sectoriel (edit_lien_zoom_sectoriel_off_liste_secteur_activite_id INT NOT NULL, edit_zoom_sectoriel_id INT NOT NULL, INDEX IDX_D9EA904022CB57AF (edit_lien_zoom_sectoriel_off_liste_secteur_activite_id), INDEX IDX_D9EA9040179B07B3 (edit_zoom_sectoriel_id), PRIMARY KEY(edit_lien_zoom_sectoriel_off_liste_secteur_activite_id, edit_zoom_sectoriel_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_off_liste_secteur_activite (edit_lien_zoom_sectoriel_off_liste_secteur_activite_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_F22027FD22CB57AF (edit_lien_zoom_sectoriel_off_liste_secteur_activite_id), INDEX IDX_F22027FDB78A629C (off_liste_secteur_activite_id), PRIMARY KEY(edit_lien_zoom_sectoriel_off_liste_secteur_activite_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_list_accueil (id INT AUTO_INCREMENT NOT NULL, date_list DATETIME DEFAULT NULL, titre VARCHAR(255) DEFAULT NULL, resume VARCHAR(255) DEFAULT NULL, url VARCHAR(255) DEFAULT NULL, target VARCHAR(255) DEFAULT NULL, geo_ville VARCHAR(255) DEFAULT NULL, type_contrat VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_list_accueil_edit_focus_accueil (edit_list_accueil_id INT NOT NULL, edit_focus_accueil_id INT NOT NULL, INDEX IDX_53F0B9C5B618F0F4 (edit_list_accueil_id), INDEX IDX_53F0B9C539FA9219 (edit_focus_accueil_id), PRIMARY KEY(edit_list_accueil_id, edit_focus_accueil_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_list_accueil_edit_theme_article (edit_list_accueil_id INT NOT NULL, edit_theme_article_id INT NOT NULL, INDEX IDX_F08631A7B618F0F4 (edit_list_accueil_id), INDEX IDX_F08631A738954BBE (edit_theme_article_id), PRIMARY KEY(edit_list_accueil_id, edit_theme_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_list_entreprise (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_list_entreprise_edit_carte_visite (edit_list_entreprise_id INT NOT NULL, edit_carte_visite_id INT NOT NULL, INDEX IDX_19503173FC81074D (edit_list_entreprise_id), INDEX IDX_19503173365DA7C7 (edit_carte_visite_id), PRIMARY KEY(edit_list_entreprise_id, edit_carte_visite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_list_entreprise_entr_entreprise (edit_list_entreprise_id INT NOT NULL, entr_entreprise_id INT NOT NULL, INDEX IDX_1D791962FC81074D (edit_list_entreprise_id), INDEX IDX_1D7919622923CBC2 (entr_entreprise_id), PRIMARY KEY(edit_list_entreprise_id, entr_entreprise_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_list_tag (id INT AUTO_INCREMENT NOT NULL, logo VARCHAR(255) DEFAULT NULL, titre VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_list_tag_edit_app (edit_list_tag_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_75ED7A12394D8891 (edit_list_tag_id), INDEX IDX_75ED7A12570A9ED9 (edit_app_id), PRIMARY KEY(edit_list_tag_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_list_tag_off_liste_secteur_activite (edit_list_tag_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_40953CF6394D8891 (edit_list_tag_id), INDEX IDX_40953CF6B78A629C (off_liste_secteur_activite_id), PRIMARY KEY(edit_list_tag_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_log_habillage (id INT AUTO_INCREMENT NOT NULL, date_log DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_log_habillage_edit_habillage (edit_log_habillage_id INT NOT NULL, edit_habillage_id INT NOT NULL, INDEX IDX_21CAEF345684EAC4 (edit_log_habillage_id), INDEX IDX_21CAEF34666748C1 (edit_habillage_id), PRIMARY KEY(edit_log_habillage_id, edit_habillage_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_page_bouche (id INT AUTO_INCREMENT NOT NULL, image_page_bouche VARCHAR(255) DEFAULT NULL, zoom_page_bouche INT DEFAULT NULL, description_page_bouche LONGTEXT DEFAULT NULL, user_user_id_user_1 INT DEFAULT NULL, user_user_id_user_2 INT DEFAULT NULL, user_user_id_user_3 INT DEFAULT NULL, user_user_id�_user_4 INT DEFAULT NULL, user_user_id_user_5 INT DEFAULT NULL, user_user_id_user_6 INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_page_bouche_edit_app (edit_page_bouche_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_B8FBE8E6DBF0A629 (edit_page_bouche_id), INDEX IDX_B8FBE8E6570A9ED9 (edit_app_id), PRIMARY KEY(edit_page_bouche_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_page_cadre (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_page_cadre_edit_app (edit_page_cadre_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_461C89A61A1CEF4 (edit_page_cadre_id), INDEX IDX_461C89A570A9ED9 (edit_app_id), PRIMARY KEY(edit_page_cadre_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_page_cadre_edit_article (edit_page_cadre_id INT NOT NULL, edit_article_id INT NOT NULL, INDEX IDX_57E9FC9C61A1CEF4 (edit_page_cadre_id), INDEX IDX_57E9FC9CE7695588 (edit_article_id), PRIMARY KEY(edit_page_cadre_id, edit_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_page_retail (id INT AUTO_INCREMENT NOT NULL, image_page_retail VARCHAR(255) DEFAULT NULL, zoom_page_retail INT DEFAULT NULL, description_page_retail LONGTEXT DEFAULT NULL, user_user_id_user_1 INT DEFAULT NULL, user_user_id_user_2 INT DEFAULT NULL, user_user_id_user_3 INT DEFAULT NULL, user_user_id_user_4 INT DEFAULT NULL, user_user_id_user_5 INT DEFAULT NULL, user_user_id_user_6 INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_page_retail_edit_app (edit_page_retail_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_22981E224D273415 (edit_page_retail_id), INDEX IDX_22981E22570A9ED9 (edit_app_id), PRIMARY KEY(edit_page_retail_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_page_thematique (id INT AUTO_INCREMENT NOT NULL, titre_presentation VARCHAR(255) DEFAULT NULL, sous_titre_presentation VARCHAR(255) DEFAULT NULL, texte_presentation LONGTEXT DEFAULT NULL, image_presentation VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_page_thematique_edit_sous_theme_article (edit_page_thematique_id INT NOT NULL, edit_sous_theme_article_id INT NOT NULL, INDEX IDX_71C035F948FA23A (edit_page_thematique_id), INDEX IDX_71C035F9F4BFBBE (edit_sous_theme_article_id), PRIMARY KEY(edit_page_thematique_id, edit_sous_theme_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_publicite_entreprise_carte_region (id INT AUTO_INCREMENT NOT NULL, user_user_id_user_2 INT DEFAULT NULL, user_user_id_user_3 INT DEFAULT NULL, user_user_id_user_4 INT DEFAULT NULL, user_user_id_user_5 INT DEFAULT NULL, user_user_id_user_6 INT DEFAULT NULL, user_user_id_user_7 INT DEFAULT NULL, user_user_id_user_8 INT DEFAULT NULL, user_user_id_user_9 INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_publicite_entreprise_carte_region_edit_app (edit_publicite_entreprise_carte_region_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_68682061BCC314D9 (edit_publicite_entreprise_carte_region_id), INDEX IDX_68682061570A9ED9 (edit_app_id), PRIMARY KEY(edit_publicite_entreprise_carte_region_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_publicite_entreprise_carte_region_edit_carte_region (edit_publicite_entreprise_carte_region_id INT NOT NULL, edit_carte_region_id INT NOT NULL, INDEX IDX_D156CC8EBCC314D9 (edit_publicite_entreprise_carte_region_id), INDEX IDX_D156CC8E6FBE7ACB (edit_carte_region_id), PRIMARY KEY(edit_publicite_entreprise_carte_region_id, edit_carte_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_publicite_entreprise_carte_region_user_user (edit_publicite_entreprise_carte_region_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_771BADDCBCC314D9 (edit_publicite_entreprise_carte_region_id), INDEX IDX_771BADDCFF63CD9F (user_user_id), PRIMARY KEY(edit_publicite_entreprise_carte_region_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_referencement (id INT AUTO_INCREMENT NOT NULL, url VARCHAR(255) DEFAULT NULL, titre VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, sitemap SMALLINT DEFAULT NULL, keywords VARCHAR(255) DEFAULT NULL, paragraphe_titre VARCHAR(255) DEFAULT NULL, paragraphe_contenu LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_referencement_edit_app (edit_referencement_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_40B1606349FF2995 (edit_referencement_id), INDEX IDX_40B16063570A9ED9 (edit_app_id), PRIMARY KEY(edit_referencement_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_sous_theme_article (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, slug VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_sous_theme_article_edit_theme_article (edit_sous_theme_article_id INT NOT NULL, edit_theme_article_id INT NOT NULL, INDEX IDX_8F53F719F4BFBBE (edit_sous_theme_article_id), INDEX IDX_8F53F71938954BBE (edit_theme_article_id), PRIMARY KEY(edit_sous_theme_article_id, edit_theme_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_statistique (id INT AUTO_INCREMENT NOT NULL, date_log DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_statistique_edit_article (edit_statistique_id INT NOT NULL, edit_article_id INT NOT NULL, INDEX IDX_51D762F376BA7F9 (edit_statistique_id), INDEX IDX_51D762F3E7695588 (edit_article_id), PRIMARY KEY(edit_statistique_id, edit_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_statistique_structure (id INT AUTO_INCREMENT NOT NULL, adresse_ip VARCHAR(255) DEFAULT NULL, date_log DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_statistique_structure_user_user (edit_statistique_structure_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_DBB3B1AECC27D7A4 (edit_statistique_structure_id), INDEX IDX_DBB3B1AEFF63CD9F (user_user_id), PRIMARY KEY(edit_statistique_structure_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_statistique_structure_ec_ecole (edit_statistique_structure_id INT NOT NULL, ec_ecole_id INT NOT NULL, INDEX IDX_D40A5E3CC27D7A4 (edit_statistique_structure_id), INDEX IDX_D40A5E3CFF27E35 (ec_ecole_id), PRIMARY KEY(edit_statistique_structure_id, ec_ecole_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_statistique_structure_ec_organisme_formation (edit_statistique_structure_id INT NOT NULL, ec_organisme_formation_id INT NOT NULL, INDEX IDX_866F9E0ECC27D7A4 (edit_statistique_structure_id), INDEX IDX_866F9E0EC540A215 (ec_organisme_formation_id), PRIMARY KEY(edit_statistique_structure_id, ec_organisme_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_statistique_structure_entr_entreprise (edit_statistique_structure_id INT NOT NULL, entr_entreprise_id INT NOT NULL, INDEX IDX_19F22B1CCC27D7A4 (edit_statistique_structure_id), INDEX IDX_19F22B1C2923CBC2 (entr_entreprise_id), PRIMARY KEY(edit_statistique_structure_id, entr_entreprise_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_zoom_accueil (id INT AUTO_INCREMENT NOT NULL, titre VARCHAR(255) DEFAULT NULL, contenu LONGTEXT DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, focus SMALLINT DEFAULT NULL, titre_onglet VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_zoom_accueil_edit_focus_accueil (edit_zoom_accueil_id INT NOT NULL, edit_focus_accueil_id INT NOT NULL, INDEX IDX_7AA3E250974E38B3 (edit_zoom_accueil_id), INDEX IDX_7AA3E25039FA9219 (edit_focus_accueil_id), PRIMARY KEY(edit_zoom_accueil_id, edit_focus_accueil_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_zoom_profil (id INT AUTO_INCREMENT NOT NULL, label VARCHAR(255) DEFAULT NULL, titre VARCHAR(255) DEFAULT NULL, sous_titre VARCHAR(255) DEFAULT NULL, contenu LONGTEXT DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, edit_article_id_article_zoom1 INT DEFAULT NULL, edit_article_id_article_zoom2 INT DEFAULT NULL, edit_article_id_article_zoom3 INT DEFAULT NULL, edit_article_id_article_zoom4 INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE edit_zoom_profil_edit_article (edit_zoom_profil_id INT NOT NULL, edit_article_id INT NOT NULL, INDEX IDX_8FB7C766F96D9DBD (edit_zoom_profil_id), INDEX IDX_8FB7C766E7695588 (edit_article_id), PRIMARY KEY(edit_zoom_profil_id, edit_article_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE entr_entreprise (id INT AUTO_INCREMENT NOT NULL, raison_sociale VARCHAR(255) DEFAULT NULL, siret VARCHAR(255) DEFAULT NULL, date_creation DATE NOT NULL, date_modif DATE DEFAULT NULL, telephone VARCHAR(255) DEFAULT NULL, fax VARCHAR(255) DEFAULT NULL, tag SMALLINT DEFAULT NULL, site_web VARCHAR(255) DEFAULT NULL, logo VARCHAR(255) DEFAULT NULL, presentation_societe LONGTEXT DEFAULT NULL, entr_profil_recruteur_id_profil_recruteur_stage INT DEFAULT NULL, volume_offre INT DEFAULT NULL, cabinet_recruteur SMALLINT DEFAULT NULL, logo_fond VARCHAR(255) DEFAULT NULL, cv_consultable INT DEFAULT NULL, agence_interim SMALLINT DEFAULT NULL, type_entreprise INT DEFAULT NULL, type_ecole INT DEFAULT NULL, actif INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE entr_entreprise_geo_adresse (entr_entreprise_id INT NOT NULL, geo_adresse_id INT NOT NULL, INDEX IDX_65592BB12923CBC2 (entr_entreprise_id), INDEX IDX_65592BB1349A5BD2 (geo_adresse_id), PRIMARY KEY(entr_entreprise_id, geo_adresse_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE entr_entreprise_entr_profil_recruteur (entr_entreprise_id INT NOT NULL, entr_profil_recruteur_id INT NOT NULL, INDEX IDX_F96B29FD2923CBC2 (entr_entreprise_id), INDEX IDX_F96B29FDBC1D969B (entr_profil_recruteur_id), PRIMARY KEY(entr_entreprise_id, entr_profil_recruteur_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE entr_lien_entreprise_off_liste_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE entr_lien_entreprise_off_liste_secteur_activite_entr_entreprise (entr_lien_entreprise_off_liste_secteur_activite_id INT NOT NULL, entr_entreprise_id INT NOT NULL, INDEX IDX_33BD929740FECEFE (entr_lien_entreprise_off_liste_secteur_activite_id), INDEX IDX_33BD92972923CBC2 (entr_entreprise_id), PRIMARY KEY(entr_lien_entreprise_off_liste_secteur_activite_id, entr_entreprise_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE entr_lien_entreprise_off_liste_secteur_activite_off_liste_secteur_activite (entr_lien_entreprise_off_liste_secteur_activite_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_56929C9A40FECEFE (entr_lien_entreprise_off_liste_secteur_activite_id), INDEX IDX_56929C9AB78A629C (off_liste_secteur_activite_id), PRIMARY KEY(entr_lien_entreprise_off_liste_secteur_activite_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE entr_lien_entreprise_off_liste_sous_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_entr_entreprise (entr_lien_entreprise_off_liste_sous_secteur_activite_id INT NOT NULL, entr_entreprise_id INT NOT NULL, INDEX IDX_B9878DB8221AA9D1 (entr_lien_entreprise_off_liste_sous_secteur_activite_id), INDEX IDX_B9878DB82923CBC2 (entr_entreprise_id), PRIMARY KEY(entr_lien_entreprise_off_liste_sous_secteur_activite_id, entr_entreprise_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite (entr_lien_entreprise_off_liste_sous_secteur_activite_id INT NOT NULL, off_liste_sous_secteur_activite_id INT NOT NULL, INDEX IDX_C85F25F1221AA9D1 (entr_lien_entreprise_off_liste_sous_secteur_activite_id), INDEX IDX_C85F25F1CE509CB7 (off_liste_sous_secteur_activite_id), PRIMARY KEY(entr_lien_entreprise_off_liste_sous_secteur_activite_id, off_liste_sous_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_cand_diplome (id INT AUTO_INCREMENT NOT NULL, libelle_formation VARCHAR(255) DEFAULT NULL, type_formation VARCHAR(255) DEFAULT NULL, niveau_diplome VARCHAR(255) DEFAULT NULL, age INT DEFAULT NULL, rang VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_correspondance (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_expression (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, tag VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_expression_exp_valeur_offre (exp_expression_id INT NOT NULL, exp_valeur_offre_id INT NOT NULL, INDEX IDX_66F01F9737DDE08D (exp_expression_id), INDEX IDX_66F01F979B3437BC (exp_valeur_offre_id), PRIMARY KEY(exp_expression_id, exp_valeur_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_expression_exp_valeur_fixe (exp_expression_id INT NOT NULL, exp_valeur_fixe_id INT NOT NULL, INDEX IDX_D8889F3637DDE08D (exp_expression_id), INDEX IDX_D8889F36F71E40CF (exp_valeur_fixe_id), PRIMARY KEY(exp_expression_id, exp_valeur_fixe_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_expression_exp_fichier (exp_expression_id INT NOT NULL, exp_fichier_id INT NOT NULL, INDEX IDX_6C9FD83837DDE08D (exp_expression_id), INDEX IDX_6C9FD8381A25ABF4 (exp_fichier_id), PRIMARY KEY(exp_expression_id, exp_fichier_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_expression_exp_correspondance (exp_expression_id INT NOT NULL, exp_correspondance_id INT NOT NULL, INDEX IDX_33235A4337DDE08D (exp_expression_id), INDEX IDX_33235A43DF14880C (exp_correspondance_id), PRIMARY KEY(exp_expression_id, exp_correspondance_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_fichier (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, entete LONGTEXT DEFAULT NULL, modele_offre LONGTEXT DEFAULT NULL, pied LONGTEXT DEFAULT NULL, requete_serialize LONGTEXT DEFAULT NULL, date_creation DATETIME DEFAULT NULL, ftp_host VARCHAR(255) DEFAULT NULL, ftp_port INT DEFAULT NULL, ftp_user VARCHAR(255) DEFAULT NULL, ftp_pass VARCHAR(255) DEFAULT NULL, encodage SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_fichier_exp_type_fichier (exp_fichier_id INT NOT NULL, exp_type_fichier_id INT NOT NULL, INDEX IDX_33BF037A1A25ABF4 (exp_fichier_id), INDEX IDX_33BF037AB6710CBC (exp_type_fichier_id), PRIMARY KEY(exp_fichier_id, exp_type_fichier_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_expression_mise_en_forme (id INT AUTO_INCREMENT NOT NULL, ordre_execution INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_expression_mise_en_forme_exp_mise_en_forme (exp_lien_expression_mise_en_forme_id INT NOT NULL, exp_mise_en_forme_id INT NOT NULL, INDEX IDX_247CE4693AE58F1D (exp_lien_expression_mise_en_forme_id), INDEX IDX_247CE4696A3FEADF (exp_mise_en_forme_id), PRIMARY KEY(exp_lien_expression_mise_en_forme_id, exp_mise_en_forme_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_expression_mise_en_forme_exp_expression (exp_lien_expression_mise_en_forme_id INT NOT NULL, exp_expression_id INT NOT NULL, INDEX IDX_97517F303AE58F1D (exp_lien_expression_mise_en_forme_id), INDEX IDX_97517F3037DDE08D (exp_expression_id), PRIMARY KEY(exp_lien_expression_mise_en_forme_id, exp_expression_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_mise_en_forme_parametre (id INT AUTO_INCREMENT NOT NULL, ordre_parametre INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_mise_en_forme_parametre_exp_mise_en_forme (exp_lien_mise_en_forme_parametre_id INT NOT NULL, exp_mise_en_forme_id INT NOT NULL, INDEX IDX_334C4113F827A23 (exp_lien_mise_en_forme_parametre_id), INDEX IDX_334C4116A3FEADF (exp_mise_en_forme_id), PRIMARY KEY(exp_lien_mise_en_forme_parametre_id, exp_mise_en_forme_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_mise_en_forme_parametre_exp_parametre (exp_lien_mise_en_forme_parametre_id INT NOT NULL, exp_parametre_id INT NOT NULL, INDEX IDX_EE87362C3F827A23 (exp_lien_mise_en_forme_parametre_id), INDEX IDX_EE87362CBA6E65E3 (exp_parametre_id), PRIMARY KEY(exp_lien_mise_en_forme_parametre_id, exp_parametre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_valeur_fixe_parametre (id INT AUTO_INCREMENT NOT NULL, ordre_parametre INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_valeur_fixe_parametre_exp_valeur_fixe (exp_lien_valeur_fixe_parametre_id INT NOT NULL, exp_valeur_fixe_id INT NOT NULL, INDEX IDX_2A27B36E2C2ED9DE (exp_lien_valeur_fixe_parametre_id), INDEX IDX_2A27B36EF71E40CF (exp_valeur_fixe_id), PRIMARY KEY(exp_lien_valeur_fixe_parametre_id, exp_valeur_fixe_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_valeur_fixe_parametre_exp_parametre (exp_lien_valeur_fixe_parametre_id INT NOT NULL, exp_parametre_id INT NOT NULL, INDEX IDX_1E1BEAA52C2ED9DE (exp_lien_valeur_fixe_parametre_id), INDEX IDX_1E1BEAA5BA6E65E3 (exp_parametre_id), PRIMARY KEY(exp_lien_valeur_fixe_parametre_id, exp_parametre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_valeur_offre_parametre (id INT AUTO_INCREMENT NOT NULL, ordre_parametre INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_valeur_offre_parametre_exp_valeur_offre (exp_lien_valeur_offre_parametre_id INT NOT NULL, exp_valeur_offre_id INT NOT NULL, INDEX IDX_B429538EA53DF93D (exp_lien_valeur_offre_parametre_id), INDEX IDX_B429538E9B3437BC (exp_valeur_offre_id), PRIMARY KEY(exp_lien_valeur_offre_parametre_id, exp_valeur_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_lien_valeur_offre_parametre_exp_parametre (exp_lien_valeur_offre_parametre_id INT NOT NULL, exp_parametre_id INT NOT NULL, INDEX IDX_36A8CC96A53DF93D (exp_lien_valeur_offre_parametre_id), INDEX IDX_36A8CC96BA6E65E3 (exp_parametre_id), PRIMARY KEY(exp_lien_valeur_offre_parametre_id, exp_parametre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_mise_en_forme (id INT AUTO_INCREMENT NOT NULL, methode LONGTEXT DEFAULT NULL, libelle VARCHAR(255) DEFAULT NULL, position_chaine INT DEFAULT NULL, to_array SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_parametre (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, parametre VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_table_correspondance (id INT AUTO_INCREMENT NOT NULL, valeur_a_remplacer VARCHAR(255) DEFAULT NULL, valeur_de_remplacement VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_table_correspondance_exp_correspondance (exp_table_correspondance_id INT NOT NULL, exp_correspondance_id INT NOT NULL, INDEX IDX_E4E3912471D3D8F4 (exp_table_correspondance_id), INDEX IDX_E4E39124DF14880C (exp_correspondance_id), PRIMARY KEY(exp_table_correspondance_id, exp_correspondance_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_type_fichier (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, mime_type VARCHAR(255) DEFAULT NULL, extention VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_valeur_fixe (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, methode LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE exp_valeur_offre (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, methode LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE fiche_carrousel_carriere (id INT AUTO_INCREMENT NOT NULL, user_user_id_user INT DEFAULT NULL, id_organisme_carrousel INT DEFAULT NULL, nom_fiche_carrousel VARCHAR(255) DEFAULT NULL, logo_fiche_carrousel VARCHAR(255) DEFAULT NULL, online_fiche_carrousel INT DEFAULT NULL, site_carriere INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE fiche_entreprise (id INT AUTO_INCREMENT NOT NULL, logo_fiche_entreprise VARCHAR(255) DEFAULT NULL, user_user_id_user VARCHAR(255) NOT NULL, intro_fiche_entreprise LONGTEXT DEFAULT NULL, online_fiche_entreprise INT DEFAULT NULL, nom_organisme_entreprise VARCHAR(255) DEFAULT NULL, position INT DEFAULT NULL, id_organisme_entreprise INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE fiche_metier (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, titre VARCHAR(255) DEFAULT NULL, chapo VARCHAR(255) DEFAULT NULL, texte LONGTEXT DEFAULT NULL, mot_cle VARCHAR(255) DEFAULT NULL, visuel VARCHAR(255) DEFAULT NULL, focus SMALLINT DEFAULT NULL, is_actif SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE fiche_metier_off_liste_secteur_activite (fiche_metier_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_9CAEE2836A7847C8 (fiche_metier_id), INDEX IDX_9CAEE283B78A629C (off_liste_secteur_activite_id), PRIMARY KEY(fiche_metier_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE fiche_metier_off_liste_sous_secteur_activite (fiche_metier_id INT NOT NULL, off_liste_sous_secteur_activite_id INT NOT NULL, INDEX IDX_58C0B9166A7847C8 (fiche_metier_id), INDEX IDX_58C0B916CE509CB7 (off_liste_sous_secteur_activite_id), PRIMARY KEY(fiche_metier_id, off_liste_sous_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE fiche_metier_edit_app (fiche_metier_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_9C26C45D6A7847C8 (fiche_metier_id), INDEX IDX_9C26C45D570A9ED9 (edit_app_id), PRIMARY KEY(fiche_metier_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE fiche_metier_activation (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, is_actif SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE form_fiche (id INT AUTO_INCREMENT NOT NULL, logo_fiche_form VARCHAR(255) DEFAULT NULL, user_user_id_user VARCHAR(255) DEFAULT NULL, intro_fiche_form LONGTEXT DEFAULT NULL, online_fiche_form INT DEFAULT NULL, id_organisme_formation INT DEFAULT NULL, nom_organisme_formation VARCHAR(255) DEFAULT NULL, url_organisme VARCHAR(255) DEFAULT NULL, position INT DEFAULT NULL, edit_app_id_app INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE form_fiche_hebdo (id INT AUTO_INCREMENT NOT NULL, form_fiche_id_organisme_formation INT DEFAULT NULL, date_creation_stats DATETIME DEFAULT NULL, form_fiche_nom_organisme_formation VARCHAR(255) DEFAULT NULL, clic INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE form_formation (id INT AUTO_INCREMENT NOT NULL, user_user_id_user INT DEFAULT NULL, nom_formation VARCHAR(255) DEFAULT NULL, reference VARCHAR(255) DEFAULT NULL, telephone VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, created_at DATETIME DEFAULT NULL, site VARCHAR(255) DEFAULT NULL, prix VARCHAR(255) DEFAULT NULL, pr�esentation LONGTEXT DEFAULT NULL, date_debut DATETIME DEFAULT NULL, date_fin DATETIME DEFAULT NULL, nom_organisme VARCHAR(255) DEFAULT NULL, ec_organisme_formation_id_organisme_formation INT DEFAULT NULL, geo_ville LONGTEXT DEFAULT NULL, statut SMALLINT DEFAULT NULL, ec_liste_type_session_id_type_session INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE form_formation_form_formation (form_formation_source INT NOT NULL, form_formation_target INT NOT NULL, INDEX IDX_623F95E1802C7D73 (form_formation_source), INDEX IDX_623F95E199C92DFC (form_formation_target), PRIMARY KEY(form_formation_source, form_formation_target)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE form_lien_formation_off_geo_departement (id INT AUTO_INCREMENT NOT NULL, geo_departement_id_departement INT DEFAULT NULL, form_formation_id_formation INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE form_lien_formation_off_geo_liste_region (id INT AUTO_INCREMENT NOT NULL, geo_liste_region_id_region INT DEFAULT NULL, form_formation_id_formation INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE form_lien_formation_off_liste_secteur_activite (id INT AUTO_INCREMENT NOT NULL, form_formation_id_formation INT DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE form_lien_formation_off_liste_sous_secteur_activite (id INT AUTO_INCREMENT NOT NULL, form_formation_id_formation INT DEFAULT NULL, off_liste_sous_secteur_activite_id_sous_secteur_activite INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE form_lien_formation_off_liste_type_formation (id INT AUTO_INCREMENT NOT NULL, form_formation_id_formation INT DEFAULT NULL, off_liste�_type_formation_id_type_formation INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE formation_footer (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_adresse (id INT AUTO_INCREMENT NOT NULL, adresse1 VARCHAR(255) DEFAULT NULL, adresse2 VARCHAR(255) DEFAULT NULL, code_postal VARCHAR(255) DEFAULT NULL, nom_ville VARCHAR(255) DEFAULT NULL, date_maj DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_adresse_geo_liste_region (geo_adresse_id INT NOT NULL, geo_liste_region_id INT NOT NULL, INDEX IDX_4CB9F5DE349A5BD2 (geo_adresse_id), INDEX IDX_4CB9F5DE19DE7EC1 (geo_liste_region_id), PRIMARY KEY(geo_adresse_id, geo_liste_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_adresse_geo_liste_pays (geo_adresse_id INT NOT NULL, geo_liste_pays_id INT NOT NULL, INDEX IDX_64567C17349A5BD2 (geo_adresse_id), INDEX IDX_64567C17A603EFEB (geo_liste_pays_id), PRIMARY KEY(geo_adresse_id, geo_liste_pays_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_departement (id INT AUTO_INCREMENT NOT NULL, code_postal INT DEFAULT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_departement_geo_liste_region (geo_departement_id INT NOT NULL, geo_liste_region_id INT NOT NULL, INDEX IDX_3CA4C82964DE230 (geo_departement_id), INDEX IDX_3CA4C8219DE7EC1 (geo_liste_region_id), PRIMARY KEY(geo_departement_id, geo_liste_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_liste_pays (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) NOT NULL, code_iso VARCHAR(3) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_liste_region (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) NOT NULL, old_region INT DEFAULT NULL, new_region INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_liste_region_geo_liste_pays (geo_liste_region_id INT NOT NULL, geo_liste_pays_id INT NOT NULL, INDEX IDX_CF4A2A019DE7EC1 (geo_liste_region_id), INDEX IDX_CF4A2A0A603EFEB (geo_liste_pays_id), PRIMARY KEY(geo_liste_region_id, geo_liste_pays_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_ville (id INT AUTO_INCREMENT NOT NULL, code_postal VARCHAR(255) DEFAULT NULL, libelle VARCHAR(255) DEFAULT NULL, lattitude DOUBLE PRECISION DEFAULT NULL, longitude DOUBLE PRECISION DEFAULT NULL, big_population INT DEFAULT NULL, code_insee INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_ville_geo_departement (geo_ville_id INT NOT NULL, geo_departement_id INT NOT NULL, INDEX IDX_76A2A14049867797 (geo_ville_id), INDEX IDX_76A2A140964DE230 (geo_departement_id), PRIMARY KEY(geo_ville_id, geo_departement_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_ville_geo_liste_region (geo_ville_id INT NOT NULL, geo_liste_region_id INT NOT NULL, INDEX IDX_9CC321C549867797 (geo_ville_id), INDEX IDX_9CC321C519DE7EC1 (geo_liste_region_id), PRIMARY KEY(geo_ville_id, geo_liste_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_ville_bck (id INT AUTO_INCREMENT NOT NULL, code_postal VARCHAR(255) DEFAULT NULL, libelle VARCHAR(255) DEFAULT NULL, lattitude DOUBLE PRECISION DEFAULT NULL, longitude DOUBLE PRECISION DEFAULT NULL, big_population INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_ville_bck_geo_departement (geo_ville_bck_id INT NOT NULL, geo_departement_id INT NOT NULL, INDEX IDX_32744ED19F74FEDE (geo_ville_bck_id), INDEX IDX_32744ED1964DE230 (geo_departement_id), PRIMARY KEY(geo_ville_bck_id, geo_departement_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_ville_bck_geo_liste_region (geo_ville_bck_id INT NOT NULL, geo_liste_region_id INT NOT NULL, INDEX IDX_1B8F54F89F74FEDE (geo_ville_bck_id), INDEX IDX_1B8F54F819DE7EC1 (geo_liste_region_id), PRIMARY KEY(geo_ville_bck_id, geo_liste_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_ville_principale (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, homepage INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_ville_svg (id INT AUTO_INCREMENT NOT NULL, code_postal VARCHAR(255) DEFAULT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_ville_svg_geo_departement (geo_ville_svg_id INT NOT NULL, geo_departement_id INT NOT NULL, INDEX IDX_A995A02DB52888AA (geo_ville_svg_id), INDEX IDX_A995A02D964DE230 (geo_departement_id), PRIMARY KEY(geo_ville_svg_id, geo_departement_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE geo_ville_svg_geo_liste_region (geo_ville_svg_id INT NOT NULL, geo_liste_region_id INT NOT NULL, INDEX IDX_AF1F0B21B52888AA (geo_ville_svg_id), INDEX IDX_AF1F0B2119DE7EC1 (geo_liste_region_id), PRIMARY KEY(geo_ville_svg_id, geo_liste_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE log_php_erreur (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', timestamp_erreur DATETIME DEFAULT NULL, ip VARCHAR(255) DEFAULT NULL, erreur VARCHAR(255) DEFAULT NULL, script VARCHAR(255) DEFAULT NULL, ligne INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE logo_maillage (id INT AUTO_INCREMENT NOT NULL, url_page VARCHAR(255) DEFAULT NULL, path_img VARCHAR(255) DEFAULT NULL, id_entreprise INT DEFAULT NULL, metier VARCHAR(255) DEFAULT NULL, secteur VARCHAR(255) DEFAULT NULL, localisation VARCHAR(255) DEFAULT NULL, site_web VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mb_css (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, fichier VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mb_moteur_recherche (id INT AUTO_INCREMENT NOT NULL, titre VARCHAR(255) DEFAULT NULL, criteria_serialize LONGTEXT DEFAULT NULL, champ_serialize LONGTEXT DEFAULT NULL, libelle VARCHAR(255) DEFAULT NULL, nb_offres_page INT DEFAULT NULL, type_lien SMALLINT DEFAULT NULL, site_redirection VARCHAR(255) DEFAULT NULL, option_serialize LONGTEXT DEFAULT NULL, champ_html LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mb_moteur_recherche_mb_css (mb_moteur_recherche_id INT NOT NULL, mb_css_id INT NOT NULL, INDEX IDX_2FA932A65A566197 (mb_moteur_recherche_id), INDEX IDX_2FA932A6DCEEB87E (mb_css_id), PRIMARY KEY(mb_moteur_recherche_id, mb_css_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_contrat (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_contrat_mc_zone_mc (mc_contrat_id INT NOT NULL, mc_zone_mc_id INT NOT NULL, INDEX IDX_1B9AEE2398805D1 (mc_contrat_id), INDEX IDX_1B9AEE23A096AC46 (mc_zone_mc_id), PRIMARY KEY(mc_contrat_id, mc_zone_mc_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_localisation (id INT AUTO_INCREMENT NOT NULL, code_postal VARCHAR(255) DEFAULT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_localisation_mc_zone_mc (mc_localisation_id INT NOT NULL, mc_zone_mc_id INT NOT NULL, INDEX IDX_9FE4002D4EF8859 (mc_localisation_id), INDEX IDX_9FE4002DA096AC46 (mc_zone_mc_id), PRIMARY KEY(mc_localisation_id, mc_zone_mc_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_localisation_mc_type_localisation (mc_localisation_id INT NOT NULL, mc_type_localisation_id INT NOT NULL, INDEX IDX_A17D70B14EF8859 (mc_localisation_id), INDEX IDX_A17D70B158CA1A9F (mc_type_localisation_id), PRIMARY KEY(mc_localisation_id, mc_type_localisation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_localisation_svg (id INT AUTO_INCREMENT NOT NULL, code_postal VARCHAR(255) DEFAULT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_localisation_svg_mc_zone_mc (mc_localisation_svg_id INT NOT NULL, mc_zone_mc_id INT NOT NULL, INDEX IDX_6ECB4F622170C8B1 (mc_localisation_svg_id), INDEX IDX_6ECB4F62A096AC46 (mc_zone_mc_id), PRIMARY KEY(mc_localisation_svg_id, mc_zone_mc_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_localisation_svg_mc_type_localisation (mc_localisation_svg_id INT NOT NULL, mc_type_localisation_id INT NOT NULL, INDEX IDX_2DB8A1DD2170C8B1 (mc_localisation_svg_id), INDEX IDX_2DB8A1DD58CA1A9F (mc_type_localisation_id), PRIMARY KEY(mc_localisation_svg_id, mc_type_localisation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_metier (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_metier_mc_zone_mc (mc_metier_id INT NOT NULL, mc_zone_mc_id INT NOT NULL, INDEX IDX_B5B1AEEE471E68FB (mc_metier_id), INDEX IDX_B5B1AEEEA096AC46 (mc_zone_mc_id), PRIMARY KEY(mc_metier_id, mc_zone_mc_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_secteur (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_secteur_mc_zone_mc (mc_secteur_id INT NOT NULL, mc_zone_mc_id INT NOT NULL, INDEX IDX_96A3D03B8ED547CB (mc_secteur_id), INDEX IDX_96A3D03BA096AC46 (mc_zone_mc_id), PRIMARY KEY(mc_secteur_id, mc_zone_mc_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_type_localisation (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_zone_mc (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE mc_zone_mc_edit_app (mc_zone_mc_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_4D0062A6A096AC46 (mc_zone_mc_id), INDEX IDX_4D0062A6570A9ED9 (edit_app_id), PRIMARY KEY(mc_zone_mc_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE metier_domaine_group (id INT AUTO_INCREMENT NOT NULL, libelle_metier_domaine_group VARCHAR(255) DEFAULT NULL, maillage INT DEFAULT NULL, id_recherche_avancee INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE metier_metier (id INT AUTO_INCREMENT NOT NULL, libelle_metier VARCHAR(255) DEFAULT NULL, domaine_id_domaine INT DEFAULT NULL, form_cand INT DEFAULT NULL, form_cvtheque INT DEFAULT NULL, page_acceuil INT DEFAULT NULL, zone_mc INT DEFAULT NULL, zone_mc_domaine INT DEFAULT NULL, zone_cadre INT DEFAULT NULL, mc_recherche_avancee INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_candidature (id INT AUTO_INCREMENT NOT NULL, date_candidature DATE DEFAULT NULL, saisie LONGTEXT DEFAULT NULL, cv VARCHAR(255) DEFAULT NULL, lm VARCHAR(255) DEFAULT NULL, nb_candidature INT DEFAULT NULL, tri_recruteur INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_candidature_user_user (off_candidature_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_9CA84FB87516ECFB (off_candidature_id), INDEX IDX_9CA84FB8FF63CD9F (user_user_id), PRIMARY KEY(off_candidature_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_candidature_off_offre (off_candidature_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_64F794507516ECFB (off_candidature_id), INDEX IDX_64F79450C18A1CCE (off_offre_id), PRIMARY KEY(off_candidature_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_candidature_off_liste_type_suivi (off_candidature_id INT NOT NULL, off_liste_type_suivi_id INT NOT NULL, INDEX IDX_6A87B2167516ECFB (off_candidature_id), INDEX IDX_6A87B2161AA99B2F (off_liste_type_suivi_id), PRIMARY KEY(off_candidature_id, off_liste_type_suivi_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_fourchette_remuneration (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_geo_departement (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_geo_departement_geo_departement (off_lien_offre_geo_departement_id INT NOT NULL, geo_departement_id INT NOT NULL, INDEX IDX_8733607318B1A9B1 (off_lien_offre_geo_departement_id), INDEX IDX_87336073964DE230 (geo_departement_id), PRIMARY KEY(off_lien_offre_geo_departement_id, geo_departement_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_geo_departement_off_offre (off_lien_offre_geo_departement_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_D05249E018B1A9B1 (off_lien_offre_geo_departement_id), INDEX IDX_D05249E0C18A1CCE (off_offre_id), PRIMARY KEY(off_lien_offre_geo_departement_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_geo_liste_pays (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_geo_liste_pays_geo_liste_pays (off_lien_offre_geo_liste_pays_id INT NOT NULL, geo_liste_pays_id INT NOT NULL, INDEX IDX_3D1F0576E4A79776 (off_lien_offre_geo_liste_pays_id), INDEX IDX_3D1F0576A603EFEB (geo_liste_pays_id), PRIMARY KEY(off_lien_offre_geo_liste_pays_id, geo_liste_pays_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_geo_liste_pays_off_offre (off_lien_offre_geo_liste_pays_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_C4884505E4A79776 (off_lien_offre_geo_liste_pays_id), INDEX IDX_C4884505C18A1CCE (off_offre_id), PRIMARY KEY(off_lien_offre_geo_liste_pays_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_geo_liste_region (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_geo_liste_region_geo_liste_region (off_lien_offre_geo_liste_region_id INT NOT NULL, geo_liste_region_id INT NOT NULL, INDEX IDX_7679E10983EF313C (off_lien_offre_geo_liste_region_id), INDEX IDX_7679E10919DE7EC1 (geo_liste_region_id), PRIMARY KEY(off_lien_offre_geo_liste_region_id, geo_liste_region_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_geo_liste_region_off_offre (off_lien_offre_geo_liste_region_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_25C441F383EF313C (off_lien_offre_geo_liste_region_id), INDEX IDX_25C441F3C18A1CCE (off_offre_id), PRIMARY KEY(off_lien_offre_geo_liste_region_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_experience_requise (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_experience_requise_off_liste_experience_requise (off_lien_offre_liste_experience_requise_id INT NOT NULL, off_liste_experience_requise_id INT NOT NULL, INDEX IDX_8CCD44ED24741D47 (off_lien_offre_liste_experience_requise_id), INDEX IDX_8CCD44EDBC4B4FB9 (off_liste_experience_requise_id), PRIMARY KEY(off_lien_offre_liste_experience_requise_id, off_liste_experience_requise_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_experience_requise_off_offre (off_lien_offre_liste_experience_requise_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_CDAF37D224741D47 (off_lien_offre_liste_experience_requise_id), INDEX IDX_CDAF37D2C18A1CCE (off_offre_id), PRIMARY KEY(off_lien_offre_liste_experience_requise_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_secteur_activite_off_offre (off_lien_offre_liste_secteur_activite_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_C641F4A57D4B001C (off_lien_offre_liste_secteur_activite_id), INDEX IDX_C641F4A5C18A1CCE (off_offre_id), PRIMARY KEY(off_lien_offre_liste_secteur_activite_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_secteur_activite_off_liste_secteur_activite (off_lien_offre_liste_secteur_activite_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_A8C66CD67D4B001C (off_lien_offre_liste_secteur_activite_id), INDEX IDX_A8C66CD6B78A629C (off_liste_secteur_activite_id), PRIMARY KEY(off_lien_offre_liste_secteur_activite_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_sous_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_sous_secteur_activite_off_offre (off_lien_offre_liste_sous_secteur_activite_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_23554EBE4FE1D01E (off_lien_offre_liste_sous_secteur_activite_id), INDEX IDX_23554EBEC18A1CCE (off_offre_id), PRIMARY KEY(off_lien_offre_liste_sous_secteur_activite_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_sous_secteur_activite_off_liste_sous_secteur_activite (off_lien_offre_liste_sous_secteur_activite_id INT NOT NULL, off_liste_sous_secteur_activite_id INT NOT NULL, INDEX IDX_3F02BDFD4FE1D01E (off_lien_offre_liste_sous_secteur_activite_id), INDEX IDX_3F02BDFDCE509CB7 (off_liste_sous_secteur_activite_id), PRIMARY KEY(off_lien_offre_liste_sous_secteur_activite_id, off_liste_sous_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_type_formation (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_type_formation_off_offre (off_lien_offre_liste_type_formation_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_F31245FCB99AEDB5 (off_lien_offre_liste_type_formation_id), INDEX IDX_F31245FCC18A1CCE (off_offre_id), PRIMARY KEY(off_lien_offre_liste_type_formation_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_offre_liste_type_formation_off_liste_type_formation (off_lien_offre_liste_type_formation_id INT NOT NULL, off_liste_type_formation_id INT NOT NULL, INDEX IDX_86D859C9B99AEDB5 (off_lien_offre_liste_type_formation_id), INDEX IDX_86D859C9A8BBB27F (off_liste_type_formation_id), PRIMARY KEY(off_lien_offre_liste_type_formation_id, off_liste_type_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_sous_secteur_activite_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_sous_secteur_activite (off_lien_sous_secteur_activite_secteur_activite_id INT NOT NULL, off_liste_sous_secteur_activite_id INT NOT NULL, INDEX IDX_5B4F9F2D16B9E322 (off_lien_sous_secteur_activite_secteur_activite_id), INDEX IDX_5B4F9F2DCE509CB7 (off_liste_sous_secteur_activite_id), PRIMARY KEY(off_lien_sous_secteur_activite_secteur_activite_id, off_liste_sous_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_secteur_activite (off_lien_sous_secteur_activite_secteur_activite_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_864B0D3C16B9E322 (off_lien_sous_secteur_activite_secteur_activite_id), INDEX IDX_864B0D3CB78A629C (off_liste_secteur_activite_id), PRIMARY KEY(off_lien_sous_secteur_activite_secteur_activite_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_experience_requise (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_metier (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_secteur_activite (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, secteur_group DOUBLE PRECISION DEFAULT NULL, mots_cles_associes LONGTEXT DEFAULT NULL, libelle_alternatif1 VARCHAR(255) DEFAULT NULL, libelle_alternatif2 VARCHAR(255) DEFAULT NULL, libelle_alternatif3 VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_secteur_activite_off_liste_type_secteur_activite (off_liste_secteur_activite_id INT NOT NULL, off_liste_type_secteur_activite_id INT NOT NULL, INDEX IDX_7AA0C6F9B78A629C (off_liste_secteur_activite_id), INDEX IDX_7AA0C6F9E5A0BF1C (off_liste_type_secteur_activite_id), PRIMARY KEY(off_liste_secteur_activite_id, off_liste_type_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_sous_secteur_activite (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, libelle_alternatif1 VARCHAR(255) DEFAULT NULL, libelle_alternatif2 VARCHAR(255) DEFAULT NULL, libelle_alternatif3 VARCHAR(255) DEFAULT NULL, fct_support SMALLINT DEFAULT NULL, mots_cles_associes LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_sous_secteur_activite_off_liste_secteur_activite (off_liste_sous_secteur_activite_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_27F44DBCCE509CB7 (off_liste_sous_secteur_activite_id), INDEX IDX_27F44DBCB78A629C (off_liste_secteur_activite_id), PRIMARY KEY(off_liste_sous_secteur_activite_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_sous_secteur_activite_off_liste_type_secteur_activite (off_liste_sous_secteur_activite_id INT NOT NULL, off_liste_type_secteur_activite_id INT NOT NULL, INDEX IDX_1BF49E88CE509CB7 (off_liste_sous_secteur_activite_id), INDEX IDX_1BF49E88E5A0BF1C (off_liste_type_secteur_activite_id), PRIMARY KEY(off_liste_sous_secteur_activite_id, off_liste_type_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_sous_secteur_activite_edit_app (off_liste_sous_secteur_activite_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_2380476CCE509CB7 (off_liste_sous_secteur_activite_id), INDEX IDX_2380476C570A9ED9 (edit_app_id), PRIMARY KEY(off_liste_sous_secteur_activite_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_type_contrat (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, type_general SMALLINT DEFAULT NULL, libelle_general VARCHAR(255) DEFAULT NULL, ordre INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_type_formation (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, is_ingenieur_generaliste SMALLINT DEFAULT NULL, use_secteur SMALLINT DEFAULT NULL, show_formation SMALLINT DEFAULT NULL, ordre INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_type_formation_com_liste_type_diffusion (off_liste_type_formation_id INT NOT NULL, com_liste_type_diffusion_id INT NOT NULL, INDEX IDX_65C134DAA8BBB27F (off_liste_type_formation_id), INDEX IDX_65C134DA35624BB3 (com_liste_type_diffusion_id), PRIMARY KEY(off_liste_type_formation_id, com_liste_type_diffusion_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_type_secteur_activite (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_liste_type_suivi (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_meta_moteur (id INT AUTO_INCREMENT NOT NULL, nom_meta_moteur VARCHAR(255) DEFAULT NULL, entete_flux LONGTEXT DEFAULT NULL, balise_ensemble_offres VARCHAR(255) DEFAULT NULL, balise_offre VARCHAR(255) DEFAULT NULL, modele_offre LONGTEXT DEFAULT NULL, adresse_envoi_post VARCHAR(255) DEFAULT NULL, date_dernier_envoi DATETIME DEFAULT NULL, pied_flux LONGTEXT DEFAULT NULL, encodage VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_meta_moteur_entr_profil_recruteur (off_meta_moteur_id INT NOT NULL, entr_profil_recruteur_id INT NOT NULL, INDEX IDX_43C04A9249B09D89 (off_meta_moteur_id), INDEX IDX_43C04A92BC1D969B (entr_profil_recruteur_id), PRIMARY KEY(off_meta_moteur_id, entr_profil_recruteur_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_off_non_exporte (id INT AUTO_INCREMENT NOT NULL, motif_rejet VARCHAR(255) DEFAULT NULL, date_rapport DATE DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_offre (id INT AUTO_INCREMENT NOT NULL, off_liste_type_contrat_id_type_contrat INT DEFAULT NULL, statut SMALLINT NOT NULL, keep SMALLINT DEFAULT NULL, user_user_id_user INT DEFAULT NULL, reference VARCHAR(255) DEFAULT NULL, intitule VARCHAR(255) DEFAULT NULL, mission LONGTEXT DEFAULT NULL, profil LONGTEXT DEFAULT NULL, date_debut VARCHAR(255) DEFAULT NULL, duree_contrat VARCHAR(255) DEFAULT NULL, date_soumission DATETIME DEFAULT NULL, date_pourvue DATE DEFAULT NULL, is_archived SMALLINT DEFAULT NULL, is_handicap SMALLINT DEFAULT NULL, geo_ville LONGTEXT DEFAULT NULL, is_anonym SMALLINT DEFAULT NULL, rep_mail VARCHAR(255) DEFAULT NULL, rep_url VARCHAR(255) DEFAULT NULL, date_creation DATETIME DEFAULT NULL, sitemap_date_fin_validite DATETIME DEFAULT NULL, sitemap_carriere_btp SMALLINT DEFAULT NULL, sitemap_carriere_industrie SMALLINT DEFAULT NULL, sitemap_carriere_informatique SMALLINT DEFAULT NULL, id_combinaison_seo INT DEFAULT NULL, created_at DATETIME DEFAULT NULL, pass_form SMALLINT DEFAULT NULL, sitemap_carriere_distribution SMALLINT DEFAULT NULL, nom_societe VARCHAR(255) DEFAULT NULL, redirection_directe SMALLINT DEFAULT NULL, date_export_pe DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_offre_com_offre_commerciale (off_offre_id INT NOT NULL, com_offre_commerciale_id INT NOT NULL, INDEX IDX_6ECB9D06C18A1CCE (off_offre_id), INDEX IDX_6ECB9D0623CD91B5 (com_offre_commerciale_id), PRIMARY KEY(off_offre_id, com_offre_commerciale_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_offre_off_fourchette_remuneration (off_offre_id INT NOT NULL, off_fourchette_remuneration_id INT NOT NULL, INDEX IDX_D3296C39C18A1CCE (off_offre_id), INDEX IDX_D3296C39AF4A7002 (off_fourchette_remuneration_id), PRIMARY KEY(off_offre_id, off_fourchette_remuneration_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_offre_com_liste_type_offre_commerciale (off_offre_id INT NOT NULL, com_liste_type_offre_commerciale_id INT NOT NULL, INDEX IDX_E49FCAA3C18A1CCE (off_offre_id), INDEX IDX_E49FCAA333987EE4 (com_liste_type_offre_commerciale_id), PRIMARY KEY(off_offre_id, com_liste_type_offre_commerciale_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_offre_non_exporte (id INT AUTO_INCREMENT NOT NULL, motif_rejet VARCHAR(255) DEFAULT NULL, date_rapport DATE DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_reponse_offre_non_identifie (id INT AUTO_INCREMENT NOT NULL, nom VARCHAR(255) DEFAULT NULL, prenom VARCHAR(255) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, cp VARCHAR(255) DEFAULT NULL, annee_sortie INT NOT NULL, cv VARCHAR(255) DEFAULT NULL, lm VARCHAR(255) DEFAULT NULL, date_candidature DATETIME DEFAULT NULL, etablissement_autre VARCHAR(255) DEFAULT NULL, date_naissance DATE DEFAULT NULL, civilite VARCHAR(255) DEFAULT NULL, tel VARCHAR(255) DEFAULT NULL, nom_diplome VARCHAR(255) DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite INT DEFAULT NULL, id_metier INT DEFAULT NULL, metier_autre VARCHAR(255) DEFAULT NULL, id_off_fourchette_remuneration INT DEFAULT NULL, tri_recruteur INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_reponse_offre_non_identifie_off_offre (off_reponse_offre_non_identifie_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_2E6A4EF45FF2EA66 (off_reponse_offre_non_identifie_id), INDEX IDX_2E6A4EF4C18A1CCE (off_offre_id), PRIMARY KEY(off_reponse_offre_non_identifie_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_reponse_offre_non_identifie_ec_ecole (off_reponse_offre_non_identifie_id INT NOT NULL, ec_ecole_id INT NOT NULL, INDEX IDX_E60E29695FF2EA66 (off_reponse_offre_non_identifie_id), INDEX IDX_E60E2969CFF27E35 (ec_ecole_id), PRIMARY KEY(off_reponse_offre_non_identifie_id, ec_ecole_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_reponse_offre_non_identifie_ec_session_formation (off_reponse_offre_non_identifie_id INT NOT NULL, ec_session_formation_id INT NOT NULL, INDEX IDX_9AD70FBE5FF2EA66 (off_reponse_offre_non_identifie_id), INDEX IDX_9AD70FBEF551B8FE (ec_session_formation_id), PRIMARY KEY(off_reponse_offre_non_identifie_id, ec_session_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_reponse_offre_non_identifie_off_liste_type_formation (off_reponse_offre_non_identifie_id INT NOT NULL, off_liste_type_formation_id INT NOT NULL, INDEX IDX_248D35485FF2EA66 (off_reponse_offre_non_identifie_id), INDEX IDX_248D3548A8BBB27F (off_liste_type_formation_id), PRIMARY KEY(off_reponse_offre_non_identifie_id, off_liste_type_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_sauvegarde_offre (id INT AUTO_INCREMENT NOT NULL, date_sauvegarde DATE DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_sauvegarde_offre_off_offre (off_sauvegarde_offre_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_2D0EFD810895F1A (off_sauvegarde_offre_id), INDEX IDX_2D0EFD8C18A1CCE (off_offre_id), PRIMARY KEY(off_sauvegarde_offre_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_sauvegarde_offre_user_user (off_sauvegarde_offre_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_FA8F343010895F1A (off_sauvegarde_offre_id), INDEX IDX_FA8F3430FF63CD9F (user_user_id), PRIMARY KEY(off_sauvegarde_offre_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_similaires_stats (id INT AUTO_INCREMENT NOT NULL, off_offre_id_offre INT DEFAULT NULL, date_log DATETIME DEFAULT NULL, entr�_entreprise_id_entreprise INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_statistique (id INT AUTO_INCREMENT NOT NULL, type_log INT DEFAULT NULL, date_log DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_statistique_off_offre (off_statistique_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_80311F51B5ACED1B (off_statistique_id), INDEX IDX_80311F51C18A1CCE (off_offre_id), PRIMARY KEY(off_statistique_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_statistique_entr_entreprise (off_statistique_id INT NOT NULL, entr_entreprise_id INT NOT NULL, INDEX IDX_3913F4FDB5ACED1B (off_statistique_id), INDEX IDX_3913F4FD2923CBC2 (entr_entreprise_id), PRIMARY KEY(off_statistique_id, entr_entreprise_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_statistique_old_save (id INT AUTO_INCREMENT NOT NULL, type_log INT DEFAULT NULL, date_log DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_statistique_old_save_entr_entreprise (off_statistique_old_save_id INT NOT NULL, entr_entreprise_id INT NOT NULL, INDEX IDX_4DD37E8A91EC602E (off_statistique_old_save_id), INDEX IDX_4DD37E8A2923CBC2 (entr_entreprise_id), PRIMARY KEY(off_statistique_old_save_id, entr_entreprise_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_stats_hebdo (id INT AUTO_INCREMENT NOT NULL, form_fiche_id_organisme_formation INT DEFAULT NULL, date_creation_stats DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', form_fiche_nom_organisme_formation VARCHAR(255) DEFAULT NULL, clic INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_stats_position (id INT AUTO_INCREMENT NOT NULL, date_creation_stats DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', positionm2 INT DEFAULT NULL, positionm1 INT DEFAULT NULL, position1 INT DEFAULT NULL, position2 INT DEFAULT NULL, position3 INT DEFAULT NULL, position4 INT DEFAULT NULL, position5 INT DEFAULT NULL, position6 INT DEFAULT NULL, total INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE off_type_xml (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, nom_variable VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE page_cadre_carousel (id INT AUTO_INCREMENT NOT NULL, user_user_id_user INT DEFAULT NULL, nom_fiche_page_cadre VARCHAR(255) DEFAULT NULL, logo_carousel VARCHAR(255) DEFAULT NULL, is_actif INT DEFAULT NULL, id_fiche_page_cadre INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE promo_lien_promotion_off_offre (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE promo_lien_promotion_off_offre_promo_promotion (promo_lien_promotion_off_offre_id INT NOT NULL, promo_promotion_id INT NOT NULL, INDEX IDX_DB5FC8CF7E590EEB (promo_lien_promotion_off_offre_id), INDEX IDX_DB5FC8CF8B9B3683 (promo_promotion_id), PRIMARY KEY(promo_lien_promotion_off_offre_id, promo_promotion_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE promo_lien_promotion_off_offre_off_offre (promo_lien_promotion_off_offre_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_5507FA8E7E590EEB (promo_lien_promotion_off_offre_id), INDEX IDX_5507FA8EC18A1CCE (off_offre_id), PRIMARY KEY(promo_lien_promotion_off_offre_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE promo_lien_promotion_offre_commerciale (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE promo_lien_promotion_offre_commerciale_promo_promotion (promo_lien_promotion_offre_commerciale_id INT NOT NULL, promo_promotion_id INT NOT NULL, INDEX IDX_BFDC6EC17A0303B9 (promo_lien_promotion_offre_commerciale_id), INDEX IDX_BFDC6EC18B9B3683 (promo_promotion_id), PRIMARY KEY(promo_lien_promotion_offre_commerciale_id, promo_promotion_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE promo_lien_promotion_offre_commerciale_com_offre_commerciale (promo_lien_promotion_offre_commerciale_id INT NOT NULL, com_offre_commerciale_id INT NOT NULL, INDEX IDX_A8E383947A0303B9 (promo_lien_promotion_offre_commerciale_id), INDEX IDX_A8E3839423CD91B5 (com_offre_commerciale_id), PRIMARY KEY(promo_lien_promotion_offre_commerciale_id, com_offre_commerciale_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE promo_lien_promotion_user_user (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE promo_lien_promotion_user_user_promo_promotion (promo_lien_promotion_user_user_id INT NOT NULL, promo_promotion_id INT NOT NULL, INDEX IDX_E218701140B0DFBA (promo_lien_promotion_user_user_id), INDEX IDX_E21870118B9B3683 (promo_promotion_id), PRIMARY KEY(promo_lien_promotion_user_user_id, promo_promotion_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE promo_lien_promotion_user_user_user_user (promo_lien_promotion_user_user_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_C4A826D340B0DFBA (promo_lien_promotion_user_user_id), INDEX IDX_C4A826D3FF63CD9F (user_user_id), PRIMARY KEY(promo_lien_promotion_user_user_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE promo_promotion (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, libelle VARCHAR(255) DEFAULT NULL, classement_compte INT DEFAULT NULL, pr�esentation LONGTEXT DEFAULT NULL, date_debut DATE DEFAULT NULL, date_fin DATE DEFAULT NULL, reduction DOUBLE PRECISION DEFAULT NULL, redirection SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE recherche_avancee (id INT AUTO_INCREMENT NOT NULL, titre VARCHAR(255) DEFAULT NULL, nuage_mots LONGTEXT DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, lien LONGTEXT DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ref_lien_multi (id INT AUTO_INCREMENT NOT NULL, id_type_lien SMALLINT DEFAULT NULL, titre VARCHAR(255) DEFAULT NULL, poid SMALLINT DEFAULT NULL, type_critere1 VARCHAR(255) DEFAULT NULL, id_critere1 INT DEFAULT NULL, type_critere2 VARCHAR(255) DEFAULT NULL, id_critere2 INT DEFAULT NULL, type_critere3 VARCHAR(255) DEFAULT NULL, id_critere3 INT DEFAULT NULL, url VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ref_lien_multi_edit_app (ref_lien_multi_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_62CE700A1024C3A3 (ref_lien_multi_id), INDEX IDX_62CE700A570A9ED9 (edit_app_id), PRIMARY KEY(ref_lien_multi_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE save_mail_candi_non_log (id INT AUTO_INCREMENT NOT NULL, email VARCHAR(255) DEFAULT NULL, date_derniere_relance DATETIME DEFAULT NULL, prenom VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE save_mail_candi_non_log_edit_app (save_mail_candi_non_log_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_54A4D0FD5EB2D766 (save_mail_candi_non_log_id), INDEX IDX_54A4D0FD570A9ED9 (edit_app_id), PRIMARY KEY(save_mail_candi_non_log_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE save_mail_candi_non_log_off_liste_type_contrat (save_mail_candi_non_log_id INT NOT NULL, off_liste_type_contrat_id INT NOT NULL, INDEX IDX_5F9281345EB2D766 (save_mail_candi_non_log_id), INDEX IDX_5F928134B4AF2FE3 (off_liste_type_contrat_id), PRIMARY KEY(save_mail_candi_non_log_id, off_liste_type_contrat_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE script_formation (id INT AUTO_INCREMENT NOT NULL, created_at DATE DEFAULT NULL, user_user_id_user LONGTEXT DEFAULT NULL, dernier_out LONGTEXT DEFAULT NULL, nom VARCHAR(255) DEFAULT NULL, dernieres_erreurs LONGTEXT DEFAULT NULL, date_debut DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', date_fin DATETIME DEFAULT NULL, actif INT DEFAULT NULL, ordre_execution INT DEFAULT NULL, commentaire VARCHAR(255) DEFAULT NULL, redirection VARCHAR(255) DEFAULT NULL, informations LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE script_principaux (id INT AUTO_INCREMENT NOT NULL, created_at DATE DEFAULT NULL, user_user_id_user LONGTEXT DEFAULT NULL, dernier_out LONGTEXT DEFAULT NULL, nom VARCHAR(255) DEFAULT NULL, dernieres_erreurs LONGTEXT DEFAULT NULL, date_debut DATETIME DEFAULT NULL, date_fin DATETIME DEFAULT NULL, actif INT DEFAULT NULL, ordre_execution INT DEFAULT NULL, commentaire VARCHAR(255) DEFAULT NULL, redirection VARCHAR(255) DEFAULT NULL, informations LONGTEXT DEFAULT NULL, entr_profil_recruteur_id_profil_recruteur INT DEFAULT NULL, day_start INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE script_secteurs_all (id INT AUTO_INCREMENT NOT NULL, intitule VARCHAR(255) DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite_1 INT DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite_2 INT DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite_3 INT DEFAULT NULL, off_liste_sous_secteur_activite_id_sous_secteur_activite INT DEFAULT NULL, user_user_id_user VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE script_secteurs_stats (id INT AUTO_INCREMENT NOT NULL, nom VARCHAR(255) DEFAULT NULL, actif SMALLINT DEFAULT NULL, complet SMALLINT DEFAULT NULL, nb_total INT DEFAULT NULL, nb_sect_manquants INT DEFAULT NULL, nb_ss_sect_manquants INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE script_secteurs_stats_user_user (script_secteurs_stats_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_EFAF919675E66279 (script_secteurs_stats_id), INDEX IDX_EFAF9196FF63CD9F (user_user_id), PRIMARY KEY(script_secteurs_stats_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE script_stats (id INT AUTO_INCREMENT NOT NULL, script_principaux_id_script INT DEFAULT NULL, user_user_id_user LONGTEXT DEFAULT NULL, sujet_mail VARCHAR(255) DEFAULT NULL, nb_offres_maj INT DEFAULT NULL, nb_offres_crees INT DEFAULT NULL, nb_offres_supprimees INT DEFAULT NULL, date_debut DATETIME DEFAULT NULL, date_fin DATETIME DEFAULT NULL, temps_execution TIME DEFAULT NULL, fatal_error_php INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE script_stats_formation (id INT AUTO_INCREMENT NOT NULL, script_formation_id_script INT DEFAULT NULL, user_user_id_user LONGTEXT DEFAULT NULL, sujet_mail VARCHAR(255) DEFAULT NULL, nb_offres_maj INT DEFAULT NULL, nb_offres_crees INT DEFAULT NULL, nb_offres_supprimees INT DEFAULT NULL, date_debut DATETIME DEFAULT NULL, date_fin DATETIME DEFAULT NULL, temps_execution TIME DEFAULT NULL, fatal_error_php INT DEFAULT NULL, commentaire LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_combinaisons (id INT AUTO_INCREMENT NOT NULL, combinaison VARCHAR(255) DEFAULT NULL, niveau_max_utilise INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_edit_referencement (id INT AUTO_INCREMENT NOT NULL, url VARCHAR(255) DEFAULT NULL, nom VARCHAR(255) DEFAULT NULL, preposition VARCHAR(255) DEFAULT NULL, code VARCHAR(255) DEFAULT NULL, photo_path VARCHAR(255) DEFAULT NULL, keywords VARCHAR(255) DEFAULT NULL, paragraphe_titre VARCHAR(255) DEFAULT NULL, paragraphe_contenu LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_edit_referencement_edit_app (seo_edit_referencement_id INT NOT NULL, edit_app_id INT NOT NULL, INDEX IDX_71978C6D79E8E6D8 (seo_edit_referencement_id), INDEX IDX_71978C6D570A9ED9 (edit_app_id), PRIMARY KEY(seo_edit_referencement_id, edit_app_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_lien_mot_cle_attente_mot_cle_racine (id INT AUTO_INCREMENT NOT NULL, ordre INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_attente (seo_lien_mot_cle_attente_mot_cle_racine_id INT NOT NULL, seo_mot_cle_attente_id INT NOT NULL, INDEX IDX_51C48026D8DDFAD1 (seo_lien_mot_cle_attente_mot_cle_racine_id), INDEX IDX_51C480265C9ABB4C (seo_mot_cle_attente_id), PRIMARY KEY(seo_lien_mot_cle_attente_mot_cle_racine_id, seo_mot_cle_attente_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_racine (seo_lien_mot_cle_attente_mot_cle_racine_id INT NOT NULL, seo_mot_cle_racine_id INT NOT NULL, INDEX IDX_96CCB205D8DDFAD1 (seo_lien_mot_cle_attente_mot_cle_racine_id), INDEX IDX_96CCB20564E2A64D (seo_mot_cle_racine_id), PRIMARY KEY(seo_lien_mot_cle_attente_mot_cle_racine_id, seo_mot_cle_racine_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_lien_mot_cle_mot_cle_racine (id INT AUTO_INCREMENT NOT NULL, ordre INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle (seo_lien_mot_cle_mot_cle_racine_id INT NOT NULL, seo_mot_cle_id INT NOT NULL, INDEX IDX_47D67C8B8C3AE25 (seo_lien_mot_cle_mot_cle_racine_id), INDEX IDX_47D67C8F8F767A5 (seo_mot_cle_id), PRIMARY KEY(seo_lien_mot_cle_mot_cle_racine_id, seo_mot_cle_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle_racine (seo_lien_mot_cle_mot_cle_racine_id INT NOT NULL, seo_mot_cle_racine_id INT NOT NULL, INDEX IDX_6659928CB8C3AE25 (seo_lien_mot_cle_mot_cle_racine_id), INDEX IDX_6659928C64E2A64D (seo_mot_cle_racine_id), PRIMARY KEY(seo_lien_mot_cle_mot_cle_racine_id, seo_mot_cle_racine_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_lien_mot_cle_racine_offre (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_lien_mot_cle_racine_offre_seo_mot_cle_racine (seo_lien_mot_cle_racine_offre_id INT NOT NULL, seo_mot_cle_racine_id INT NOT NULL, INDEX IDX_F77287D59E2269A3 (seo_lien_mot_cle_racine_offre_id), INDEX IDX_F77287D564E2A64D (seo_mot_cle_racine_id), PRIMARY KEY(seo_lien_mot_cle_racine_offre_id, seo_mot_cle_racine_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_lien_mot_cle_racine_offre_off_offre (seo_lien_mot_cle_racine_offre_id INT NOT NULL, off_offre_id INT NOT NULL, INDEX IDX_6F6F6E949E2269A3 (seo_lien_mot_cle_racine_offre_id), INDEX IDX_6F6F6E94C18A1CCE (off_offre_id), PRIMARY KEY(seo_lien_mot_cle_racine_offre_id, off_offre_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_liste_secteur (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_liste_theme (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_liste_type (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_liste_url (id INT AUTO_INCREMENT NOT NULL, name_url LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle (id INT AUTO_INCREMENT NOT NULL, mot_cle VARCHAR(255) DEFAULT NULL, is_publie SMALLINT DEFAULT NULL, poids SMALLINT DEFAULT NULL, utilisation_offre INT DEFAULT NULL, liste_id_racine VARCHAR(255) DEFAULT NULL, nb_racine INT DEFAULT NULL, slug_url VARCHAR(255) DEFAULT NULL, mot_cle_similaires VARCHAR(255) DEFAULT NULL, html_combinaisons LONGTEXT DEFAULT NULL, niveau_combinaison INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_seo_liste_type (seo_mot_cle_id INT NOT NULL, seo_liste_type_id INT NOT NULL, INDEX IDX_AB416ADBF8F767A5 (seo_mot_cle_id), INDEX IDX_AB416ADBA1F29F8E (seo_liste_type_id), PRIMARY KEY(seo_mot_cle_id, seo_liste_type_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_seo_liste_theme (seo_mot_cle_id INT NOT NULL, seo_liste_theme_id INT NOT NULL, INDEX IDX_C4E1EB05F8F767A5 (seo_mot_cle_id), INDEX IDX_C4E1EB053A60A64D (seo_liste_theme_id), PRIMARY KEY(seo_mot_cle_id, seo_liste_theme_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_seo_liste_secteur (seo_mot_cle_id INT NOT NULL, seo_liste_secteur_id INT NOT NULL, INDEX IDX_3C5D44EDF8F767A5 (seo_mot_cle_id), INDEX IDX_3C5D44EDC6B6AB5B (seo_liste_secteur_id), PRIMARY KEY(seo_mot_cle_id, seo_liste_secteur_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_seo_combinaisons (seo_mot_cle_id INT NOT NULL, seo_combinaisons_id INT NOT NULL, INDEX IDX_B5FE012AF8F767A5 (seo_mot_cle_id), INDEX IDX_B5FE012A7768CF7C (seo_combinaisons_id), PRIMARY KEY(seo_mot_cle_id, seo_combinaisons_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_attente (id INT AUTO_INCREMENT NOT NULL, mot_cle VARCHAR(255) DEFAULT NULL, is_publie SMALLINT DEFAULT NULL, poids SMALLINT DEFAULT NULL, liste_id_racine VARCHAR(255) DEFAULT NULL, nb_racine INT DEFAULT NULL, chaines_manquantes LONGTEXT DEFAULT NULL, slug_url VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_attente_seo_liste_type (seo_mot_cle_attente_id INT NOT NULL, seo_liste_type_id INT NOT NULL, INDEX IDX_E66975FE5C9ABB4C (seo_mot_cle_attente_id), INDEX IDX_E66975FEA1F29F8E (seo_liste_type_id), PRIMARY KEY(seo_mot_cle_attente_id, seo_liste_type_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_attente_seo_liste_theme (seo_mot_cle_attente_id INT NOT NULL, seo_liste_theme_id INT NOT NULL, INDEX IDX_8FA8175D5C9ABB4C (seo_mot_cle_attente_id), INDEX IDX_8FA8175D3A60A64D (seo_liste_theme_id), PRIMARY KEY(seo_mot_cle_attente_id, seo_liste_theme_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_attente_seo_liste_secteur (seo_mot_cle_attente_id INT NOT NULL, seo_liste_secteur_id INT NOT NULL, INDEX IDX_FA3466CF5C9ABB4C (seo_mot_cle_attente_id), INDEX IDX_FA3466CFC6B6AB5B (seo_liste_secteur_id), PRIMARY KEY(seo_mot_cle_attente_id, seo_liste_secteur_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_racine (id INT AUTO_INCREMENT NOT NULL, keyword VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_racine_seo_liste_type (seo_mot_cle_racine_id INT NOT NULL, seo_liste_type_id INT NOT NULL, INDEX IDX_68D917FB64E2A64D (seo_mot_cle_racine_id), INDEX IDX_68D917FBA1F29F8E (seo_liste_type_id), PRIMARY KEY(seo_mot_cle_racine_id, seo_liste_type_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_racine_seo_liste_theme (seo_mot_cle_racine_id INT NOT NULL, seo_liste_theme_id INT NOT NULL, INDEX IDX_FF4C53B064E2A64D (seo_mot_cle_racine_id), INDEX IDX_FF4C53B03A60A64D (seo_liste_theme_id), PRIMARY KEY(seo_mot_cle_racine_id, seo_liste_theme_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE seo_mot_cle_racine_seo_liste_secteur (seo_mot_cle_racine_id INT NOT NULL, seo_liste_secteur_id INT NOT NULL, INDEX IDX_60551E0364E2A64D (seo_mot_cle_racine_id), INDEX IDX_60551E03C6B6AB5B (seo_liste_secteur_id), PRIMARY KEY(seo_mot_cle_racine_id, seo_liste_secteur_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE sf_exception (id INT AUTO_INCREMENT NOT NULL, created_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', message VARCHAR(255) DEFAULT NULL, stack_trace LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE statistique_pole_emploi (id INT AUTO_INCREMENT NOT NULL, rejet INT DEFAULT NULL, agregation INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE stats_clique_pub (id INT AUTO_INCREMENT NOT NULL, off_offre_id_offre INT DEFAULT NULL, nb_clique INT DEFAULT NULL, user_user_id_user INT DEFAULT NULL, id_site INT DEFAULT NULL, date VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE stats_search (id INT AUTO_INCREMENT NOT NULL, stats_type VARCHAR(255) DEFAULT NULL, stats_recherche VARCHAR(255) DEFAULT NULL, stats_mot_cle VARCHAR(255) DEFAULT NULL, stats_localisation VARCHAR(255) DEFAULT NULL, stats_type_secteur VARCHAR(255) DEFAULT NULL, stats_id_offre_veille INT DEFAULT NULL, stats_id_entreprise INT DEFAULT NULL, stats_id_mot_cle INT DEFAULT NULL, stats_id_type_contrat INT DEFAULT NULL, stats_id_secteur_activite INT DEFAULT NULL, stats_id_sous_secteur_activite INT DEFAULT NULL, stats_id_region INT DEFAULT NULL, stats_id_departement INT DEFAULT NULL, stats_id_pays INT DEFAULT NULL, stats_id_type_formation INT DEFAULT NULL, stats_id_experience_requise INT DEFAULT NULL, stats_id_remuneration INT DEFAULT NULL, stats_id_sous_theme INT DEFAULT NULL, stats_id_zoom_sectoriel INT DEFAULT NULL, stats_ville VARCHAR(255) DEFAULT NULL, stats_date_creation DATE DEFAULT NULL, stats_type_search VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE stats_search_tend (id INT AUTO_INCREMENT NOT NULL, tend_value VARCHAR(255) DEFAULT NULL, tend_position INT DEFAULT NULL, tend_type_value INT DEFAULT NULL, tend_nbr_value INT DEFAULT NULL, tend_date_creation DATE DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE temp_greg (id INT AUTO_INCREMENT NOT NULL, autre LONGTEXT DEFAULT NULL, autre2 LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE tra_lien (id INT AUTO_INCREMENT NOT NULL, date_creation DATETIME DEFAULT NULL, titre VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, lien_redirection VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE tra_statistique (id INT AUTO_INCREMENT NOT NULL, tra_lien_id_lien INT DEFAULT NULL, user_user_id_user INT DEFAULT NULL, adresse_ip VARCHAR(255) DEFAULT NULL, date_log DATETIME DEFAULT NULL, type_log SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_offre_veille_user_user (user_offre_veille_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_22A1FB981BCD8316 (user_offre_veille_id), INDEX IDX_22A1FB98FF63CD9F (user_user_id), PRIMARY KEY(user_offre_veille_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation (id INT AUTO_INCREMENT NOT NULL, nationalite VARCHAR(255) DEFAULT NULL, etablissement_autre VARCHAR(255) DEFAULT NULL, annee_obtention_diplome VARCHAR(255) DEFAULT NULL, nom_diplome VARCHAR(255) DEFAULT NULL, id_metier INT DEFAULT NULL, poste_actuel VARCHAR(255) DEFAULT NULL, id_off_fourchette_remuneration INT DEFAULT NULL, off_liste_secteur_activite_id_secteur_activite_poste INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_user_user (user_situation_id INT NOT NULL, user_user_id INT NOT NULL, INDEX IDX_618B9C3B4C494AD (user_situation_id), INDEX IDX_618B9C3FF63CD9F (user_user_id), PRIMARY KEY(user_situation_id, user_user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_user_situation_activite (user_situation_id INT NOT NULL, user_situation_activite_id INT NOT NULL, INDEX IDX_F7A2F118B4C494AD (user_situation_id), INDEX IDX_F7A2F118E23C140F (user_situation_activite_id), PRIMARY KEY(user_situation_id, user_situation_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_user_situation_statut (user_situation_id INT NOT NULL, user_situation_statut_id INT NOT NULL, INDEX IDX_DE067EF2B4C494AD (user_situation_id), INDEX IDX_DE067EF272A1B47 (user_situation_statut_id), PRIMARY KEY(user_situation_id, user_situation_statut_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_off_liste_type_formation (user_situation_id INT NOT NULL, off_liste_type_formation_id INT NOT NULL, INDEX IDX_FF6415CEB4C494AD (user_situation_id), INDEX IDX_FF6415CEA8BBB27F (off_liste_type_formation_id), PRIMARY KEY(user_situation_id, off_liste_type_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_user_situation_experience (user_situation_id INT NOT NULL, user_situation_experience_id INT NOT NULL, INDEX IDX_61859768B4C494AD (user_situation_id), INDEX IDX_61859768C5162B4D (user_situation_experience_id), PRIMARY KEY(user_situation_id, user_situation_experience_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_user_disponibilite (user_situation_id INT NOT NULL, user_disponibilite_id INT NOT NULL, INDEX IDX_286CACFAB4C494AD (user_situation_id), INDEX IDX_286CACFA5688264E (user_disponibilite_id), PRIMARY KEY(user_situation_id, user_disponibilite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_user_situation_mobilite (user_situation_id INT NOT NULL, user_situation_mobilite_id INT NOT NULL, INDEX IDX_8D86D85EB4C494AD (user_situation_id), INDEX IDX_8D86D85EEE6E9E3D (user_situation_mobilite_id), PRIMARY KEY(user_situation_id, user_situation_mobilite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_ec_ecole (user_situation_id INT NOT NULL, ec_ecole_id INT NOT NULL, INDEX IDX_AB34018CB4C494AD (user_situation_id), INDEX IDX_AB34018CCFF27E35 (ec_ecole_id), PRIMARY KEY(user_situation_id, ec_ecole_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_ec_session_formation (user_situation_id INT NOT NULL, ec_session_formation_id INT NOT NULL, INDEX IDX_26B2E375B4C494AD (user_situation_id), INDEX IDX_26B2E375F551B8FE (ec_session_formation_id), PRIMARY KEY(user_situation_id, ec_session_formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_off_liste_secteur_activite (user_situation_id INT NOT NULL, off_liste_secteur_activite_id INT NOT NULL, INDEX IDX_506B56B4C494AD (user_situation_id), INDEX IDX_506B56B78A629C (off_liste_secteur_activite_id), PRIMARY KEY(user_situation_id, off_liste_secteur_activite_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_experience (id INT AUTO_INCREMENT NOT NULL, libelle VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_situation_experience_off_liste_experience_requise (user_situation_experience_id INT NOT NULL, off_liste_experience_requise_id INT NOT NULL, INDEX IDX_D9B522F6C5162B4D (user_situation_experience_id), INDEX IDX_D9B522F6BC4B4FB9 (off_liste_experience_requise_id), PRIMARY KEY(user_situation_experience_id, off_liste_experience_requise_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_stats (id INT AUTO_INCREMENT NOT NULL, user_user_id_user INT DEFAULT NULL, date_fin_abo DATE DEFAULT NULL, nb_offres_emploi INT DEFAULT NULL, nb_offres_stage INT DEFAULT NULL, nb_offres_alternance INT DEFAULT NULL, nb_offres_emploi_recente INT DEFAULT NULL, nb_offres_stage_recente INT DEFAULT NULL, nb_offres_alternance_recente INT DEFAULT NULL, nb_candidatures_recente INT DEFAULT NULL, nb_candidatures_mail_recente INT DEFAULT NULL, nb_candidatures_ancienne INT DEFAULT NULL, nb_candidatures_2mois INT DEFAULT NULL, nb_candidatures_mail_3mois INT DEFAULT NULL, nb_candidatures_mail_2mois INT DEFAULT NULL, nb_candidatures_3mois INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE web_user_agent (id INT AUTO_INCREMENT NOT NULL, libelle LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', has_been_checked SMALLINT DEFAULT NULL, is_bot SMALLINT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE cv_lien_exp_pro_off_liste_sect_activite_off_liste_secteur_activite ADD CONSTRAINT FK_2BA542C95E1A0290 FOREIGN KEY (cv_lien_exp_pro_off_liste_sect_activite_id) REFERENCES cv_lien_exp_pro_off_liste_sect_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_exp_pro_off_liste_sect_activite_off_liste_secteur_activite ADD CONSTRAINT FK_2BA542C9B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_exp_pro_off_liste_sect_activite_cv_experience_pro ADD CONSTRAINT FK_9A6799955E1A0290 FOREIGN KEY (cv_lien_exp_pro_off_liste_sect_activite_id) REFERENCES cv_lien_exp_pro_off_liste_sect_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_exp_pro_off_liste_sect_activite_cv_experience_pro ADD CONSTRAINT FK_9A679995F6D4F645 FOREIGN KEY (cv_experience_pro_id) REFERENCES cv_experience_pro (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_departement_geo_departement ADD CONSTRAINT FK_21872B647E037882 FOREIGN KEY (cv_lien_formation_geo_departement_id) REFERENCES cv_lien_formation_geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_departement_geo_departement ADD CONSTRAINT FK_21872B64964DE230 FOREIGN KEY (geo_departement_id) REFERENCES geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_departement_cv_formation ADD CONSTRAINT FK_8A2201AD7E037882 FOREIGN KEY (cv_lien_formation_geo_departement_id) REFERENCES cv_lien_formation_geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_departement_cv_formation ADD CONSTRAINT FK_8A2201AD9112D88B FOREIGN KEY (cv_formation_id) REFERENCES cv_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_pays_geo_liste_pays ADD CONSTRAINT FK_4C528D3B504D6EAC FOREIGN KEY (cv_lien_formation_geo_liste_pays_id) REFERENCES cv_lien_formation_geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_pays_geo_liste_pays ADD CONSTRAINT FK_4C528D3BA603EFEB FOREIGN KEY (geo_liste_pays_id) REFERENCES geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_pays_cv_formation ADD CONSTRAINT FK_A7F99B2B504D6EAC FOREIGN KEY (cv_lien_formation_geo_liste_pays_id) REFERENCES cv_lien_formation_geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_pays_cv_formation ADD CONSTRAINT FK_A7F99B2B9112D88B FOREIGN KEY (cv_formation_id) REFERENCES cv_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_region_geo_liste_region ADD CONSTRAINT FK_92F45B333C59E2FB FOREIGN KEY (cv_lien_formation_geo_liste_region_id) REFERENCES cv_lien_formation_geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_region_geo_liste_region ADD CONSTRAINT FK_92F45B3319DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_region_cv_formation ADD CONSTRAINT FK_C8B7EC013C59E2FB FOREIGN KEY (cv_lien_formation_geo_liste_region_id) REFERENCES cv_lien_formation_geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_region_cv_formation ADD CONSTRAINT FK_C8B7EC019112D88B FOREIGN KEY (cv_formation_id) REFERENCES cv_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lienformation_off_liste_secteur_activite_cv_formation ADD CONSTRAINT FK_AF1F34838E8D19DF FOREIGN KEY (cv_lienformation_off_liste_secteur_activite_id) REFERENCES cv_lienformation_off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lienformation_off_liste_secteur_activite_cv_formation ADD CONSTRAINT FK_AF1F34839112D88B FOREIGN KEY (cv_formation_id) REFERENCES cv_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lienformation_off_liste_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_9737B8928E8D19DF FOREIGN KEY (cv_lienformation_off_liste_secteur_activite_id) REFERENCES cv_lienformation_off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lienformation_off_liste_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_9737B892B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_log_cv_cv ADD CONSTRAINT FK_7A54423167E054A3 FOREIGN KEY (cv_log_id) REFERENCES cv_log (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_log_cv_cv ADD CONSTRAINT FK_7A54423140E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_log_user_user ADD CONSTRAINT FK_476F439967E054A3 FOREIGN KEY (cv_log_id) REFERENCES cv_log (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_log_user_user ADD CONSTRAINT FK_476F4399FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_commentaire_cv_cv ADD CONSTRAINT FK_E2677317DF49666 FOREIGN KEY (cv_recruteur_cv_commentaire_id) REFERENCES cv_recruteur_cv_commentaire (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_commentaire_cv_cv ADD CONSTRAINT FK_E26773140E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_commentaire_user_user ADD CONSTRAINT FK_2FAECAB37DF49666 FOREIGN KEY (cv_recruteur_cv_commentaire_id) REFERENCES cv_recruteur_cv_commentaire (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_commentaire_user_user ADD CONSTRAINT FK_2FAECAB3FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_dossier_user_user ADD CONSTRAINT FK_744301F035A241C FOREIGN KEY (cv_recruteur_cv_dossier_id) REFERENCES cv_recruteur_cv_dossier (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_dossier_user_user ADD CONSTRAINT FK_744301F0FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_note_cv_cv ADD CONSTRAINT FK_C1E40BBDAF01F264 FOREIGN KEY (cv_recruteur_cv_note_id) REFERENCES cv_recruteur_cv_note (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_note_cv_cv ADD CONSTRAINT FK_C1E40BBD40E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_note_user_user ADD CONSTRAINT FK_27DD38E9AF01F264 FOREIGN KEY (cv_recruteur_cv_note_id) REFERENCES cv_recruteur_cv_note (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_note_user_user ADD CONSTRAINT FK_27DD38E9FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_sauvegarde_cv_cv ADD CONSTRAINT FK_665AE5062ECB73F6 FOREIGN KEY (cv_recruteur_cv_sauvegarde_id) REFERENCES cv_recruteur_cv_sauvegarde (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_sauvegarde_cv_cv ADD CONSTRAINT FK_665AE50640E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_sauvegarde_user_user ADD CONSTRAINT FK_7C26B9A52ECB73F6 FOREIGN KEY (cv_recruteur_cv_sauvegarde_id) REFERENCES cv_recruteur_cv_sauvegarde (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_sauvegarde_user_user ADD CONSTRAINT FK_7C26B9A5FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_sauvegarde_cv_recruteur_cv_dossier ADD CONSTRAINT FK_B03894282ECB73F6 FOREIGN KEY (cv_recruteur_cv_sauvegarde_id) REFERENCES cv_recruteur_cv_sauvegarde (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_recruteur_cv_sauvegarde_cv_recruteur_cv_dossier ADD CONSTRAINT FK_B038942835A241C FOREIGN KEY (cv_recruteur_cv_dossier_id) REFERENCES cv_recruteur_cv_dossier (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_forum_ec_ecole ADD CONSTRAINT FK_BFA1651712142D25 FOREIGN KEY (ec_calendrier_forum_id) REFERENCES ec_calendrier_forum (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_forum_ec_ecole ADD CONSTRAINT FK_BFA16517CFF27E35 FOREIGN KEY (ec_ecole_id) REFERENCES ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_forum_off_liste_type_formation ADD CONSTRAINT FK_C2FBBC3612142D25 FOREIGN KEY (ec_calendrier_forum_id) REFERENCES ec_calendrier_forum (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_forum_off_liste_type_formation ADD CONSTRAINT FK_C2FBBC36A8BBB27F FOREIGN KEY (off_liste_type_formation_id) REFERENCES off_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_forum_off_liste_secteur_activite ADD CONSTRAINT FK_733F263012142D25 FOREIGN KEY (ec_calendrier_forum_id) REFERENCES ec_calendrier_forum (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_forum_off_liste_secteur_activite ADD CONSTRAINT FK_733F2630B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_forum_geo_adresse ADD CONSTRAINT FK_7DC887C712142D25 FOREIGN KEY (ec_calendrier_forum_id) REFERENCES ec_calendrier_forum (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_forum_geo_adresse ADD CONSTRAINT FK_7DC887C7349A5BD2 FOREIGN KEY (geo_adresse_id) REFERENCES geo_adresse (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_forum_user_civilite ADD CONSTRAINT FK_88DA01A712142D25 FOREIGN KEY (ec_calendrier_forum_id) REFERENCES ec_calendrier_forum (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_forum_user_civilite ADD CONSTRAINT FK_88DA01A79002832A FOREIGN KEY (user_civilite_id) REFERENCES user_civilite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_stage_ec_ecole ADD CONSTRAINT FK_90B52C9D19404666 FOREIGN KEY (ec_calendrier_stage_id) REFERENCES ec_calendrier_stage (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_stage_ec_ecole ADD CONSTRAINT FK_90B52C9DCFF27E35 FOREIGN KEY (ec_ecole_id) REFERENCES ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_stage_ec_liste_annee_ecole ADD CONSTRAINT FK_912D78B019404666 FOREIGN KEY (ec_calendrier_stage_id) REFERENCES ec_calendrier_stage (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_stage_ec_liste_annee_ecole ADD CONSTRAINT FK_912D78B0F70135A FOREIGN KEY (ec_liste_annee_ecole_id) REFERENCES ec_liste_annee_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_stage_off_liste_type_formation ADD CONSTRAINT FK_3D2CAA4B19404666 FOREIGN KEY (ec_calendrier_stage_id) REFERENCES ec_calendrier_stage (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_stage_off_liste_type_formation ADD CONSTRAINT FK_3D2CAA4BA8BBB27F FOREIGN KEY (off_liste_type_formation_id) REFERENCES off_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_stage_off_liste_secteur_activite ADD CONSTRAINT FK_1D7A6B0D19404666 FOREIGN KEY (ec_calendrier_stage_id) REFERENCES ec_calendrier_stage (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_stage_off_liste_secteur_activite ADD CONSTRAINT FK_1D7A6B0DB78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_stage_user_civilite ADD CONSTRAINT FK_9CA38DE219404666 FOREIGN KEY (ec_calendrier_stage_id) REFERENCES ec_calendrier_stage (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_calendrier_stage_user_civilite ADD CONSTRAINT FK_9CA38DE29002832A FOREIGN KEY (user_civilite_id) REFERENCES user_civilite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_ecole_geo_adresse ADD CONSTRAINT FK_C471AD4FCFF27E35 FOREIGN KEY (ec_ecole_id) REFERENCES ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_ecole_geo_adresse ADD CONSTRAINT FK_C471AD4F349A5BD2 FOREIGN KEY (geo_adresse_id) REFERENCES geo_adresse (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_ecole_off_type_xml ADD CONSTRAINT FK_42A8C242CFF27E35 FOREIGN KEY (ec_ecole_id) REFERENCES ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_ecole_off_type_xml ADD CONSTRAINT FK_42A8C242C2329246 FOREIGN KEY (off_type_xml_id) REFERENCES off_type_xml (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_ecole_ec_liste_type_ecole ADD CONSTRAINT FK_FB032AA0CFF27E35 FOREIGN KEY (ec_ecole_id) REFERENCES ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_ecole_ec_liste_type_ecole ADD CONSTRAINT FK_FB032AA0D51A810 FOREIGN KEY (ec_liste_type_ecole_id) REFERENCES ec_liste_type_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_ecole ADD CONSTRAINT FK_93D9B7C7632F2121 FOREIGN KEY (ec_formation_temp_id) REFERENCES ec_formation_temp (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_ecole ADD CONSTRAINT FK_93D9B7C7CFF27E35 FOREIGN KEY (ec_ecole_id) REFERENCES ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_organisme_formation ADD CONSTRAINT FK_FCEB1883632F2121 FOREIGN KEY (ec_formation_temp_id) REFERENCES ec_formation_temp (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_organisme_formation ADD CONSTRAINT FK_FCEB1883C540A215 FOREIGN KEY (ec_organisme_formation_id) REFERENCES ec_organisme_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_liste_type_session ADD CONSTRAINT FK_9C9346F9632F2121 FOREIGN KEY (ec_formation_temp_id) REFERENCES ec_formation_temp (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_liste_type_session ADD CONSTRAINT FK_9C9346F9ADD459D9 FOREIGN KEY (ec_liste_type_session_id) REFERENCES ec_liste_type_session (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_lien_session_form_off_sect_activite_ec_session_formation ADD CONSTRAINT FK_8834768AB9AD7B2 FOREIGN KEY (ec_lien_session_form_off_sect_activite_id) REFERENCES ec_lien_session_form_off_sect_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_lien_session_form_off_sect_activite_ec_session_formation ADD CONSTRAINT FK_8834768F551B8FE FOREIGN KEY (ec_session_formation_id) REFERENCES ec_session_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_lien_session_form_off_sect_activite_off_liste_secteur_activite ADD CONSTRAINT FK_40FBB76BAB9AD7B2 FOREIGN KEY (ec_lien_session_form_off_sect_activite_id) REFERENCES ec_lien_session_form_off_sect_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_lien_session_form_off_sect_activite_off_liste_secteur_activite ADD CONSTRAINT FK_40FBB76BB78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_ec_session_formation ADD CONSTRAINT FK_2C0245D0AF8007FD FOREIGN KEY (ec_lien_session_formation_off_liste_sous_secteur_activite_id) REFERENCES ec_lien_session_formation_off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_ec_session_formation ADD CONSTRAINT FK_2C0245D0F551B8FE FOREIGN KEY (ec_session_formation_id) REFERENCES ec_session_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite ADD CONSTRAINT FK_16E29B8BAF8007FD FOREIGN KEY (ec_lien_session_formation_off_liste_sous_secteur_activite_id) REFERENCES ec_lien_session_formation_off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite ADD CONSTRAINT FK_16E29B8BCE509CB7 FOREIGN KEY (off_liste_sous_secteur_activite_id) REFERENCES off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_organisme_formation_geo_adresse ADD CONSTRAINT FK_1433150C540A215 FOREIGN KEY (ec_organisme_formation_id) REFERENCES ec_organisme_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_organisme_formation_geo_adresse ADD CONSTRAINT FK_1433150349A5BD2 FOREIGN KEY (geo_adresse_id) REFERENCES geo_adresse (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_session_formation ADD CONSTRAINT FK_6B9DBA02B16F300B FOREIGN KEY (user_civilit�e_id_user_civilite_id) REFERENCES user_civilite (id)');
        $this->addSql('ALTER TABLE ec_session_formation_geo_adresse ADD CONSTRAINT FK_91889352F551B8FE FOREIGN KEY (ec_session_formation_id) REFERENCES ec_session_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_session_formation_geo_adresse ADD CONSTRAINT FK_91889352349A5BD2 FOREIGN KEY (geo_adresse_id) REFERENCES geo_adresse (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_session_formation_off_liste_type_formation ADD CONSTRAINT FK_3F31AE7BF551B8FE FOREIGN KEY (ec_session_formation_id) REFERENCES ec_session_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_session_formation_off_liste_type_formation ADD CONSTRAINT FK_3F31AE7BA8BBB27F FOREIGN KEY (off_liste_type_formation_id) REFERENCES off_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_stat_calendrier_forum_ec_calendrier_forum ADD CONSTRAINT FK_2E42C2578E254D36 FOREIGN KEY (ec_stat_calendrier_forum_id) REFERENCES ec_stat_calendrier_forum (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_stat_calendrier_forum_ec_calendrier_forum ADD CONSTRAINT FK_2E42C25712142D25 FOREIGN KEY (ec_calendrier_forum_id) REFERENCES ec_calendrier_forum (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_stat_calendrier_stage_ec_calendrier_stage ADD CONSTRAINT FK_4F8ECD0585712675 FOREIGN KEY (ec_stat_calendrier_stage_id) REFERENCES ec_stat_calendrier_stage (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_stat_calendrier_stage_ec_calendrier_stage ADD CONSTRAINT FK_4F8ECD0519404666 FOREIGN KEY (ec_calendrier_stage_id) REFERENCES ec_calendrier_stage (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_statistique_ec_session_formation ADD CONSTRAINT FK_5BF9C58E232F3941 FOREIGN KEY (ec_statistique_id) REFERENCES ec_statistique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_statistique_ec_session_formation ADD CONSTRAINT FK_5BF9C58EF551B8FE FOREIGN KEY (ec_session_formation_id) REFERENCES ec_session_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_statistique_user_user ADD CONSTRAINT FK_18095843232F3941 FOREIGN KEY (ec_statistique_id) REFERENCES ec_statistique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ec_statistique_user_user ADD CONSTRAINT FK_18095843FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_article_off_liste_secteur_activite ADD CONSTRAINT FK_722B70E2E7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_article_off_liste_secteur_activite ADD CONSTRAINT FK_722B70E2B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_article_edit_type_article ADD CONSTRAINT FK_6DBDE3C1E7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_article_edit_type_article ADD CONSTRAINT FK_6DBDE3C17F3CE950 FOREIGN KEY (edit_type_article_id) REFERENCES edit_type_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_article_edit_theme_article ADD CONSTRAINT FK_DE7B9D1FE7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_article_edit_theme_article ADD CONSTRAINT FK_DE7B9D1F38954BBE FOREIGN KEY (edit_theme_article_id) REFERENCES edit_theme_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_article_edit_sous_theme_article ADD CONSTRAINT FK_F18B9958E7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_article_edit_sous_theme_article ADD CONSTRAINT FK_F18B9958F4BFBBE FOREIGN KEY (edit_sous_theme_article_id) REFERENCES edit_sous_theme_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_article_user_user ADD CONSTRAINT FK_10269982E7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_article_user_user ADD CONSTRAINT FK_10269982FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_departement_geo_departement ADD CONSTRAINT FK_76249A0DB60CC0C0 FOREIGN KEY (edit_carte_departement_id) REFERENCES edit_carte_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_departement_geo_departement ADD CONSTRAINT FK_76249A0D964DE230 FOREIGN KEY (geo_departement_id) REFERENCES geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_departement_edit_carte_region ADD CONSTRAINT FK_8E326622B60CC0C0 FOREIGN KEY (edit_carte_departement_id) REFERENCES edit_carte_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_departement_edit_carte_region ADD CONSTRAINT FK_8E3266226FBE7ACB FOREIGN KEY (edit_carte_region_id) REFERENCES edit_carte_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_region_geo_liste_region ADD CONSTRAINT FK_AA1D1E656FBE7ACB FOREIGN KEY (edit_carte_region_id) REFERENCES edit_carte_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_region_geo_liste_region ADD CONSTRAINT FK_AA1D1E6519DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_region_edit_article ADD CONSTRAINT FK_2E3DEC626FBE7ACB FOREIGN KEY (edit_carte_region_id) REFERENCES edit_carte_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_region_edit_article ADD CONSTRAINT FK_2E3DEC62E7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_visite_edit_article ADD CONSTRAINT FK_6A5B2464365DA7C7 FOREIGN KEY (edit_carte_visite_id) REFERENCES edit_carte_visite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_visite_edit_article ADD CONSTRAINT FK_6A5B2464E7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_visite_geo_adresse ADD CONSTRAINT FK_E65DC032365DA7C7 FOREIGN KEY (edit_carte_visite_id) REFERENCES edit_carte_visite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_carte_visite_geo_adresse ADD CONSTRAINT FK_E65DC032349A5BD2 FOREIGN KEY (geo_adresse_id) REFERENCES geo_adresse (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_focus_accueil_edit_app ADD CONSTRAINT FK_2DFB15A739FA9219 FOREIGN KEY (edit_focus_accueil_id) REFERENCES edit_focus_accueil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_focus_accueil_edit_app ADD CONSTRAINT FK_2DFB15A7570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_image_accueil_edit_focus_accueil ADD CONSTRAINT FK_DCCFEA79C986DB92 FOREIGN KEY (edit_image_accueil_id) REFERENCES edit_image_accueil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_image_accueil_edit_focus_accueil ADD CONSTRAINT FK_DCCFEA7939FA9219 FOREIGN KEY (edit_focus_accueil_id) REFERENCES edit_focus_accueil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_image_accueil_edit_theme_article ADD CONSTRAINT FK_7FB9621BC986DB92 FOREIGN KEY (edit_image_accueil_id) REFERENCES edit_image_accueil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_image_accueil_edit_theme_article ADD CONSTRAINT FK_7FB9621B38954BBE FOREIGN KEY (edit_theme_article_id) REFERENCES edit_theme_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_image_profil_edit_zoom_profil ADD CONSTRAINT FK_735519E1EF77B2C5 FOREIGN KEY (edit_image_profil_id) REFERENCES edit_image_profil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_image_profil_edit_zoom_profil ADD CONSTRAINT FK_735519E1F96D9DBD FOREIGN KEY (edit_zoom_profil_id) REFERENCES edit_zoom_profil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_image_zoom_edit_zoom_sectoriel ADD CONSTRAINT FK_AB532EAF80DE7A6C FOREIGN KEY (edit_image_zoom_id) REFERENCES edit_image_zoom (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_image_zoom_edit_zoom_sectoriel ADD CONSTRAINT FK_AB532EAF179B07B3 FOREIGN KEY (edit_zoom_sectoriel_id) REFERENCES edit_zoom_sectoriel (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_photo_article_edit_article ADD CONSTRAINT FK_F0A9E0E39D70811F FOREIGN KEY (edit_lien_photo_article_id) REFERENCES edit_lien_photo_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_photo_article_edit_article ADD CONSTRAINT FK_F0A9E0E3E7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_photo_article_edit_photo ADD CONSTRAINT FK_2E9E95C99D70811F FOREIGN KEY (edit_lien_photo_article_id) REFERENCES edit_lien_photo_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_photo_article_edit_photo ADD CONSTRAINT FK_2E9E95C95590E8A5 FOREIGN KEY (edit_photo_id) REFERENCES edit_photo (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_departement_edit_rss_thematique ADD CONSTRAINT FK_6651E1476A97D0E4 FOREIGN KEY (edit_lien_rss_thematique_departement_id) REFERENCES edit_lien_rss_thematique_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_departement_edit_rss_thematique ADD CONSTRAINT FK_6651E1479C672457 FOREIGN KEY (edit_rss_thematique_id) REFERENCES edit_rss_thematique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_departement_geo_departement ADD CONSTRAINT FK_B6E04F456A97D0E4 FOREIGN KEY (edit_lien_rss_thematique_departement_id) REFERENCES edit_lien_rss_thematique_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_departement_geo_departement ADD CONSTRAINT FK_B6E04F45964DE230 FOREIGN KEY (geo_departement_id) REFERENCES geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_region_edit_rss_thematique ADD CONSTRAINT FK_35AC6E2E49E1C286 FOREIGN KEY (edit_lien_rss_thematique_region_id) REFERENCES edit_lien_rss_thematique_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_region_edit_rss_thematique ADD CONSTRAINT FK_35AC6E2E9C672457 FOREIGN KEY (edit_rss_thematique_id) REFERENCES edit_rss_thematique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_region_geo_liste_region ADD CONSTRAINT FK_8188027349E1C286 FOREIGN KEY (edit_lien_rss_thematique_region_id) REFERENCES edit_lien_rss_thematique_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_region_geo_liste_region ADD CONSTRAINT FK_8188027319DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_secteur_activite_edit_rss_thematique ADD CONSTRAINT FK_997FF11F1A88EAE4 FOREIGN KEY (edit_lien_rss_thematique_secteur_activite_id) REFERENCES edit_lien_rss_thematique_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_secteur_activite_edit_rss_thematique ADD CONSTRAINT FK_997FF11F9C672457 FOREIGN KEY (edit_rss_thematique_id) REFERENCES edit_rss_thematique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_C79248CE1A88EAE4 FOREIGN KEY (edit_lien_rss_thematique_secteur_activite_id) REFERENCES edit_lien_rss_thematique_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_C79248CEB78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_sous_secteur_activite_edit_rss_thematique ADD CONSTRAINT FK_BA12FEE23732BFA7 FOREIGN KEY (edit_lien_rss_thematique_sous_secteur_activite_id) REFERENCES edit_lien_rss_thematique_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_sous_secteur_activite_edit_rss_thematique ADD CONSTRAINT FK_BA12FEE29C672457 FOREIGN KEY (edit_rss_thematique_id) REFERENCES edit_rss_thematique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_sous_secteur_activite_off_liste_sous_secteur_activite ADD CONSTRAINT FK_78F46E7E3732BFA7 FOREIGN KEY (edit_lien_rss_thematique_sous_secteur_activite_id) REFERENCES edit_lien_rss_thematique_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_sous_secteur_activite_off_liste_sous_secteur_activite ADD CONSTRAINT FK_78F46E7ECE509CB7 FOREIGN KEY (off_liste_sous_secteur_activite_id) REFERENCES off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_video_article_edit_article ADD CONSTRAINT FK_7AEE545251B377A1 FOREIGN KEY (edit_lien_video_article_id) REFERENCES edit_lien_video_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_video_article_edit_article ADD CONSTRAINT FK_7AEE5452E7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_video_article_edit_video ADD CONSTRAINT FK_39A5694B51B377A1 FOREIGN KEY (edit_lien_video_article_id) REFERENCES edit_lien_video_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_video_article_edit_video ADD CONSTRAINT FK_39A5694B2CFA467 FOREIGN KEY (edit_video_id) REFERENCES edit_video (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_article_edit_zoom_sectoriel ADD CONSTRAINT FK_DFFE86E83036924A FOREIGN KEY (edit_lien_zoom_sectoriel_article_id) REFERENCES edit_lien_zoom_sectoriel_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_article_edit_zoom_sectoriel ADD CONSTRAINT FK_DFFE86E8179B07B3 FOREIGN KEY (edit_zoom_sectoriel_id) REFERENCES edit_zoom_sectoriel (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_article_edit_article ADD CONSTRAINT FK_F357AA9F3036924A FOREIGN KEY (edit_lien_zoom_sectoriel_article_id) REFERENCES edit_lien_zoom_sectoriel_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_article_edit_article ADD CONSTRAINT FK_F357AA9FE7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_edit_zoom_sectoriel ADD CONSTRAINT FK_D9EA904022CB57AF FOREIGN KEY (edit_lien_zoom_sectoriel_off_liste_secteur_activite_id) REFERENCES edit_lien_zoom_sectoriel_off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_edit_zoom_sectoriel ADD CONSTRAINT FK_D9EA9040179B07B3 FOREIGN KEY (edit_zoom_sectoriel_id) REFERENCES edit_zoom_sectoriel (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_F22027FD22CB57AF FOREIGN KEY (edit_lien_zoom_sectoriel_off_liste_secteur_activite_id) REFERENCES edit_lien_zoom_sectoriel_off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_F22027FDB78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_accueil_edit_focus_accueil ADD CONSTRAINT FK_53F0B9C5B618F0F4 FOREIGN KEY (edit_list_accueil_id) REFERENCES edit_list_accueil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_accueil_edit_focus_accueil ADD CONSTRAINT FK_53F0B9C539FA9219 FOREIGN KEY (edit_focus_accueil_id) REFERENCES edit_focus_accueil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_accueil_edit_theme_article ADD CONSTRAINT FK_F08631A7B618F0F4 FOREIGN KEY (edit_list_accueil_id) REFERENCES edit_list_accueil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_accueil_edit_theme_article ADD CONSTRAINT FK_F08631A738954BBE FOREIGN KEY (edit_theme_article_id) REFERENCES edit_theme_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_entreprise_edit_carte_visite ADD CONSTRAINT FK_19503173FC81074D FOREIGN KEY (edit_list_entreprise_id) REFERENCES edit_list_entreprise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_entreprise_edit_carte_visite ADD CONSTRAINT FK_19503173365DA7C7 FOREIGN KEY (edit_carte_visite_id) REFERENCES edit_carte_visite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_entreprise_entr_entreprise ADD CONSTRAINT FK_1D791962FC81074D FOREIGN KEY (edit_list_entreprise_id) REFERENCES edit_list_entreprise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_entreprise_entr_entreprise ADD CONSTRAINT FK_1D7919622923CBC2 FOREIGN KEY (entr_entreprise_id) REFERENCES entr_entreprise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_tag_edit_app ADD CONSTRAINT FK_75ED7A12394D8891 FOREIGN KEY (edit_list_tag_id) REFERENCES edit_list_tag (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_tag_edit_app ADD CONSTRAINT FK_75ED7A12570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_tag_off_liste_secteur_activite ADD CONSTRAINT FK_40953CF6394D8891 FOREIGN KEY (edit_list_tag_id) REFERENCES edit_list_tag (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_list_tag_off_liste_secteur_activite ADD CONSTRAINT FK_40953CF6B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_log_habillage_edit_habillage ADD CONSTRAINT FK_21CAEF345684EAC4 FOREIGN KEY (edit_log_habillage_id) REFERENCES edit_log_habillage (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_log_habillage_edit_habillage ADD CONSTRAINT FK_21CAEF34666748C1 FOREIGN KEY (edit_habillage_id) REFERENCES edit_habillage (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_page_bouche_edit_app ADD CONSTRAINT FK_B8FBE8E6DBF0A629 FOREIGN KEY (edit_page_bouche_id) REFERENCES edit_page_bouche (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_page_bouche_edit_app ADD CONSTRAINT FK_B8FBE8E6570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_page_cadre_edit_app ADD CONSTRAINT FK_461C89A61A1CEF4 FOREIGN KEY (edit_page_cadre_id) REFERENCES edit_page_cadre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_page_cadre_edit_app ADD CONSTRAINT FK_461C89A570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_page_cadre_edit_article ADD CONSTRAINT FK_57E9FC9C61A1CEF4 FOREIGN KEY (edit_page_cadre_id) REFERENCES edit_page_cadre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_page_cadre_edit_article ADD CONSTRAINT FK_57E9FC9CE7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_page_retail_edit_app ADD CONSTRAINT FK_22981E224D273415 FOREIGN KEY (edit_page_retail_id) REFERENCES edit_page_retail (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_page_retail_edit_app ADD CONSTRAINT FK_22981E22570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_page_thematique_edit_sous_theme_article ADD CONSTRAINT FK_71C035F948FA23A FOREIGN KEY (edit_page_thematique_id) REFERENCES edit_page_thematique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_page_thematique_edit_sous_theme_article ADD CONSTRAINT FK_71C035F9F4BFBBE FOREIGN KEY (edit_sous_theme_article_id) REFERENCES edit_sous_theme_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_publicite_entreprise_carte_region_edit_app ADD CONSTRAINT FK_68682061BCC314D9 FOREIGN KEY (edit_publicite_entreprise_carte_region_id) REFERENCES edit_publicite_entreprise_carte_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_publicite_entreprise_carte_region_edit_app ADD CONSTRAINT FK_68682061570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_publicite_entreprise_carte_region_edit_carte_region ADD CONSTRAINT FK_D156CC8EBCC314D9 FOREIGN KEY (edit_publicite_entreprise_carte_region_id) REFERENCES edit_publicite_entreprise_carte_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_publicite_entreprise_carte_region_edit_carte_region ADD CONSTRAINT FK_D156CC8E6FBE7ACB FOREIGN KEY (edit_carte_region_id) REFERENCES edit_carte_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_publicite_entreprise_carte_region_user_user ADD CONSTRAINT FK_771BADDCBCC314D9 FOREIGN KEY (edit_publicite_entreprise_carte_region_id) REFERENCES edit_publicite_entreprise_carte_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_publicite_entreprise_carte_region_user_user ADD CONSTRAINT FK_771BADDCFF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_referencement_edit_app ADD CONSTRAINT FK_40B1606349FF2995 FOREIGN KEY (edit_referencement_id) REFERENCES edit_referencement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_referencement_edit_app ADD CONSTRAINT FK_40B16063570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_sous_theme_article_edit_theme_article ADD CONSTRAINT FK_8F53F719F4BFBBE FOREIGN KEY (edit_sous_theme_article_id) REFERENCES edit_sous_theme_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_sous_theme_article_edit_theme_article ADD CONSTRAINT FK_8F53F71938954BBE FOREIGN KEY (edit_theme_article_id) REFERENCES edit_theme_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_statistique_edit_article ADD CONSTRAINT FK_51D762F376BA7F9 FOREIGN KEY (edit_statistique_id) REFERENCES edit_statistique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_statistique_edit_article ADD CONSTRAINT FK_51D762F3E7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_statistique_structure_user_user ADD CONSTRAINT FK_DBB3B1AECC27D7A4 FOREIGN KEY (edit_statistique_structure_id) REFERENCES edit_statistique_structure (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_statistique_structure_user_user ADD CONSTRAINT FK_DBB3B1AEFF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_statistique_structure_ec_ecole ADD CONSTRAINT FK_D40A5E3CC27D7A4 FOREIGN KEY (edit_statistique_structure_id) REFERENCES edit_statistique_structure (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_statistique_structure_ec_ecole ADD CONSTRAINT FK_D40A5E3CFF27E35 FOREIGN KEY (ec_ecole_id) REFERENCES ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_statistique_structure_ec_organisme_formation ADD CONSTRAINT FK_866F9E0ECC27D7A4 FOREIGN KEY (edit_statistique_structure_id) REFERENCES edit_statistique_structure (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_statistique_structure_ec_organisme_formation ADD CONSTRAINT FK_866F9E0EC540A215 FOREIGN KEY (ec_organisme_formation_id) REFERENCES ec_organisme_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_statistique_structure_entr_entreprise ADD CONSTRAINT FK_19F22B1CCC27D7A4 FOREIGN KEY (edit_statistique_structure_id) REFERENCES edit_statistique_structure (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_statistique_structure_entr_entreprise ADD CONSTRAINT FK_19F22B1C2923CBC2 FOREIGN KEY (entr_entreprise_id) REFERENCES entr_entreprise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_zoom_accueil_edit_focus_accueil ADD CONSTRAINT FK_7AA3E250974E38B3 FOREIGN KEY (edit_zoom_accueil_id) REFERENCES edit_zoom_accueil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_zoom_accueil_edit_focus_accueil ADD CONSTRAINT FK_7AA3E25039FA9219 FOREIGN KEY (edit_focus_accueil_id) REFERENCES edit_focus_accueil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_zoom_profil_edit_article ADD CONSTRAINT FK_8FB7C766F96D9DBD FOREIGN KEY (edit_zoom_profil_id) REFERENCES edit_zoom_profil (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE edit_zoom_profil_edit_article ADD CONSTRAINT FK_8FB7C766E7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_entreprise_geo_adresse ADD CONSTRAINT FK_65592BB12923CBC2 FOREIGN KEY (entr_entreprise_id) REFERENCES entr_entreprise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_entreprise_geo_adresse ADD CONSTRAINT FK_65592BB1349A5BD2 FOREIGN KEY (geo_adresse_id) REFERENCES geo_adresse (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_entreprise_entr_profil_recruteur ADD CONSTRAINT FK_F96B29FD2923CBC2 FOREIGN KEY (entr_entreprise_id) REFERENCES entr_entreprise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_entreprise_entr_profil_recruteur ADD CONSTRAINT FK_F96B29FDBC1D969B FOREIGN KEY (entr_profil_recruteur_id) REFERENCES entr_profil_recruteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_secteur_activite_entr_entreprise ADD CONSTRAINT FK_33BD929740FECEFE FOREIGN KEY (entr_lien_entreprise_off_liste_secteur_activite_id) REFERENCES entr_lien_entreprise_off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_secteur_activite_entr_entreprise ADD CONSTRAINT FK_33BD92972923CBC2 FOREIGN KEY (entr_entreprise_id) REFERENCES entr_entreprise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_56929C9A40FECEFE FOREIGN KEY (entr_lien_entreprise_off_liste_secteur_activite_id) REFERENCES entr_lien_entreprise_off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_56929C9AB78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_entr_entreprise ADD CONSTRAINT FK_B9878DB8221AA9D1 FOREIGN KEY (entr_lien_entreprise_off_liste_sous_secteur_activite_id) REFERENCES entr_lien_entreprise_off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_entr_entreprise ADD CONSTRAINT FK_B9878DB82923CBC2 FOREIGN KEY (entr_entreprise_id) REFERENCES entr_entreprise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite ADD CONSTRAINT FK_C85F25F1221AA9D1 FOREIGN KEY (entr_lien_entreprise_off_liste_sous_secteur_activite_id) REFERENCES entr_lien_entreprise_off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite ADD CONSTRAINT FK_C85F25F1CE509CB7 FOREIGN KEY (off_liste_sous_secteur_activite_id) REFERENCES off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_expression_exp_valeur_offre ADD CONSTRAINT FK_66F01F9737DDE08D FOREIGN KEY (exp_expression_id) REFERENCES exp_expression (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_expression_exp_valeur_offre ADD CONSTRAINT FK_66F01F979B3437BC FOREIGN KEY (exp_valeur_offre_id) REFERENCES exp_valeur_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_expression_exp_valeur_fixe ADD CONSTRAINT FK_D8889F3637DDE08D FOREIGN KEY (exp_expression_id) REFERENCES exp_expression (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_expression_exp_valeur_fixe ADD CONSTRAINT FK_D8889F36F71E40CF FOREIGN KEY (exp_valeur_fixe_id) REFERENCES exp_valeur_fixe (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_expression_exp_fichier ADD CONSTRAINT FK_6C9FD83837DDE08D FOREIGN KEY (exp_expression_id) REFERENCES exp_expression (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_expression_exp_fichier ADD CONSTRAINT FK_6C9FD8381A25ABF4 FOREIGN KEY (exp_fichier_id) REFERENCES exp_fichier (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_expression_exp_correspondance ADD CONSTRAINT FK_33235A4337DDE08D FOREIGN KEY (exp_expression_id) REFERENCES exp_expression (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_expression_exp_correspondance ADD CONSTRAINT FK_33235A43DF14880C FOREIGN KEY (exp_correspondance_id) REFERENCES exp_correspondance (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_fichier_exp_type_fichier ADD CONSTRAINT FK_33BF037A1A25ABF4 FOREIGN KEY (exp_fichier_id) REFERENCES exp_fichier (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_fichier_exp_type_fichier ADD CONSTRAINT FK_33BF037AB6710CBC FOREIGN KEY (exp_type_fichier_id) REFERENCES exp_type_fichier (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_expression_mise_en_forme_exp_mise_en_forme ADD CONSTRAINT FK_247CE4693AE58F1D FOREIGN KEY (exp_lien_expression_mise_en_forme_id) REFERENCES exp_lien_expression_mise_en_forme (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_expression_mise_en_forme_exp_mise_en_forme ADD CONSTRAINT FK_247CE4696A3FEADF FOREIGN KEY (exp_mise_en_forme_id) REFERENCES exp_mise_en_forme (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_expression_mise_en_forme_exp_expression ADD CONSTRAINT FK_97517F303AE58F1D FOREIGN KEY (exp_lien_expression_mise_en_forme_id) REFERENCES exp_lien_expression_mise_en_forme (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_expression_mise_en_forme_exp_expression ADD CONSTRAINT FK_97517F3037DDE08D FOREIGN KEY (exp_expression_id) REFERENCES exp_expression (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_mise_en_forme_parametre_exp_mise_en_forme ADD CONSTRAINT FK_334C4113F827A23 FOREIGN KEY (exp_lien_mise_en_forme_parametre_id) REFERENCES exp_lien_mise_en_forme_parametre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_mise_en_forme_parametre_exp_mise_en_forme ADD CONSTRAINT FK_334C4116A3FEADF FOREIGN KEY (exp_mise_en_forme_id) REFERENCES exp_mise_en_forme (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_mise_en_forme_parametre_exp_parametre ADD CONSTRAINT FK_EE87362C3F827A23 FOREIGN KEY (exp_lien_mise_en_forme_parametre_id) REFERENCES exp_lien_mise_en_forme_parametre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_mise_en_forme_parametre_exp_parametre ADD CONSTRAINT FK_EE87362CBA6E65E3 FOREIGN KEY (exp_parametre_id) REFERENCES exp_parametre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_valeur_fixe_parametre_exp_valeur_fixe ADD CONSTRAINT FK_2A27B36E2C2ED9DE FOREIGN KEY (exp_lien_valeur_fixe_parametre_id) REFERENCES exp_lien_valeur_fixe_parametre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_valeur_fixe_parametre_exp_valeur_fixe ADD CONSTRAINT FK_2A27B36EF71E40CF FOREIGN KEY (exp_valeur_fixe_id) REFERENCES exp_valeur_fixe (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_valeur_fixe_parametre_exp_parametre ADD CONSTRAINT FK_1E1BEAA52C2ED9DE FOREIGN KEY (exp_lien_valeur_fixe_parametre_id) REFERENCES exp_lien_valeur_fixe_parametre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_valeur_fixe_parametre_exp_parametre ADD CONSTRAINT FK_1E1BEAA5BA6E65E3 FOREIGN KEY (exp_parametre_id) REFERENCES exp_parametre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_valeur_offre_parametre_exp_valeur_offre ADD CONSTRAINT FK_B429538EA53DF93D FOREIGN KEY (exp_lien_valeur_offre_parametre_id) REFERENCES exp_lien_valeur_offre_parametre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_valeur_offre_parametre_exp_valeur_offre ADD CONSTRAINT FK_B429538E9B3437BC FOREIGN KEY (exp_valeur_offre_id) REFERENCES exp_valeur_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_valeur_offre_parametre_exp_parametre ADD CONSTRAINT FK_36A8CC96A53DF93D FOREIGN KEY (exp_lien_valeur_offre_parametre_id) REFERENCES exp_lien_valeur_offre_parametre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_lien_valeur_offre_parametre_exp_parametre ADD CONSTRAINT FK_36A8CC96BA6E65E3 FOREIGN KEY (exp_parametre_id) REFERENCES exp_parametre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_table_correspondance_exp_correspondance ADD CONSTRAINT FK_E4E3912471D3D8F4 FOREIGN KEY (exp_table_correspondance_id) REFERENCES exp_table_correspondance (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE exp_table_correspondance_exp_correspondance ADD CONSTRAINT FK_E4E39124DF14880C FOREIGN KEY (exp_correspondance_id) REFERENCES exp_correspondance (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE fiche_metier_off_liste_secteur_activite ADD CONSTRAINT FK_9CAEE2836A7847C8 FOREIGN KEY (fiche_metier_id) REFERENCES fiche_metier (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE fiche_metier_off_liste_secteur_activite ADD CONSTRAINT FK_9CAEE283B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE fiche_metier_off_liste_sous_secteur_activite ADD CONSTRAINT FK_58C0B9166A7847C8 FOREIGN KEY (fiche_metier_id) REFERENCES fiche_metier (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE fiche_metier_off_liste_sous_secteur_activite ADD CONSTRAINT FK_58C0B916CE509CB7 FOREIGN KEY (off_liste_sous_secteur_activite_id) REFERENCES off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE fiche_metier_edit_app ADD CONSTRAINT FK_9C26C45D6A7847C8 FOREIGN KEY (fiche_metier_id) REFERENCES fiche_metier (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE fiche_metier_edit_app ADD CONSTRAINT FK_9C26C45D570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE form_formation_form_formation ADD CONSTRAINT FK_623F95E1802C7D73 FOREIGN KEY (form_formation_source) REFERENCES form_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE form_formation_form_formation ADD CONSTRAINT FK_623F95E199C92DFC FOREIGN KEY (form_formation_target) REFERENCES form_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_adresse_geo_liste_region ADD CONSTRAINT FK_4CB9F5DE349A5BD2 FOREIGN KEY (geo_adresse_id) REFERENCES geo_adresse (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_adresse_geo_liste_region ADD CONSTRAINT FK_4CB9F5DE19DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_adresse_geo_liste_pays ADD CONSTRAINT FK_64567C17349A5BD2 FOREIGN KEY (geo_adresse_id) REFERENCES geo_adresse (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_adresse_geo_liste_pays ADD CONSTRAINT FK_64567C17A603EFEB FOREIGN KEY (geo_liste_pays_id) REFERENCES geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_departement_geo_liste_region ADD CONSTRAINT FK_3CA4C82964DE230 FOREIGN KEY (geo_departement_id) REFERENCES geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_departement_geo_liste_region ADD CONSTRAINT FK_3CA4C8219DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_liste_region_geo_liste_pays ADD CONSTRAINT FK_CF4A2A019DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_liste_region_geo_liste_pays ADD CONSTRAINT FK_CF4A2A0A603EFEB FOREIGN KEY (geo_liste_pays_id) REFERENCES geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_geo_departement ADD CONSTRAINT FK_76A2A14049867797 FOREIGN KEY (geo_ville_id) REFERENCES geo_ville (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_geo_departement ADD CONSTRAINT FK_76A2A140964DE230 FOREIGN KEY (geo_departement_id) REFERENCES geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_geo_liste_region ADD CONSTRAINT FK_9CC321C549867797 FOREIGN KEY (geo_ville_id) REFERENCES geo_ville (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_geo_liste_region ADD CONSTRAINT FK_9CC321C519DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_bck_geo_departement ADD CONSTRAINT FK_32744ED19F74FEDE FOREIGN KEY (geo_ville_bck_id) REFERENCES geo_ville_bck (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_bck_geo_departement ADD CONSTRAINT FK_32744ED1964DE230 FOREIGN KEY (geo_departement_id) REFERENCES geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_bck_geo_liste_region ADD CONSTRAINT FK_1B8F54F89F74FEDE FOREIGN KEY (geo_ville_bck_id) REFERENCES geo_ville_bck (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_bck_geo_liste_region ADD CONSTRAINT FK_1B8F54F819DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_svg_geo_departement ADD CONSTRAINT FK_A995A02DB52888AA FOREIGN KEY (geo_ville_svg_id) REFERENCES geo_ville_svg (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_svg_geo_departement ADD CONSTRAINT FK_A995A02D964DE230 FOREIGN KEY (geo_departement_id) REFERENCES geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_svg_geo_liste_region ADD CONSTRAINT FK_AF1F0B21B52888AA FOREIGN KEY (geo_ville_svg_id) REFERENCES geo_ville_svg (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE geo_ville_svg_geo_liste_region ADD CONSTRAINT FK_AF1F0B2119DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mb_moteur_recherche_mb_css ADD CONSTRAINT FK_2FA932A65A566197 FOREIGN KEY (mb_moteur_recherche_id) REFERENCES mb_moteur_recherche (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mb_moteur_recherche_mb_css ADD CONSTRAINT FK_2FA932A6DCEEB87E FOREIGN KEY (mb_css_id) REFERENCES mb_css (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_contrat_mc_zone_mc ADD CONSTRAINT FK_1B9AEE2398805D1 FOREIGN KEY (mc_contrat_id) REFERENCES mc_contrat (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_contrat_mc_zone_mc ADD CONSTRAINT FK_1B9AEE23A096AC46 FOREIGN KEY (mc_zone_mc_id) REFERENCES mc_zone_mc (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_localisation_mc_zone_mc ADD CONSTRAINT FK_9FE4002D4EF8859 FOREIGN KEY (mc_localisation_id) REFERENCES mc_localisation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_localisation_mc_zone_mc ADD CONSTRAINT FK_9FE4002DA096AC46 FOREIGN KEY (mc_zone_mc_id) REFERENCES mc_zone_mc (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_localisation_mc_type_localisation ADD CONSTRAINT FK_A17D70B14EF8859 FOREIGN KEY (mc_localisation_id) REFERENCES mc_localisation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_localisation_mc_type_localisation ADD CONSTRAINT FK_A17D70B158CA1A9F FOREIGN KEY (mc_type_localisation_id) REFERENCES mc_type_localisation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_localisation_svg_mc_zone_mc ADD CONSTRAINT FK_6ECB4F622170C8B1 FOREIGN KEY (mc_localisation_svg_id) REFERENCES mc_localisation_svg (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_localisation_svg_mc_zone_mc ADD CONSTRAINT FK_6ECB4F62A096AC46 FOREIGN KEY (mc_zone_mc_id) REFERENCES mc_zone_mc (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_localisation_svg_mc_type_localisation ADD CONSTRAINT FK_2DB8A1DD2170C8B1 FOREIGN KEY (mc_localisation_svg_id) REFERENCES mc_localisation_svg (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_localisation_svg_mc_type_localisation ADD CONSTRAINT FK_2DB8A1DD58CA1A9F FOREIGN KEY (mc_type_localisation_id) REFERENCES mc_type_localisation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_metier_mc_zone_mc ADD CONSTRAINT FK_B5B1AEEE471E68FB FOREIGN KEY (mc_metier_id) REFERENCES mc_metier (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_metier_mc_zone_mc ADD CONSTRAINT FK_B5B1AEEEA096AC46 FOREIGN KEY (mc_zone_mc_id) REFERENCES mc_zone_mc (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_secteur_mc_zone_mc ADD CONSTRAINT FK_96A3D03B8ED547CB FOREIGN KEY (mc_secteur_id) REFERENCES mc_secteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_secteur_mc_zone_mc ADD CONSTRAINT FK_96A3D03BA096AC46 FOREIGN KEY (mc_zone_mc_id) REFERENCES mc_zone_mc (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_zone_mc_edit_app ADD CONSTRAINT FK_4D0062A6A096AC46 FOREIGN KEY (mc_zone_mc_id) REFERENCES mc_zone_mc (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE mc_zone_mc_edit_app ADD CONSTRAINT FK_4D0062A6570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_candidature_user_user ADD CONSTRAINT FK_9CA84FB87516ECFB FOREIGN KEY (off_candidature_id) REFERENCES off_candidature (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_candidature_user_user ADD CONSTRAINT FK_9CA84FB8FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_candidature_off_offre ADD CONSTRAINT FK_64F794507516ECFB FOREIGN KEY (off_candidature_id) REFERENCES off_candidature (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_candidature_off_offre ADD CONSTRAINT FK_64F79450C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_candidature_off_liste_type_suivi ADD CONSTRAINT FK_6A87B2167516ECFB FOREIGN KEY (off_candidature_id) REFERENCES off_candidature (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_candidature_off_liste_type_suivi ADD CONSTRAINT FK_6A87B2161AA99B2F FOREIGN KEY (off_liste_type_suivi_id) REFERENCES off_liste_type_suivi (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_departement_geo_departement ADD CONSTRAINT FK_8733607318B1A9B1 FOREIGN KEY (off_lien_offre_geo_departement_id) REFERENCES off_lien_offre_geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_departement_geo_departement ADD CONSTRAINT FK_87336073964DE230 FOREIGN KEY (geo_departement_id) REFERENCES geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_departement_off_offre ADD CONSTRAINT FK_D05249E018B1A9B1 FOREIGN KEY (off_lien_offre_geo_departement_id) REFERENCES off_lien_offre_geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_departement_off_offre ADD CONSTRAINT FK_D05249E0C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_pays_geo_liste_pays ADD CONSTRAINT FK_3D1F0576E4A79776 FOREIGN KEY (off_lien_offre_geo_liste_pays_id) REFERENCES off_lien_offre_geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_pays_geo_liste_pays ADD CONSTRAINT FK_3D1F0576A603EFEB FOREIGN KEY (geo_liste_pays_id) REFERENCES geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_pays_off_offre ADD CONSTRAINT FK_C4884505E4A79776 FOREIGN KEY (off_lien_offre_geo_liste_pays_id) REFERENCES off_lien_offre_geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_pays_off_offre ADD CONSTRAINT FK_C4884505C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_region_geo_liste_region ADD CONSTRAINT FK_7679E10983EF313C FOREIGN KEY (off_lien_offre_geo_liste_region_id) REFERENCES off_lien_offre_geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_region_geo_liste_region ADD CONSTRAINT FK_7679E10919DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_region_off_offre ADD CONSTRAINT FK_25C441F383EF313C FOREIGN KEY (off_lien_offre_geo_liste_region_id) REFERENCES off_lien_offre_geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_region_off_offre ADD CONSTRAINT FK_25C441F3C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_experience_requise_off_liste_experience_requise ADD CONSTRAINT FK_8CCD44ED24741D47 FOREIGN KEY (off_lien_offre_liste_experience_requise_id) REFERENCES off_lien_offre_liste_experience_requise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_experience_requise_off_liste_experience_requise ADD CONSTRAINT FK_8CCD44EDBC4B4FB9 FOREIGN KEY (off_liste_experience_requise_id) REFERENCES off_liste_experience_requise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_experience_requise_off_offre ADD CONSTRAINT FK_CDAF37D224741D47 FOREIGN KEY (off_lien_offre_liste_experience_requise_id) REFERENCES off_lien_offre_liste_experience_requise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_experience_requise_off_offre ADD CONSTRAINT FK_CDAF37D2C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_secteur_activite_off_offre ADD CONSTRAINT FK_C641F4A57D4B001C FOREIGN KEY (off_lien_offre_liste_secteur_activite_id) REFERENCES off_lien_offre_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_secteur_activite_off_offre ADD CONSTRAINT FK_C641F4A5C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_A8C66CD67D4B001C FOREIGN KEY (off_lien_offre_liste_secteur_activite_id) REFERENCES off_lien_offre_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_A8C66CD6B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_sous_secteur_activite_off_offre ADD CONSTRAINT FK_23554EBE4FE1D01E FOREIGN KEY (off_lien_offre_liste_sous_secteur_activite_id) REFERENCES off_lien_offre_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_sous_secteur_activite_off_offre ADD CONSTRAINT FK_23554EBEC18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_sous_secteur_activite_off_liste_sous_secteur_activite ADD CONSTRAINT FK_3F02BDFD4FE1D01E FOREIGN KEY (off_lien_offre_liste_sous_secteur_activite_id) REFERENCES off_lien_offre_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_sous_secteur_activite_off_liste_sous_secteur_activite ADD CONSTRAINT FK_3F02BDFDCE509CB7 FOREIGN KEY (off_liste_sous_secteur_activite_id) REFERENCES off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_type_formation_off_offre ADD CONSTRAINT FK_F31245FCB99AEDB5 FOREIGN KEY (off_lien_offre_liste_type_formation_id) REFERENCES off_lien_offre_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_type_formation_off_offre ADD CONSTRAINT FK_F31245FCC18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_type_formation_off_liste_type_formation ADD CONSTRAINT FK_86D859C9B99AEDB5 FOREIGN KEY (off_lien_offre_liste_type_formation_id) REFERENCES off_lien_offre_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_type_formation_off_liste_type_formation ADD CONSTRAINT FK_86D859C9A8BBB27F FOREIGN KEY (off_liste_type_formation_id) REFERENCES off_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_sous_secteur_activite ADD CONSTRAINT FK_5B4F9F2D16B9E322 FOREIGN KEY (off_lien_sous_secteur_activite_secteur_activite_id) REFERENCES off_lien_sous_secteur_activite_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_sous_secteur_activite ADD CONSTRAINT FK_5B4F9F2DCE509CB7 FOREIGN KEY (off_liste_sous_secteur_activite_id) REFERENCES off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_864B0D3C16B9E322 FOREIGN KEY (off_lien_sous_secteur_activite_secteur_activite_id) REFERENCES off_lien_sous_secteur_activite_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_864B0D3CB78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_liste_secteur_activite_off_liste_type_secteur_activite ADD CONSTRAINT FK_7AA0C6F9B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_liste_secteur_activite_off_liste_type_secteur_activite ADD CONSTRAINT FK_7AA0C6F9E5A0BF1C FOREIGN KEY (off_liste_type_secteur_activite_id) REFERENCES off_liste_type_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_27F44DBCCE509CB7 FOREIGN KEY (off_liste_sous_secteur_activite_id) REFERENCES off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_off_liste_secteur_activite ADD CONSTRAINT FK_27F44DBCB78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_off_liste_type_secteur_activite ADD CONSTRAINT FK_1BF49E88CE509CB7 FOREIGN KEY (off_liste_sous_secteur_activite_id) REFERENCES off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_off_liste_type_secteur_activite ADD CONSTRAINT FK_1BF49E88E5A0BF1C FOREIGN KEY (off_liste_type_secteur_activite_id) REFERENCES off_liste_type_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_edit_app ADD CONSTRAINT FK_2380476CCE509CB7 FOREIGN KEY (off_liste_sous_secteur_activite_id) REFERENCES off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_edit_app ADD CONSTRAINT FK_2380476C570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_liste_type_formation_com_liste_type_diffusion ADD CONSTRAINT FK_65C134DAA8BBB27F FOREIGN KEY (off_liste_type_formation_id) REFERENCES off_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_liste_type_formation_com_liste_type_diffusion ADD CONSTRAINT FK_65C134DA35624BB3 FOREIGN KEY (com_liste_type_diffusion_id) REFERENCES com_liste_type_diffusion (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_meta_moteur_entr_profil_recruteur ADD CONSTRAINT FK_43C04A9249B09D89 FOREIGN KEY (off_meta_moteur_id) REFERENCES off_meta_moteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_meta_moteur_entr_profil_recruteur ADD CONSTRAINT FK_43C04A92BC1D969B FOREIGN KEY (entr_profil_recruteur_id) REFERENCES entr_profil_recruteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_offre_com_offre_commerciale ADD CONSTRAINT FK_6ECB9D06C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_offre_com_offre_commerciale ADD CONSTRAINT FK_6ECB9D0623CD91B5 FOREIGN KEY (com_offre_commerciale_id) REFERENCES com_offre_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_offre_off_fourchette_remuneration ADD CONSTRAINT FK_D3296C39C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_offre_off_fourchette_remuneration ADD CONSTRAINT FK_D3296C39AF4A7002 FOREIGN KEY (off_fourchette_remuneration_id) REFERENCES off_fourchette_remuneration (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_offre_com_liste_type_offre_commerciale ADD CONSTRAINT FK_E49FCAA3C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_offre_com_liste_type_offre_commerciale ADD CONSTRAINT FK_E49FCAA333987EE4 FOREIGN KEY (com_liste_type_offre_commerciale_id) REFERENCES com_liste_type_offre_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_off_offre ADD CONSTRAINT FK_2E6A4EF45FF2EA66 FOREIGN KEY (off_reponse_offre_non_identifie_id) REFERENCES off_reponse_offre_non_identifie (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_off_offre ADD CONSTRAINT FK_2E6A4EF4C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_ec_ecole ADD CONSTRAINT FK_E60E29695FF2EA66 FOREIGN KEY (off_reponse_offre_non_identifie_id) REFERENCES off_reponse_offre_non_identifie (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_ec_ecole ADD CONSTRAINT FK_E60E2969CFF27E35 FOREIGN KEY (ec_ecole_id) REFERENCES ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_ec_session_formation ADD CONSTRAINT FK_9AD70FBE5FF2EA66 FOREIGN KEY (off_reponse_offre_non_identifie_id) REFERENCES off_reponse_offre_non_identifie (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_ec_session_formation ADD CONSTRAINT FK_9AD70FBEF551B8FE FOREIGN KEY (ec_session_formation_id) REFERENCES ec_session_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_off_liste_type_formation ADD CONSTRAINT FK_248D35485FF2EA66 FOREIGN KEY (off_reponse_offre_non_identifie_id) REFERENCES off_reponse_offre_non_identifie (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_off_liste_type_formation ADD CONSTRAINT FK_248D3548A8BBB27F FOREIGN KEY (off_liste_type_formation_id) REFERENCES off_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_sauvegarde_offre_off_offre ADD CONSTRAINT FK_2D0EFD810895F1A FOREIGN KEY (off_sauvegarde_offre_id) REFERENCES off_sauvegarde_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_sauvegarde_offre_off_offre ADD CONSTRAINT FK_2D0EFD8C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_sauvegarde_offre_user_user ADD CONSTRAINT FK_FA8F343010895F1A FOREIGN KEY (off_sauvegarde_offre_id) REFERENCES off_sauvegarde_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_sauvegarde_offre_user_user ADD CONSTRAINT FK_FA8F3430FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_statistique_off_offre ADD CONSTRAINT FK_80311F51B5ACED1B FOREIGN KEY (off_statistique_id) REFERENCES off_statistique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_statistique_off_offre ADD CONSTRAINT FK_80311F51C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_statistique_entr_entreprise ADD CONSTRAINT FK_3913F4FDB5ACED1B FOREIGN KEY (off_statistique_id) REFERENCES off_statistique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_statistique_entr_entreprise ADD CONSTRAINT FK_3913F4FD2923CBC2 FOREIGN KEY (entr_entreprise_id) REFERENCES entr_entreprise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_statistique_old_save_entr_entreprise ADD CONSTRAINT FK_4DD37E8A91EC602E FOREIGN KEY (off_statistique_old_save_id) REFERENCES off_statistique_old_save (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE off_statistique_old_save_entr_entreprise ADD CONSTRAINT FK_4DD37E8A2923CBC2 FOREIGN KEY (entr_entreprise_id) REFERENCES entr_entreprise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_off_offre_promo_promotion ADD CONSTRAINT FK_DB5FC8CF7E590EEB FOREIGN KEY (promo_lien_promotion_off_offre_id) REFERENCES promo_lien_promotion_off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_off_offre_promo_promotion ADD CONSTRAINT FK_DB5FC8CF8B9B3683 FOREIGN KEY (promo_promotion_id) REFERENCES promo_promotion (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_off_offre_off_offre ADD CONSTRAINT FK_5507FA8E7E590EEB FOREIGN KEY (promo_lien_promotion_off_offre_id) REFERENCES promo_lien_promotion_off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_off_offre_off_offre ADD CONSTRAINT FK_5507FA8EC18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_offre_commerciale_promo_promotion ADD CONSTRAINT FK_BFDC6EC17A0303B9 FOREIGN KEY (promo_lien_promotion_offre_commerciale_id) REFERENCES promo_lien_promotion_offre_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_offre_commerciale_promo_promotion ADD CONSTRAINT FK_BFDC6EC18B9B3683 FOREIGN KEY (promo_promotion_id) REFERENCES promo_promotion (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_offre_commerciale_com_offre_commerciale ADD CONSTRAINT FK_A8E383947A0303B9 FOREIGN KEY (promo_lien_promotion_offre_commerciale_id) REFERENCES promo_lien_promotion_offre_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_offre_commerciale_com_offre_commerciale ADD CONSTRAINT FK_A8E3839423CD91B5 FOREIGN KEY (com_offre_commerciale_id) REFERENCES com_offre_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_user_user_promo_promotion ADD CONSTRAINT FK_E218701140B0DFBA FOREIGN KEY (promo_lien_promotion_user_user_id) REFERENCES promo_lien_promotion_user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_user_user_promo_promotion ADD CONSTRAINT FK_E21870118B9B3683 FOREIGN KEY (promo_promotion_id) REFERENCES promo_promotion (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_user_user_user_user ADD CONSTRAINT FK_C4A826D340B0DFBA FOREIGN KEY (promo_lien_promotion_user_user_id) REFERENCES promo_lien_promotion_user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE promo_lien_promotion_user_user_user_user ADD CONSTRAINT FK_C4A826D3FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ref_lien_multi_edit_app ADD CONSTRAINT FK_62CE700A1024C3A3 FOREIGN KEY (ref_lien_multi_id) REFERENCES ref_lien_multi (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ref_lien_multi_edit_app ADD CONSTRAINT FK_62CE700A570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE save_mail_candi_non_log_edit_app ADD CONSTRAINT FK_54A4D0FD5EB2D766 FOREIGN KEY (save_mail_candi_non_log_id) REFERENCES save_mail_candi_non_log (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE save_mail_candi_non_log_edit_app ADD CONSTRAINT FK_54A4D0FD570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE save_mail_candi_non_log_off_liste_type_contrat ADD CONSTRAINT FK_5F9281345EB2D766 FOREIGN KEY (save_mail_candi_non_log_id) REFERENCES save_mail_candi_non_log (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE save_mail_candi_non_log_off_liste_type_contrat ADD CONSTRAINT FK_5F928134B4AF2FE3 FOREIGN KEY (off_liste_type_contrat_id) REFERENCES off_liste_type_contrat (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE script_secteurs_stats_user_user ADD CONSTRAINT FK_EFAF919675E66279 FOREIGN KEY (script_secteurs_stats_id) REFERENCES script_secteurs_stats (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE script_secteurs_stats_user_user ADD CONSTRAINT FK_EFAF9196FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_edit_referencement_edit_app ADD CONSTRAINT FK_71978C6D79E8E6D8 FOREIGN KEY (seo_edit_referencement_id) REFERENCES seo_edit_referencement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_edit_referencement_edit_app ADD CONSTRAINT FK_71978C6D570A9ED9 FOREIGN KEY (edit_app_id) REFERENCES edit_app (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_attente ADD CONSTRAINT FK_51C48026D8DDFAD1 FOREIGN KEY (seo_lien_mot_cle_attente_mot_cle_racine_id) REFERENCES seo_lien_mot_cle_attente_mot_cle_racine (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_attente ADD CONSTRAINT FK_51C480265C9ABB4C FOREIGN KEY (seo_mot_cle_attente_id) REFERENCES seo_mot_cle_attente (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_racine ADD CONSTRAINT FK_96CCB205D8DDFAD1 FOREIGN KEY (seo_lien_mot_cle_attente_mot_cle_racine_id) REFERENCES seo_lien_mot_cle_attente_mot_cle_racine (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_racine ADD CONSTRAINT FK_96CCB20564E2A64D FOREIGN KEY (seo_mot_cle_racine_id) REFERENCES seo_mot_cle_racine (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle ADD CONSTRAINT FK_47D67C8B8C3AE25 FOREIGN KEY (seo_lien_mot_cle_mot_cle_racine_id) REFERENCES seo_lien_mot_cle_mot_cle_racine (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle ADD CONSTRAINT FK_47D67C8F8F767A5 FOREIGN KEY (seo_mot_cle_id) REFERENCES seo_mot_cle (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle_racine ADD CONSTRAINT FK_6659928CB8C3AE25 FOREIGN KEY (seo_lien_mot_cle_mot_cle_racine_id) REFERENCES seo_lien_mot_cle_mot_cle_racine (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle_racine ADD CONSTRAINT FK_6659928C64E2A64D FOREIGN KEY (seo_mot_cle_racine_id) REFERENCES seo_mot_cle_racine (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_racine_offre_seo_mot_cle_racine ADD CONSTRAINT FK_F77287D59E2269A3 FOREIGN KEY (seo_lien_mot_cle_racine_offre_id) REFERENCES seo_lien_mot_cle_racine_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_racine_offre_seo_mot_cle_racine ADD CONSTRAINT FK_F77287D564E2A64D FOREIGN KEY (seo_mot_cle_racine_id) REFERENCES seo_mot_cle_racine (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_racine_offre_off_offre ADD CONSTRAINT FK_6F6F6E949E2269A3 FOREIGN KEY (seo_lien_mot_cle_racine_offre_id) REFERENCES seo_lien_mot_cle_racine_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_racine_offre_off_offre ADD CONSTRAINT FK_6F6F6E94C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_type ADD CONSTRAINT FK_AB416ADBF8F767A5 FOREIGN KEY (seo_mot_cle_id) REFERENCES seo_mot_cle (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_type ADD CONSTRAINT FK_AB416ADBA1F29F8E FOREIGN KEY (seo_liste_type_id) REFERENCES seo_liste_type (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_theme ADD CONSTRAINT FK_C4E1EB05F8F767A5 FOREIGN KEY (seo_mot_cle_id) REFERENCES seo_mot_cle (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_theme ADD CONSTRAINT FK_C4E1EB053A60A64D FOREIGN KEY (seo_liste_theme_id) REFERENCES seo_liste_theme (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_secteur ADD CONSTRAINT FK_3C5D44EDF8F767A5 FOREIGN KEY (seo_mot_cle_id) REFERENCES seo_mot_cle (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_secteur ADD CONSTRAINT FK_3C5D44EDC6B6AB5B FOREIGN KEY (seo_liste_secteur_id) REFERENCES seo_liste_secteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_combinaisons ADD CONSTRAINT FK_B5FE012AF8F767A5 FOREIGN KEY (seo_mot_cle_id) REFERENCES seo_mot_cle (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_combinaisons ADD CONSTRAINT FK_B5FE012A7768CF7C FOREIGN KEY (seo_combinaisons_id) REFERENCES seo_combinaisons (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_type ADD CONSTRAINT FK_E66975FE5C9ABB4C FOREIGN KEY (seo_mot_cle_attente_id) REFERENCES seo_mot_cle_attente (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_type ADD CONSTRAINT FK_E66975FEA1F29F8E FOREIGN KEY (seo_liste_type_id) REFERENCES seo_liste_type (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_theme ADD CONSTRAINT FK_8FA8175D5C9ABB4C FOREIGN KEY (seo_mot_cle_attente_id) REFERENCES seo_mot_cle_attente (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_theme ADD CONSTRAINT FK_8FA8175D3A60A64D FOREIGN KEY (seo_liste_theme_id) REFERENCES seo_liste_theme (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_secteur ADD CONSTRAINT FK_FA3466CF5C9ABB4C FOREIGN KEY (seo_mot_cle_attente_id) REFERENCES seo_mot_cle_attente (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_secteur ADD CONSTRAINT FK_FA3466CFC6B6AB5B FOREIGN KEY (seo_liste_secteur_id) REFERENCES seo_liste_secteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_type ADD CONSTRAINT FK_68D917FB64E2A64D FOREIGN KEY (seo_mot_cle_racine_id) REFERENCES seo_mot_cle_racine (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_type ADD CONSTRAINT FK_68D917FBA1F29F8E FOREIGN KEY (seo_liste_type_id) REFERENCES seo_liste_type (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_theme ADD CONSTRAINT FK_FF4C53B064E2A64D FOREIGN KEY (seo_mot_cle_racine_id) REFERENCES seo_mot_cle_racine (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_theme ADD CONSTRAINT FK_FF4C53B03A60A64D FOREIGN KEY (seo_liste_theme_id) REFERENCES seo_liste_theme (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_secteur ADD CONSTRAINT FK_60551E0364E2A64D FOREIGN KEY (seo_mot_cle_racine_id) REFERENCES seo_mot_cle_racine (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_secteur ADD CONSTRAINT FK_60551E03C6B6AB5B FOREIGN KEY (seo_liste_secteur_id) REFERENCES seo_liste_secteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_offre_veille_user_user ADD CONSTRAINT FK_22A1FB981BCD8316 FOREIGN KEY (user_offre_veille_id) REFERENCES user_offre_veille (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_offre_veille_user_user ADD CONSTRAINT FK_22A1FB98FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_user ADD CONSTRAINT FK_618B9C3B4C494AD FOREIGN KEY (user_situation_id) REFERENCES user_situation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_user ADD CONSTRAINT FK_618B9C3FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_situation_activite ADD CONSTRAINT FK_F7A2F118B4C494AD FOREIGN KEY (user_situation_id) REFERENCES user_situation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_situation_activite ADD CONSTRAINT FK_F7A2F118E23C140F FOREIGN KEY (user_situation_activite_id) REFERENCES user_situation_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_situation_statut ADD CONSTRAINT FK_DE067EF2B4C494AD FOREIGN KEY (user_situation_id) REFERENCES user_situation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_situation_statut ADD CONSTRAINT FK_DE067EF272A1B47 FOREIGN KEY (user_situation_statut_id) REFERENCES user_situation_statut (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_off_liste_type_formation ADD CONSTRAINT FK_FF6415CEB4C494AD FOREIGN KEY (user_situation_id) REFERENCES user_situation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_off_liste_type_formation ADD CONSTRAINT FK_FF6415CEA8BBB27F FOREIGN KEY (off_liste_type_formation_id) REFERENCES off_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_situation_experience ADD CONSTRAINT FK_61859768B4C494AD FOREIGN KEY (user_situation_id) REFERENCES user_situation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_situation_experience ADD CONSTRAINT FK_61859768C5162B4D FOREIGN KEY (user_situation_experience_id) REFERENCES user_situation_experience (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_disponibilite ADD CONSTRAINT FK_286CACFAB4C494AD FOREIGN KEY (user_situation_id) REFERENCES user_situation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_disponibilite ADD CONSTRAINT FK_286CACFA5688264E FOREIGN KEY (user_disponibilite_id) REFERENCES user_disponibilite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_situation_mobilite ADD CONSTRAINT FK_8D86D85EB4C494AD FOREIGN KEY (user_situation_id) REFERENCES user_situation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_user_situation_mobilite ADD CONSTRAINT FK_8D86D85EEE6E9E3D FOREIGN KEY (user_situation_mobilite_id) REFERENCES user_situation_mobilite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_ec_ecole ADD CONSTRAINT FK_AB34018CB4C494AD FOREIGN KEY (user_situation_id) REFERENCES user_situation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_ec_ecole ADD CONSTRAINT FK_AB34018CCFF27E35 FOREIGN KEY (ec_ecole_id) REFERENCES ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_ec_session_formation ADD CONSTRAINT FK_26B2E375B4C494AD FOREIGN KEY (user_situation_id) REFERENCES user_situation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_ec_session_formation ADD CONSTRAINT FK_26B2E375F551B8FE FOREIGN KEY (ec_session_formation_id) REFERENCES ec_session_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_off_liste_secteur_activite ADD CONSTRAINT FK_506B56B4C494AD FOREIGN KEY (user_situation_id) REFERENCES user_situation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_off_liste_secteur_activite ADD CONSTRAINT FK_506B56B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_experience_off_liste_experience_requise ADD CONSTRAINT FK_D9B522F6C5162B4D FOREIGN KEY (user_situation_experience_id) REFERENCES user_situation_experience (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_situation_experience_off_liste_experience_requise ADD CONSTRAINT FK_D9B522F6BC4B4FB9 FOREIGN KEY (off_liste_experience_requise_id) REFERENCES off_liste_experience_requise (id) ON DELETE CASCADE');
        $this->addSql('DROP TABLE com_lien_offre_commerciale_off_liste_type_contrat');
        $this->addSql('DROP TABLE cv_lien_experience_pro_off_liste_secteur_activite');
        $this->addSql('ALTER TABLE annuaire_interim CHANGE code_p?ostal code_postal VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE balises_html_categories_page ADD CONSTRAINT FK_D0A0CF22B995E37A FOREIGN KEY (balises_html_id) REFERENCES balises_html (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE balises_html_categories_page ADD CONSTRAINT FK_D0A0CF22244CB244 FOREIGN KEY (categories_page_id) REFERENCES categories_page (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_com_liste_diffusion ADD CONSTRAINT FK_2E12D7AD7E686F8A FOREIGN KEY (com_lien_liste_diffusion_ec_ecole_id) REFERENCES com_lien_liste_diffusion_ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_com_liste_diffusion ADD CONSTRAINT FK_2E12D7AD714A20DF FOREIGN KEY (com_liste_diffusion_id) REFERENCES com_liste_diffusion (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_ec_ecole ADD CONSTRAINT FK_15FCE0657E686F8A FOREIGN KEY (com_lien_liste_diffusion_ec_ecole_id) REFERENCES com_lien_liste_diffusion_ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_ec_ecole ADD CONSTRAINT FK_15FCE065CFF27E35 FOREIGN KEY (ec_ecole_id) REFERENCES ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_ec_session_formation ADD CONSTRAINT FK_FEFBF3317E686F8A FOREIGN KEY (com_lien_liste_diffusion_ec_ecole_id) REFERENCES com_lien_liste_diffusion_ec_ecole (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_ec_session_formation ADD CONSTRAINT FK_FEFBF331F551B8FE FOREIGN KEY (ec_session_formation_id) REFERENCES ec_session_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lien_off_com_type_contrat_off_liste_type_contrat ADD CONSTRAINT FK_686EB500AB55031C FOREIGN KEY (com_lien_off_com_type_contrat_id) REFERENCES com_lien_off_com_type_contrat (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lien_off_com_type_contrat_off_liste_type_contrat ADD CONSTRAINT FK_686EB500B4AF2FE3 FOREIGN KEY (off_liste_type_contrat_id) REFERENCES off_liste_type_contrat (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lien_off_com_type_contrat_com_offre_commerciale ADD CONSTRAINT FK_FD135FEAAB55031C FOREIGN KEY (com_lien_off_com_type_contrat_id) REFERENCES com_lien_off_com_type_contrat (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lien_off_com_type_contrat_com_offre_commerciale ADD CONSTRAINT FK_FD135FEA23CD91B5 FOREIGN KEY (com_offre_commerciale_id) REFERENCES com_offre_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_liste_diffusion_off_offre ADD CONSTRAINT FK_4BAEE28D714A20DF FOREIGN KEY (com_liste_diffusion_id) REFERENCES com_liste_diffusion (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_liste_diffusion_off_offre ADD CONSTRAINT FK_4BAEE28DC18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_liste_diffusion_off_liste_type_formation ADD CONSTRAINT FK_5DEE3BAE714A20DF FOREIGN KEY (com_liste_diffusion_id) REFERENCES com_liste_diffusion (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_liste_diffusion_off_liste_type_formation ADD CONSTRAINT FK_5DEE3BAEA8BBB27F FOREIGN KEY (off_liste_type_formation_id) REFERENCES off_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lot_souscription_user_user ADD CONSTRAINT FK_7B3B67504BC5E32 FOREIGN KEY (com_lot_souscription_id) REFERENCES com_lot_souscription (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_lot_souscription_user_user ADD CONSTRAINT FK_7B3B6750FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_offre_commerciale_com_liste_type_offre_commerciale ADD CONSTRAINT FK_52786F4923CD91B5 FOREIGN KEY (com_offre_commerciale_id) REFERENCES com_offre_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_offre_commerciale_com_liste_type_offre_commerciale ADD CONSTRAINT FK_52786F4933987EE4 FOREIGN KEY (com_liste_type_offre_commerciale_id) REFERENCES com_liste_type_offre_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_souscription_commerciale_user_user ADD CONSTRAINT FK_9536C10B89ED7EC0 FOREIGN KEY (com_souscription_commerciale_id) REFERENCES com_souscription_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_souscription_commerciale_user_user ADD CONSTRAINT FK_9536C10BFF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_offre_commerciale ADD CONSTRAINT FK_16B36AB389ED7EC0 FOREIGN KEY (com_souscription_commerciale_id) REFERENCES com_souscription_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_offre_commerciale ADD CONSTRAINT FK_16B36AB323CD91B5 FOREIGN KEY (com_offre_commerciale_id) REFERENCES com_offre_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_type_paiement ADD CONSTRAINT FK_B28F03A289ED7EC0 FOREIGN KEY (com_souscription_commerciale_id) REFERENCES com_souscription_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_type_paiement ADD CONSTRAINT FK_B28F03A2B2703AF3 FOREIGN KEY (com_type_paiement_id) REFERENCES com_type_paiement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_lot_souscription ADD CONSTRAINT FK_63ADFB0B89ED7EC0 FOREIGN KEY (com_souscription_commerciale_id) REFERENCES com_souscription_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_lot_souscription ADD CONSTRAINT FK_63ADFB0B4BC5E32 FOREIGN KEY (com_lot_souscription_id) REFERENCES com_lot_souscription (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_souscription_commerciale_promo_promotion ADD CONSTRAINT FK_B660278C89ED7EC0 FOREIGN KEY (com_souscription_commerciale_id) REFERENCES com_souscription_commerciale (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE com_souscription_commerciale_promo_promotion ADD CONSTRAINT FK_B660278C8B9B3683 FOREIGN KEY (promo_promotion_id) REFERENCES promo_promotion (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_attachement_mail_edit_article ADD CONSTRAINT FK_2023388C2B5F8DB6 FOREIGN KEY (common_attachement_mail_id) REFERENCES common_attachement_mail (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_attachement_mail_edit_article ADD CONSTRAINT FK_2023388CE7695588 FOREIGN KEY (edit_article_id) REFERENCES edit_article (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_message_common_type_message ADD CONSTRAINT FK_74B2FF26687A3DD7 FOREIGN KEY (common_message_id) REFERENCES common_message (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_message_common_type_message ADD CONSTRAINT FK_74B2FF26C5221C05 FOREIGN KEY (common_type_message_id) REFERENCES common_type_message (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_statistique_cv_cv ADD CONSTRAINT FK_4D4808A593C9F739 FOREIGN KEY (common_statistique_id) REFERENCES common_statistique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_statistique_cv_cv ADD CONSTRAINT FK_4D4808A540E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_statistique_off_offre ADD CONSTRAINT FK_AED51EC693C9F739 FOREIGN KEY (common_statistique_id) REFERENCES common_statistique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_statistique_off_offre ADD CONSTRAINT FK_AED51EC6C18A1CCE FOREIGN KEY (off_offre_id) REFERENCES off_offre (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_statistique_ec_session_formation ADD CONSTRAINT FK_9723F1BD93C9F739 FOREIGN KEY (common_statistique_id) REFERENCES common_statistique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_statistique_ec_session_formation ADD CONSTRAINT FK_9723F1BDF551B8FE FOREIGN KEY (ec_session_formation_id) REFERENCES ec_session_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_statistique_ec_organisme_formation ADD CONSTRAINT FK_4195F6BA93C9F739 FOREIGN KEY (common_statistique_id) REFERENCES common_statistique (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE common_statistique_ec_organisme_formation ADD CONSTRAINT FK_4195F6BAC540A215 FOREIGN KEY (ec_organisme_formation_id) REFERENCES ec_organisme_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor123_contrat_off_liste_type_contrat ADD CONSTRAINT FK_B7A5784ED245226E FOREIGN KEY (cor123_contrat_id) REFERENCES cor123_contrat (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor123_contrat_off_liste_type_contrat ADD CONSTRAINT FK_B7A5784EB4AF2FE3 FOREIGN KEY (off_liste_type_contrat_id) REFERENCES off_liste_type_contrat (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_easyoffre_secteur_off_liste_secteur_activite ADD CONSTRAINT FK_80AF6B22A40B1A86 FOREIGN KEY (cor_easyoffre_secteur_id) REFERENCES cor_easyoffre_secteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_easyoffre_secteur_off_liste_secteur_activite ADD CONSTRAINT FK_80AF6B22B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_contrat_off_liste_type_contrat ADD CONSTRAINT FK_41E313B745D090ED FOREIGN KEY (cor_keljob_contrat_id) REFERENCES cor_keljob_contrat (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_contrat_off_liste_type_contrat ADD CONSTRAINT FK_41E313B7B4AF2FE3 FOREIGN KEY (off_liste_type_contrat_id) REFERENCES off_liste_type_contrat (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_experience_off_liste_experience_requise ADD CONSTRAINT FK_1A20B21FF30627FE FOREIGN KEY (cor_keljob_experience_id) REFERENCES cor_keljob_experience (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_experience_off_liste_experience_requise ADD CONSTRAINT FK_1A20B21FBC4B4FB9 FOREIGN KEY (off_liste_experience_requise_id) REFERENCES off_liste_experience_requise (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_lieu_geo_departement ADD CONSTRAINT FK_11BC02663B5DE6CA FOREIGN KEY (cor_keljob_lieu_id) REFERENCES cor_keljob_lieu (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_lieu_geo_departement ADD CONSTRAINT FK_11BC0266964DE230 FOREIGN KEY (geo_departement_id) REFERENCES geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_lieu_geo_liste_region ADD CONSTRAINT FK_4EA9BA9B3B5DE6CA FOREIGN KEY (cor_keljob_lieu_id) REFERENCES cor_keljob_lieu (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_lieu_geo_liste_region ADD CONSTRAINT FK_4EA9BA9B19DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_salaire_off_fourchette_remuneration ADD CONSTRAINT FK_9A10DC107B8B5173 FOREIGN KEY (cor_keljob_salaire_id) REFERENCES cor_keljob_salaire (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_salaire_off_fourchette_remuneration ADD CONSTRAINT FK_9A10DC10AF4A7002 FOREIGN KEY (off_fourchette_remuneration_id) REFERENCES off_fourchette_remuneration (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_secteur_off_liste_secteur_activite ADD CONSTRAINT FK_46B21C28DD2F7 FOREIGN KEY (cor_keljob_secteur_id) REFERENCES cor_keljob_secteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_keljob_secteur_off_liste_secteur_activite ADD CONSTRAINT FK_46B21B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_plusrh_secteur_off_liste_secteur_activite ADD CONSTRAINT FK_7B45C2245EC53487 FOREIGN KEY (cor_plusrh_secteur_id) REFERENCES cor_plusrh_secteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_plusrh_secteur_off_liste_secteur_activite ADD CONSTRAINT FK_7B45C224B78A629C FOREIGN KEY (off_liste_secteur_activite_id) REFERENCES off_liste_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_plusrh_secteur_off_liste_sous_secteur_activite ADD CONSTRAINT FK_8E7D0FC45EC53487 FOREIGN KEY (cor_plusrh_secteur_id) REFERENCES cor_plusrh_secteur (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cor_plusrh_secteur_off_liste_sous_secteur_activite ADD CONSTRAINT FK_8E7D0FC4CE509CB7 FOREIGN KEY (off_liste_sous_secteur_activite_id) REFERENCES off_liste_sous_secteur_activite (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_centre_interet_cv_cv ADD CONSTRAINT FK_9FBD0F24946F3433 FOREIGN KEY (cv_centre_interet_id) REFERENCES cv_centre_interet (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_centre_interet_cv_cv ADD CONSTRAINT FK_9FBD0F2440E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_cv_user_user ADD CONSTRAINT FK_A898436940E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_cv_user_user ADD CONSTRAINT FK_A8984369FF63CD9F FOREIGN KEY (user_user_id) REFERENCES user_user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_cv_off_fourchette_remuneration ADD CONSTRAINT FK_4015982A40E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_cv_off_fourchette_remuneration ADD CONSTRAINT FK_4015982AAF4A7002 FOREIGN KEY (off_fourchette_remuneration_id) REFERENCES off_fourchette_remuneration (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_experience_pro_cv_cv ADD CONSTRAINT FK_6ABEC46CF6D4F645 FOREIGN KEY (cv_experience_pro_id) REFERENCES cv_experience_pro (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_experience_pro_cv_cv ADD CONSTRAINT FK_6ABEC46C40E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_formation_cv_cv ADD CONSTRAINT FK_888F11E89112D88B FOREIGN KEY (cv_formation_id) REFERENCES cv_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_formation_cv_cv ADD CONSTRAINT FK_888F11E840E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_formation_off_liste_type_formation ADD CONSTRAINT FK_E402B0D59112D88B FOREIGN KEY (cv_formation_id) REFERENCES cv_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_formation_off_liste_type_formation ADD CONSTRAINT FK_E402B0D5A8BBB27F FOREIGN KEY (off_liste_type_formation_id) REFERENCES off_liste_type_formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_langue_cv_cv ADD CONSTRAINT FK_339FB7BCB6359381 FOREIGN KEY (cv_langue_id) REFERENCES cv_langue (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_langue_cv_cv ADD CONSTRAINT FK_339FB7BC40E0C9FD FOREIGN KEY (cv_cv_id) REFERENCES cv_cv (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_langue_cv_liste_choix_langue ADD CONSTRAINT FK_60575B56B6359381 FOREIGN KEY (cv_langue_id) REFERENCES cv_langue (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_langue_cv_liste_choix_langue ADD CONSTRAINT FK_60575B569CBED8F8 FOREIGN KEY (cv_liste_choix_langue_id) REFERENCES cv_liste_choix_langue (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_langue_cv_niveau_langue ADD CONSTRAINT FK_C0B168EB6359381 FOREIGN KEY (cv_langue_id) REFERENCES cv_langue (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_langue_cv_niveau_langue ADD CONSTRAINT FK_C0B168ECD942705 FOREIGN KEY (cv_niveau_langue_id) REFERENCES cv_niveau_langue (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_departement_geo_departement ADD CONSTRAINT FK_B7CDDF28992E7864 FOREIGN KEY (cv_lien_experience_pro_geo_departement_id) REFERENCES cv_lien_experience_pro_geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_departement_geo_departement ADD CONSTRAINT FK_B7CDDF28964DE230 FOREIGN KEY (geo_departement_id) REFERENCES geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_departement_cv_experience_pro ADD CONSTRAINT FK_83640919992E7864 FOREIGN KEY (cv_lien_experience_pro_geo_departement_id) REFERENCES cv_lien_experience_pro_geo_departement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_departement_cv_experience_pro ADD CONSTRAINT FK_83640919F6D4F645 FOREIGN KEY (cv_experience_pro_id) REFERENCES cv_experience_pro (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_pays_geo_liste_pays ADD CONSTRAINT FK_7979AD53C5608FA2 FOREIGN KEY (cv_lien_experience_pro_geo_liste_pays_id) REFERENCES cv_lien_experience_pro_geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_pays_geo_liste_pays ADD CONSTRAINT FK_7979AD53A603EFEB FOREIGN KEY (geo_liste_pays_id) REFERENCES geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_pays_cv_experience_pro ADD CONSTRAINT FK_57292ADCC5608FA2 FOREIGN KEY (cv_lien_experience_pro_geo_liste_pays_id) REFERENCES cv_lien_experience_pro_geo_liste_pays (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_pays_cv_experience_pro ADD CONSTRAINT FK_57292ADCF6D4F645 FOREIGN KEY (cv_experience_pro_id) REFERENCES cv_experience_pro (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_region_geo_liste_region ADD CONSTRAINT FK_74E8FB7575D788B6 FOREIGN KEY (cv_lien_experience_pro_geo_liste_region_id) REFERENCES cv_lien_experience_pro_geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_region_geo_liste_region ADD CONSTRAINT FK_74E8FB7519DE7EC1 FOREIGN KEY (geo_liste_region_id) REFERENCES geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_region_cv_experience_pro ADD CONSTRAINT FK_5274EDB475D788B6 FOREIGN KEY (cv_lien_experience_pro_geo_liste_region_id) REFERENCES cv_lien_experience_pro_geo_liste_region (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_region_cv_experience_pro ADD CONSTRAINT FK_5274EDB4F6D4F645 FOREIGN KEY (cv_experience_pro_id) REFERENCES cv_experience_pro (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_lien_situation_mobilite_geo_liste_region ADD user_situation_mobilite_id_situation_mobilite INT DEFAULT NULL, ADD geo_liste_region_id_region INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE cv_lien_exp_pro_off_liste_sect_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_2BA542C95E1A0290');
        $this->addSql('ALTER TABLE cv_lien_exp_pro_off_liste_sect_activite_cv_experience_pro DROP FOREIGN KEY FK_9A6799955E1A0290');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_departement_geo_departement DROP FOREIGN KEY FK_21872B647E037882');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_departement_cv_formation DROP FOREIGN KEY FK_8A2201AD7E037882');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_pays_geo_liste_pays DROP FOREIGN KEY FK_4C528D3B504D6EAC');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_pays_cv_formation DROP FOREIGN KEY FK_A7F99B2B504D6EAC');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_region_geo_liste_region DROP FOREIGN KEY FK_92F45B333C59E2FB');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_region_cv_formation DROP FOREIGN KEY FK_C8B7EC013C59E2FB');
        $this->addSql('ALTER TABLE cv_lienformation_off_liste_secteur_activite_cv_formation DROP FOREIGN KEY FK_AF1F34838E8D19DF');
        $this->addSql('ALTER TABLE cv_lienformation_off_liste_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_9737B8928E8D19DF');
        $this->addSql('ALTER TABLE cv_log_cv_cv DROP FOREIGN KEY FK_7A54423167E054A3');
        $this->addSql('ALTER TABLE cv_log_user_user DROP FOREIGN KEY FK_476F439967E054A3');
        $this->addSql('ALTER TABLE cv_recruteur_cv_commentaire_cv_cv DROP FOREIGN KEY FK_E2677317DF49666');
        $this->addSql('ALTER TABLE cv_recruteur_cv_commentaire_user_user DROP FOREIGN KEY FK_2FAECAB37DF49666');
        $this->addSql('ALTER TABLE cv_recruteur_cv_dossier_user_user DROP FOREIGN KEY FK_744301F035A241C');
        $this->addSql('ALTER TABLE cv_recruteur_cv_sauvegarde_cv_recruteur_cv_dossier DROP FOREIGN KEY FK_B038942835A241C');
        $this->addSql('ALTER TABLE cv_recruteur_cv_note_cv_cv DROP FOREIGN KEY FK_C1E40BBDAF01F264');
        $this->addSql('ALTER TABLE cv_recruteur_cv_note_user_user DROP FOREIGN KEY FK_27DD38E9AF01F264');
        $this->addSql('ALTER TABLE cv_recruteur_cv_sauvegarde_cv_cv DROP FOREIGN KEY FK_665AE5062ECB73F6');
        $this->addSql('ALTER TABLE cv_recruteur_cv_sauvegarde_user_user DROP FOREIGN KEY FK_7C26B9A52ECB73F6');
        $this->addSql('ALTER TABLE cv_recruteur_cv_sauvegarde_cv_recruteur_cv_dossier DROP FOREIGN KEY FK_B03894282ECB73F6');
        $this->addSql('ALTER TABLE ec_calendrier_forum_ec_ecole DROP FOREIGN KEY FK_BFA1651712142D25');
        $this->addSql('ALTER TABLE ec_calendrier_forum_off_liste_type_formation DROP FOREIGN KEY FK_C2FBBC3612142D25');
        $this->addSql('ALTER TABLE ec_calendrier_forum_off_liste_secteur_activite DROP FOREIGN KEY FK_733F263012142D25');
        $this->addSql('ALTER TABLE ec_calendrier_forum_geo_adresse DROP FOREIGN KEY FK_7DC887C712142D25');
        $this->addSql('ALTER TABLE ec_calendrier_forum_user_civilite DROP FOREIGN KEY FK_88DA01A712142D25');
        $this->addSql('ALTER TABLE ec_stat_calendrier_forum_ec_calendrier_forum DROP FOREIGN KEY FK_2E42C25712142D25');
        $this->addSql('ALTER TABLE ec_calendrier_stage_ec_ecole DROP FOREIGN KEY FK_90B52C9D19404666');
        $this->addSql('ALTER TABLE ec_calendrier_stage_ec_liste_annee_ecole DROP FOREIGN KEY FK_912D78B019404666');
        $this->addSql('ALTER TABLE ec_calendrier_stage_off_liste_type_formation DROP FOREIGN KEY FK_3D2CAA4B19404666');
        $this->addSql('ALTER TABLE ec_calendrier_stage_off_liste_secteur_activite DROP FOREIGN KEY FK_1D7A6B0D19404666');
        $this->addSql('ALTER TABLE ec_calendrier_stage_user_civilite DROP FOREIGN KEY FK_9CA38DE219404666');
        $this->addSql('ALTER TABLE ec_stat_calendrier_stage_ec_calendrier_stage DROP FOREIGN KEY FK_4F8ECD0519404666');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_ec_ecole DROP FOREIGN KEY FK_15FCE065CFF27E35');
        $this->addSql('ALTER TABLE ec_calendrier_forum_ec_ecole DROP FOREIGN KEY FK_BFA16517CFF27E35');
        $this->addSql('ALTER TABLE ec_calendrier_stage_ec_ecole DROP FOREIGN KEY FK_90B52C9DCFF27E35');
        $this->addSql('ALTER TABLE ec_ecole_geo_adresse DROP FOREIGN KEY FK_C471AD4FCFF27E35');
        $this->addSql('ALTER TABLE ec_ecole_off_type_xml DROP FOREIGN KEY FK_42A8C242CFF27E35');
        $this->addSql('ALTER TABLE ec_ecole_ec_liste_type_ecole DROP FOREIGN KEY FK_FB032AA0CFF27E35');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_ecole DROP FOREIGN KEY FK_93D9B7C7CFF27E35');
        $this->addSql('ALTER TABLE edit_statistique_structure_ec_ecole DROP FOREIGN KEY FK_D40A5E3CFF27E35');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_ec_ecole DROP FOREIGN KEY FK_E60E2969CFF27E35');
        $this->addSql('ALTER TABLE user_situation_ec_ecole DROP FOREIGN KEY FK_AB34018CCFF27E35');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_ecole DROP FOREIGN KEY FK_93D9B7C7632F2121');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_organisme_formation DROP FOREIGN KEY FK_FCEB1883632F2121');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_liste_type_session DROP FOREIGN KEY FK_9C9346F9632F2121');
        $this->addSql('ALTER TABLE ec_lien_session_form_off_sect_activite_ec_session_formation DROP FOREIGN KEY FK_8834768AB9AD7B2');
        $this->addSql('ALTER TABLE ec_lien_session_form_off_sect_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_40FBB76BAB9AD7B2');
        $this->addSql('ALTER TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_ec_session_formation DROP FOREIGN KEY FK_2C0245D0AF8007FD');
        $this->addSql('ALTER TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_16E29B8BAF8007FD');
        $this->addSql('ALTER TABLE common_statistique_ec_organisme_formation DROP FOREIGN KEY FK_4195F6BAC540A215');
        $this->addSql('ALTER TABLE ec_formation_temp_ec_organisme_formation DROP FOREIGN KEY FK_FCEB1883C540A215');
        $this->addSql('ALTER TABLE ec_organisme_formation_geo_adresse DROP FOREIGN KEY FK_1433150C540A215');
        $this->addSql('ALTER TABLE edit_statistique_structure_ec_organisme_formation DROP FOREIGN KEY FK_866F9E0EC540A215');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_ec_session_formation DROP FOREIGN KEY FK_FEFBF331F551B8FE');
        $this->addSql('ALTER TABLE common_statistique_ec_session_formation DROP FOREIGN KEY FK_9723F1BDF551B8FE');
        $this->addSql('ALTER TABLE ec_lien_session_form_off_sect_activite_ec_session_formation DROP FOREIGN KEY FK_8834768F551B8FE');
        $this->addSql('ALTER TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_ec_session_formation DROP FOREIGN KEY FK_2C0245D0F551B8FE');
        $this->addSql('ALTER TABLE ec_session_formation_geo_adresse DROP FOREIGN KEY FK_91889352F551B8FE');
        $this->addSql('ALTER TABLE ec_session_formation_off_liste_type_formation DROP FOREIGN KEY FK_3F31AE7BF551B8FE');
        $this->addSql('ALTER TABLE ec_statistique_ec_session_formation DROP FOREIGN KEY FK_5BF9C58EF551B8FE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_ec_session_formation DROP FOREIGN KEY FK_9AD70FBEF551B8FE');
        $this->addSql('ALTER TABLE user_situation_ec_session_formation DROP FOREIGN KEY FK_26B2E375F551B8FE');
        $this->addSql('ALTER TABLE ec_stat_calendrier_forum_ec_calendrier_forum DROP FOREIGN KEY FK_2E42C2578E254D36');
        $this->addSql('ALTER TABLE ec_stat_calendrier_stage_ec_calendrier_stage DROP FOREIGN KEY FK_4F8ECD0585712675');
        $this->addSql('ALTER TABLE ec_statistique_ec_session_formation DROP FOREIGN KEY FK_5BF9C58E232F3941');
        $this->addSql('ALTER TABLE ec_statistique_user_user DROP FOREIGN KEY FK_18095843232F3941');
        $this->addSql('ALTER TABLE common_attachement_mail_edit_article DROP FOREIGN KEY FK_2023388CE7695588');
        $this->addSql('ALTER TABLE edit_article_off_liste_secteur_activite DROP FOREIGN KEY FK_722B70E2E7695588');
        $this->addSql('ALTER TABLE edit_article_edit_type_article DROP FOREIGN KEY FK_6DBDE3C1E7695588');
        $this->addSql('ALTER TABLE edit_article_edit_theme_article DROP FOREIGN KEY FK_DE7B9D1FE7695588');
        $this->addSql('ALTER TABLE edit_article_edit_sous_theme_article DROP FOREIGN KEY FK_F18B9958E7695588');
        $this->addSql('ALTER TABLE edit_article_user_user DROP FOREIGN KEY FK_10269982E7695588');
        $this->addSql('ALTER TABLE edit_carte_region_edit_article DROP FOREIGN KEY FK_2E3DEC62E7695588');
        $this->addSql('ALTER TABLE edit_carte_visite_edit_article DROP FOREIGN KEY FK_6A5B2464E7695588');
        $this->addSql('ALTER TABLE edit_lien_photo_article_edit_article DROP FOREIGN KEY FK_F0A9E0E3E7695588');
        $this->addSql('ALTER TABLE edit_lien_video_article_edit_article DROP FOREIGN KEY FK_7AEE5452E7695588');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_article_edit_article DROP FOREIGN KEY FK_F357AA9FE7695588');
        $this->addSql('ALTER TABLE edit_page_cadre_edit_article DROP FOREIGN KEY FK_57E9FC9CE7695588');
        $this->addSql('ALTER TABLE edit_statistique_edit_article DROP FOREIGN KEY FK_51D762F3E7695588');
        $this->addSql('ALTER TABLE edit_zoom_profil_edit_article DROP FOREIGN KEY FK_8FB7C766E7695588');
        $this->addSql('ALTER TABLE edit_carte_departement_geo_departement DROP FOREIGN KEY FK_76249A0DB60CC0C0');
        $this->addSql('ALTER TABLE edit_carte_departement_edit_carte_region DROP FOREIGN KEY FK_8E326622B60CC0C0');
        $this->addSql('ALTER TABLE edit_carte_departement_edit_carte_region DROP FOREIGN KEY FK_8E3266226FBE7ACB');
        $this->addSql('ALTER TABLE edit_carte_region_geo_liste_region DROP FOREIGN KEY FK_AA1D1E656FBE7ACB');
        $this->addSql('ALTER TABLE edit_carte_region_edit_article DROP FOREIGN KEY FK_2E3DEC626FBE7ACB');
        $this->addSql('ALTER TABLE edit_publicite_entreprise_carte_region_edit_carte_region DROP FOREIGN KEY FK_D156CC8E6FBE7ACB');
        $this->addSql('ALTER TABLE edit_carte_visite_edit_article DROP FOREIGN KEY FK_6A5B2464365DA7C7');
        $this->addSql('ALTER TABLE edit_carte_visite_geo_adresse DROP FOREIGN KEY FK_E65DC032365DA7C7');
        $this->addSql('ALTER TABLE edit_list_entreprise_edit_carte_visite DROP FOREIGN KEY FK_19503173365DA7C7');
        $this->addSql('ALTER TABLE edit_focus_accueil_edit_app DROP FOREIGN KEY FK_2DFB15A739FA9219');
        $this->addSql('ALTER TABLE edit_image_accueil_edit_focus_accueil DROP FOREIGN KEY FK_DCCFEA7939FA9219');
        $this->addSql('ALTER TABLE edit_list_accueil_edit_focus_accueil DROP FOREIGN KEY FK_53F0B9C539FA9219');
        $this->addSql('ALTER TABLE edit_zoom_accueil_edit_focus_accueil DROP FOREIGN KEY FK_7AA3E25039FA9219');
        $this->addSql('ALTER TABLE edit_image_accueil_edit_focus_accueil DROP FOREIGN KEY FK_DCCFEA79C986DB92');
        $this->addSql('ALTER TABLE edit_image_accueil_edit_theme_article DROP FOREIGN KEY FK_7FB9621BC986DB92');
        $this->addSql('ALTER TABLE edit_image_profil_edit_zoom_profil DROP FOREIGN KEY FK_735519E1EF77B2C5');
        $this->addSql('ALTER TABLE edit_image_zoom_edit_zoom_sectoriel DROP FOREIGN KEY FK_AB532EAF80DE7A6C');
        $this->addSql('ALTER TABLE edit_lien_photo_article_edit_article DROP FOREIGN KEY FK_F0A9E0E39D70811F');
        $this->addSql('ALTER TABLE edit_lien_photo_article_edit_photo DROP FOREIGN KEY FK_2E9E95C99D70811F');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_departement_edit_rss_thematique DROP FOREIGN KEY FK_6651E1476A97D0E4');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_departement_geo_departement DROP FOREIGN KEY FK_B6E04F456A97D0E4');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_region_edit_rss_thematique DROP FOREIGN KEY FK_35AC6E2E49E1C286');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_region_geo_liste_region DROP FOREIGN KEY FK_8188027349E1C286');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_secteur_activite_edit_rss_thematique DROP FOREIGN KEY FK_997FF11F1A88EAE4');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_C79248CE1A88EAE4');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_sous_secteur_activite_edit_rss_thematique DROP FOREIGN KEY FK_BA12FEE23732BFA7');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_sous_secteur_activite_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_78F46E7E3732BFA7');
        $this->addSql('ALTER TABLE edit_lien_video_article_edit_article DROP FOREIGN KEY FK_7AEE545251B377A1');
        $this->addSql('ALTER TABLE edit_lien_video_article_edit_video DROP FOREIGN KEY FK_39A5694B51B377A1');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_article_edit_zoom_sectoriel DROP FOREIGN KEY FK_DFFE86E83036924A');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_article_edit_article DROP FOREIGN KEY FK_F357AA9F3036924A');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_edit_zoom_sectoriel DROP FOREIGN KEY FK_D9EA904022CB57AF');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_F22027FD22CB57AF');
        $this->addSql('ALTER TABLE edit_list_accueil_edit_focus_accueil DROP FOREIGN KEY FK_53F0B9C5B618F0F4');
        $this->addSql('ALTER TABLE edit_list_accueil_edit_theme_article DROP FOREIGN KEY FK_F08631A7B618F0F4');
        $this->addSql('ALTER TABLE edit_list_entreprise_edit_carte_visite DROP FOREIGN KEY FK_19503173FC81074D');
        $this->addSql('ALTER TABLE edit_list_entreprise_entr_entreprise DROP FOREIGN KEY FK_1D791962FC81074D');
        $this->addSql('ALTER TABLE edit_list_tag_edit_app DROP FOREIGN KEY FK_75ED7A12394D8891');
        $this->addSql('ALTER TABLE edit_list_tag_off_liste_secteur_activite DROP FOREIGN KEY FK_40953CF6394D8891');
        $this->addSql('ALTER TABLE edit_log_habillage_edit_habillage DROP FOREIGN KEY FK_21CAEF345684EAC4');
        $this->addSql('ALTER TABLE edit_page_bouche_edit_app DROP FOREIGN KEY FK_B8FBE8E6DBF0A629');
        $this->addSql('ALTER TABLE edit_page_cadre_edit_app DROP FOREIGN KEY FK_461C89A61A1CEF4');
        $this->addSql('ALTER TABLE edit_page_cadre_edit_article DROP FOREIGN KEY FK_57E9FC9C61A1CEF4');
        $this->addSql('ALTER TABLE edit_page_retail_edit_app DROP FOREIGN KEY FK_22981E224D273415');
        $this->addSql('ALTER TABLE edit_page_thematique_edit_sous_theme_article DROP FOREIGN KEY FK_71C035F948FA23A');
        $this->addSql('ALTER TABLE edit_publicite_entreprise_carte_region_edit_app DROP FOREIGN KEY FK_68682061BCC314D9');
        $this->addSql('ALTER TABLE edit_publicite_entreprise_carte_region_edit_carte_region DROP FOREIGN KEY FK_D156CC8EBCC314D9');
        $this->addSql('ALTER TABLE edit_publicite_entreprise_carte_region_user_user DROP FOREIGN KEY FK_771BADDCBCC314D9');
        $this->addSql('ALTER TABLE edit_referencement_edit_app DROP FOREIGN KEY FK_40B1606349FF2995');
        $this->addSql('ALTER TABLE edit_article_edit_sous_theme_article DROP FOREIGN KEY FK_F18B9958F4BFBBE');
        $this->addSql('ALTER TABLE edit_page_thematique_edit_sous_theme_article DROP FOREIGN KEY FK_71C035F9F4BFBBE');
        $this->addSql('ALTER TABLE edit_sous_theme_article_edit_theme_article DROP FOREIGN KEY FK_8F53F719F4BFBBE');
        $this->addSql('ALTER TABLE edit_statistique_edit_article DROP FOREIGN KEY FK_51D762F376BA7F9');
        $this->addSql('ALTER TABLE edit_statistique_structure_user_user DROP FOREIGN KEY FK_DBB3B1AECC27D7A4');
        $this->addSql('ALTER TABLE edit_statistique_structure_ec_ecole DROP FOREIGN KEY FK_D40A5E3CC27D7A4');
        $this->addSql('ALTER TABLE edit_statistique_structure_ec_organisme_formation DROP FOREIGN KEY FK_866F9E0ECC27D7A4');
        $this->addSql('ALTER TABLE edit_statistique_structure_entr_entreprise DROP FOREIGN KEY FK_19F22B1CCC27D7A4');
        $this->addSql('ALTER TABLE edit_zoom_accueil_edit_focus_accueil DROP FOREIGN KEY FK_7AA3E250974E38B3');
        $this->addSql('ALTER TABLE edit_image_profil_edit_zoom_profil DROP FOREIGN KEY FK_735519E1F96D9DBD');
        $this->addSql('ALTER TABLE edit_zoom_profil_edit_article DROP FOREIGN KEY FK_8FB7C766F96D9DBD');
        $this->addSql('ALTER TABLE edit_list_entreprise_entr_entreprise DROP FOREIGN KEY FK_1D7919622923CBC2');
        $this->addSql('ALTER TABLE edit_statistique_structure_entr_entreprise DROP FOREIGN KEY FK_19F22B1C2923CBC2');
        $this->addSql('ALTER TABLE entr_entreprise_geo_adresse DROP FOREIGN KEY FK_65592BB12923CBC2');
        $this->addSql('ALTER TABLE entr_entreprise_entr_profil_recruteur DROP FOREIGN KEY FK_F96B29FD2923CBC2');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_secteur_activite_entr_entreprise DROP FOREIGN KEY FK_33BD92972923CBC2');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_entr_entreprise DROP FOREIGN KEY FK_B9878DB82923CBC2');
        $this->addSql('ALTER TABLE off_statistique_entr_entreprise DROP FOREIGN KEY FK_3913F4FD2923CBC2');
        $this->addSql('ALTER TABLE off_statistique_old_save_entr_entreprise DROP FOREIGN KEY FK_4DD37E8A2923CBC2');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_secteur_activite_entr_entreprise DROP FOREIGN KEY FK_33BD929740FECEFE');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_56929C9A40FECEFE');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_entr_entreprise DROP FOREIGN KEY FK_B9878DB8221AA9D1');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_C85F25F1221AA9D1');
        $this->addSql('ALTER TABLE exp_expression_exp_correspondance DROP FOREIGN KEY FK_33235A43DF14880C');
        $this->addSql('ALTER TABLE exp_table_correspondance_exp_correspondance DROP FOREIGN KEY FK_E4E39124DF14880C');
        $this->addSql('ALTER TABLE exp_expression_exp_valeur_offre DROP FOREIGN KEY FK_66F01F9737DDE08D');
        $this->addSql('ALTER TABLE exp_expression_exp_valeur_fixe DROP FOREIGN KEY FK_D8889F3637DDE08D');
        $this->addSql('ALTER TABLE exp_expression_exp_fichier DROP FOREIGN KEY FK_6C9FD83837DDE08D');
        $this->addSql('ALTER TABLE exp_expression_exp_correspondance DROP FOREIGN KEY FK_33235A4337DDE08D');
        $this->addSql('ALTER TABLE exp_lien_expression_mise_en_forme_exp_expression DROP FOREIGN KEY FK_97517F3037DDE08D');
        $this->addSql('ALTER TABLE exp_expression_exp_fichier DROP FOREIGN KEY FK_6C9FD8381A25ABF4');
        $this->addSql('ALTER TABLE exp_fichier_exp_type_fichier DROP FOREIGN KEY FK_33BF037A1A25ABF4');
        $this->addSql('ALTER TABLE exp_lien_expression_mise_en_forme_exp_mise_en_forme DROP FOREIGN KEY FK_247CE4693AE58F1D');
        $this->addSql('ALTER TABLE exp_lien_expression_mise_en_forme_exp_expression DROP FOREIGN KEY FK_97517F303AE58F1D');
        $this->addSql('ALTER TABLE exp_lien_mise_en_forme_parametre_exp_mise_en_forme DROP FOREIGN KEY FK_334C4113F827A23');
        $this->addSql('ALTER TABLE exp_lien_mise_en_forme_parametre_exp_parametre DROP FOREIGN KEY FK_EE87362C3F827A23');
        $this->addSql('ALTER TABLE exp_lien_valeur_fixe_parametre_exp_valeur_fixe DROP FOREIGN KEY FK_2A27B36E2C2ED9DE');
        $this->addSql('ALTER TABLE exp_lien_valeur_fixe_parametre_exp_parametre DROP FOREIGN KEY FK_1E1BEAA52C2ED9DE');
        $this->addSql('ALTER TABLE exp_lien_valeur_offre_parametre_exp_valeur_offre DROP FOREIGN KEY FK_B429538EA53DF93D');
        $this->addSql('ALTER TABLE exp_lien_valeur_offre_parametre_exp_parametre DROP FOREIGN KEY FK_36A8CC96A53DF93D');
        $this->addSql('ALTER TABLE exp_lien_expression_mise_en_forme_exp_mise_en_forme DROP FOREIGN KEY FK_247CE4696A3FEADF');
        $this->addSql('ALTER TABLE exp_lien_mise_en_forme_parametre_exp_mise_en_forme DROP FOREIGN KEY FK_334C4116A3FEADF');
        $this->addSql('ALTER TABLE exp_lien_mise_en_forme_parametre_exp_parametre DROP FOREIGN KEY FK_EE87362CBA6E65E3');
        $this->addSql('ALTER TABLE exp_lien_valeur_fixe_parametre_exp_parametre DROP FOREIGN KEY FK_1E1BEAA5BA6E65E3');
        $this->addSql('ALTER TABLE exp_lien_valeur_offre_parametre_exp_parametre DROP FOREIGN KEY FK_36A8CC96BA6E65E3');
        $this->addSql('ALTER TABLE exp_table_correspondance_exp_correspondance DROP FOREIGN KEY FK_E4E3912471D3D8F4');
        $this->addSql('ALTER TABLE exp_fichier_exp_type_fichier DROP FOREIGN KEY FK_33BF037AB6710CBC');
        $this->addSql('ALTER TABLE exp_expression_exp_valeur_fixe DROP FOREIGN KEY FK_D8889F36F71E40CF');
        $this->addSql('ALTER TABLE exp_lien_valeur_fixe_parametre_exp_valeur_fixe DROP FOREIGN KEY FK_2A27B36EF71E40CF');
        $this->addSql('ALTER TABLE exp_expression_exp_valeur_offre DROP FOREIGN KEY FK_66F01F979B3437BC');
        $this->addSql('ALTER TABLE exp_lien_valeur_offre_parametre_exp_valeur_offre DROP FOREIGN KEY FK_B429538E9B3437BC');
        $this->addSql('ALTER TABLE fiche_metier_off_liste_secteur_activite DROP FOREIGN KEY FK_9CAEE2836A7847C8');
        $this->addSql('ALTER TABLE fiche_metier_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_58C0B9166A7847C8');
        $this->addSql('ALTER TABLE fiche_metier_edit_app DROP FOREIGN KEY FK_9C26C45D6A7847C8');
        $this->addSql('ALTER TABLE form_formation_form_formation DROP FOREIGN KEY FK_623F95E1802C7D73');
        $this->addSql('ALTER TABLE form_formation_form_formation DROP FOREIGN KEY FK_623F95E199C92DFC');
        $this->addSql('ALTER TABLE ec_calendrier_forum_geo_adresse DROP FOREIGN KEY FK_7DC887C7349A5BD2');
        $this->addSql('ALTER TABLE ec_ecole_geo_adresse DROP FOREIGN KEY FK_C471AD4F349A5BD2');
        $this->addSql('ALTER TABLE ec_organisme_formation_geo_adresse DROP FOREIGN KEY FK_1433150349A5BD2');
        $this->addSql('ALTER TABLE ec_session_formation_geo_adresse DROP FOREIGN KEY FK_91889352349A5BD2');
        $this->addSql('ALTER TABLE edit_carte_visite_geo_adresse DROP FOREIGN KEY FK_E65DC032349A5BD2');
        $this->addSql('ALTER TABLE entr_entreprise_geo_adresse DROP FOREIGN KEY FK_65592BB1349A5BD2');
        $this->addSql('ALTER TABLE geo_adresse_geo_liste_region DROP FOREIGN KEY FK_4CB9F5DE349A5BD2');
        $this->addSql('ALTER TABLE geo_adresse_geo_liste_pays DROP FOREIGN KEY FK_64567C17349A5BD2');
        $this->addSql('ALTER TABLE cor_keljob_lieu_geo_departement DROP FOREIGN KEY FK_11BC0266964DE230');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_departement_geo_departement DROP FOREIGN KEY FK_B7CDDF28964DE230');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_departement_geo_departement DROP FOREIGN KEY FK_21872B64964DE230');
        $this->addSql('ALTER TABLE edit_carte_departement_geo_departement DROP FOREIGN KEY FK_76249A0D964DE230');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_departement_geo_departement DROP FOREIGN KEY FK_B6E04F45964DE230');
        $this->addSql('ALTER TABLE geo_departement_geo_liste_region DROP FOREIGN KEY FK_3CA4C82964DE230');
        $this->addSql('ALTER TABLE geo_ville_geo_departement DROP FOREIGN KEY FK_76A2A140964DE230');
        $this->addSql('ALTER TABLE geo_ville_bck_geo_departement DROP FOREIGN KEY FK_32744ED1964DE230');
        $this->addSql('ALTER TABLE geo_ville_svg_geo_departement DROP FOREIGN KEY FK_A995A02D964DE230');
        $this->addSql('ALTER TABLE off_lien_offre_geo_departement_geo_departement DROP FOREIGN KEY FK_87336073964DE230');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_pays_geo_liste_pays DROP FOREIGN KEY FK_7979AD53A603EFEB');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_pays_geo_liste_pays DROP FOREIGN KEY FK_4C528D3BA603EFEB');
        $this->addSql('ALTER TABLE geo_adresse_geo_liste_pays DROP FOREIGN KEY FK_64567C17A603EFEB');
        $this->addSql('ALTER TABLE geo_liste_region_geo_liste_pays DROP FOREIGN KEY FK_CF4A2A0A603EFEB');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_pays_geo_liste_pays DROP FOREIGN KEY FK_3D1F0576A603EFEB');
        $this->addSql('ALTER TABLE cor_keljob_lieu_geo_liste_region DROP FOREIGN KEY FK_4EA9BA9B19DE7EC1');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_region_geo_liste_region DROP FOREIGN KEY FK_74E8FB7519DE7EC1');
        $this->addSql('ALTER TABLE cv_lien_formation_geo_liste_region_geo_liste_region DROP FOREIGN KEY FK_92F45B3319DE7EC1');
        $this->addSql('ALTER TABLE edit_carte_region_geo_liste_region DROP FOREIGN KEY FK_AA1D1E6519DE7EC1');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_region_geo_liste_region DROP FOREIGN KEY FK_8188027319DE7EC1');
        $this->addSql('ALTER TABLE geo_adresse_geo_liste_region DROP FOREIGN KEY FK_4CB9F5DE19DE7EC1');
        $this->addSql('ALTER TABLE geo_departement_geo_liste_region DROP FOREIGN KEY FK_3CA4C8219DE7EC1');
        $this->addSql('ALTER TABLE geo_liste_region_geo_liste_pays DROP FOREIGN KEY FK_CF4A2A019DE7EC1');
        $this->addSql('ALTER TABLE geo_ville_geo_liste_region DROP FOREIGN KEY FK_9CC321C519DE7EC1');
        $this->addSql('ALTER TABLE geo_ville_bck_geo_liste_region DROP FOREIGN KEY FK_1B8F54F819DE7EC1');
        $this->addSql('ALTER TABLE geo_ville_svg_geo_liste_region DROP FOREIGN KEY FK_AF1F0B2119DE7EC1');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_region_geo_liste_region DROP FOREIGN KEY FK_7679E10919DE7EC1');
        $this->addSql('ALTER TABLE geo_ville_geo_departement DROP FOREIGN KEY FK_76A2A14049867797');
        $this->addSql('ALTER TABLE geo_ville_geo_liste_region DROP FOREIGN KEY FK_9CC321C549867797');
        $this->addSql('ALTER TABLE geo_ville_bck_geo_departement DROP FOREIGN KEY FK_32744ED19F74FEDE');
        $this->addSql('ALTER TABLE geo_ville_bck_geo_liste_region DROP FOREIGN KEY FK_1B8F54F89F74FEDE');
        $this->addSql('ALTER TABLE geo_ville_svg_geo_departement DROP FOREIGN KEY FK_A995A02DB52888AA');
        $this->addSql('ALTER TABLE geo_ville_svg_geo_liste_region DROP FOREIGN KEY FK_AF1F0B21B52888AA');
        $this->addSql('ALTER TABLE mb_moteur_recherche_mb_css DROP FOREIGN KEY FK_2FA932A6DCEEB87E');
        $this->addSql('ALTER TABLE mb_moteur_recherche_mb_css DROP FOREIGN KEY FK_2FA932A65A566197');
        $this->addSql('ALTER TABLE mc_contrat_mc_zone_mc DROP FOREIGN KEY FK_1B9AEE2398805D1');
        $this->addSql('ALTER TABLE mc_localisation_mc_zone_mc DROP FOREIGN KEY FK_9FE4002D4EF8859');
        $this->addSql('ALTER TABLE mc_localisation_mc_type_localisation DROP FOREIGN KEY FK_A17D70B14EF8859');
        $this->addSql('ALTER TABLE mc_localisation_svg_mc_zone_mc DROP FOREIGN KEY FK_6ECB4F622170C8B1');
        $this->addSql('ALTER TABLE mc_localisation_svg_mc_type_localisation DROP FOREIGN KEY FK_2DB8A1DD2170C8B1');
        $this->addSql('ALTER TABLE mc_metier_mc_zone_mc DROP FOREIGN KEY FK_B5B1AEEE471E68FB');
        $this->addSql('ALTER TABLE mc_secteur_mc_zone_mc DROP FOREIGN KEY FK_96A3D03B8ED547CB');
        $this->addSql('ALTER TABLE mc_localisation_mc_type_localisation DROP FOREIGN KEY FK_A17D70B158CA1A9F');
        $this->addSql('ALTER TABLE mc_localisation_svg_mc_type_localisation DROP FOREIGN KEY FK_2DB8A1DD58CA1A9F');
        $this->addSql('ALTER TABLE mc_contrat_mc_zone_mc DROP FOREIGN KEY FK_1B9AEE23A096AC46');
        $this->addSql('ALTER TABLE mc_localisation_mc_zone_mc DROP FOREIGN KEY FK_9FE4002DA096AC46');
        $this->addSql('ALTER TABLE mc_localisation_svg_mc_zone_mc DROP FOREIGN KEY FK_6ECB4F62A096AC46');
        $this->addSql('ALTER TABLE mc_metier_mc_zone_mc DROP FOREIGN KEY FK_B5B1AEEEA096AC46');
        $this->addSql('ALTER TABLE mc_secteur_mc_zone_mc DROP FOREIGN KEY FK_96A3D03BA096AC46');
        $this->addSql('ALTER TABLE mc_zone_mc_edit_app DROP FOREIGN KEY FK_4D0062A6A096AC46');
        $this->addSql('ALTER TABLE off_candidature_user_user DROP FOREIGN KEY FK_9CA84FB87516ECFB');
        $this->addSql('ALTER TABLE off_candidature_off_offre DROP FOREIGN KEY FK_64F794507516ECFB');
        $this->addSql('ALTER TABLE off_candidature_off_liste_type_suivi DROP FOREIGN KEY FK_6A87B2167516ECFB');
        $this->addSql('ALTER TABLE cor_keljob_salaire_off_fourchette_remuneration DROP FOREIGN KEY FK_9A10DC10AF4A7002');
        $this->addSql('ALTER TABLE cv_cv_off_fourchette_remuneration DROP FOREIGN KEY FK_4015982AAF4A7002');
        $this->addSql('ALTER TABLE off_offre_off_fourchette_remuneration DROP FOREIGN KEY FK_D3296C39AF4A7002');
        $this->addSql('ALTER TABLE off_lien_offre_geo_departement_geo_departement DROP FOREIGN KEY FK_8733607318B1A9B1');
        $this->addSql('ALTER TABLE off_lien_offre_geo_departement_off_offre DROP FOREIGN KEY FK_D05249E018B1A9B1');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_pays_geo_liste_pays DROP FOREIGN KEY FK_3D1F0576E4A79776');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_pays_off_offre DROP FOREIGN KEY FK_C4884505E4A79776');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_region_geo_liste_region DROP FOREIGN KEY FK_7679E10983EF313C');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_region_off_offre DROP FOREIGN KEY FK_25C441F383EF313C');
        $this->addSql('ALTER TABLE off_lien_offre_liste_experience_requise_off_liste_experience_requise DROP FOREIGN KEY FK_8CCD44ED24741D47');
        $this->addSql('ALTER TABLE off_lien_offre_liste_experience_requise_off_offre DROP FOREIGN KEY FK_CDAF37D224741D47');
        $this->addSql('ALTER TABLE off_lien_offre_liste_secteur_activite_off_offre DROP FOREIGN KEY FK_C641F4A57D4B001C');
        $this->addSql('ALTER TABLE off_lien_offre_liste_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_A8C66CD67D4B001C');
        $this->addSql('ALTER TABLE off_lien_offre_liste_sous_secteur_activite_off_offre DROP FOREIGN KEY FK_23554EBE4FE1D01E');
        $this->addSql('ALTER TABLE off_lien_offre_liste_sous_secteur_activite_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_3F02BDFD4FE1D01E');
        $this->addSql('ALTER TABLE off_lien_offre_liste_type_formation_off_offre DROP FOREIGN KEY FK_F31245FCB99AEDB5');
        $this->addSql('ALTER TABLE off_lien_offre_liste_type_formation_off_liste_type_formation DROP FOREIGN KEY FK_86D859C9B99AEDB5');
        $this->addSql('ALTER TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_5B4F9F2D16B9E322');
        $this->addSql('ALTER TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_864B0D3C16B9E322');
        $this->addSql('ALTER TABLE cor_keljob_experience_off_liste_experience_requise DROP FOREIGN KEY FK_1A20B21FBC4B4FB9');
        $this->addSql('ALTER TABLE off_lien_offre_liste_experience_requise_off_liste_experience_requise DROP FOREIGN KEY FK_8CCD44EDBC4B4FB9');
        $this->addSql('ALTER TABLE user_situation_experience_off_liste_experience_requise DROP FOREIGN KEY FK_D9B522F6BC4B4FB9');
        $this->addSql('ALTER TABLE cor_easyoffre_secteur_off_liste_secteur_activite DROP FOREIGN KEY FK_80AF6B22B78A629C');
        $this->addSql('ALTER TABLE cor_keljob_secteur_off_liste_secteur_activite DROP FOREIGN KEY FK_46B21B78A629C');
        $this->addSql('ALTER TABLE cor_plusrh_secteur_off_liste_secteur_activite DROP FOREIGN KEY FK_7B45C224B78A629C');
        $this->addSql('ALTER TABLE cv_lien_exp_pro_off_liste_sect_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_2BA542C9B78A629C');
        $this->addSql('ALTER TABLE cv_lienformation_off_liste_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_9737B892B78A629C');
        $this->addSql('ALTER TABLE ec_calendrier_forum_off_liste_secteur_activite DROP FOREIGN KEY FK_733F2630B78A629C');
        $this->addSql('ALTER TABLE ec_calendrier_stage_off_liste_secteur_activite DROP FOREIGN KEY FK_1D7A6B0DB78A629C');
        $this->addSql('ALTER TABLE ec_lien_session_form_off_sect_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_40FBB76BB78A629C');
        $this->addSql('ALTER TABLE edit_article_off_liste_secteur_activite DROP FOREIGN KEY FK_722B70E2B78A629C');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_C79248CEB78A629C');
        $this->addSql('ALTER TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_F22027FDB78A629C');
        $this->addSql('ALTER TABLE edit_list_tag_off_liste_secteur_activite DROP FOREIGN KEY FK_40953CF6B78A629C');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_56929C9AB78A629C');
        $this->addSql('ALTER TABLE fiche_metier_off_liste_secteur_activite DROP FOREIGN KEY FK_9CAEE283B78A629C');
        $this->addSql('ALTER TABLE off_lien_offre_liste_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_A8C66CD6B78A629C');
        $this->addSql('ALTER TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_864B0D3CB78A629C');
        $this->addSql('ALTER TABLE off_liste_secteur_activite_off_liste_type_secteur_activite DROP FOREIGN KEY FK_7AA0C6F9B78A629C');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_27F44DBCB78A629C');
        $this->addSql('ALTER TABLE user_situation_off_liste_secteur_activite DROP FOREIGN KEY FK_506B56B78A629C');
        $this->addSql('ALTER TABLE cor_plusrh_secteur_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_8E7D0FC4CE509CB7');
        $this->addSql('ALTER TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_16E29B8BCE509CB7');
        $this->addSql('ALTER TABLE edit_lien_rss_thematique_sous_secteur_activite_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_78F46E7ECE509CB7');
        $this->addSql('ALTER TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_C85F25F1CE509CB7');
        $this->addSql('ALTER TABLE fiche_metier_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_58C0B916CE509CB7');
        $this->addSql('ALTER TABLE off_lien_offre_liste_sous_secteur_activite_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_3F02BDFDCE509CB7');
        $this->addSql('ALTER TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_5B4F9F2DCE509CB7');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_off_liste_secteur_activite DROP FOREIGN KEY FK_27F44DBCCE509CB7');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_off_liste_type_secteur_activite DROP FOREIGN KEY FK_1BF49E88CE509CB7');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_edit_app DROP FOREIGN KEY FK_2380476CCE509CB7');
        $this->addSql('ALTER TABLE com_lien_off_com_type_contrat_off_liste_type_contrat DROP FOREIGN KEY FK_686EB500B4AF2FE3');
        $this->addSql('ALTER TABLE cor123_contrat_off_liste_type_contrat DROP FOREIGN KEY FK_B7A5784EB4AF2FE3');
        $this->addSql('ALTER TABLE cor_keljob_contrat_off_liste_type_contrat DROP FOREIGN KEY FK_41E313B7B4AF2FE3');
        $this->addSql('ALTER TABLE save_mail_candi_non_log_off_liste_type_contrat DROP FOREIGN KEY FK_5F928134B4AF2FE3');
        $this->addSql('ALTER TABLE com_liste_diffusion_off_liste_type_formation DROP FOREIGN KEY FK_5DEE3BAEA8BBB27F');
        $this->addSql('ALTER TABLE cv_formation_off_liste_type_formation DROP FOREIGN KEY FK_E402B0D5A8BBB27F');
        $this->addSql('ALTER TABLE ec_calendrier_forum_off_liste_type_formation DROP FOREIGN KEY FK_C2FBBC36A8BBB27F');
        $this->addSql('ALTER TABLE ec_calendrier_stage_off_liste_type_formation DROP FOREIGN KEY FK_3D2CAA4BA8BBB27F');
        $this->addSql('ALTER TABLE ec_session_formation_off_liste_type_formation DROP FOREIGN KEY FK_3F31AE7BA8BBB27F');
        $this->addSql('ALTER TABLE off_lien_offre_liste_type_formation_off_liste_type_formation DROP FOREIGN KEY FK_86D859C9A8BBB27F');
        $this->addSql('ALTER TABLE off_liste_type_formation_com_liste_type_diffusion DROP FOREIGN KEY FK_65C134DAA8BBB27F');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_off_liste_type_formation DROP FOREIGN KEY FK_248D3548A8BBB27F');
        $this->addSql('ALTER TABLE user_situation_off_liste_type_formation DROP FOREIGN KEY FK_FF6415CEA8BBB27F');
        $this->addSql('ALTER TABLE off_liste_secteur_activite_off_liste_type_secteur_activite DROP FOREIGN KEY FK_7AA0C6F9E5A0BF1C');
        $this->addSql('ALTER TABLE off_liste_sous_secteur_activite_off_liste_type_secteur_activite DROP FOREIGN KEY FK_1BF49E88E5A0BF1C');
        $this->addSql('ALTER TABLE off_candidature_off_liste_type_suivi DROP FOREIGN KEY FK_6A87B2161AA99B2F');
        $this->addSql('ALTER TABLE off_meta_moteur_entr_profil_recruteur DROP FOREIGN KEY FK_43C04A9249B09D89');
        $this->addSql('ALTER TABLE com_liste_diffusion_off_offre DROP FOREIGN KEY FK_4BAEE28DC18A1CCE');
        $this->addSql('ALTER TABLE common_statistique_off_offre DROP FOREIGN KEY FK_AED51EC6C18A1CCE');
        $this->addSql('ALTER TABLE off_candidature_off_offre DROP FOREIGN KEY FK_64F79450C18A1CCE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_departement_off_offre DROP FOREIGN KEY FK_D05249E0C18A1CCE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_pays_off_offre DROP FOREIGN KEY FK_C4884505C18A1CCE');
        $this->addSql('ALTER TABLE off_lien_offre_geo_liste_region_off_offre DROP FOREIGN KEY FK_25C441F3C18A1CCE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_experience_requise_off_offre DROP FOREIGN KEY FK_CDAF37D2C18A1CCE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_secteur_activite_off_offre DROP FOREIGN KEY FK_C641F4A5C18A1CCE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_sous_secteur_activite_off_offre DROP FOREIGN KEY FK_23554EBEC18A1CCE');
        $this->addSql('ALTER TABLE off_lien_offre_liste_type_formation_off_offre DROP FOREIGN KEY FK_F31245FCC18A1CCE');
        $this->addSql('ALTER TABLE off_offre_com_offre_commerciale DROP FOREIGN KEY FK_6ECB9D06C18A1CCE');
        $this->addSql('ALTER TABLE off_offre_off_fourchette_remuneration DROP FOREIGN KEY FK_D3296C39C18A1CCE');
        $this->addSql('ALTER TABLE off_offre_com_liste_type_offre_commerciale DROP FOREIGN KEY FK_E49FCAA3C18A1CCE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_off_offre DROP FOREIGN KEY FK_2E6A4EF4C18A1CCE');
        $this->addSql('ALTER TABLE off_sauvegarde_offre_off_offre DROP FOREIGN KEY FK_2D0EFD8C18A1CCE');
        $this->addSql('ALTER TABLE off_statistique_off_offre DROP FOREIGN KEY FK_80311F51C18A1CCE');
        $this->addSql('ALTER TABLE promo_lien_promotion_off_offre_off_offre DROP FOREIGN KEY FK_5507FA8EC18A1CCE');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_racine_offre_off_offre DROP FOREIGN KEY FK_6F6F6E94C18A1CCE');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_off_offre DROP FOREIGN KEY FK_2E6A4EF45FF2EA66');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_ec_ecole DROP FOREIGN KEY FK_E60E29695FF2EA66');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_ec_session_formation DROP FOREIGN KEY FK_9AD70FBE5FF2EA66');
        $this->addSql('ALTER TABLE off_reponse_offre_non_identifie_off_liste_type_formation DROP FOREIGN KEY FK_248D35485FF2EA66');
        $this->addSql('ALTER TABLE off_sauvegarde_offre_off_offre DROP FOREIGN KEY FK_2D0EFD810895F1A');
        $this->addSql('ALTER TABLE off_sauvegarde_offre_user_user DROP FOREIGN KEY FK_FA8F343010895F1A');
        $this->addSql('ALTER TABLE off_statistique_off_offre DROP FOREIGN KEY FK_80311F51B5ACED1B');
        $this->addSql('ALTER TABLE off_statistique_entr_entreprise DROP FOREIGN KEY FK_3913F4FDB5ACED1B');
        $this->addSql('ALTER TABLE off_statistique_old_save_entr_entreprise DROP FOREIGN KEY FK_4DD37E8A91EC602E');
        $this->addSql('ALTER TABLE ec_ecole_off_type_xml DROP FOREIGN KEY FK_42A8C242C2329246');
        $this->addSql('ALTER TABLE promo_lien_promotion_off_offre_promo_promotion DROP FOREIGN KEY FK_DB5FC8CF7E590EEB');
        $this->addSql('ALTER TABLE promo_lien_promotion_off_offre_off_offre DROP FOREIGN KEY FK_5507FA8E7E590EEB');
        $this->addSql('ALTER TABLE promo_lien_promotion_offre_commerciale_promo_promotion DROP FOREIGN KEY FK_BFDC6EC17A0303B9');
        $this->addSql('ALTER TABLE promo_lien_promotion_offre_commerciale_com_offre_commerciale DROP FOREIGN KEY FK_A8E383947A0303B9');
        $this->addSql('ALTER TABLE promo_lien_promotion_user_user_promo_promotion DROP FOREIGN KEY FK_E218701140B0DFBA');
        $this->addSql('ALTER TABLE promo_lien_promotion_user_user_user_user DROP FOREIGN KEY FK_C4A826D340B0DFBA');
        $this->addSql('ALTER TABLE com_souscription_commerciale_promo_promotion DROP FOREIGN KEY FK_B660278C8B9B3683');
        $this->addSql('ALTER TABLE promo_lien_promotion_off_offre_promo_promotion DROP FOREIGN KEY FK_DB5FC8CF8B9B3683');
        $this->addSql('ALTER TABLE promo_lien_promotion_offre_commerciale_promo_promotion DROP FOREIGN KEY FK_BFDC6EC18B9B3683');
        $this->addSql('ALTER TABLE promo_lien_promotion_user_user_promo_promotion DROP FOREIGN KEY FK_E21870118B9B3683');
        $this->addSql('ALTER TABLE ref_lien_multi_edit_app DROP FOREIGN KEY FK_62CE700A1024C3A3');
        $this->addSql('ALTER TABLE save_mail_candi_non_log_edit_app DROP FOREIGN KEY FK_54A4D0FD5EB2D766');
        $this->addSql('ALTER TABLE save_mail_candi_non_log_off_liste_type_contrat DROP FOREIGN KEY FK_5F9281345EB2D766');
        $this->addSql('ALTER TABLE script_secteurs_stats_user_user DROP FOREIGN KEY FK_EFAF919675E66279');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_combinaisons DROP FOREIGN KEY FK_B5FE012A7768CF7C');
        $this->addSql('ALTER TABLE seo_edit_referencement_edit_app DROP FOREIGN KEY FK_71978C6D79E8E6D8');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_attente DROP FOREIGN KEY FK_51C48026D8DDFAD1');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_racine DROP FOREIGN KEY FK_96CCB205D8DDFAD1');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle DROP FOREIGN KEY FK_47D67C8B8C3AE25');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle_racine DROP FOREIGN KEY FK_6659928CB8C3AE25');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_racine_offre_seo_mot_cle_racine DROP FOREIGN KEY FK_F77287D59E2269A3');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_racine_offre_off_offre DROP FOREIGN KEY FK_6F6F6E949E2269A3');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_secteur DROP FOREIGN KEY FK_3C5D44EDC6B6AB5B');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_secteur DROP FOREIGN KEY FK_FA3466CFC6B6AB5B');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_secteur DROP FOREIGN KEY FK_60551E03C6B6AB5B');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_theme DROP FOREIGN KEY FK_C4E1EB053A60A64D');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_theme DROP FOREIGN KEY FK_8FA8175D3A60A64D');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_theme DROP FOREIGN KEY FK_FF4C53B03A60A64D');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_type DROP FOREIGN KEY FK_AB416ADBA1F29F8E');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_type DROP FOREIGN KEY FK_E66975FEA1F29F8E');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_type DROP FOREIGN KEY FK_68D917FBA1F29F8E');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle DROP FOREIGN KEY FK_47D67C8F8F767A5');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_type DROP FOREIGN KEY FK_AB416ADBF8F767A5');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_theme DROP FOREIGN KEY FK_C4E1EB05F8F767A5');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_liste_secteur DROP FOREIGN KEY FK_3C5D44EDF8F767A5');
        $this->addSql('ALTER TABLE seo_mot_cle_seo_combinaisons DROP FOREIGN KEY FK_B5FE012AF8F767A5');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_attente DROP FOREIGN KEY FK_51C480265C9ABB4C');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_type DROP FOREIGN KEY FK_E66975FE5C9ABB4C');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_theme DROP FOREIGN KEY FK_8FA8175D5C9ABB4C');
        $this->addSql('ALTER TABLE seo_mot_cle_attente_seo_liste_secteur DROP FOREIGN KEY FK_FA3466CF5C9ABB4C');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_racine DROP FOREIGN KEY FK_96CCB20564E2A64D');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle_racine DROP FOREIGN KEY FK_6659928C64E2A64D');
        $this->addSql('ALTER TABLE seo_lien_mot_cle_racine_offre_seo_mot_cle_racine DROP FOREIGN KEY FK_F77287D564E2A64D');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_type DROP FOREIGN KEY FK_68D917FB64E2A64D');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_theme DROP FOREIGN KEY FK_FF4C53B064E2A64D');
        $this->addSql('ALTER TABLE seo_mot_cle_racine_seo_liste_secteur DROP FOREIGN KEY FK_60551E0364E2A64D');
        $this->addSql('ALTER TABLE user_situation_user_user DROP FOREIGN KEY FK_618B9C3B4C494AD');
        $this->addSql('ALTER TABLE user_situation_user_situation_activite DROP FOREIGN KEY FK_F7A2F118B4C494AD');
        $this->addSql('ALTER TABLE user_situation_user_situation_statut DROP FOREIGN KEY FK_DE067EF2B4C494AD');
        $this->addSql('ALTER TABLE user_situation_off_liste_type_formation DROP FOREIGN KEY FK_FF6415CEB4C494AD');
        $this->addSql('ALTER TABLE user_situation_user_situation_experience DROP FOREIGN KEY FK_61859768B4C494AD');
        $this->addSql('ALTER TABLE user_situation_user_disponibilite DROP FOREIGN KEY FK_286CACFAB4C494AD');
        $this->addSql('ALTER TABLE user_situation_user_situation_mobilite DROP FOREIGN KEY FK_8D86D85EB4C494AD');
        $this->addSql('ALTER TABLE user_situation_ec_ecole DROP FOREIGN KEY FK_AB34018CB4C494AD');
        $this->addSql('ALTER TABLE user_situation_ec_session_formation DROP FOREIGN KEY FK_26B2E375B4C494AD');
        $this->addSql('ALTER TABLE user_situation_off_liste_secteur_activite DROP FOREIGN KEY FK_506B56B4C494AD');
        $this->addSql('ALTER TABLE user_situation_user_situation_experience DROP FOREIGN KEY FK_61859768C5162B4D');
        $this->addSql('ALTER TABLE user_situation_experience_off_liste_experience_requise DROP FOREIGN KEY FK_D9B522F6C5162B4D');
        $this->addSql('CREATE TABLE com_lien_offre_commerciale_off_liste_type_contrat (com_lien_offre_commerciale_off_liste_type_contrat_id INT NOT NULL, off_liste_type_contrat_id INT NOT NULL, INDEX IDX_1ACE4412507E2244 (com_lien_offre_commerciale_off_liste_type_contrat_id), INDEX IDX_1ACE4412B4AF2FE3 (off_liste_type_contrat_id), PRIMARY KEY(com_lien_offre_commerciale_off_liste_type_contrat_id, off_liste_type_contrat_id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE cv_lien_experience_pro_off_liste_secteur_activite (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('DROP TABLE cv_lien_exp_pro_off_liste_sect_activite');
        $this->addSql('DROP TABLE cv_lien_exp_pro_off_liste_sect_activite_off_liste_secteur_activite');
        $this->addSql('DROP TABLE cv_lien_exp_pro_off_liste_sect_activite_cv_experience_pro');
        $this->addSql('DROP TABLE cv_lien_formation_geo_departement');
        $this->addSql('DROP TABLE cv_lien_formation_geo_departement_geo_departement');
        $this->addSql('DROP TABLE cv_lien_formation_geo_departement_cv_formation');
        $this->addSql('DROP TABLE cv_lien_formation_geo_liste_pays');
        $this->addSql('DROP TABLE cv_lien_formation_geo_liste_pays_geo_liste_pays');
        $this->addSql('DROP TABLE cv_lien_formation_geo_liste_pays_cv_formation');
        $this->addSql('DROP TABLE cv_lien_formation_geo_liste_region');
        $this->addSql('DROP TABLE cv_lien_formation_geo_liste_region_geo_liste_region');
        $this->addSql('DROP TABLE cv_lien_formation_geo_liste_region_cv_formation');
        $this->addSql('DROP TABLE cv_lienformation_off_liste_secteur_activite');
        $this->addSql('DROP TABLE cv_lienformation_off_liste_secteur_activite_cv_formation');
        $this->addSql('DROP TABLE cv_lienformation_off_liste_secteur_activite_off_liste_secteur_activite');
        $this->addSql('DROP TABLE cv_log');
        $this->addSql('DROP TABLE cv_log_cv_cv');
        $this->addSql('DROP TABLE cv_log_user_user');
        $this->addSql('DROP TABLE cv_recruteur_cv_commentaire');
        $this->addSql('DROP TABLE cv_recruteur_cv_commentaire_cv_cv');
        $this->addSql('DROP TABLE cv_recruteur_cv_commentaire_user_user');
        $this->addSql('DROP TABLE cv_recruteur_cv_dossier');
        $this->addSql('DROP TABLE cv_recruteur_cv_dossier_user_user');
        $this->addSql('DROP TABLE cv_recruteur_cv_note');
        $this->addSql('DROP TABLE cv_recruteur_cv_note_cv_cv');
        $this->addSql('DROP TABLE cv_recruteur_cv_note_user_user');
        $this->addSql('DROP TABLE cv_recruteur_cv_sauvegarde');
        $this->addSql('DROP TABLE cv_recruteur_cv_sauvegarde_cv_cv');
        $this->addSql('DROP TABLE cv_recruteur_cv_sauvegarde_user_user');
        $this->addSql('DROP TABLE cv_recruteur_cv_sauvegarde_cv_recruteur_cv_dossier');
        $this->addSql('DROP TABLE cvteque_profils');
        $this->addSql('DROP TABLE cvteque_profils_n');
        $this->addSql('DROP TABLE cvteque_profils_n1');
        $this->addSql('DROP TABLE cvteque_profils_n2');
        $this->addSql('DROP TABLE cvtheque_mots_cles');
        $this->addSql('DROP TABLE ec_calendrier_forum');
        $this->addSql('DROP TABLE ec_calendrier_forum_ec_ecole');
        $this->addSql('DROP TABLE ec_calendrier_forum_off_liste_type_formation');
        $this->addSql('DROP TABLE ec_calendrier_forum_off_liste_secteur_activite');
        $this->addSql('DROP TABLE ec_calendrier_forum_geo_adresse');
        $this->addSql('DROP TABLE ec_calendrier_forum_user_civilite');
        $this->addSql('DROP TABLE ec_calendrier_stage');
        $this->addSql('DROP TABLE ec_calendrier_stage_ec_ecole');
        $this->addSql('DROP TABLE ec_calendrier_stage_ec_liste_annee_ecole');
        $this->addSql('DROP TABLE ec_calendrier_stage_off_liste_type_formation');
        $this->addSql('DROP TABLE ec_calendrier_stage_off_liste_secteur_activite');
        $this->addSql('DROP TABLE ec_calendrier_stage_user_civilite');
        $this->addSql('DROP TABLE ec_ecole');
        $this->addSql('DROP TABLE ec_ecole_geo_adresse');
        $this->addSql('DROP TABLE ec_ecole_off_type_xml');
        $this->addSql('DROP TABLE ec_ecole_ec_liste_type_ecole');
        $this->addSql('DROP TABLE ec_formation_temp');
        $this->addSql('DROP TABLE ec_formation_temp_ec_ecole');
        $this->addSql('DROP TABLE ec_formation_temp_ec_organisme_formation');
        $this->addSql('DROP TABLE ec_formation_temp_ec_liste_type_session');
        $this->addSql('DROP TABLE ec_lien_session_form_off_sect_activite');
        $this->addSql('DROP TABLE ec_lien_session_form_off_sect_activite_ec_session_formation');
        $this->addSql('DROP TABLE ec_lien_session_form_off_sect_activite_off_liste_secteur_activite');
        $this->addSql('DROP TABLE ec_lien_session_formation_off_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_ec_session_formation');
        $this->addSql('DROP TABLE ec_lien_session_formation_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE ec_organisme_formation');
        $this->addSql('DROP TABLE ec_organisme_formation_geo_adresse');
        $this->addSql('DROP TABLE ec_session_formation');
        $this->addSql('DROP TABLE ec_session_formation_geo_adresse');
        $this->addSql('DROP TABLE ec_session_formation_off_liste_type_formation');
        $this->addSql('DROP TABLE ec_stat_calendrier_forum');
        $this->addSql('DROP TABLE ec_stat_calendrier_forum_ec_calendrier_forum');
        $this->addSql('DROP TABLE ec_stat_calendrier_stage');
        $this->addSql('DROP TABLE ec_stat_calendrier_stage_ec_calendrier_stage');
        $this->addSql('DROP TABLE ec_statistique');
        $this->addSql('DROP TABLE ec_statistique_ec_session_formation');
        $this->addSql('DROP TABLE ec_statistique_user_user');
        $this->addSql('DROP TABLE edit_article');
        $this->addSql('DROP TABLE edit_article_off_liste_secteur_activite');
        $this->addSql('DROP TABLE edit_article_edit_type_article');
        $this->addSql('DROP TABLE edit_article_edit_theme_article');
        $this->addSql('DROP TABLE edit_article_edit_sous_theme_article');
        $this->addSql('DROP TABLE edit_article_user_user');
        $this->addSql('DROP TABLE edit_carte_departement');
        $this->addSql('DROP TABLE edit_carte_departement_geo_departement');
        $this->addSql('DROP TABLE edit_carte_departement_edit_carte_region');
        $this->addSql('DROP TABLE edit_carte_region');
        $this->addSql('DROP TABLE edit_carte_region_geo_liste_region');
        $this->addSql('DROP TABLE edit_carte_region_edit_article');
        $this->addSql('DROP TABLE edit_carte_visite');
        $this->addSql('DROP TABLE edit_carte_visite_edit_article');
        $this->addSql('DROP TABLE edit_carte_visite_geo_adresse');
        $this->addSql('DROP TABLE edit_focus_accueil');
        $this->addSql('DROP TABLE edit_focus_accueil_edit_app');
        $this->addSql('DROP TABLE edit_image_accueil');
        $this->addSql('DROP TABLE edit_image_accueil_edit_focus_accueil');
        $this->addSql('DROP TABLE edit_image_accueil_edit_theme_article');
        $this->addSql('DROP TABLE edit_image_profil');
        $this->addSql('DROP TABLE edit_image_profil_edit_zoom_profil');
        $this->addSql('DROP TABLE edit_image_zoom');
        $this->addSql('DROP TABLE edit_image_zoom_edit_zoom_sectoriel');
        $this->addSql('DROP TABLE edit_lien_photo_article');
        $this->addSql('DROP TABLE edit_lien_photo_article_edit_article');
        $this->addSql('DROP TABLE edit_lien_photo_article_edit_photo');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_departement');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_departement_edit_rss_thematique');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_departement_geo_departement');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_region');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_region_edit_rss_thematique');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_region_geo_liste_region');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_secteur_activite');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_secteur_activite_edit_rss_thematique');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_secteur_activite_off_liste_secteur_activite');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_sous_secteur_activite');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_sous_secteur_activite_edit_rss_thematique');
        $this->addSql('DROP TABLE edit_lien_rss_thematique_sous_secteur_activite_off_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE edit_lien_video_article');
        $this->addSql('DROP TABLE edit_lien_video_article_edit_article');
        $this->addSql('DROP TABLE edit_lien_video_article_edit_video');
        $this->addSql('DROP TABLE edit_lien_zoom_sectoriel_article');
        $this->addSql('DROP TABLE edit_lien_zoom_sectoriel_article_edit_zoom_sectoriel');
        $this->addSql('DROP TABLE edit_lien_zoom_sectoriel_article_edit_article');
        $this->addSql('DROP TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite');
        $this->addSql('DROP TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_edit_zoom_sectoriel');
        $this->addSql('DROP TABLE edit_lien_zoom_sectoriel_off_liste_secteur_activite_off_liste_secteur_activite');
        $this->addSql('DROP TABLE edit_list_accueil');
        $this->addSql('DROP TABLE edit_list_accueil_edit_focus_accueil');
        $this->addSql('DROP TABLE edit_list_accueil_edit_theme_article');
        $this->addSql('DROP TABLE edit_list_entreprise');
        $this->addSql('DROP TABLE edit_list_entreprise_edit_carte_visite');
        $this->addSql('DROP TABLE edit_list_entreprise_entr_entreprise');
        $this->addSql('DROP TABLE edit_list_tag');
        $this->addSql('DROP TABLE edit_list_tag_edit_app');
        $this->addSql('DROP TABLE edit_list_tag_off_liste_secteur_activite');
        $this->addSql('DROP TABLE edit_log_habillage');
        $this->addSql('DROP TABLE edit_log_habillage_edit_habillage');
        $this->addSql('DROP TABLE edit_page_bouche');
        $this->addSql('DROP TABLE edit_page_bouche_edit_app');
        $this->addSql('DROP TABLE edit_page_cadre');
        $this->addSql('DROP TABLE edit_page_cadre_edit_app');
        $this->addSql('DROP TABLE edit_page_cadre_edit_article');
        $this->addSql('DROP TABLE edit_page_retail');
        $this->addSql('DROP TABLE edit_page_retail_edit_app');
        $this->addSql('DROP TABLE edit_page_thematique');
        $this->addSql('DROP TABLE edit_page_thematique_edit_sous_theme_article');
        $this->addSql('DROP TABLE edit_publicite_entreprise_carte_region');
        $this->addSql('DROP TABLE edit_publicite_entreprise_carte_region_edit_app');
        $this->addSql('DROP TABLE edit_publicite_entreprise_carte_region_edit_carte_region');
        $this->addSql('DROP TABLE edit_publicite_entreprise_carte_region_user_user');
        $this->addSql('DROP TABLE edit_referencement');
        $this->addSql('DROP TABLE edit_referencement_edit_app');
        $this->addSql('DROP TABLE edit_sous_theme_article');
        $this->addSql('DROP TABLE edit_sous_theme_article_edit_theme_article');
        $this->addSql('DROP TABLE edit_statistique');
        $this->addSql('DROP TABLE edit_statistique_edit_article');
        $this->addSql('DROP TABLE edit_statistique_structure');
        $this->addSql('DROP TABLE edit_statistique_structure_user_user');
        $this->addSql('DROP TABLE edit_statistique_structure_ec_ecole');
        $this->addSql('DROP TABLE edit_statistique_structure_ec_organisme_formation');
        $this->addSql('DROP TABLE edit_statistique_structure_entr_entreprise');
        $this->addSql('DROP TABLE edit_zoom_accueil');
        $this->addSql('DROP TABLE edit_zoom_accueil_edit_focus_accueil');
        $this->addSql('DROP TABLE edit_zoom_profil');
        $this->addSql('DROP TABLE edit_zoom_profil_edit_article');
        $this->addSql('DROP TABLE entr_entreprise');
        $this->addSql('DROP TABLE entr_entreprise_geo_adresse');
        $this->addSql('DROP TABLE entr_entreprise_entr_profil_recruteur');
        $this->addSql('DROP TABLE entr_lien_entreprise_off_liste_secteur_activite');
        $this->addSql('DROP TABLE entr_lien_entreprise_off_liste_secteur_activite_entr_entreprise');
        $this->addSql('DROP TABLE entr_lien_entreprise_off_liste_secteur_activite_off_liste_secteur_activite');
        $this->addSql('DROP TABLE entr_lien_entreprise_off_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_entr_entreprise');
        $this->addSql('DROP TABLE entr_lien_entreprise_off_liste_sous_secteur_activite_off_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE exp_cand_diplome');
        $this->addSql('DROP TABLE exp_correspondance');
        $this->addSql('DROP TABLE exp_expression');
        $this->addSql('DROP TABLE exp_expression_exp_valeur_offre');
        $this->addSql('DROP TABLE exp_expression_exp_valeur_fixe');
        $this->addSql('DROP TABLE exp_expression_exp_fichier');
        $this->addSql('DROP TABLE exp_expression_exp_correspondance');
        $this->addSql('DROP TABLE exp_fichier');
        $this->addSql('DROP TABLE exp_fichier_exp_type_fichier');
        $this->addSql('DROP TABLE exp_lien_expression_mise_en_forme');
        $this->addSql('DROP TABLE exp_lien_expression_mise_en_forme_exp_mise_en_forme');
        $this->addSql('DROP TABLE exp_lien_expression_mise_en_forme_exp_expression');
        $this->addSql('DROP TABLE exp_lien_mise_en_forme_parametre');
        $this->addSql('DROP TABLE exp_lien_mise_en_forme_parametre_exp_mise_en_forme');
        $this->addSql('DROP TABLE exp_lien_mise_en_forme_parametre_exp_parametre');
        $this->addSql('DROP TABLE exp_lien_valeur_fixe_parametre');
        $this->addSql('DROP TABLE exp_lien_valeur_fixe_parametre_exp_valeur_fixe');
        $this->addSql('DROP TABLE exp_lien_valeur_fixe_parametre_exp_parametre');
        $this->addSql('DROP TABLE exp_lien_valeur_offre_parametre');
        $this->addSql('DROP TABLE exp_lien_valeur_offre_parametre_exp_valeur_offre');
        $this->addSql('DROP TABLE exp_lien_valeur_offre_parametre_exp_parametre');
        $this->addSql('DROP TABLE exp_mise_en_forme');
        $this->addSql('DROP TABLE exp_parametre');
        $this->addSql('DROP TABLE exp_table_correspondance');
        $this->addSql('DROP TABLE exp_table_correspondance_exp_correspondance');
        $this->addSql('DROP TABLE exp_type_fichier');
        $this->addSql('DROP TABLE exp_valeur_fixe');
        $this->addSql('DROP TABLE exp_valeur_offre');
        $this->addSql('DROP TABLE fiche_carrousel_carriere');
        $this->addSql('DROP TABLE fiche_entreprise');
        $this->addSql('DROP TABLE fiche_metier');
        $this->addSql('DROP TABLE fiche_metier_off_liste_secteur_activite');
        $this->addSql('DROP TABLE fiche_metier_off_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE fiche_metier_edit_app');
        $this->addSql('DROP TABLE fiche_metier_activation');
        $this->addSql('DROP TABLE form_fiche');
        $this->addSql('DROP TABLE form_fiche_hebdo');
        $this->addSql('DROP TABLE form_formation');
        $this->addSql('DROP TABLE form_formation_form_formation');
        $this->addSql('DROP TABLE form_lien_formation_off_geo_departement');
        $this->addSql('DROP TABLE form_lien_formation_off_geo_liste_region');
        $this->addSql('DROP TABLE form_lien_formation_off_liste_secteur_activite');
        $this->addSql('DROP TABLE form_lien_formation_off_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE form_lien_formation_off_liste_type_formation');
        $this->addSql('DROP TABLE formation_footer');
        $this->addSql('DROP TABLE geo_adresse');
        $this->addSql('DROP TABLE geo_adresse_geo_liste_region');
        $this->addSql('DROP TABLE geo_adresse_geo_liste_pays');
        $this->addSql('DROP TABLE geo_departement');
        $this->addSql('DROP TABLE geo_departement_geo_liste_region');
        $this->addSql('DROP TABLE geo_liste_pays');
        $this->addSql('DROP TABLE geo_liste_region');
        $this->addSql('DROP TABLE geo_liste_region_geo_liste_pays');
        $this->addSql('DROP TABLE geo_ville');
        $this->addSql('DROP TABLE geo_ville_geo_departement');
        $this->addSql('DROP TABLE geo_ville_geo_liste_region');
        $this->addSql('DROP TABLE geo_ville_bck');
        $this->addSql('DROP TABLE geo_ville_bck_geo_departement');
        $this->addSql('DROP TABLE geo_ville_bck_geo_liste_region');
        $this->addSql('DROP TABLE geo_ville_principale');
        $this->addSql('DROP TABLE geo_ville_svg');
        $this->addSql('DROP TABLE geo_ville_svg_geo_departement');
        $this->addSql('DROP TABLE geo_ville_svg_geo_liste_region');
        $this->addSql('DROP TABLE log_php_erreur');
        $this->addSql('DROP TABLE logo_maillage');
        $this->addSql('DROP TABLE mb_css');
        $this->addSql('DROP TABLE mb_moteur_recherche');
        $this->addSql('DROP TABLE mb_moteur_recherche_mb_css');
        $this->addSql('DROP TABLE mc_contrat');
        $this->addSql('DROP TABLE mc_contrat_mc_zone_mc');
        $this->addSql('DROP TABLE mc_localisation');
        $this->addSql('DROP TABLE mc_localisation_mc_zone_mc');
        $this->addSql('DROP TABLE mc_localisation_mc_type_localisation');
        $this->addSql('DROP TABLE mc_localisation_svg');
        $this->addSql('DROP TABLE mc_localisation_svg_mc_zone_mc');
        $this->addSql('DROP TABLE mc_localisation_svg_mc_type_localisation');
        $this->addSql('DROP TABLE mc_metier');
        $this->addSql('DROP TABLE mc_metier_mc_zone_mc');
        $this->addSql('DROP TABLE mc_secteur');
        $this->addSql('DROP TABLE mc_secteur_mc_zone_mc');
        $this->addSql('DROP TABLE mc_type_localisation');
        $this->addSql('DROP TABLE mc_zone_mc');
        $this->addSql('DROP TABLE mc_zone_mc_edit_app');
        $this->addSql('DROP TABLE metier_domaine_group');
        $this->addSql('DROP TABLE metier_metier');
        $this->addSql('DROP TABLE off_candidature');
        $this->addSql('DROP TABLE off_candidature_user_user');
        $this->addSql('DROP TABLE off_candidature_off_offre');
        $this->addSql('DROP TABLE off_candidature_off_liste_type_suivi');
        $this->addSql('DROP TABLE off_fourchette_remuneration');
        $this->addSql('DROP TABLE off_lien_offre_geo_departement');
        $this->addSql('DROP TABLE off_lien_offre_geo_departement_geo_departement');
        $this->addSql('DROP TABLE off_lien_offre_geo_departement_off_offre');
        $this->addSql('DROP TABLE off_lien_offre_geo_liste_pays');
        $this->addSql('DROP TABLE off_lien_offre_geo_liste_pays_geo_liste_pays');
        $this->addSql('DROP TABLE off_lien_offre_geo_liste_pays_off_offre');
        $this->addSql('DROP TABLE off_lien_offre_geo_liste_region');
        $this->addSql('DROP TABLE off_lien_offre_geo_liste_region_geo_liste_region');
        $this->addSql('DROP TABLE off_lien_offre_geo_liste_region_off_offre');
        $this->addSql('DROP TABLE off_lien_offre_liste_experience_requise');
        $this->addSql('DROP TABLE off_lien_offre_liste_experience_requise_off_liste_experience_requise');
        $this->addSql('DROP TABLE off_lien_offre_liste_experience_requise_off_offre');
        $this->addSql('DROP TABLE off_lien_offre_liste_secteur_activite');
        $this->addSql('DROP TABLE off_lien_offre_liste_secteur_activite_off_offre');
        $this->addSql('DROP TABLE off_lien_offre_liste_secteur_activite_off_liste_secteur_activite');
        $this->addSql('DROP TABLE off_lien_offre_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE off_lien_offre_liste_sous_secteur_activite_off_offre');
        $this->addSql('DROP TABLE off_lien_offre_liste_sous_secteur_activite_off_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE off_lien_offre_liste_type_formation');
        $this->addSql('DROP TABLE off_lien_offre_liste_type_formation_off_offre');
        $this->addSql('DROP TABLE off_lien_offre_liste_type_formation_off_liste_type_formation');
        $this->addSql('DROP TABLE off_lien_sous_secteur_activite_secteur_activite');
        $this->addSql('DROP TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE off_lien_sous_secteur_activite_secteur_activite_off_liste_secteur_activite');
        $this->addSql('DROP TABLE off_liste_experience_requise');
        $this->addSql('DROP TABLE off_liste_metier');
        $this->addSql('DROP TABLE off_liste_secteur_activite');
        $this->addSql('DROP TABLE off_liste_secteur_activite_off_liste_type_secteur_activite');
        $this->addSql('DROP TABLE off_liste_sous_secteur_activite');
        $this->addSql('DROP TABLE off_liste_sous_secteur_activite_off_liste_secteur_activite');
        $this->addSql('DROP TABLE off_liste_sous_secteur_activite_off_liste_type_secteur_activite');
        $this->addSql('DROP TABLE off_liste_sous_secteur_activite_edit_app');
        $this->addSql('DROP TABLE off_liste_type_contrat');
        $this->addSql('DROP TABLE off_liste_type_formation');
        $this->addSql('DROP TABLE off_liste_type_formation_com_liste_type_diffusion');
        $this->addSql('DROP TABLE off_liste_type_secteur_activite');
        $this->addSql('DROP TABLE off_liste_type_suivi');
        $this->addSql('DROP TABLE off_meta_moteur');
        $this->addSql('DROP TABLE off_meta_moteur_entr_profil_recruteur');
        $this->addSql('DROP TABLE off_off_non_exporte');
        $this->addSql('DROP TABLE off_offre');
        $this->addSql('DROP TABLE off_offre_com_offre_commerciale');
        $this->addSql('DROP TABLE off_offre_off_fourchette_remuneration');
        $this->addSql('DROP TABLE off_offre_com_liste_type_offre_commerciale');
        $this->addSql('DROP TABLE off_offre_non_exporte');
        $this->addSql('DROP TABLE off_reponse_offre_non_identifie');
        $this->addSql('DROP TABLE off_reponse_offre_non_identifie_off_offre');
        $this->addSql('DROP TABLE off_reponse_offre_non_identifie_ec_ecole');
        $this->addSql('DROP TABLE off_reponse_offre_non_identifie_ec_session_formation');
        $this->addSql('DROP TABLE off_reponse_offre_non_identifie_off_liste_type_formation');
        $this->addSql('DROP TABLE off_sauvegarde_offre');
        $this->addSql('DROP TABLE off_sauvegarde_offre_off_offre');
        $this->addSql('DROP TABLE off_sauvegarde_offre_user_user');
        $this->addSql('DROP TABLE off_similaires_stats');
        $this->addSql('DROP TABLE off_statistique');
        $this->addSql('DROP TABLE off_statistique_off_offre');
        $this->addSql('DROP TABLE off_statistique_entr_entreprise');
        $this->addSql('DROP TABLE off_statistique_old_save');
        $this->addSql('DROP TABLE off_statistique_old_save_entr_entreprise');
        $this->addSql('DROP TABLE off_stats_hebdo');
        $this->addSql('DROP TABLE off_stats_position');
        $this->addSql('DROP TABLE off_type_xml');
        $this->addSql('DROP TABLE page_cadre_carousel');
        $this->addSql('DROP TABLE promo_lien_promotion_off_offre');
        $this->addSql('DROP TABLE promo_lien_promotion_off_offre_promo_promotion');
        $this->addSql('DROP TABLE promo_lien_promotion_off_offre_off_offre');
        $this->addSql('DROP TABLE promo_lien_promotion_offre_commerciale');
        $this->addSql('DROP TABLE promo_lien_promotion_offre_commerciale_promo_promotion');
        $this->addSql('DROP TABLE promo_lien_promotion_offre_commerciale_com_offre_commerciale');
        $this->addSql('DROP TABLE promo_lien_promotion_user_user');
        $this->addSql('DROP TABLE promo_lien_promotion_user_user_promo_promotion');
        $this->addSql('DROP TABLE promo_lien_promotion_user_user_user_user');
        $this->addSql('DROP TABLE promo_promotion');
        $this->addSql('DROP TABLE recherche_avancee');
        $this->addSql('DROP TABLE ref_lien_multi');
        $this->addSql('DROP TABLE ref_lien_multi_edit_app');
        $this->addSql('DROP TABLE save_mail_candi_non_log');
        $this->addSql('DROP TABLE save_mail_candi_non_log_edit_app');
        $this->addSql('DROP TABLE save_mail_candi_non_log_off_liste_type_contrat');
        $this->addSql('DROP TABLE script_formation');
        $this->addSql('DROP TABLE script_principaux');
        $this->addSql('DROP TABLE script_secteurs_all');
        $this->addSql('DROP TABLE script_secteurs_stats');
        $this->addSql('DROP TABLE script_secteurs_stats_user_user');
        $this->addSql('DROP TABLE script_stats');
        $this->addSql('DROP TABLE script_stats_formation');
        $this->addSql('DROP TABLE seo_combinaisons');
        $this->addSql('DROP TABLE seo_edit_referencement');
        $this->addSql('DROP TABLE seo_edit_referencement_edit_app');
        $this->addSql('DROP TABLE seo_lien_mot_cle_attente_mot_cle_racine');
        $this->addSql('DROP TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_attente');
        $this->addSql('DROP TABLE seo_lien_mot_cle_attente_mot_cle_racine_seo_mot_cle_racine');
        $this->addSql('DROP TABLE seo_lien_mot_cle_mot_cle_racine');
        $this->addSql('DROP TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle');
        $this->addSql('DROP TABLE seo_lien_mot_cle_mot_cle_racine_seo_mot_cle_racine');
        $this->addSql('DROP TABLE seo_lien_mot_cle_racine_offre');
        $this->addSql('DROP TABLE seo_lien_mot_cle_racine_offre_seo_mot_cle_racine');
        $this->addSql('DROP TABLE seo_lien_mot_cle_racine_offre_off_offre');
        $this->addSql('DROP TABLE seo_liste_secteur');
        $this->addSql('DROP TABLE seo_liste_theme');
        $this->addSql('DROP TABLE seo_liste_type');
        $this->addSql('DROP TABLE seo_liste_url');
        $this->addSql('DROP TABLE seo_mot_cle');
        $this->addSql('DROP TABLE seo_mot_cle_seo_liste_type');
        $this->addSql('DROP TABLE seo_mot_cle_seo_liste_theme');
        $this->addSql('DROP TABLE seo_mot_cle_seo_liste_secteur');
        $this->addSql('DROP TABLE seo_mot_cle_seo_combinaisons');
        $this->addSql('DROP TABLE seo_mot_cle_attente');
        $this->addSql('DROP TABLE seo_mot_cle_attente_seo_liste_type');
        $this->addSql('DROP TABLE seo_mot_cle_attente_seo_liste_theme');
        $this->addSql('DROP TABLE seo_mot_cle_attente_seo_liste_secteur');
        $this->addSql('DROP TABLE seo_mot_cle_racine');
        $this->addSql('DROP TABLE seo_mot_cle_racine_seo_liste_type');
        $this->addSql('DROP TABLE seo_mot_cle_racine_seo_liste_theme');
        $this->addSql('DROP TABLE seo_mot_cle_racine_seo_liste_secteur');
        $this->addSql('DROP TABLE sf_exception');
        $this->addSql('DROP TABLE statistique_pole_emploi');
        $this->addSql('DROP TABLE stats_clique_pub');
        $this->addSql('DROP TABLE stats_search');
        $this->addSql('DROP TABLE stats_search_tend');
        $this->addSql('DROP TABLE temp_greg');
        $this->addSql('DROP TABLE tra_lien');
        $this->addSql('DROP TABLE tra_statistique');
        $this->addSql('DROP TABLE user_offre_veille_user_user');
        $this->addSql('DROP TABLE user_situation');
        $this->addSql('DROP TABLE user_situation_user_user');
        $this->addSql('DROP TABLE user_situation_user_situation_activite');
        $this->addSql('DROP TABLE user_situation_user_situation_statut');
        $this->addSql('DROP TABLE user_situation_off_liste_type_formation');
        $this->addSql('DROP TABLE user_situation_user_situation_experience');
        $this->addSql('DROP TABLE user_situation_user_disponibilite');
        $this->addSql('DROP TABLE user_situation_user_situation_mobilite');
        $this->addSql('DROP TABLE user_situation_ec_ecole');
        $this->addSql('DROP TABLE user_situation_ec_session_formation');
        $this->addSql('DROP TABLE user_situation_off_liste_secteur_activite');
        $this->addSql('DROP TABLE user_situation_experience');
        $this->addSql('DROP TABLE user_situation_experience_off_liste_experience_requise');
        $this->addSql('DROP TABLE user_stats');
        $this->addSql('DROP TABLE web_user_agent');
        $this->addSql('ALTER TABLE annuaire_interim CHANGE code_postal code_p?ostal VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`');
        $this->addSql('ALTER TABLE balises_html_categories_page DROP FOREIGN KEY FK_D0A0CF22B995E37A');
        $this->addSql('ALTER TABLE balises_html_categories_page DROP FOREIGN KEY FK_D0A0CF22244CB244');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_com_liste_diffusion DROP FOREIGN KEY FK_2E12D7AD7E686F8A');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_com_liste_diffusion DROP FOREIGN KEY FK_2E12D7AD714A20DF');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_ec_ecole DROP FOREIGN KEY FK_15FCE0657E686F8A');
        $this->addSql('ALTER TABLE com_lien_liste_diffusion_ec_ecole_ec_session_formation DROP FOREIGN KEY FK_FEFBF3317E686F8A');
        $this->addSql('ALTER TABLE com_lien_off_com_type_contrat_com_offre_commerciale DROP FOREIGN KEY FK_FD135FEAAB55031C');
        $this->addSql('ALTER TABLE com_lien_off_com_type_contrat_com_offre_commerciale DROP FOREIGN KEY FK_FD135FEA23CD91B5');
        $this->addSql('ALTER TABLE com_lien_off_com_type_contrat_off_liste_type_contrat DROP FOREIGN KEY FK_686EB500AB55031C');
        $this->addSql('ALTER TABLE com_liste_diffusion_off_liste_type_formation DROP FOREIGN KEY FK_5DEE3BAE714A20DF');
        $this->addSql('ALTER TABLE com_liste_diffusion_off_offre DROP FOREIGN KEY FK_4BAEE28D714A20DF');
        $this->addSql('ALTER TABLE com_lot_souscription_user_user DROP FOREIGN KEY FK_7B3B67504BC5E32');
        $this->addSql('ALTER TABLE com_lot_souscription_user_user DROP FOREIGN KEY FK_7B3B6750FF63CD9F');
        $this->addSql('ALTER TABLE com_offre_commerciale_com_liste_type_offre_commerciale DROP FOREIGN KEY FK_52786F4923CD91B5');
        $this->addSql('ALTER TABLE com_offre_commerciale_com_liste_type_offre_commerciale DROP FOREIGN KEY FK_52786F4933987EE4');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_lot_souscription DROP FOREIGN KEY FK_63ADFB0B89ED7EC0');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_lot_souscription DROP FOREIGN KEY FK_63ADFB0B4BC5E32');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_offre_commerciale DROP FOREIGN KEY FK_16B36AB389ED7EC0');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_offre_commerciale DROP FOREIGN KEY FK_16B36AB323CD91B5');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_type_paiement DROP FOREIGN KEY FK_B28F03A289ED7EC0');
        $this->addSql('ALTER TABLE com_souscription_commerciale_com_type_paiement DROP FOREIGN KEY FK_B28F03A2B2703AF3');
        $this->addSql('ALTER TABLE com_souscription_commerciale_promo_promotion DROP FOREIGN KEY FK_B660278C89ED7EC0');
        $this->addSql('ALTER TABLE com_souscription_commerciale_user_user DROP FOREIGN KEY FK_9536C10B89ED7EC0');
        $this->addSql('ALTER TABLE com_souscription_commerciale_user_user DROP FOREIGN KEY FK_9536C10BFF63CD9F');
        $this->addSql('ALTER TABLE common_attachement_mail_edit_article DROP FOREIGN KEY FK_2023388C2B5F8DB6');
        $this->addSql('ALTER TABLE common_message_common_type_message DROP FOREIGN KEY FK_74B2FF26687A3DD7');
        $this->addSql('ALTER TABLE common_message_common_type_message DROP FOREIGN KEY FK_74B2FF26C5221C05');
        $this->addSql('ALTER TABLE common_statistique_cv_cv DROP FOREIGN KEY FK_4D4808A593C9F739');
        $this->addSql('ALTER TABLE common_statistique_cv_cv DROP FOREIGN KEY FK_4D4808A540E0C9FD');
        $this->addSql('ALTER TABLE common_statistique_ec_organisme_formation DROP FOREIGN KEY FK_4195F6BA93C9F739');
        $this->addSql('ALTER TABLE common_statistique_ec_session_formation DROP FOREIGN KEY FK_9723F1BD93C9F739');
        $this->addSql('ALTER TABLE common_statistique_off_offre DROP FOREIGN KEY FK_AED51EC693C9F739');
        $this->addSql('ALTER TABLE cor123_contrat_off_liste_type_contrat DROP FOREIGN KEY FK_B7A5784ED245226E');
        $this->addSql('ALTER TABLE cor_easyoffre_secteur_off_liste_secteur_activite DROP FOREIGN KEY FK_80AF6B22A40B1A86');
        $this->addSql('ALTER TABLE cor_keljob_contrat_off_liste_type_contrat DROP FOREIGN KEY FK_41E313B745D090ED');
        $this->addSql('ALTER TABLE cor_keljob_experience_off_liste_experience_requise DROP FOREIGN KEY FK_1A20B21FF30627FE');
        $this->addSql('ALTER TABLE cor_keljob_lieu_geo_departement DROP FOREIGN KEY FK_11BC02663B5DE6CA');
        $this->addSql('ALTER TABLE cor_keljob_lieu_geo_liste_region DROP FOREIGN KEY FK_4EA9BA9B3B5DE6CA');
        $this->addSql('ALTER TABLE cor_keljob_salaire_off_fourchette_remuneration DROP FOREIGN KEY FK_9A10DC107B8B5173');
        $this->addSql('ALTER TABLE cor_keljob_secteur_off_liste_secteur_activite DROP FOREIGN KEY FK_46B21C28DD2F7');
        $this->addSql('ALTER TABLE cor_plusrh_secteur_off_liste_secteur_activite DROP FOREIGN KEY FK_7B45C2245EC53487');
        $this->addSql('ALTER TABLE cor_plusrh_secteur_off_liste_sous_secteur_activite DROP FOREIGN KEY FK_8E7D0FC45EC53487');
        $this->addSql('ALTER TABLE cv_centre_interet_cv_cv DROP FOREIGN KEY FK_9FBD0F24946F3433');
        $this->addSql('ALTER TABLE cv_centre_interet_cv_cv DROP FOREIGN KEY FK_9FBD0F2440E0C9FD');
        $this->addSql('ALTER TABLE cv_cv_off_fourchette_remuneration DROP FOREIGN KEY FK_4015982A40E0C9FD');
        $this->addSql('ALTER TABLE cv_cv_user_user DROP FOREIGN KEY FK_A898436940E0C9FD');
        $this->addSql('ALTER TABLE cv_cv_user_user DROP FOREIGN KEY FK_A8984369FF63CD9F');
        $this->addSql('ALTER TABLE cv_experience_pro_cv_cv DROP FOREIGN KEY FK_6ABEC46CF6D4F645');
        $this->addSql('ALTER TABLE cv_experience_pro_cv_cv DROP FOREIGN KEY FK_6ABEC46C40E0C9FD');
        $this->addSql('ALTER TABLE cv_formation_cv_cv DROP FOREIGN KEY FK_888F11E89112D88B');
        $this->addSql('ALTER TABLE cv_formation_cv_cv DROP FOREIGN KEY FK_888F11E840E0C9FD');
        $this->addSql('ALTER TABLE cv_formation_off_liste_type_formation DROP FOREIGN KEY FK_E402B0D59112D88B');
        $this->addSql('ALTER TABLE cv_langue_cv_cv DROP FOREIGN KEY FK_339FB7BCB6359381');
        $this->addSql('ALTER TABLE cv_langue_cv_cv DROP FOREIGN KEY FK_339FB7BC40E0C9FD');
        $this->addSql('ALTER TABLE cv_langue_cv_liste_choix_langue DROP FOREIGN KEY FK_60575B56B6359381');
        $this->addSql('ALTER TABLE cv_langue_cv_liste_choix_langue DROP FOREIGN KEY FK_60575B569CBED8F8');
        $this->addSql('ALTER TABLE cv_langue_cv_niveau_langue DROP FOREIGN KEY FK_C0B168EB6359381');
        $this->addSql('ALTER TABLE cv_langue_cv_niveau_langue DROP FOREIGN KEY FK_C0B168ECD942705');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_departement_cv_experience_pro DROP FOREIGN KEY FK_83640919992E7864');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_departement_cv_experience_pro DROP FOREIGN KEY FK_83640919F6D4F645');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_departement_geo_departement DROP FOREIGN KEY FK_B7CDDF28992E7864');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_pays_cv_experience_pro DROP FOREIGN KEY FK_57292ADCC5608FA2');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_pays_cv_experience_pro DROP FOREIGN KEY FK_57292ADCF6D4F645');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_pays_geo_liste_pays DROP FOREIGN KEY FK_7979AD53C5608FA2');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_region_cv_experience_pro DROP FOREIGN KEY FK_5274EDB475D788B6');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_region_cv_experience_pro DROP FOREIGN KEY FK_5274EDB4F6D4F645');
        $this->addSql('ALTER TABLE cv_lien_experience_pro_geo_liste_region_geo_liste_region DROP FOREIGN KEY FK_74E8FB7575D788B6');
        $this->addSql('ALTER TABLE user_lien_situation_mobilite_geo_liste_region DROP user_situation_mobilite_id_situation_mobilite, DROP geo_liste_region_id_region');
    }
}
